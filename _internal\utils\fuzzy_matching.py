#!/usr/bin/env python3
"""
PlexAutomator/_internal/utils/fuzzy_matching.py

Enhanced fuzzy metadata validation using RapidFuzz and TMDb best practices.
Implements intelligent title matching with confidence scoring and error handling.
"""

import re
import logging
import time
import json
from datetime import datetime, timed<PERSON><PERSON>
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from rapidfuzz import fuzz, process

logger = logging.getLogger(__name__)

# Default confidence thresholds (can be overridden by config)
HIGH_CONFIDENCE_THRESHOLD = 95  # Auto-approve matches
MODERATE_CONFIDENCE_THRESHOLD = 80  # Silent correction with warning
LOW_CONFIDENCE_THRESHOLD = 60  # Requires user confirmation
REJECT_THRESHOLD = 60  # Below this, reject the match

# Year tolerance for metadata matching
YEAR_TOLERANCE = 3  # ±3 years


def get_config_or_default():
    """Get fuzzy matching configuration or use defaults."""
    try:
        from _internal.utils.fuzzy_config import get_fuzzy_config
        return get_fuzzy_config()
    except ImportError:
        return None


def get_threshold(threshold_type: str) -> float:
    """Get confidence threshold with config override support."""
    config = get_config_or_default()
    if config:
        return config.get_threshold(threshold_type)
    
    # Fallback to defaults
    thresholds = {
        "high": HIGH_CONFIDENCE_THRESHOLD,
        "moderate": MODERATE_CONFIDENCE_THRESHOLD,
        "low": LOW_CONFIDENCE_THRESHOLD
    }
    return thresholds.get(threshold_type, HIGH_CONFIDENCE_THRESHOLD)


def get_year_tolerance() -> int:
    """Get year tolerance with config override support."""
    config = get_config_or_default()
    if config:
        return config.get_year_tolerance()
    return YEAR_TOLERANCE


def filter_exact_matches(query_title: str, candidates: List[Dict[str, Any]], content_type: str = "movie") -> List[Dict[str, Any]]:
    """
    Filter candidates to prioritize exact matches over movies with subtitles/extra content.
    
    Args:
        query_title: User's search query
        candidates: List of TMDb candidates
        content_type: "movie" or "tv"
    
    Returns:
        Filtered list prioritizing exact matches
    """
    if not candidates:
        return candidates
    
    # Normalize the query for comparison
    query_norm = normalize_title(query_title)
    query_words = set(query_norm.split())
    
    # Categorize candidates
    exact_matches = []
    close_matches = []
    loose_matches = []
    
    for candidate in candidates:
        title_field = "title" if content_type == "movie" else "name"
        candidate_title = candidate.get(title_field, "")
        candidate_norm = normalize_title(candidate_title)
        candidate_words = set(candidate_norm.split())
        
        # Remove common articles for comparison
        query_core_words = query_words - {'the', 'a', 'an'}
        candidate_core_words = candidate_words - {'the', 'a', 'an'}
        
        # Check for exact match (ignoring articles)
        if query_core_words == candidate_core_words:
            exact_matches.append(candidate)
            logger.debug(f"Exact match: '{query_title}' -> '{candidate_title}'")
            
        # Check if query words are subset of candidate (but candidate has extra words)
        elif query_core_words.issubset(candidate_core_words):
            extra_words = candidate_core_words - query_core_words
            
            # If there are only a few extra words and they're not sequel indicators, it's close
            if len(extra_words) <= 2 and not any(word in extra_words for word in ['2', '3', '4', '5', 'ii', 'iii', 'iv', 'v', 'sequel', 'returns', 'rises', 'begins']):
                close_matches.append(candidate)
                logger.debug(f"Close match: '{query_title}' -> '{candidate_title}' (extra: {extra_words})")
            else:
                loose_matches.append(candidate)
                logger.debug(f"Loose match: '{query_title}' -> '{candidate_title}' (extra: {extra_words})")
        else:
            loose_matches.append(candidate)
    
    # Return prioritized list: exact matches first, then close, then loose
    prioritized = exact_matches + close_matches + loose_matches
    
    if exact_matches:
        logger.info(f"Found {len(exact_matches)} exact matches for '{query_title}', prioritizing them")
    
    return prioritized


def detect_multiple_candidates(candidates: List[Dict[str, Any]], content_type: str = "movie") -> Dict[str, List[Dict[str, Any]]]:
    """
    Detect multiple candidates with similar titles but different years.
    Groups candidates by normalized title.
    
    Args:
        candidates: List of TMDb search results
        content_type: "movie" or "tv"
        
    Returns:
        Dictionary mapping normalized titles to lists of candidates
    """
    grouped = {}
    
    for candidate in candidates:
        if content_type == "movie":
            title = candidate.get("title", "")
        else:
            title = candidate.get("name", "")
        
        # Normalize title for grouping
        norm_title = normalize_title(title)
        
        if norm_title:
            if norm_title not in grouped:
                grouped[norm_title] = []
            grouped[norm_title].append(candidate)
    
    # Only return groups with multiple candidates
    return {title: candidates for title, candidates in grouped.items() if len(candidates) > 1}


def should_request_user_confirmation_for_multiple(candidates: List[Dict[str, Any]], 
                                                 user_year: Optional[int] = None,
                                                 content_type: str = "movie") -> bool:
    """
    Determine if user confirmation is needed when multiple similar candidates exist.
    
    Args:
        candidates: List of candidates with similar titles
        user_year: Year provided by user
        content_type: "movie" or "tv"
        
    Returns:
        True if user confirmation is needed
    """
    if len(candidates) <= 1:
        return False
    
    # Extract years from candidates
    candidate_years = []
    for candidate in candidates:
        year = extract_candidate_year(candidate, content_type)
        if year:
            candidate_years.append(year)
    
    # If user provided a year, check if there are multiple candidates within tolerance
    if user_year:
        tolerance = get_year_tolerance()
        close_matches = [year for year in candidate_years if abs(year - user_year) <= tolerance]
        if len(close_matches) > 1:
            return True
    
    # If no user year provided and there are multiple candidates with different years
    unique_years = set(candidate_years)
    if len(unique_years) > 1:
        return True
    
    return False


def format_multiple_candidates_message(candidates: List[Dict[str, Any]], 
                                     content_type: str = "movie") -> str:
    """
    Format a user-friendly message for multiple candidates.
    
    Args:
        candidates: List of candidates with similar titles
        content_type: "movie" or "tv"
        
    Returns:
        Formatted message string
    """
    if content_type == "movie":
        title_key = "title"
        year_key = "release_date"
    else:
        title_key = "name"
        year_key = "first_air_date"
    
    message_parts = ["Multiple versions found:"]
    
    for i, candidate in enumerate(candidates[:5], 1):  # Limit to 5 options
        title = candidate.get(title_key, "Unknown")
        date_str = candidate.get(year_key, "")
        year = date_str[:4] if date_str and len(date_str) >= 4 else "Unknown"
        
        message_parts.append(f"{i}. {title} ({year})")
    
    if len(candidates) > 5:
        message_parts.append(f"... and {len(candidates) - 5} more")
    
    return "\n".join(message_parts)


def normalize_title(title: str) -> str:
    """
    Enhanced normalize title for better comparison by standardizing:
    - Case (lowercase)
    - Punctuation removal
    - Special character handling
    - Whitespace normalization
    - Common typos and abbreviations
    - Spell checking for obvious errors
    """
    if not title:
        return ""
    
    # Convert to lowercase
    normalized = title.lower()
    
    # FIRST: Handle critical abbreviations before any other processing
    # This ensures LOTR -> Lord of the Rings happens immediately
    critical_abbreviations = {
        r'\blotr\b': 'lord of the rings',
        r'\blotr fellowship\b': 'lord of the rings fellowship of the ring',
        r'\blotr two towers\b': 'lord of the rings two towers',
        r'\blotr return\b': 'lord of the rings return of the king',
    }
    
    for pattern, expansion in critical_abbreviations.items():
        if re.search(pattern, normalized, re.IGNORECASE):
            normalized = re.sub(pattern, expansion, normalized, flags=re.IGNORECASE)
            logger.info(f"Applied critical abbreviation: '{title}' -> '{normalized}'")
    
    # Handle common typos BEFORE other normalization
    typo_corrections = {
        'jurrassic': 'jurassic',
        'shinning': 'shining',  # The Shining - this is critical!
        'professonal': 'professional',
        'seperated': 'separated',
        'definately': 'definitely',
        'occured': 'occurred',
        'recieve': 'receive',
        'seperately': 'separately',
        'begining': 'beginning',
        'occurance': 'occurrence',
        'empier': 'empire',  # Empire Strikes Back
        'strikse': 'strikes',
        'striek': 'strike',
        'metrix': 'matrix',
        'fantastik': 'fantastic',
        'shineing': 'shining',
        'teh': 'the',
    }
    
    for typo, correct in typo_corrections.items():
        if typo in normalized:
            normalized = normalized.replace(typo, correct)
            logger.info(f"Applied typo correction: '{typo}' -> '{correct}'")
    
    # Enhanced spell checker for single-word corrections
    try:
        from spellchecker import SpellChecker
        spell = SpellChecker()
        
        words = normalized.split()
        corrected_words = []
        corrections_made = []
        
        for word in words:
            # Skip common movie words, numbers, and very short words
            if len(word) <= 2 or word.isdigit():
                corrected_words.append(word)
                continue
                
            # Skip common stop words and movie-specific terms
            skip_words = {
                'the', 'and', 'or', 'of', 'in', 'on', 'at', 'to', 'for', 'with', 'a', 'an',
                'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had',
                'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might',
                'movie', 'film', 'series', 'show', 'episode', 'season', 'part', 'vol',
                'volume', 'chapter', 'book', 'novel', 'story', 'tale', 'saga', 'trilogy'
            }
            
            if word.lower() in skip_words:
                corrected_words.append(word)
                continue
                
            # Check if word is misspelled
            if word not in spell:
                # Get the most likely correction
                candidates = spell.candidates(word)
                if candidates:
                    # Sort candidates by edit distance and frequency
                    candidate_list = list(candidates)
                    
                    # Prefer candidates with similar length (within 2 characters)
                    similar_length_candidates = [c for c in candidate_list if abs(len(c) - len(word)) <= 2]
                    
                    if similar_length_candidates:
                        best_candidate = similar_length_candidates[0]
                        # Additional validation: ensure significant similarity
                        similarity = fuzz.ratio(word.lower(), best_candidate.lower())
                        if similarity >= 60:  # At least 60% similar
                            corrected_words.append(best_candidate)
                            corrections_made.append(f"'{word}' -> '{best_candidate}'")
                            continue
            
            corrected_words.append(word)
        
        if corrections_made:
            logger.info(f"Spell corrections applied: {', '.join(corrections_made)}")
        
        normalized = ' '.join(corrected_words)
        
    except ImportError:
        # Spell checker not available, continue without it
        logger.debug("SpellChecker not available, skipping spell correction")
    except Exception as e:
        # Any other error, continue without spell checking
        logger.debug(f"Spell checking failed: {e}")
    
    # Phonetic matching for additional spell correction as fallback
    try:
        import jellyfish
        
        # Only apply phonetic matching if we suspect there are still errors
        words = normalized.split()
        if len(words) >= 2:  # For multi-word titles
            phonetic_corrections = {
                # Known phonetic issues
                'filladelphia': 'philadelphia',
                'philladelphia': 'philadelphia', 
                'shinning': 'shining',
                'comming': 'coming',
                'loosing': 'losing',
                'teh': 'the',
                'hte': 'the',
                'nad': 'and',
            }
            
            for i, word in enumerate(words):
                for typo, correct in phonetic_corrections.items():
                    if word.lower() == typo:
                        words[i] = correct
                        logger.debug(f"Applied phonetic correction: '{typo}' -> '{correct}'")
                        break
            
            normalized = ' '.join(words)
            
    except ImportError:
        pass  # jellyfish not available
    except Exception:
        pass  # Continue without phonetic matching
    
    # Handle common abbreviations with improved word boundary detection
    abbreviation_expansions = {
        r'\blotr\b': 'lord of the rings',
        r'\blotr fellowship\b': 'lord of the rings fellowship of the ring',
        r'\blotr two towers\b': 'lord of the rings two towers',
        r'\blotr return\b': 'lord of the rings return of the king',
        r'\bsw\b': 'star wars',
        r'\bhp\b': 'harry potter',
        r'\bgot\b': 'game of thrones',
        r'\bpotc\b': 'pirates of the caribbean',
        r'\bx men\b': 'x-men',
        r'\bxmen\b': 'x-men',
        r'\bmcu\b': 'marvel cinematic universe',
        r'\bdcu\b': 'dc cinematic universe',
        r'\bfotr\b': 'fellowship of the ring',
        r'\bttt\b': 'two towers',
        r'\brotk\b': 'return of the king',
        # Common contractions
        r'\bdont\b': "don't",
        r'\bwont\b': "won't",
        r'\bcant\b': "can't",
        r'\bshouldnt\b': "shouldn't",
        r'\bcouldnt\b': "couldn't",
        r'\bwouldnt\b': "wouldn't",
        r'\bisnt\b': "isn't",
        r'\barent\b': "aren't",
        r'\bwasnt\b': "wasn't",
        r'\bwerent\b': "weren't",
    }
    
    # Apply abbreviation expansions with regex for better word boundary matching
    for pattern, expansion in abbreviation_expansions.items():
        old_normalized = normalized
        normalized = re.sub(pattern, expansion, normalized, flags=re.IGNORECASE)
        if normalized != old_normalized:
            logger.debug(f"Expanded abbreviation: '{old_normalized}' -> '{normalized}'")
            break  # Only apply first match to avoid conflicts
    
    # Handle regional differences
    regional_variants = {
        "sorcerer's stone": "philosopher's stone",
        "sorcerers stone": "philosophers stone",
        "philosopher's stone": "sorcerer's stone",  # Also try reverse
        "philosophers stone": "sorcerers stone",
    }
    
    for variant, canonical in regional_variants.items():
        if variant in normalized:
            normalized = normalized.replace(variant, canonical)
    
    # Handle common variations
    # Remove/normalize punctuation
    normalized = re.sub(r'[:\-–—]', ' ', normalized)  # Replace separators with spaces
    normalized = re.sub(r'[&]', 'and', normalized)    # Ampersand to 'and'
    normalized = re.sub(r"['']", "'", normalized)     # Normalize apostrophes
    normalized = re.sub(r'[^\w\s\']', '', normalized) # Remove other punctuation except apostrophes
    
    # Normalize whitespace
    normalized = re.sub(r'\s+', ' ', normalized).strip()
    
    # Handle Roman numerals (basic ones)
    roman_map = {
        ' ii ': ' 2 ', ' iii ': ' 3 ', ' iv ': ' 4 ', ' v ': ' 5 ',
        ' vi ': ' 6 ', ' vii ': ' 7 ', ' viii ': ' 8 ', ' ix ': ' 9 ',
        ' x ': ' 10 '
    }
    for roman, number in roman_map.items():
        normalized = normalized.replace(roman, number)
    
    # Handle endings - roman numerals at end of string
    for roman, number in roman_map.items():
        if normalized.endswith(roman.strip()):
            normalized = normalized[:-len(roman.strip())] + number.strip()
    
    # Handle number/word conversions
    number_conversions = {
        'fantastic 4': 'fantastic four',
        'fantastic four': 'fantastic 4',  # Also try reverse
        'rocky 4': 'rocky iv',
        'rocky iv': 'rocky 4',
        'terminator 2': 'terminator',
        'rush hour 2': 'rush hour',
        'shrek 2': 'shrek',
    }
    
    for number_form, word_form in number_conversions.items():
        if number_form in normalized:
            normalized = normalized.replace(number_form, word_form)
    
    return normalized


def preprocess_search_query(query: str) -> Tuple[str, Optional[int], List[str]]:
    """
    Enhanced preprocessing of search queries to extract title, year, and generate variants.
    
    Args:
        query: Raw search query from user
        
    Returns:
        Tuple of (clean_title, extracted_year, title_variants)
    """
    import re
    
    clean_query = query.strip()
    extracted_year = None
    
    # Extract year with multiple patterns
    year_patterns = [
        r'\((\d{4})\)$',       # "(2021)" at end
        r'\s(\d{4})$',         # " 2021" at end
        r'\s-\s(\d{4})$',      # " - 2021" at end
        r'\[(\d{4})\]$',       # "[2021]" at end
    ]
    
    for pattern in year_patterns:
        year_match = re.search(pattern, clean_query)
        if year_match:
            extracted_year = int(year_match.group(1))
            clean_query = clean_query[:year_match.start()].strip()
            break
    
    # Generate title variants with corrected version prioritized
    variants = []
    
    # Add normalized version first (with spell checking and typo correction)
    normalized = normalize_title(clean_query)
    if normalized and normalized != clean_query.lower():
        variants.append(normalized)
        # If normalization made significant changes, also add title-cased version
        if normalized != clean_query.lower():
            variants.append(normalized.title())
    
    # Add original query (but lower priority now)
    variants.append(clean_query)
    
    # Handle "The" variations more aggressively
    clean_lower = clean_query.lower()
    
    # If starts with "the ", add version without it
    if clean_lower.startswith('the '):
        without_the = clean_query[4:].strip()
        variants.append(without_the)
        # Also add normalized version without "the"
        normalized_without_the = normalize_title(without_the)
        if normalized_without_the != without_the.lower():
            variants.append(normalized_without_the)
    
    # If doesn't start with "the ", add version with it
    else:
        with_the = 'the ' + clean_query
        variants.append(with_the)
        # Also add normalized version with "the"
        normalized_with_the = normalize_title(with_the)
        if normalized_with_the != with_the.lower():
            variants.append(normalized_with_the)
    
    # Add version without common suffixes
    suffixes_to_remove = [' movie', ' film', ' the movie', ' the film']
    for suffix in suffixes_to_remove:
        if clean_lower.endswith(suffix):
            without_suffix = clean_query[:-len(suffix)].strip()
            variants.append(without_suffix)
            # Also try with/without "the" for the suffix-removed version
            if without_suffix.lower().startswith('the '):
                variants.append(without_suffix[4:].strip())
            else:
                variants.append('the ' + without_suffix)
    
    # For very short queries, add enhanced expansions and special handling
    if len(clean_query.split()) <= 2:
        # Enhanced expansions with context awareness
        base_expansions = {
            'matrix': ['the matrix'],
            'empire': ['the empire strikes back', 'empire strikes back', 'star wars empire strikes back'],
            'fantastic': ['fantastic four', 'fantastic 4'],
            'professional': ['leon the professional', 'the professional'],
            'terminator': ['the terminator'],
            'rocky': ['rocky'],
            'shining': ['the shining'],
            'godfather': ['the godfather'],
            # LOTR expansions
            'lotr': ['lord of the rings', 'the lord of the rings'],
            'fellowship': ['the lord of the rings the fellowship of the ring', 'fellowship of the ring'],
            'towers': ['the lord of the rings the two towers', 'two towers'],
            'return': ['the lord of the rings the return of the king', 'return of the king'],
        }
        
        # Special LOTR combination handling
        if 'lotr' in clean_lower:
            if 'fellowship' in clean_lower:
                variants.extend(['the lord of the rings the fellowship of the ring', 'lord of the rings fellowship of the ring', 'fellowship of the ring'])
            elif 'towers' in clean_lower or 'two towers' in clean_lower:
                variants.extend(['the lord of the rings the two towers', 'lord of the rings two towers', 'two towers'])
            elif 'return' in clean_lower or 'king' in clean_lower:
                variants.extend(['the lord of the rings the return of the king', 'lord of the rings return of the king', 'return of the king'])
            else:
                # Generic LOTR - add all movies
                variants.extend(['the lord of the rings', 'lord of the rings', 'the lord of the rings the fellowship of the ring'])
                logger.debug(f"Added LOTR expansions for query: {clean_query}")
        
        # Year-specific expansions for vague queries
        year_specific_expansions = {
            2001: ['2001 a space odyssey', '2001 space odyssey'],
            1999: ['the matrix', 'american beauty', 'star wars episode 1'],
            1977: ['star wars', 'star wars a new hope'],
            1980: ['the empire strikes back', 'the shining'],
            1982: ['blade runner', 'e.t.'],
            1994: ['the lion king', 'forrest gump', 'leon the professional'],
            2005: ['fantastic four', 'batman begins'],
            2015: ['fantastic four'],  # Remake
        }
        
        # Apply base expansions
        for key, expansion_list in base_expansions.items():
            if key in clean_lower:
                variants.extend(expansion_list)
        
        # Apply year-specific expansions for vague queries
        if extracted_year and extracted_year in year_specific_expansions:
            year_expansions = year_specific_expansions[extracted_year]
            
            # Check if query is vague enough to warrant year-based expansion
            vague_keywords = ['space', 'movie', 'film', 'show', 'man', 'woman', 'war', 'love', 'night', 'day']
            if any(keyword in clean_lower for keyword in vague_keywords):
                variants.extend(year_expansions)
                logger.debug(f"Added year-specific expansions for vague query '{clean_query}' with year {extracted_year}")
        
        # Special handling for "Space Movie 2001" type queries
        if 'space' in clean_lower and extracted_year == 2001:
            variants.extend(['2001 a space odyssey', '2001 space odyssey', 'space odyssey'])
        elif 'space' in clean_lower and not extracted_year:
            variants.extend(['2001 a space odyssey', 'space odyssey', 'alien', 'star wars'])
        
        # Handle number/word variations for franchises
        franchise_numbers = {
            '2': ['ii', 'two'],
            '3': ['iii', 'three'],
            '4': ['iv', 'four'],
            '5': ['v', 'five'],
            'ii': ['2', 'two'],
            'iii': ['3', 'three'],
            'iv': ['4', 'four'],
            'v': ['5', 'five'],
        }
        
        for number, alternatives in franchise_numbers.items():
            if number in clean_lower:
                for alt in alternatives:
                    variant_with_alt = clean_query.lower().replace(number, alt)
                    variants.append(variant_with_alt)
                    variants.append(variant_with_alt.title())  # Title case version
    
    # Remove duplicates while preserving order
    seen = set()
    unique_variants = []
    for variant in variants:
        if variant and variant.lower() not in seen:
            seen.add(variant.lower())
            unique_variants.append(variant)
    
    return clean_query, extracted_year, unique_variants


def calculate_year_penalty(candidate_year: Optional[int], user_year: Optional[int], 
                          query_norm: str = "", is_vague_query: bool = False) -> int:
    """
    Calculate penalty points for year mismatch.
    Returns 0 if within tolerance, penalty points otherwise.
    Enhanced to handle vague queries and year-in-title scenarios.
    
    Args:
        candidate_year: Year from the candidate movie/show
        user_year: Year provided by user
        query_norm: Normalized query text for context analysis
        is_vague_query: Whether the query text is considered vague/generic
    """
    if not user_year or not candidate_year:
        return 0  # No penalty if either year is missing
    
    year_tolerance = get_year_tolerance()
    year_diff = abs(candidate_year - user_year)
    
    if year_diff <= year_tolerance:
        return 0  # Within tolerance
    
    # Check if the user's year appears in the candidate title (like "2001: A Space Odyssey")
    if str(user_year) in query_norm:
        # Year might be part of title rather than release year
        # Reduce penalty significantly
        base_penalty = min(15, year_diff * 2)
        logger.debug(f"Reduced year penalty for year-in-title scenario: {user_year} in '{query_norm}'")
        return base_penalty
    
    # For vague queries, be more lenient with year penalties
    if is_vague_query:
        # Vague queries like "Space Movie 2001" should prioritize year clues differently
        base_penalty = min(20, year_diff * 3)
        logger.debug(f"Reduced year penalty for vague query: {year_diff} years difference")
        return base_penalty
    
    # Standard year penalty calculation
    # Apply penalty: 5 points per year beyond tolerance, but cap at 30
    base_penalty = min(30, (year_diff - year_tolerance) * 5)
    
    # For very large year differences (>10 years), apply heavier penalty
    if year_diff > 10:
        base_penalty = min(50, base_penalty + (year_diff - 10) * 2)
    
    return base_penalty


def extract_candidate_year(candidate: Dict[str, Any], content_type: str = "movie") -> Optional[int]:
    """
    Extract year from TMDb candidate result based on content type.
    """
    if content_type == "movie":
        release_date = candidate.get("release_date", "")
        if release_date and len(release_date) >= 4:
            try:
                return int(release_date[:4])
            except ValueError:
                return None
    elif content_type == "tv":
        first_air_date = candidate.get("first_air_date", "")
        if first_air_date and len(first_air_date) >= 4:
            try:
                return int(first_air_date[:4])
            except ValueError:
                return None
    
    return None


def compute_fuzzy_score(query_title: str, candidate: Dict[str, Any], 
                       user_year: Optional[int] = None, content_type: str = "movie") -> Tuple[float, Dict[str, Any]]:
    """
    Enhanced compute fuzzy similarity score between query title and TMDb candidate.
    Uses multiple scoring strategies to handle difficult cases.
    
    Args:
        query_title: Normalized user input title
        candidate: TMDb search result
        user_year: Year provided by user (if any)
        content_type: "movie" or "tv"
    
    Returns:
        Tuple of (score, metadata) where metadata contains scoring details
    """
    # Get title fields based on content type
    if content_type == "movie":
        main_title = candidate.get("title", "")
        original_title = candidate.get("original_title", "")
    else:  # TV
        main_title = candidate.get("name", "")
        original_title = candidate.get("original_name", "")
    
    # Normalize titles for comparison
    query_norm = normalize_title(query_title)
    main_title_norm = normalize_title(main_title)
    original_title_norm = normalize_title(original_title)
    
    if not main_title_norm:
        return 0.0, {"error": "No title found in candidate"}
    
    # Check if this is a vague query (short and generic)
    query_words = query_norm.split()
    is_vague_query = (len(query_words) <= 2 and 
                     any(word in ['movie', 'film', 'show', 'series', 'space', 'man', 'woman'] 
                         for word in query_words))
    
    # Calculate multiple types of similarity scores
    scores = {}
    
    # 1. Token set ratio (handles word order and duplicates)
    scores['main_token_set'] = fuzz.token_set_ratio(query_norm, main_title_norm)
    scores['orig_token_set'] = 0
    if original_title_norm and original_title_norm != main_title_norm:
        scores['orig_token_set'] = fuzz.token_set_ratio(query_norm, original_title_norm)
    
    # 2. Token sort ratio (handles word order)
    scores['main_token_sort'] = fuzz.token_sort_ratio(query_norm, main_title_norm)
    scores['orig_token_sort'] = 0
    if original_title_norm and original_title_norm != main_title_norm:
        scores['orig_token_sort'] = fuzz.token_sort_ratio(query_norm, original_title_norm)
    
    # 3. Simple ratio (exact character similarity)
    scores['main_simple'] = fuzz.ratio(query_norm, main_title_norm)
    scores['orig_simple'] = 0
    if original_title_norm and original_title_norm != main_title_norm:
        scores['orig_simple'] = fuzz.ratio(query_norm, original_title_norm)
    
    # 4. Partial ratio (substring matching)
    scores['main_partial'] = fuzz.partial_ratio(query_norm, main_title_norm)
    scores['orig_partial'] = 0
    if original_title_norm and original_title_norm != main_title_norm:
        scores['orig_partial'] = fuzz.partial_ratio(query_norm, original_title_norm)
    
    # Choose the best strategy for main and original titles
    main_best_score = max(scores['main_token_set'], scores['main_token_sort'], 
                         scores['main_simple'], scores['main_partial'])
    orig_best_score = max(scores['orig_token_set'], scores['orig_token_sort'], 
                         scores['orig_simple'], scores['orig_partial'])
    
    # Take the overall best score
    best_score = max(main_best_score, orig_best_score)
    best_title = main_title if main_best_score >= orig_best_score else original_title
    
    # Additional scoring bonuses for common patterns
    bonus_points = 0
    
    # Bonus for exact word matches (case insensitive)
    query_words = set(query_norm.split())
    title_words = set(main_title_norm.split())
    common_words = query_words.intersection(title_words)
    if common_words:
        word_match_ratio = len(common_words) / max(len(query_words), len(title_words))
        bonus_points += word_match_ratio * 10  # Up to 10 bonus points
    
    # Bonus for substring matches (handles abbreviations)
    if len(query_norm) >= 3:  # Only for meaningful queries
        if query_norm in main_title_norm or main_title_norm in query_norm:
            bonus_points += 15  # Good substring match
        elif any(word in main_title_norm for word in query_norm.split() if len(word) >= 3):
            bonus_points += 8   # Partial word matches
    
    # Enhanced bonus for year-in-title scenarios
    if user_year and str(user_year) in main_title_norm:
        bonus_points += 20  # Strong bonus for year appearing in title
        logger.debug(f"Year-in-title bonus: {user_year} found in '{main_title}'")
    
    # Apply year considerations with enhanced context awareness
    candidate_year = extract_candidate_year(candidate, content_type)
    year_penalty = calculate_year_penalty(candidate_year, user_year, query_norm, is_vague_query)
    
    # For challenging cases, be more lenient with year penalties based on text match quality
    if best_score >= 70:  # Good text match, reduce year penalty
        year_penalty = year_penalty * 0.4  # More aggressive reduction
    elif best_score >= 50:  # Moderate text match, moderate year penalty reduction
        year_penalty = year_penalty * 0.7
    
    # Calculate final score with bonuses and penalties
    final_score = min(100, max(0, best_score + bonus_points - year_penalty))
    
    metadata = {
        "main_token_set_score": scores['main_token_set'],
        "main_token_sort_score": scores['main_token_sort'],
        "main_simple_score": scores['main_simple'],
        "main_partial_score": scores['main_partial'],
        "original_token_set_score": scores['orig_token_set'],
        "original_token_sort_score": scores['orig_token_sort'],
        "original_simple_score": scores['orig_simple'],
        "original_partial_score": scores['orig_partial'],
        "best_raw_score": best_score,
        "bonus_points": bonus_points,
        "year_penalty": year_penalty,
        "final_score": final_score,
        "matched_title": best_title,
        "candidate_year": candidate_year,
        "user_year": user_year,
        "within_year_tolerance": year_penalty == 0,
        "is_vague_query": is_vague_query,
        "year_in_title_bonus": user_year and str(user_year) in main_title_norm
    }
    
    return final_score, metadata


def select_best_match(query_title: str, candidates: List[Dict[str, Any]], 
                     user_year: Optional[int] = None, content_type: str = "movie") -> Tuple[Optional[Dict[str, Any]], float, Dict[str, Any]]:
    """
    Select the best match from TMDb candidates using fuzzy matching and confidence scoring.
    Enhanced with intelligent multiple-candidate handling for better original movie selection.
    
    Args:
        query_title: User input title
        candidates: List of TMDb search results
        user_year: Year provided by user (if any)
        content_type: "movie" or "tv"
    
    Returns:
        Tuple of (best_candidate, confidence_score, match_metadata)
    """
    if not candidates:
        return None, 0.0, {"error": "No candidates provided"}
    
    # Filter out candidates with missing essential fields
    valid_candidates = []
    for candidate in candidates:
        title_field = "title" if content_type == "movie" else "name"
        date_field = "release_date" if content_type == "movie" else "first_air_date"
        
        title = candidate.get(title_field, "")
        date = candidate.get(date_field, "")
        
        # Skip candidates with missing title or invalid date
        if not title or not title.strip():
            logger.warning(f"Skipping candidate with missing {title_field}")
            continue
            
        if date and len(date) >= 4:
            try:
                int(date[:4])  # Validate year format
                valid_candidates.append(candidate)
            except ValueError:
                logger.warning(f"Skipping candidate with invalid date: {date}")
                continue
        else:
            # Allow candidates with missing dates but log it
            logger.debug(f"Candidate has missing/invalid date: {title}")
            valid_candidates.append(candidate)
    
    if not valid_candidates:
        return None, 0.0, {"error": "No valid candidates after filtering"}
    
    # Apply exact match filtering to prioritize precise matches
    filtered_candidates = filter_exact_matches(query_title, valid_candidates, content_type)
    
    # Group candidates by normalized title for intelligent selection
    grouped_candidates = detect_multiple_candidates(filtered_candidates, content_type)
    
    # Score all filtered candidates
    scored_candidates = []
    for candidate in filtered_candidates:
        score, metadata = compute_fuzzy_score(query_title, candidate, user_year, content_type)
        scored_candidates.append({
            "candidate": candidate,
            "score": score,
            "metadata": metadata
        })
    
    # Enhanced selection logic for multiple versions/remakes
    final_candidates = []
    
    for group_title, group_candidates in grouped_candidates.items():
        if len(group_candidates) > 1:
            # Multiple versions of the same title - apply intelligent selection
            enhanced_group = []
            
            for candidate in group_candidates:
                # Find the scored version
                scored_version = next((sc for sc in scored_candidates if sc["candidate"]["id"] == candidate["id"]), None)
                if scored_version:
                    enhanced_candidate = scored_version.copy()
                    
                    # Calculate composite score for version selection
                    base_score = enhanced_candidate["score"]
                    popularity = candidate.get("popularity", 0)
                    vote_count = candidate.get("vote_count", 0)
                    vote_average = candidate.get("vote_average", 0)
                    
                    # Extract year for original bias
                    candidate_year = extract_candidate_year(candidate, content_type)
                    
                    # Calculate composite selection score
                    # Base on popularity and ratings, with original bias
                    popularity_score = min(20, popularity / 10) if popularity else 0
                    rating_score = (vote_average * vote_count / 1000) if vote_count > 0 else 0
                    rating_score = min(15, rating_score)
                    
                    # Bias toward original (earliest) release with franchise-specific knowledge
                    years_in_group = [extract_candidate_year(gc, content_type) for gc in group_candidates]
                    valid_years = [y for y in years_in_group if y]
                    
                    # Enhanced original bias with franchise-specific rules
                    original_bonus = 0
                    if valid_years and candidate_year:
                        earliest_year = min(valid_years)
                        
                        # Get normalized title for franchise detection
                        title_for_franchise = candidate.get("title" if content_type == "movie" else "name", "").lower()
                        
                        # Normalize the franchise detection title too
                        title_for_franchise = normalize_title(title_for_franchise)
                        
                        # Special franchise rules with enhanced logic
                        franchise_rules = {
                            'fantastic': {
                                'preferred_years': [2005],  # Original is generally preferred over 2015/2025
                                'avoid_years': [2025, 2015],  # Future/recent remakes usually less popular
                                'bonus_multiplier': 2.0,  # Stronger preference
                                'future_penalty': 50  # Heavy penalty for future releases
                            },
                            'spider': {
                                'preferred_years': [2002, 2021],  # Tobey Maguire original, MCU recent
                                'bonus_multiplier': 1.2
                            },
                            'batman': {
                                'preferred_years': [1989, 2008],  # Burton original, Nolan trilogy
                                'bonus_multiplier': 1.3
                            },
                            'star wars': {
                                'preferred_years': [1977, 1980, 1983],  # Original trilogy
                                'bonus_multiplier': 1.4
                            },
                            'matrix': {
                                'preferred_years': [1999],  # Original Matrix
                                'avoid_years': [2023, 2025],  # Recent/future reboots
                                'bonus_multiplier': 1.5
                            }
                        }
                        
                        # Check if this is a known franchise
                        franchise_bonus = 0
                        current_year = 2025  # Current year for future release detection
                        
                        for franchise_key, rules in franchise_rules.items():
                            if franchise_key in title_for_franchise:
                                logger.info(f"Detected franchise '{franchise_key}' in title '{title_for_franchise}' (year: {candidate_year})")
                                
                                if candidate_year in rules.get('preferred_years', []):
                                    franchise_bonus = 15 * rules.get('bonus_multiplier', 1.0)
                                    logger.info(f"Applied franchise preference bonus for {franchise_key}: {franchise_bonus}")
                                elif candidate_year in rules.get('avoid_years', []):
                                    franchise_bonus = -20  # Strong penalty for known problematic versions
                                    logger.warning(f"Applied franchise avoidance penalty for {franchise_key}: {franchise_bonus}")
                                elif candidate_year > current_year:
                                    # Future release - apply heavy penalty
                                    franchise_bonus = -rules.get('future_penalty', 30)
                                    logger.warning(f"Applied future release penalty for {franchise_key}: {franchise_bonus}")
                                break
                        
                        # Standard original bias
                        if candidate_year == earliest_year:
                            original_bonus = 10 + franchise_bonus  # Bonus for being the original
                        else:
                            year_diff = candidate_year - earliest_year
                            original_bonus = max(-5, 5 - year_diff) + franchise_bonus  # Decreasing bonus, can go negative
                    
                    # If user provided specific year, heavily favor exact matches
                    year_match_bonus = 0
                    if user_year and candidate_year and abs(candidate_year - user_year) <= 1:
                        year_match_bonus = 25  # Strong bonus for user-specified year
                    
                    composite_score = base_score + popularity_score + rating_score + original_bonus + year_match_bonus
                    enhanced_candidate["composite_score"] = composite_score
                    enhanced_candidate["original_bonus"] = original_bonus
                    enhanced_candidate["popularity_score"] = popularity_score
                    enhanced_candidate["year_match_bonus"] = year_match_bonus
                    
                    enhanced_group.append(enhanced_candidate)
            
            # Sort group by composite score and take the best
            enhanced_group.sort(key=lambda x: x["composite_score"], reverse=True)
            if enhanced_group:
                best_in_group = enhanced_group[0]
                # Check if the choice is decisive or needs user confirmation
                if len(enhanced_group) > 1:
                    runner_up = enhanced_group[1]
                    score_diff = best_in_group["composite_score"] - runner_up["composite_score"]
                    if score_diff < 5:  # Very close, might need confirmation
                        best_in_group["needs_confirmation"] = True
                        logger.info(f"Close decision between versions: {score_diff:.1f} point difference")
                
                final_candidates.append(best_in_group)
                logger.info(f"Selected '{candidate.get('title' if content_type == 'movie' else 'name')}' "
                           f"from {len(group_candidates)} versions (composite score: {best_in_group['composite_score']:.1f})")
        else:
            # Single candidate in group, add it normally
            for candidate in group_candidates:
                scored_version = next((sc for sc in scored_candidates if sc["candidate"]["id"] == candidate["id"]), None)
                if scored_version:
                    final_candidates.append(scored_version)
    
    # Add any ungrouped candidates (didn't have multiple versions)
    grouped_ids = set()
    for group_candidates in grouped_candidates.values():
        for candidate in group_candidates:
            grouped_ids.add(candidate["id"])
    
    for scored_candidate in scored_candidates:
        if scored_candidate["candidate"]["id"] not in grouped_ids:
            final_candidates.append(scored_candidate)
    
    if not final_candidates:
        return None, 0.0, {"error": "No valid candidates after enhanced selection"}
    
    # Sort final candidates by score (or composite score if available)
    final_candidates.sort(key=lambda x: x.get("composite_score", x["score"]), reverse=True)
    
    best = final_candidates[0]
    best_score = best["score"]
    
    # Enhanced tie-breaking with popularity and vote metrics
    if len(final_candidates) > 1:
        second_best = final_candidates[1]
        score_threshold = 3  # Within 3 points
        
        if second_best["score"] >= best_score - score_threshold:
            best_popularity = best["candidate"].get("popularity", 0)
            second_popularity = second_best["candidate"].get("popularity", 0)
            
            best_votes = best["candidate"].get("vote_count", 0)
            second_votes = second_best["candidate"].get("vote_count", 0)
            
            # Use popularity primarily, vote count as tiebreaker
            if second_popularity > best_popularity * 1.2:  # 20% higher popularity
                logger.info(f"Tie-breaking with popularity: {second_popularity} > {best_popularity}")
                best = second_best
                best_score = second_best["score"]
            elif abs(second_popularity - best_popularity) < (best_popularity * 0.1) and second_votes > best_votes * 1.5:
                logger.info(f"Tie-breaking with vote count: {second_votes} > {best_votes}")
                best = second_best
                best_score = second_best["score"]
    
    match_metadata = {
        "total_candidates": len(candidates),
        "valid_candidates": len(valid_candidates),
        "filtered_candidates": len(filtered_candidates),
        "scored_candidates": len(scored_candidates),
        "grouped_titles": len(grouped_candidates),
        "best_score": best_score,
        "score_details": best["metadata"],
        "runner_up_score": final_candidates[1]["score"] if len(final_candidates) > 1 else None,
        "used_popularity_tiebreak": len(final_candidates) > 1 and final_candidates[1]["score"] >= best_score - 3,
        "version_selection_applied": len(grouped_candidates) > 0,
        "exact_match_filtering_applied": len(filtered_candidates) < len(valid_candidates),
        "composite_score": best.get("composite_score"),
        "needs_confirmation": best.get("needs_confirmation", False)
    }
    
    return best["candidate"], best_score, match_metadata


def get_confidence_level(score: float) -> str:
    """Get confidence level description based on score."""
    if score >= get_threshold("high"):
        return "HIGH"
    elif score >= get_threshold("moderate"):
        return "MODERATE"
    elif score >= get_threshold("low"):
        return "LOW"
    else:
        return "VERY_LOW"


def should_auto_approve(score: float) -> bool:
    """Determine if a match should be automatically approved."""
    return score >= get_threshold("high")


def should_silent_correct(score: float) -> bool:
    """Determine if a match should be silently corrected with warning."""
    return get_threshold("moderate") <= score < get_threshold("high")


def should_request_confirmation(score: float) -> bool:
    """Determine if a match requires user confirmation."""
    return get_threshold("low") <= score < get_threshold("moderate")


def should_reject(score: float) -> bool:
    """Determine if a match should be rejected."""
    return score < get_threshold("low")


def format_match_result(candidate: Dict[str, Any], score: float, match_metadata: Dict[str, Any], 
                       content_type: str = "movie", original_query: str = "") -> Dict[str, Any]:
    """
    Format the final match result with confidence information.
    
    Args:
        candidate: Selected TMDb candidate
        score: Final confidence score
        match_metadata: Metadata about the matching process
        content_type: "movie" or "tv"
        original_query: Original user query for logging
    
    Returns:
        Dictionary with match result and confidence information
    """
    confidence_level = get_confidence_level(score)
    
    # Extract title based on content type
    if content_type == "movie":
        matched_title = candidate.get("title", "")
        candidate_year = extract_candidate_year(candidate, "movie")
    else:
        matched_title = candidate.get("name", "")
        candidate_year = extract_candidate_year(candidate, "tv")
    
    result = {
        "candidate": candidate,
        "confidence_score": score,
        "confidence_level": confidence_level,
        "matched_title": matched_title,
        "candidate_year": candidate_year,
        "auto_approve": should_auto_approve(score),
        "silent_correct": should_silent_correct(score),
        "needs_confirmation": should_request_confirmation(score),
        "should_reject": should_reject(score),
        "match_metadata": match_metadata,
        "original_query": original_query
    }
    
    # Add warning or error messages
    if should_auto_approve(score):
        result["message"] = f"High confidence match ({score:.1f}%) - auto-approved"
    elif should_silent_correct(score):
        result["message"] = f"Good match ({score:.1f}%) - applying with caution"
        result["warning"] = f"Fuzzy matched '{original_query}' to '{matched_title}' with {score:.1f}% confidence"
    elif should_request_confirmation(score):
        result["message"] = f"Ambiguous match ({score:.1f}%) - confirmation needed"
        result["suggestion"] = f"Did you mean '{matched_title}' ({candidate_year})?"
    else:
        result["message"] = f"Low confidence match ({score:.1f}%) - rejected"
        result["error"] = f"No reliable match found for '{original_query}'"
    
    return result


def get_alternative_titles(tmdb_id: str, api_key: str, content_type: str = "movie") -> List[str]:
    """
    Fetch alternative titles from TMDb for improved matching.
    This is a fallback function for cases where initial matching is poor.
    
    Args:
        tmdb_id: TMDb ID to fetch alternative titles for
        api_key: TMDb API key
        content_type: "movie" or "tv"
    
    Returns:
        List of alternative title strings
    """
    import requests
    
    try:
        if content_type == "movie":
            url = f"https://api.themoviedb.org/3/movie/{tmdb_id}/alternative_titles"
        else:
            url = f"https://api.themoviedb.org/3/tv/{tmdb_id}/alternative_titles"
        
        response = requests.get(url, params={"api_key": api_key}, timeout=10)
        response.raise_for_status()
        
        data = response.json()
        titles = []
        
        if content_type == "movie":
            for alt in data.get("titles", []):
                title = alt.get("title")
                if title:
                    titles.append(title)
        else:
            for alt in data.get("results", []):
                title = alt.get("title")
                if title:
                    titles.append(title)
        
        return titles
        
    except Exception as e:
        logger.warning(f"Failed to fetch alternative titles for {tmdb_id}: {e}")
        return []


def enhance_matching_with_alternatives(query_title: str, candidates: List[Dict[str, Any]], 
                                     api_key: str, user_year: Optional[int] = None, 
                                     content_type: str = "movie", score_threshold: float = 80.0) -> Tuple[Optional[Dict[str, Any]], float, Dict[str, Any]]:
    """
    Enhanced matching that uses alternative titles as a fallback for low-confidence matches.
    
    This function is called when initial fuzzy matching yields scores below the threshold.
    It fetches alternative titles for the top candidates and re-scores them.
    
    Args:
        query_title: User input title
        candidates: List of TMDb search results
        api_key: TMDb API key for alternative title lookup
        user_year: Year provided by user (if any)
        content_type: "movie" or "tv"
        score_threshold: Minimum score to trigger alternative title lookup
    
    Returns:
        Tuple of (best_candidate, confidence_score, match_metadata)
    """
    # First, try standard matching
    best_candidate, best_score, match_metadata = select_best_match(
        query_title, candidates, user_year, content_type
    )
    
    # If score is good enough, return it
    if best_score >= score_threshold or not best_candidate:
        return best_candidate, best_score, match_metadata
    
    # Try alternative titles for top candidates
    logger.info(f"Low initial score ({best_score:.1f}%), trying alternative titles...")
    
    enhanced_candidates = []
    top_candidates = candidates[:3]  # Only check top 3 to avoid excessive API calls
    
    for candidate in top_candidates:
        tmdb_id = candidate.get("id")
        if not tmdb_id:
            continue
        
        # Get alternative titles
        alt_titles = get_alternative_titles(str(tmdb_id), api_key, content_type)
        
        # Score against alternative titles
        best_alt_score = 0
        best_alt_title = ""
        
        query_norm = normalize_title(query_title)
        for alt_title in alt_titles:
            alt_title_norm = normalize_title(alt_title)
            if alt_title_norm:
                alt_score = fuzz.token_set_ratio(query_norm, alt_title_norm)
                if alt_score > best_alt_score:
                    best_alt_score = alt_score
                    best_alt_title = alt_title
        
        # Apply year penalty to alternative title score
        candidate_year = extract_candidate_year(candidate, content_type)
        year_penalty = calculate_year_penalty(candidate_year, user_year)
        final_alt_score = max(0, best_alt_score - year_penalty)
        
        if final_alt_score > best_score:
            # Create enhanced candidate with alternative title info
            enhanced_candidate = candidate.copy()
            enhanced_candidate["_alt_title_match"] = best_alt_title
            enhanced_candidate["_alt_score"] = final_alt_score
            enhanced_candidates.append(enhanced_candidate)
            
            logger.info(f"Alternative title '{best_alt_title}' scored {final_alt_score:.1f}% for {candidate.get('title', 'Unknown')}")
    
    # If we found better matches with alternative titles, use them
    if enhanced_candidates:
        # Re-run selection with enhanced candidates
        best_enhanced = max(enhanced_candidates, key=lambda x: x.get("_alt_score", 0))
        enhanced_score = best_enhanced.get("_alt_score", 0)
        
        if enhanced_score > best_score:
            logger.info(f"Using alternative title match: {enhanced_score:.1f}% vs {best_score:.1f}%")
            
            enhanced_metadata = match_metadata.copy()
            enhanced_metadata["used_alternative_titles"] = True
            enhanced_metadata["alternative_title_match"] = best_enhanced.get("_alt_title_match")
            enhanced_metadata["original_score"] = best_score
            enhanced_metadata["enhanced_score"] = enhanced_score
            
            return best_enhanced, enhanced_score, enhanced_metadata
    
    # No improvement found with alternative titles
    match_metadata["used_alternative_titles"] = False
    return best_candidate, best_score, match_metadata


# ==============================================================================
# ENHANCED FUZZY MATCHING CLASSES (previously optimized_fuzzy_matcher.py)
# ==============================================================================

class MatchResult:
    """Structured result from fuzzy matching operations."""
    
    def __init__(self, success: bool, tmdb_id: Optional[int] = None, title: Optional[str] = None, 
                 year: Optional[int] = None, confidence: float = 0.0, from_cache: bool = False):
        self.success = success
        self.tmdb_id = tmdb_id
        self.title = title
        self.year = year
        self.confidence = confidence
        self.from_cache = from_cache
        self.warning = None
        self.error = None
        self.metadata = {}
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API compatibility."""
        result = {
            "success": self.success,
            "tmdb_id": self.tmdb_id,
            "title": self.title,
            "year": self.year,
            "confidence": self.confidence,
            "from_cache": self.from_cache,
            "metadata": self.metadata
        }
        if self.warning:
            result["warning"] = self.warning
        if self.error:
            result["error"] = self.error
        return result


class MatchCache:
    """Enhanced caching system for match decisions with 7-day TTL."""
    
    def __init__(self, cache_file: Path = None):
        self.cache_file = cache_file or Path("data/metadata_cache/fuzzy_match_cache.json")
        self.cache_file.parent.mkdir(parents=True, exist_ok=True)
        self._cache = {}
        self._load_cache()
        
    def _load_cache(self):
        """Load cache from disk."""
        if self.cache_file.exists():
            try:
                with open(self.cache_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    # Filter out expired entries while loading
                    now = datetime.now()
                    self._cache = {
                        key: value for key, value in data.items()
                        if datetime.fromisoformat(value.get('timestamp', '1970-01-01')) + timedelta(days=7) > now
                    }
                logger.debug(f"Loaded {len(self._cache)} valid cache entries")
            except Exception as e:
                logger.warning(f"Failed to load match cache: {e}")
                self._cache = {}
                
    def _save_cache(self):
        """Save cache to disk."""
        try:
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(self._cache, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.warning(f"Failed to save match cache: {e}")
            
    def _make_cache_key(self, title: str, year: Optional[int], content_type: str) -> str:
        """Generate cache key for a query."""
        normalized_title = self._normalize_for_cache(title)
        year_part = str(year) if year else ""
        return f"{content_type}:{normalized_title}:{year_part}"
        
    def _normalize_for_cache(self, title: str) -> str:
        """Normalize title for cache key consistency."""
        # Basic normalization for cache keys
        normalized = title.lower().strip()
        normalized = re.sub(r'[^\w\s]', '', normalized)  # Remove punctuation
        normalized = re.sub(r'\s+', ' ', normalized)  # Normalize whitespace
        return normalized
        
    def get(self, title: str, year: Optional[int], content_type: str) -> Optional[Dict[str, Any]]:
        """Get cached match result."""
        cache_key = self._make_cache_key(title, year, content_type)
        cached_data = self._cache.get(cache_key)
        
        if cached_data:
            # Check if still valid (7 days)
            cached_time = datetime.fromisoformat(cached_data['timestamp'])
            if datetime.now() - cached_time < timedelta(days=7):
                logger.debug(f"Cache hit for key: {cache_key}")
                return cached_data['match']
            else:
                # Expired, remove it
                del self._cache[cache_key]
                logger.debug(f"Cache entry expired for key: {cache_key}")
                
        return None
        
    def set(self, title: str, year: Optional[int], content_type: str, match_data: Dict[str, Any]):
        """Cache a match result."""
        cache_key = self._make_cache_key(title, year, content_type)
        self._cache[cache_key] = {
            'match': match_data,
            'timestamp': datetime.now().isoformat()
        }
        logger.debug(f"Cached match for key: {cache_key}")
        
        # Save to disk periodically (every 10 entries or so)
        if len(self._cache) % 10 == 0:
            self._save_cache()
            
    def clear_expired(self):
        """Remove expired entries from cache."""
        now = datetime.now()
        expired_keys = []
        
        for key, data in self._cache.items():
            cached_time = datetime.fromisoformat(data['timestamp'])
            if now - cached_time >= timedelta(days=7):
                expired_keys.append(key)
                
        for key in expired_keys:
            del self._cache[key]
            
        if expired_keys:
            logger.info(f"Cleared {len(expired_keys)} expired cache entries")
            self._save_cache()


class EnhancedFuzzyMatcher:
    """
    Enhanced fuzzy matching class implementing fast-path and slow-path logic.
    Designed for batch automation with minimal user interaction.
    """
    
    def __init__(self, cache_enabled: bool = None, cache_file: Path = None, config: 'FuzzyMatchingConfig' = None):
        # Load configuration
        if config is None:
            try:
                from _internal.utils.fuzzy_config import get_fuzzy_config
                self.config = get_fuzzy_config()
            except ImportError:
                logger.warning("Fuzzy config not available, using defaults")
                self.config = None
        else:
            self.config = config
            
        # Use config values or fallbacks
        self.cache_enabled = cache_enabled if cache_enabled is not None else (
            self.config.is_caching_enabled() if self.config else True
        )
        self.cache = MatchCache(cache_file) if self.cache_enabled else None
        
        # Load thresholds from config or use defaults
        if self.config:
            self.thresholds = {
                'auto_approve': self.config.get_auto_approve_threshold(),
                'silent_accept': self.config.get_silent_accept_threshold(),
                'low_confidence': self.config.get_low_confidence_threshold(),
                'reject': self.config.get_reject_threshold()
            }
            
            # Load franchise preferences
            self.franchise_preferences = self.config.get_franchise_preferences()
            
            # Load performance settings
            self.fast_path_enabled = self.config.is_fast_path_enabled()
            self.batch_automation_mode = self.config.is_batch_automation_mode()
            
        else:
            # Fallback defaults
            self.thresholds = {
                'auto_approve': 95.0,
                'silent_accept': 80.0,
                'low_confidence': 60.0,
                'reject': 40.0
            }
            self.franchise_preferences = {}
            self.fast_path_enabled = True
            self.batch_automation_mode = True
        
        # Performance tracking
        self._stats = {
            'total_matches': 0,
            'fast_path_hits': 0,
            'cache_hits': 0,
            'slow_path_fallbacks': 0,
            'rejections': 0
        }
        
    def get_stats(self) -> Dict[str, int]:
        """Get performance statistics."""
        return self._stats.copy()
        
    def match_media_title(self, raw_title: str, content_type: str = "movie", 
                         tmdb_search_func: callable = None, tmdb_details_func: callable = None) -> MatchResult:
        """
        Main entry point for enhanced fuzzy matching.
        
        Args:
            raw_title: Raw title input from user (may include year)
            content_type: "movie" or "tv"
            tmdb_search_func: Function to search TMDb (title, year, type) -> List[Dict]
            tmdb_details_func: Function to get TMDb details (id, type) -> Dict
            
        Returns:
            MatchResult with success status and match details
        """
        start_time = time.time()
        self._stats['total_matches'] += 1
        
        logger.info(f"Starting enhanced fuzzy matching for: '{raw_title}' (type: {content_type})")
        
        # Extract title and year
        title, user_year = self._extract_year_from_title(raw_title)
        
        # 1. Fast path: Check cache for recent result
        if self.cache_enabled and self.cache:
            cached_match = self.cache.get(title, user_year, content_type)
            if cached_match:
                self._stats['cache_hits'] += 1
                elapsed = time.time() - start_time
                logger.info(f"Cache hit for '{raw_title}' -> TMDb ID {cached_match['tmdb_id']} ({elapsed:.2f}s)")
                
                result = MatchResult(
                    success=True,
                    tmdb_id=cached_match['tmdb_id'],
                    title=cached_match['title'],
                    year=cached_match['year'],
                    confidence=cached_match.get('confidence', 95.0),
                    from_cache=True
                )
                result.metadata['elapsed_time'] = elapsed
                return result
        
        # 2. Search TMDb (initial query)
        if not tmdb_search_func:
            return MatchResult(success=False, error="No TMDb search function provided")
            
        try:
            results = tmdb_search_func(title, user_year, content_type)
        except Exception as e:
            logger.error(f"TMDb search failed for '{raw_title}': {e}")
            return MatchResult(success=False, error=f"TMDb search failed: {e}")
            
        if not results:
            logger.warning(f"No TMDb results for '{raw_title}'")
            return MatchResult(success=False, error="No results found")
            
        logger.info(f"Found {len(results)} candidates for '{raw_title}' via TMDb")
        
        # 3. Fast path: Evaluate top results for immediate acceptance
        fast_path_result = self._try_fast_path(title, user_year, results[:3], content_type)
        if fast_path_result:
            self._stats['fast_path_hits'] += 1
            elapsed = time.time() - start_time
            
            # Cache the successful fast-path result
            if self.cache_enabled and self.cache:
                cache_data = {
                    'tmdb_id': fast_path_result.tmdb_id,
                    'title': fast_path_result.title,
                    'year': fast_path_result.year,
                    'confidence': fast_path_result.confidence
                }
                self.cache.set(title, user_year, content_type, cache_data)
            
            fast_path_result.metadata['elapsed_time'] = elapsed
            fast_path_result.metadata['match_path'] = 'fast_path'
            logger.info(f"Fast-path match found for '{raw_title}' ({elapsed:.2f}s)")
            return fast_path_result
        
        # 4. Handle multiple versions/franchises
        filtered_results = self._handle_multiple_versions(title, user_year, results, content_type)
        
        # 5. Slow path: Full fuzzy matching
        self._stats['slow_path_fallbacks'] += 1
        slow_path_result = self._try_slow_path(title, user_year, filtered_results, content_type, raw_title)
        
        elapsed = time.time() - start_time
        
        if slow_path_result.success:
            # Cache the successful slow-path result
            if self.cache_enabled and self.cache:
                cache_data = {
                    'tmdb_id': slow_path_result.tmdb_id,
                    'title': slow_path_result.title,
                    'year': slow_path_result.year,
                    'confidence': slow_path_result.confidence
                }
                self.cache.set(title, user_year, content_type, cache_data)
        else:
            self._stats['rejections'] += 1
            
        slow_path_result.metadata['elapsed_time'] = elapsed
        slow_path_result.metadata['match_path'] = 'slow_path'
        logger.info(f"Slow-path matching completed for '{raw_title}' ({elapsed:.2f}s)")
        
        return slow_path_result
        
    def _extract_year_from_title(self, title: str) -> Tuple[str, Optional[int]]:
        """Extract year from title string."""
        year_patterns = [
            r'\((\d{4})\)$',       # "(2021)" at end
            r'\s(\d{4})$',         # " 2021" at end
            r'\s-\s(\d{4})$',      # " - 2021" at end
            r'\[(\d{4})\]$',       # "[2021]" at end
        ]
        
        clean_title = title.strip()
        extracted_year = None
        
        for pattern in year_patterns:
            match = re.search(pattern, clean_title)
            if match:
                extracted_year = int(match.group(1))
                clean_title = clean_title[:match.start()].strip()
                break
                
        return clean_title, extracted_year
        
    def _try_fast_path(self, title: str, user_year: Optional[int], top_results: List[Dict[str, Any]], 
                      content_type: str) -> Optional[MatchResult]:
        """
        Fast path evaluation for immediate acceptance.
        Returns MatchResult if a high-confidence match is found, None otherwise.
        """
        if not top_results:
            return None
            
        normalized_query = normalize_title(title)
        
        for candidate in top_results:
            # Get candidate title and year
            if content_type == "movie":
                cand_title = candidate.get("title", "")
                cand_year = extract_candidate_year(candidate, "movie")
            else:
                cand_title = candidate.get("name", "")
                cand_year = extract_candidate_year(candidate, "tv")
                
            if not cand_title:
                continue
                
            cand_title_norm = normalize_title(cand_title)
            
            # Fast path condition 1: Exact title match
            if cand_title_norm == normalized_query:
                if not user_year or not cand_year or abs(cand_year - user_year) <= 1:
                    logger.info(f"Fast path: Exact title match - '{cand_title}' ({cand_year})")
                    return MatchResult(
                        success=True,
                        tmdb_id=candidate.get("id"),
                        title=cand_title,
                        year=cand_year,
                        confidence=97.0  # High confidence for exact match
                    )
            
            # Fast path condition 2: High similarity with year match
            similarity = fuzz.ratio(normalized_query, cand_title_norm)
            if similarity >= 90:
                # Check year compatibility
                year_compatible = True
                if user_year and cand_year:
                    tolerance = self.config.get_year_tolerance(content_type) if self.config else (5 if content_type == "tv" else 3)
                    year_compatible = abs(cand_year - user_year) <= tolerance
                    
                if year_compatible:
                    confidence = min(96.0, similarity + 5)  # Slight bonus for fast path
                    logger.info(f"Fast path: High similarity {similarity}% match - '{cand_title}' ({cand_year})")
                    return MatchResult(
                        success=True,
                        tmdb_id=candidate.get("id"),
                        title=cand_title,
                        year=cand_year,
                        confidence=confidence
                    )
        
        return None
        
    def _handle_multiple_versions(self, title: str, user_year: Optional[int], 
                                 results: List[Dict[str, Any]], content_type: str) -> List[Dict[str, Any]]:
        """
        Handle multiple versions/franchises by detecting and filtering appropriately.
        """
        # Use existing utility to detect multiple candidates
        grouped = detect_multiple_candidates(results, content_type)
        
        if not grouped:
            # No multiple versions detected, return as-is
            return results
            
        filtered_results = []
        
        for group_title, candidates in grouped.items():
            if len(candidates) > 1:
                logger.info(f"Multiple versions detected for '{group_title}': {len(candidates)} candidates")
                
                # Extract years for disambiguation
                candidate_years = []
                for candidate in candidates:
                    year = extract_candidate_year(candidate, content_type)
                    if year:
                        candidate_years.append((candidate, year))
                        
                if user_year and candidate_years:
                    # User specified year - find closest match
                    tolerance = self.config.get_year_tolerance(content_type) if self.config else (5 if content_type == "tv" else 3)
                    year_matches = [
                        (candidate, year) for candidate, year in candidate_years 
                        if abs(year - user_year) <= tolerance
                    ]
                    
                    if len(year_matches) == 1:
                        # Unique year match found
                        selected_candidate = year_matches[0][0]
                        filtered_results.append(selected_candidate)
                        logger.info(f"Selected by year match: {user_year} -> {year_matches[0][1]}")
                        continue
                        
                # Default preference logic (oldest for movies, popularity for TV)
                if content_type == "movie":
                    # Prefer original (earliest) for movies
                    if candidate_years:
                        candidate_years.sort(key=lambda x: x[1])  # Sort by year
                        selected = candidate_years[0][0]
                        filtered_results.append(selected)
                        logger.info(f"Selected oldest version: {candidate_years[0][1]}")
                        continue
                else:
                    # For TV, prefer by popularity if no clear year match
                    candidates_by_popularity = sorted(
                        candidates, 
                        key=lambda x: x.get("popularity", 0), 
                        reverse=True
                    )
                    filtered_results.append(candidates_by_popularity[0])
                    logger.info(f"Selected most popular TV version")
                    continue
                    
                # Fallback: add all candidates for further processing
                filtered_results.extend(candidates)
            else:
                # Single candidate in group
                filtered_results.extend(candidates)
                
        return filtered_results
        
    def _try_slow_path(self, title: str, user_year: Optional[int], candidates: List[Dict[str, Any]], 
                      content_type: str, original_query: str) -> MatchResult:
        """
        Slow path fuzzy matching for complex cases.
        """
        if not candidates:
            return MatchResult(success=False, error="No candidates for slow path matching")
            
        logger.info(f"Entering slow path fuzzy matching with {len(candidates)} candidates")
        
        # Use existing fuzzy scoring logic
        try:
            # Score all candidates
            scored_candidates = []
            for candidate in candidates:
                score, metadata = compute_fuzzy_score(title, candidate, user_year, content_type)
                scored_candidates.append({
                    'candidate': candidate,
                    'score': score,
                    'metadata': metadata
                })
                
            if not scored_candidates:
                return MatchResult(success=False, error="No valid scored candidates")
                
            # Sort by score
            scored_candidates.sort(key=lambda x: x['score'], reverse=True)
            best = scored_candidates[0]
            best_score = best['score']
            
            # Apply confidence-based decision making
            return self._make_confidence_decision(best, best_score, content_type, original_query)
            
        except Exception as e:
            logger.error(f"Slow path fuzzy matching failed: {e}")
            return MatchResult(success=False, error=f"Fuzzy matching error: {e}")
            
    def _make_confidence_decision(self, best_match: Dict[str, Any], score: float, 
                                 content_type: str, original_query: str) -> MatchResult:
        """
        Make automated decisions based on confidence thresholds.
        """
        candidate = best_match['candidate']
        
        if content_type == "movie":
            title = candidate.get("title", "")
            year = extract_candidate_year(candidate, "movie")
        else:
            title = candidate.get("name", "")
            year = extract_candidate_year(candidate, "tv")
            
        tmdb_id = candidate.get("id")
        
        if score >= self.thresholds['auto_approve']:
            # ≥95% - Auto-approve
            logger.info(f"AUTO-APPROVED: {score:.1f}% confidence match for '{original_query}' -> '{title}'")
            return MatchResult(
                success=True,
                tmdb_id=tmdb_id,
                title=title,
                year=year,
                confidence=score
            )
            
        elif score >= self.thresholds['silent_accept']:
            # 80-94% - Accept with warning
            logger.info(f"ACCEPTED: {score:.1f}% confidence match for '{original_query}' -> '{title}'")
            result = MatchResult(
                success=True,
                tmdb_id=tmdb_id,
                title=title,
                year=year,
                confidence=score
            )
            result.warning = f"Moderate confidence match ({score:.1f}%) - review recommended"
            return result
            
        elif score >= self.thresholds['low_confidence']:
            # 60-79% - Proceed with warning
            logger.warning(f"LOW CONFIDENCE: {score:.1f}% match for '{original_query}' -> '{title}', proceeding anyway")
            result = MatchResult(
                success=True,
                tmdb_id=tmdb_id,
                title=title,
                year=year,
                confidence=score
            )
            result.warning = f"Low confidence match ({score:.1f}%) - manual review strongly recommended"
            return result
            
        elif score >= self.thresholds['reject']:
            # 40-59% - Reject but provide suggestion
            logger.warning(f"REJECTED: {score:.1f}% confidence below 60% threshold for '{original_query}'")
            result = MatchResult(success=False)
            result.error = f"Match confidence ({score:.1f}%) below 60% threshold"
            result.metadata['suggestion'] = f"Closest match was '{title}' ({year}) with {score:.1f}% confidence"
            return result
            
        else:
            # <40% - Hard reject
            logger.warning(f"HARD REJECT: {score:.1f}% confidence below 40% threshold for '{original_query}'")
            result = MatchResult(success=False)
            result.error = f"No reliable match found - highest confidence was {score:.1f}%"
            return result


# Factory function for easy integration
def create_enhanced_matcher(cache_enabled: bool = None, cache_file: Path = None, config_path: Path = None) -> EnhancedFuzzyMatcher:
    """Create an enhanced fuzzy matcher instance."""
    config = None
    if config_path:
        try:
            from _internal.utils.fuzzy_config import FuzzyMatchingConfig
            config = FuzzyMatchingConfig(config_path)
        except ImportError:
            logger.warning("Could not load fuzzy config, using defaults")
    
    return EnhancedFuzzyMatcher(cache_enabled=cache_enabled, cache_file=cache_file, config=config)


# Backward compatibility - keep the old function name but use new implementation
def create_optimized_matcher(cache_enabled: bool = None, cache_file: Path = None, config_path: Path = None) -> EnhancedFuzzyMatcher:
    """Legacy function name for backward compatibility."""
    return create_enhanced_matcher(cache_enabled=cache_enabled, cache_file=cache_file, config_path=config_path)
