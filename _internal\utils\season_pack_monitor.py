#!/usr/bin/env python3
"""
Continuous Season Pack Monitor - Runs in background to remove season packs as they appear
Enhanced to respect preflight analyzer decisions for approved season packs.
"""
import asyncio
import aiohttp
import sys
import logging
import re
import json
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent))

def is_pack_approved_by_preflight(title, series_title=None):
    """
    Check if a season pack was approved by the preflight analyzer.
    Returns True if the pack should be allowed (not removed).
    """
    try:
        decisions_dir = Path('workspace') / 'preflight_decisions'
        if not decisions_dir.exists():
            return False
        
        # Look for decision files that might match this series
        search_patterns = []
        if series_title:
            clean_title = series_title.replace(' ', '_').replace(':', '').replace('(', '').replace(')', '')
            search_patterns.append(f"{clean_title}_S*.json")
        
        # Also try to extract series name from the pack title
        # Match patterns like "FLCL.Complete.S01-S05" -> "FLCL"
        title_match = re.match(r'^([A-Za-z0-9\.\-_]+?)[\.\s]', title)
        if title_match:
            extracted_title = title_match.group(1).replace('.', '_')
            search_patterns.append(f"{extracted_title}_S*.json")
        
        # Check all matching decision files
        for pattern in search_patterns:
            for decision_file in decisions_dir.glob(pattern):
                try:
                    with open(decision_file, 'r', encoding='utf-8') as f:
                        decision = json.load(f)
                    
                    # Check if this decision chose pack_only strategy and has a matching pack
                    if decision.get('strategy') == 'pack_only':
                        pack_plan = decision.get('plan', {}).get('pack')
                        if pack_plan:
                            # If we find any pack approved for this series, allow it
                            return True
                except Exception:
                    continue  # Skip corrupted files
        
        return False
    except Exception:
        return False  # Default to not approved if we can't check

async def continuous_season_pack_monitor():
    """
    Continuously monitor Sonarr queue and remove season packs as they appear.
    """
    from _internal.utils.common_helpers import get_setting, load_settings
    
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger(__name__)
    
    # Load settings
    settings_dict = load_settings()
    sonarr_url = get_setting('Sonarr', 'url', settings_dict=settings_dict, default='http://localhost:8989')
    sonarr_api_key = get_setting('Sonarr', 'api_key', settings_dict=settings_dict)
    
    if not sonarr_api_key:
        logger.error("❌ Sonarr API key not configured")
        return
    
    headers = {'X-Api-Key': sonarr_api_key}
    
    # Season pack detection patterns
    season_pack_patterns = [
        r'\bS\d{1,2}(?![\dE])',           # S01, S02 without episode
        r'\bS\d{1,2}\b(?!.*E\d)',         # S01, S02 anywhere without E## later
        r'\bSeason[\s\._-]*\d{1,2}',      # Season 1, Season.01, Season_01
        r'\bComplete[\s\._-]*Season',     # Complete Season, Complete.Season
        r'\bFull[\s\._-]*Season',         # Full Season, Full.Season
        r'\bS\d{1,2}\.',                  # S01., S02. (period after season)
        r'\bS\d{1,2}\s+\d{4}p',           # S01 1080p, S01 720p
        r'FLCL\.S01\.',                   # Block FLCL.S01. specifically
        r'FLCL\.S\d{1,2}\.',              # Block any FLCL.S##. pattern
        r'\bS\d{1,2}\.\d{3,4}p',          # S01.1080p, S02.720p format
    ]
    
    logger.info("🚨 Starting continuous season pack monitoring...")
    logger.info("💡 Press Ctrl+C to stop monitoring")
    
    monitoring_count = 0
    removed_count = 0
    
    try:
        async with aiohttp.ClientSession() as session:
            while True:
                monitoring_count += 1
                
                try:
                    # Check queue
                    async with session.get(f"{sonarr_url}/api/v3/queue", headers=headers) as resp:
                        if resp.status != 200:
                            logger.warning(f"⚠️ Queue check failed: HTTP {resp.status}")
                            await asyncio.sleep(30)
                            continue
                            
                        queue = await resp.json()
                        records = queue.get('records', [])
                        
                        if monitoring_count % 12 == 1:  # Log every 2 minutes (12 * 10s = 120s)
                            logger.info(f"🔍 Monitor check #{monitoring_count}: {len(records)} items in queue")
                        
                        season_packs_found = []
                        
                        for item in records:
                            title = item.get('title', '')
                            item_id = item.get('id')
                            
                            # Check if this looks like a season pack
                            is_season_pack = False
                            matched_pattern = None
                            for pattern in season_pack_patterns:
                                if re.search(pattern, title, re.IGNORECASE):
                                    is_season_pack = True
                                    matched_pattern = pattern
                                    break
                            
                            if is_season_pack:
                                # Check if this pack was approved by preflight analyzer
                                series_title = item.get('series', {}).get('title') if isinstance(item.get('series'), dict) else None
                                if is_pack_approved_by_preflight(title, series_title):
                                    if monitoring_count % 12 == 1:  # Log occasionally to avoid spam
                                        logger.info(f"✅ ALLOWING preflight-approved pack: {title}")
                                    continue  # Skip removal - this pack was approved by preflight
                                
                                season_packs_found.append((item_id, title, matched_pattern))
                        
                        # Remove any season packs found (with deduplication)
                        removed_titles = set()
                        for item_id, title, pattern in season_packs_found:
                            if title in removed_titles:
                                continue  # Skip if we already removed this title
                                
                            try:
                                logger.warning(f"🚨 REMOVING SEASON PACK: {title} (matched: {pattern})")
                                
                                # Remove from queue and blocklist
                                params = {
                                    'removeFromClient': 'true',
                                    'blocklist': 'true'
                                }
                                async with session.delete(f"{sonarr_url}/api/v3/queue/{item_id}", 
                                                        headers=headers, params=params) as del_resp:
                                    if del_resp.status in [200, 204]:
                                        logger.info(f"✅ Successfully removed and blocklisted: {title}")
                                        removed_count += 1
                                        removed_titles.add(title)
                                    else:
                                        logger.error(f"❌ Failed to remove season pack: HTTP {del_resp.status}")
                            except Exception as e:
                                logger.error(f"❌ Error removing season pack {title}: {e}")
                        
                        if season_packs_found:
                            unique_titles = len(set(title for _, title, _ in season_packs_found))
                            logger.warning(f"🚨 Processed {len(season_packs_found)} season pack entries ({unique_titles} unique titles)")
                        
                except Exception as e:
                    logger.error(f"❌ Monitor cycle error: {e}")
                
                # Wait 10 seconds before next check
                await asyncio.sleep(10)
                
    except KeyboardInterrupt:
        logger.info(f"🛑 Monitoring stopped by user")
        logger.info(f"📊 Total monitoring cycles: {monitoring_count}")
        logger.info(f"🚨 Total season packs removed: {removed_count}")
    except Exception as e:
        logger.error(f"❌ Fatal monitoring error: {e}")

if __name__ == "__main__":
    asyncio.run(continuous_season_pack_monitor())
