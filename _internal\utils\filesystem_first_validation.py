#!/usr/bin/env python3
"""
Filesystem-First Validation Engine for PlexMovieAutomator pipeline.

This module provides validation capabilities for the new filesystem-first architecture:
- Validates directory structure and marker files
- Checks for orphaned files and missing content
- Monitors file integrity and processing consistency  
- Reports health metrics based on filesystem state
"""

import logging
import time
from pathlib import Path
from datetime import datetime, timezone
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass

from .filesystem_first_state_manager import FilesystemFirstStateManager, MetadataOnlyDatabase

logger = logging.getLogger(__name__)


@dataclass
class ValidationResult:
    """Result of a validation operation."""
    success: bool
    issues_found: int
    corrections_made: int
    errors: List[str]
    warnings: List[str]
    performance_metrics: Dict[str, Any]


class FilesystemFirstValidationEngine:
    """
    Validation engine for filesystem-first state management.
    
    Focuses on validating the filesystem state, marker files, and directory structure
    rather than database status tracking.
    """
    
    def __init__(self, workspace_root: Path):
        self.workspace_root = Path(workspace_root)
        self.filesystem_manager = FilesystemFirstStateManager(workspace_root)
        self.metadata_db = MetadataOnlyDatabase(workspace_root)
        
        # Validation rules and thresholds
        self.validation_rules: Dict[str, Any] = {
            'max_stale_markers_hours': 48,
            'max_orphaned_files': 10,
            'required_directories': [
                'workspace/1_downloading',
                'workspace/2_downloaded_and_organized',
                'workspace/3_mkv_cleaned_subtitles_extracted',
                'workspace/4_ready_for_final_mux',
                'workspace/issues_hold'
            ]
        }
    
    def run_full_validation(self, fix_issues: bool = False) -> ValidationResult:
        """
        Run comprehensive validation of the filesystem-first pipeline.
        
        Args:
            fix_issues: Whether to automatically fix issues found
            
        Returns:
            ValidationResult with detailed findings
        """
        start_time = time.time()
        result = ValidationResult(
            success=True,
            issues_found=0,
            corrections_made=0,
            errors=[],
            warnings=[],
            performance_metrics={}
        )
        
        logger.info("🔍 Starting filesystem-first validation")
        
        try:
            # 1. Validate directory structure
            self._validate_directory_structure(result)
            
            # 2. Validate marker files
            self._validate_marker_files(result, fix_issues)
            
            # 3. Check for orphaned files
            self._check_orphaned_files(result)
            
            # 4. Validate movie consistency
            self._validate_movie_consistency(result)
            
            # 5. Check for stale processing
            self._check_stale_processing(result, fix_issues)
            
            # 6. Validate metadata integrity
            self._validate_metadata_integrity(result)
            
            # Calculate performance metrics
            end_time = time.time()
            result.performance_metrics = {
                'validation_duration_seconds': end_time - start_time,
                'movies_discovered': self._count_discovered_movies(),
                'directories_scanned': len(self.validation_rules['required_directories']),
                'timestamp': datetime.now().isoformat()
            }
            
            # Final assessment
            result.success = len(result.errors) == 0
            
            logger.info(f"✅ Validation completed: {result.issues_found} issues found, "
                       f"{result.corrections_made} corrections made")
            
        except Exception as e:
            result.success = False
            result.errors.append(f"Validation failed with exception: {e}")
            logger.error(f"❌ Validation failed: {e}")
        
        return result
    
    def _validate_directory_structure(self, result: ValidationResult):
        """Validate that all required directories exist."""
        logger.debug("Validating directory structure...")
        
        for dir_path in self.validation_rules['required_directories']:
            full_path = self.workspace_root / dir_path
            if not full_path.exists():
                result.errors.append(f"Required directory missing: {dir_path}")
                result.issues_found += 1
                
                # Auto-create missing directories
                try:
                    full_path.mkdir(parents=True, exist_ok=True)
                    result.corrections_made += 1
                    logger.info(f"Created missing directory: {dir_path}")
                except Exception as e:
                    result.errors.append(f"Failed to create directory {dir_path}: {e}")
    
    def _validate_marker_files(self, result: ValidationResult, fix_issues: bool):
        """Validate marker file consistency and cleanup stale markers."""
        logger.debug("Validating marker files...")
        
        discovered_movies = self.filesystem_manager.discover_movies_by_stage()
        
        for stage, movies in discovered_movies.items():
            for movie in movies:
                movie_dir = Path(movie.get('movie_directory', ''))
                if not movie_dir.exists():
                    continue
                
                # Check for conflicting markers
                conflicts = self._check_marker_conflicts(movie_dir)
                if conflicts:
                    result.warnings.extend(conflicts)
                    result.issues_found += len(conflicts)
                    
                    if fix_issues:
                        self.filesystem_manager.cleanup_stale_markers(movie_dir)
                        result.corrections_made += 1
                
                # Check for incomplete marker data
                for marker_type in self.filesystem_manager.MARKERS.keys():
                    marker_data = self.filesystem_manager.get_stage_marker_data(movie_dir, marker_type)
                    if marker_data and not marker_data.get('timestamp'):
                        result.warnings.append(f"Marker {marker_type} in {movie_dir.name} missing timestamp")
                        result.issues_found += 1
    
    def _check_marker_conflicts(self, movie_dir: Path) -> List[str]:
        """Check for conflicting marker files in a movie directory."""
        conflicts = []
        markers = self.filesystem_manager.MARKERS
        
        # Check for processing markers without completion
        if (movie_dir / markers['mkv_processing']).exists() and (movie_dir / markers['mkv_complete']).exists():
            conflicts.append(f"Movie {movie_dir.name} has both mkv_processing and mkv_complete markers")
        
        if (movie_dir / markers['subtitle_processing']).exists() and (movie_dir / markers['subtitle_complete']).exists():
            conflicts.append(f"Movie {movie_dir.name} has both subtitle_processing and subtitle_complete markers")
        
        # Check for progression without prerequisites
        if (movie_dir / markers['mkv_complete']).exists() and not (movie_dir / markers['organized']).exists():
            conflicts.append(f"Movie {movie_dir.name} has mkv_complete but missing organized marker")
        
        if (movie_dir / markers['subtitle_complete']).exists() and not (movie_dir / markers['mkv_complete']).exists():
            conflicts.append(f"Movie {movie_dir.name} has subtitle_complete but missing mkv_complete marker")
        
        return conflicts
    
    def _check_orphaned_files(self, result: ValidationResult):
        """Check for orphaned files and directories."""
        logger.debug("Checking for orphaned files...")
        
        # Check for movies in directories without any markers
        for stage_name, stage_dir in self.filesystem_manager.stage_directories.items():
            if not stage_dir.exists():
                continue
                
            for item in stage_dir.iterdir():
                if not item.is_dir():
                    continue
                    
                # Check if this directory has any marker files
                has_markers = any(
                    (item / marker).exists() 
                    for marker in self.filesystem_manager.MARKERS.values()
                )
                
                # Check if it has movie content
                has_content = any(item.glob("*.mkv")) or (item / "_Processed_VideoAudio").exists()
                
                if has_content and not has_markers:
                    result.warnings.append(f"Orphaned movie directory (no markers): {item}")
                    result.issues_found += 1
                elif not has_content and has_markers:
                    result.warnings.append(f"Empty directory with markers: {item}")
                    result.issues_found += 1
    
    def _validate_movie_consistency(self, result: ValidationResult):
        """Validate consistency between discovered movies and expected states."""
        logger.debug("Validating movie consistency...")
        
        discovered_movies = self.filesystem_manager.discover_movies_by_stage()
        
        # Check for movies appearing in multiple stages
        all_movie_ids = {}
        for stage, movies in discovered_movies.items():
            for movie in movies:
                movie_id = self.filesystem_manager.generate_movie_identifier(movie)
                if movie_id in all_movie_ids:
                    result.errors.append(
                        f"Movie {movie_id} appears in multiple stages: "
                        f"{all_movie_ids[movie_id]} and {stage}"
                    )
                    result.issues_found += 1
                else:
                    all_movie_ids[movie_id] = stage
        
        # Check for movies with inconsistent stage vs directory location
        for stage, movies in discovered_movies.items():
            for movie in movies:
                movie_dir = Path(movie.get('movie_directory', ''))
                if movie_dir.exists():
                    expected_stage = self.filesystem_manager.determine_movie_stage(movie_dir)
                    if expected_stage != stage and expected_stage != 'unknown':
                        result.warnings.append(
                            f"Movie {movie_dir.name} stage mismatch: "
                            f"detected as {stage}, markers indicate {expected_stage}"
                        )
                        result.issues_found += 1
    
    def _check_stale_processing(self, result: ValidationResult, fix_issues: bool):
        """Check for movies stuck in processing states."""
        logger.debug("Checking for stale processing...")
        
        discovered_movies = self.filesystem_manager.discover_movies_by_stage()
        max_hours = self.validation_rules['max_stale_markers_hours']
        
        processing_stages = [
            'mkv_processing_active', 'mkv_processing_interrupted',
            'subtitle_processing_active'
        ]
        
        for stage in processing_stages:
            if stage in discovered_movies:
                for movie in discovered_movies[stage]:
                    movie_dir = Path(movie.get('movie_directory', ''))
                    if not movie_dir.exists():
                        continue
                    
                    # Check marker timestamp
                    marker_type = 'mkv_processing' if 'mkv' in stage else 'subtitle_processing'
                    marker_data = self.filesystem_manager.get_stage_marker_data(movie_dir, marker_type)
                    
                    if marker_data and marker_data.get('timestamp'):
                        try:
                            marker_time = datetime.fromisoformat(marker_data['timestamp'])
                            age_hours = (datetime.now() - marker_time).total_seconds() / 3600
                            
                            if age_hours > max_hours:
                                result.warnings.append(
                                    f"Stale processing marker for {movie_dir.name}: "
                                    f"{age_hours:.1f} hours old"
                                )
                                result.issues_found += 1
                                
                                if fix_issues:
                                    # Clear stale processing marker
                                    self.filesystem_manager.clear_stage_marker(movie_dir, marker_type)
                                    result.corrections_made += 1
                                    
                        except ValueError:
                            result.warnings.append(f"Invalid timestamp in marker for {movie_dir.name}")
                            result.issues_found += 1
    
    def _validate_metadata_integrity(self, result: ValidationResult):
        """Validate metadata database integrity."""
        logger.debug("Validating metadata integrity...")
        
        try:
            # Get all metadata records
            metadata_movies = self.metadata_db.get_all_movies()
            
            # Check for records with missing required fields
            for movie in metadata_movies:
                if not movie.get('unique_id'):
                    result.errors.append("Metadata record missing unique_id")
                    result.issues_found += 1
                
                if not movie.get('title'):
                    result.errors.append(f"Metadata record {movie.get('unique_id')} missing title")
                    result.issues_found += 1
            
            # Check for duplicate unique_ids (should not be possible with UNIQUE constraint)
            unique_ids = [movie.get('unique_id') for movie in metadata_movies]
            duplicates = set([uid for uid in unique_ids if unique_ids.count(uid) > 1])
            
            if duplicates:
                result.errors.append(f"Duplicate unique_ids in metadata: {duplicates}")
                result.issues_found += len(duplicates)
            
        except Exception as e:
            result.errors.append(f"Failed to validate metadata integrity: {e}")
            result.issues_found += 1
    
    def _count_discovered_movies(self) -> int:
        """Count total movies discovered across all stages."""
        try:
            discovered_movies = self.filesystem_manager.discover_movies_by_stage()
            return sum(len(movies) for movies in discovered_movies.values())
        except Exception:
            return 0
    
    def generate_health_report(self) -> Dict[str, Any]:
        """Generate a comprehensive health report."""
        validation_result = self.run_full_validation()
        
        # Discover current state
        discovered_movies = self.filesystem_manager.discover_movies_by_stage()
        metadata_count = len(self.metadata_db.get_all_movies())
        
        # Calculate health score
        total_checks = 10  # Number of different validation categories
        issues_weight = min(validation_result.issues_found / total_checks, 1.0)
        health_score = max(0, 100 - (issues_weight * 100))
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'health_score': round(health_score, 1),
            'validation_summary': {
                'success': validation_result.success,
                'issues_found': validation_result.issues_found,
                'corrections_made': validation_result.corrections_made,
                'errors': validation_result.errors,
                'warnings': validation_result.warnings[:10]  # Limit to first 10 warnings
            },
            'movie_statistics': {
                'total_discovered': sum(len(movies) for movies in discovered_movies.values()),
                'by_stage': {stage: len(movies) for stage, movies in discovered_movies.items()},
                'metadata_records': metadata_count
            },
            'filesystem_status': {
                'directories_checked': len(self.validation_rules['required_directories']),
                'all_directories_exist': validation_result.issues_found == 0
            },
            'performance_metrics': validation_result.performance_metrics,
            'recommendations': self._generate_recommendations(validation_result, discovered_movies)
        }
        
        return report
    
    def _generate_recommendations(self, validation_result: ValidationResult, 
                                discovered_movies: Dict[str, List[Dict[str, Any]]]) -> List[str]:
        """Generate actionable recommendations based on validation results."""
        recommendations = []
        
        if validation_result.errors:
            recommendations.append("Address critical errors before running pipeline")
        
        if validation_result.issues_found > 10:
            recommendations.append("Run validation with auto-fix enabled to resolve issues")
        
        # Check for stuck movies
        stuck_stages = ['mkv_processing_interrupted', 'subtitle_processing_active']
        stuck_count = sum(len(discovered_movies.get(stage, [])) for stage in stuck_stages)
        if stuck_count > 0:
            recommendations.append(f"Investigate {stuck_count} movies stuck in processing")
        
        # Check for error movies
        error_count = len(discovered_movies.get('error', []))
        if error_count > 0:
            recommendations.append(f"Review and resolve {error_count} movies in error state")
        
        # Check for orphaned content
        if any('orphaned' in warning.lower() for warning in validation_result.warnings):
            recommendations.append("Clean up orphaned files and directories")
        
        if not recommendations:
            recommendations.append("System is healthy - no action required")
        
        return recommendations
