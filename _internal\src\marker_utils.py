#!/usr/bin/env python3
from __future__ import annotations

from pathlib import Path
from typing import Any, Dict, Optional

from _internal.utils.marker_manager import get_marker_manager


def write_organized_marker(folder: Path, metadata: Optional[Dict[str, Any]] = None) -> bool:
    try:
        mm = get_marker_manager(folder)
        return mm.write('.organized', metadata or {})
    except Exception:
        try:
            (Path(folder) / '.organized').touch(exist_ok=True)
            return True
        except Exception:
            return False


def write_mkv_complete_marker(folder: Path, metadata: Optional[Dict[str, Any]] = None) -> bool:
    try:
        mm = get_marker_manager(folder)
        return mm.write('.mkv_complete', metadata or {})
    except Exception:
        try:
            (Path(folder) / '.mkv_complete').touch(exist_ok=True)
            return True
        except Exception:
            return False

