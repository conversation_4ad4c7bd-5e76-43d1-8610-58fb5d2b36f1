"""
Enhanced Season Progression Daemon
Monitors opted-in series and advances seasons when current season is complete.
Runs independently of Script 02 for real-time progression.
"""
import asyncio
import aiohttp
import logging
import re
from pathlib import Path
from _internal.utils.common_helpers import get_setting, load_settings
from _internal.utils.tv_season_progression_manager import TVSeasonProgressionManager

async def run_progression_daemon(interval_seconds: int = 30):
    """
    Enhanced progression daemon with better logging and season pack removal.
    """
    # Setup enhanced logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger("progression_daemon")
    
    settings = load_settings()
    sonarr_url = get_setting("Sonarr", "url", settings_dict=settings, default="http://localhost:8989")
    api_key = get_setting("Sonarr", "api_key", settings_dict=settings)
    
    if not api_key:
        logger.error("❌ Sonarr API key not configured")
        return
        
    logger.info("🚀 Starting enhanced season progression monitoring...")
    logger.info("💡 Press Ctrl+C to stop monitoring")
    
    mgr = TVSeasonProgressionManager(logger)
    sequential_file = Path("config/sequential_series.txt")
    check_count = 0

    while True:
        try:
            check_count += 1
            
            # Load opt-in series (allows runtime updates)
            opt_in = set()
            if sequential_file.exists():
                try:
                    for line in sequential_file.read_text(encoding="utf-8").splitlines():
                        line = line.strip()
                        if line and not line.startswith("#"):
                            opt_in.add(line)
                except Exception as e:
                    logger.warning(f"Failed to load config/sequential_series.txt: {e}")
                    
            if not opt_in:
                await asyncio.sleep(interval_seconds)
                continue
                
            if check_count % 10 == 1:  # Log status every 10 checks
                logger.info(f"🔍 Check #{check_count}: Monitoring {len(opt_in)} series for progression")
                
            headers = {"X-Api-Key": api_key}
            timeout = aiohttp.ClientTimeout(total=60)
            
            async with aiohttp.ClientSession(timeout=timeout) as session:
                # Get all series from Sonarr
                async with session.get(f"{sonarr_url}/api/v3/series", headers=headers) as sr:
                    if sr.status != 200:
                        logger.warning(f"Failed to get series list: HTTP {sr.status}")
                        await asyncio.sleep(interval_seconds)
                        continue
                    series_list = await sr.json()
                    
                # Check each opted-in series
                for series_obj in series_list:
                    try:
                        series_title = series_obj.get('title')
                        series_year = series_obj.get('year')
                        display = f"{series_title} ({series_year})" if series_year else series_title
                        
                        if display not in opt_in:
                            continue
                            
                        sid = series_obj.get("id")
                        async with session.get(f"{sonarr_url}/api/v3/episode?seriesId={sid}", headers=headers) as er:
                            if er.status != 200:
                                continue
                            episodes = await er.json()
                            
                        # Initialize or get existing progression state
                        key = await mgr.init_if_needed(series_obj, episodes, opt_in)
                        if not key:
                            key = mgr.series_key(series_obj.get("title"), series_obj.get("year"), sid)
                            
                        if key:
                            # Check current season status
                            state = mgr.load(key)
                            current_season = state.get('current_season', 1)
                            
                            # Log current status periodically
                            if check_count % 20 == 1:  # Every 20 checks
                                logger.info(f"📺 {display}: Currently on Season {current_season}")
                            
                            # Enforce monitoring + searches
                            await mgr.enforce_monitoring(key, sonarr_url, api_key)
                            
                            # Check for completion and advance if needed
                            if await mgr.is_current_season_complete(key, sonarr_url, api_key):
                                logger.info(f"✅ {display}: Season {current_season} complete!")
                                await mgr.advance(key, sonarr_url, api_key)
                                
                                # Force removal of any season packs after advancement
                                await remove_season_packs_for_series(session, sonarr_url, headers, sid, logger)
                                
                                # Log new state
                                new_state = mgr.load(key)
                                if new_state.get('series_complete'):
                                    logger.info(f"🎉 {display}: SERIES COMPLETE!")
                                else:
                                    new_season = new_state.get('current_season')
                                    logger.info(f"⏭️ {display}: Advanced to Season {new_season}")
                                    
                    except Exception as e:
                        logger.error(f"Error checking {display}: {e}")
                        continue
                        
        except KeyboardInterrupt:
            logger.info(f"🛑 Monitoring stopped by user after {check_count} checks")
            break
        except Exception as e:
            logger.error(f"❌ Monitoring error: {e}")
            
        await asyncio.sleep(interval_seconds)


async def remove_season_packs_for_series(session, sonarr_url: str, headers: dict, 
                                        series_id: int, logger):
    """Remove any season packs for a specific series from the queue."""
    try:
        # Get queue
        async with session.get(f"{sonarr_url}/api/v3/queue", headers=headers) as resp:
            if resp.status != 200:
                return
            
            queue = await resp.json()
            records = queue.get('records', [])
        
        # Find and remove season packs for this series
        season_pack_patterns = [
            r'\bS\d{1,2}(?![\dE])',
            r'\bSeason[\s\._-]*\d{1,2}',
            r'\bComplete[\s\._-]*Season',
        ]
        
        for item in records:
            if item.get('seriesId') != series_id:
                continue
                
            title = item.get('title', '')
            for pattern in season_pack_patterns:
                if re.search(pattern, title, re.IGNORECASE):
                    item_id = item.get('id')
                    params = {'removeFromClient': 'true', 'blocklist': 'true'}
                    async with session.delete(f"{sonarr_url}/api/v3/queue/{item_id}", 
                                            headers=headers, params=params) as del_resp:
                        if del_resp.status in [200, 204]:
                            logger.info(f"🗑️ Removed season pack after progression: {title}")
                    break
                    
    except Exception as e:
        logger.warning(f"Failed to remove season packs: {e}")


# Backward compatibility wrapper
async def continuous_progression_monitor():
    """Wrapper for backward compatibility."""
    await run_progression_daemon(interval_seconds=30)