#!/usr/bin/env python3
"""
PlexMovieAutomator/maintenance/nzb_watch_folder_manager.py

Helper utility to manage NZB files between organized storage folders and SABnzbd watch folder.

This script handles:
- Moving NZB files from organized locations (movies/tv_shows) to the centralized watch folder
- Archiving processed NZB files after SABnzbd picks them up
- Monitoring watch folder status
- Batch operations for multiple NZB files

Usage:
    python maintenance/nzb_watch_folder_manager.py --move-to-watch movies/some_movie.nzb
    python maintenance/nzb_watch_folder_manager.py --move-to-watch tv_shows/some_show.nzb
    python maintenance/nzb_watch_folder_manager.py --auto-move  # Move all pending NZB files
    python maintenance/nzb_watch_folder_manager.py --status     # Show watch folder status
"""

import sys
import os
import shutil
import argparse
import logging
from pathlib import Path
from datetime import datetime
from typing import List, Optional, Tuple

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

try:
    from _internal.utils.common_helpers import load_settings, get_path_setting
except ImportError:
    # Fallback for direct execution
    def load_settings(path):
        import configparser
        config = configparser.ConfigParser()
        config.read(path)
        return {section: dict(config[section]) for section in config.sections()}
    
    def get_path_setting(section, key, settings_dict=None, default=None):
        return settings_dict.get(section, {}).get(key, default) if settings_dict else default

class NZBWatchFolderManager:
    """Manages NZB files between organized storage and SABnzbd watch folder."""
    
    def __init__(self, settings_dict: dict = None):
        """Initialize the manager with settings."""
        self.settings_dict = settings_dict or load_settings("_internal/config/settings.ini")
        self.workspace_root = Path.cwd()
        
        # Get folder paths from settings
        self.new_requests_dir = self.workspace_root / get_path_setting(
            "Paths", "new_requests_dir", self.settings_dict, "workspace/0_new_requests"
        )
        self.movie_requests_dir = self.workspace_root / get_path_setting(
            "Paths", "movie_requests_dir", self.settings_dict, "workspace/0_new_requests/movies"
        )
        self.tv_requests_dir = self.workspace_root / get_path_setting(
            "Paths", "tv_requests_dir", self.settings_dict, "workspace/0_new_requests/tv_shows"
        )
        self.watch_folder = self.workspace_root / get_path_setting(
            "Paths", "sabnzbd_watch_folder", self.settings_dict, "workspace/0_new_requests/watched"
        )
        
        # Archive directories
        self.movie_archive_dir = self.workspace_root / get_path_setting(
            "Paths", "movie_nzb_archive_dir", self.settings_dict, "workspace/0_new_requests/movies/nzb_files_launched_archive"
        )
        self.tv_archive_dir = self.workspace_root / get_path_setting(
            "Paths", "tv_nzb_archive_dir", self.settings_dict, "workspace/0_new_requests/tv_shows/nzb_files_launched_archive"
        )
        
        # Ensure directories exist
        self._ensure_directories()
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger("NZBWatchManager")
    
    def _ensure_directories(self):
        """Ensure all required directories exist."""
        for directory in [self.watch_folder, self.movie_archive_dir, self.tv_archive_dir]:
            directory.mkdir(parents=True, exist_ok=True)
    
    def find_nzb_files(self, content_type: str = "both") -> List[Path]:
        """
        Find NZB files in organized folders.
        
        Args:
            content_type: "movies", "tv_shows", or "both"
            
        Returns:
            List of NZB file paths
        """
        nzb_files = []
        
        if content_type in ["movies", "both"]:
            # Look in movies/nzb_files_for_download/
            movie_nzb_dir = self.movie_requests_dir / "nzb_files_for_download"
            if movie_nzb_dir.exists():
                nzb_files.extend(list(movie_nzb_dir.glob("*.nzb")))
        
        if content_type in ["tv_shows", "both"]:
            # Look in tv_shows/nzb_files_for_download/
            tv_nzb_dir = self.tv_requests_dir / "nzb_files_for_download"
            if tv_nzb_dir.exists():
                nzb_files.extend(list(tv_nzb_dir.glob("*.nzb")))
        
        return nzb_files
    
    def move_to_watch_folder(self, nzb_file_path: Path) -> bool:
        """
        Move an NZB file to the watch folder.
        
        Args:
            nzb_file_path: Path to the NZB file to move
            
        Returns:
            True if successful, False otherwise
        """
        if not nzb_file_path.exists():
            self.logger.error(f"NZB file not found: {nzb_file_path}")
            return False
        
        if not nzb_file_path.suffix.lower() == ".nzb":
            self.logger.error(f"File is not an NZB file: {nzb_file_path}")
            return False
        
        destination = self.watch_folder / nzb_file_path.name
        
        # Handle name conflicts
        counter = 1
        while destination.exists():
            stem = nzb_file_path.stem
            suffix = nzb_file_path.suffix
            destination = self.watch_folder / f"{stem}_{counter}{suffix}"
            counter += 1
        
        try:
            shutil.move(str(nzb_file_path), str(destination))
            self.logger.info(f"✅ Moved to watch folder: {nzb_file_path.name} → {destination.name}")
            return True
        except Exception as e:
            self.logger.error(f"❌ Failed to move {nzb_file_path.name}: {e}")
            return False
    
    def archive_processed_nzb(self, nzb_file_path: Path, content_type: str) -> bool:
        """
        Move a processed NZB file to the appropriate archive folder.
        
        Args:
            nzb_file_path: Path to the NZB file in watch folder
            content_type: "movies" or "tv_shows"
            
        Returns:
            True if successful, False otherwise
        """
        if content_type == "movies":
            archive_dir = self.movie_archive_dir
        elif content_type == "tv_shows":
            archive_dir = self.tv_archive_dir
        else:
            self.logger.error(f"Invalid content type: {content_type}")
            return False
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        archived_name = f"{timestamp}_{nzb_file_path.name}"
        destination = archive_dir / archived_name
        
        try:
            shutil.move(str(nzb_file_path), str(destination))
            self.logger.info(f"📁 Archived: {nzb_file_path.name} → {archived_name}")
            return True
        except Exception as e:
            self.logger.error(f"❌ Failed to archive {nzb_file_path.name}: {e}")
            return False
    
    def get_watch_folder_status(self) -> dict:
        """
        Get status of the watch folder.
        
        Returns:
            Dictionary with watch folder status information
        """
        nzb_files_in_watch = list(self.watch_folder.glob("*.nzb"))
        pending_movies = self.find_nzb_files("movies")
        pending_tv = self.find_nzb_files("tv_shows")
        
        return {
            "watch_folder_path": str(self.watch_folder),
            "files_in_watch_folder": len(nzb_files_in_watch),
            "watch_folder_files": [f.name for f in nzb_files_in_watch],
            "pending_movie_nzbs": len(pending_movies),
            "pending_tv_nzbs": len(pending_tv),
            "total_pending": len(pending_movies) + len(pending_tv),
            "pending_movie_files": [f.name for f in pending_movies],
            "pending_tv_files": [f.name for f in pending_tv]
        }
    
    def auto_move_all_pending(self) -> Tuple[int, int]:
        """
        Automatically move all pending NZB files to watch folder.
        
        Returns:
            Tuple of (successful_moves, failed_moves)
        """
        pending_files = self.find_nzb_files("both")
        successful = 0
        failed = 0
        
        self.logger.info(f"🔄 Auto-moving {len(pending_files)} pending NZB files...")
        
        for nzb_file in pending_files:
            if self.move_to_watch_folder(nzb_file):
                successful += 1
            else:
                failed += 1
        
        self.logger.info(f"✅ Auto-move complete: {successful} successful, {failed} failed")
        return successful, failed


def main():
    """Main CLI interface."""
    parser = argparse.ArgumentParser(description="Manage NZB files between organized folders and SABnzbd watch folder")
    parser.add_argument("--move-to-watch", help="Move specific NZB file to watch folder (relative path from 0_new_requests)")
    parser.add_argument("--auto-move", action="store_true", help="Auto-move all pending NZB files to watch folder")
    parser.add_argument("--status", action="store_true", help="Show watch folder status")
    parser.add_argument("--clean-watch", action="store_true", help="Clean processed files from watch folder")
    
    args = parser.parse_args()
    
    # Initialize manager
    try:
        manager = NZBWatchFolderManager()
    except Exception as e:
        print(f"❌ Failed to initialize manager: {e}")
        return 1
    
    # Handle different operations
    if args.status:
        status = manager.get_watch_folder_status()
        print(f"\n📂 SABnzbd Watch Folder Status")
        print(f"{'='*50}")
        print(f"Watch Folder: {status['watch_folder_path']}")
        print(f"Files in Watch Folder: {status['files_in_watch_folder']}")
        if status['watch_folder_files']:
            print(f"  {', '.join(status['watch_folder_files'])}")
        print(f"\nPending Files:")
        print(f"  Movies: {status['pending_movie_nzbs']} files")
        if status['pending_movie_files']:
            print(f"    {', '.join(status['pending_movie_files'])}")
        print(f"  TV Shows: {status['pending_tv_nzbs']} files")
        if status['pending_tv_files']:
            print(f"    {', '.join(status['pending_tv_files'])}")
        print(f"  Total Pending: {status['total_pending']} files")
        
    elif args.move_to_watch:
        nzb_path = Path(args.move_to_watch)
        if not nzb_path.is_absolute():
            # Make it relative to 0_new_requests
            nzb_path = manager.new_requests_dir / nzb_path
        
        if manager.move_to_watch_folder(nzb_path):
            print(f"✅ Successfully moved {nzb_path.name} to watch folder")
            return 0
        else:
            print(f"❌ Failed to move {nzb_path.name}")
            return 1
            
    elif args.auto_move:
        successful, failed = manager.auto_move_all_pending()
        print(f"✅ Auto-move complete: {successful} successful, {failed} failed")
        return 0 if failed == 0 else 1
        
    elif args.clean_watch:
        # This would be implemented to clean old files from watch folder
        print("🧹 Clean watch folder functionality not yet implemented")
        return 0
        
    else:
        parser.print_help()
        return 0


if __name__ == "__main__":
    sys.exit(main())
