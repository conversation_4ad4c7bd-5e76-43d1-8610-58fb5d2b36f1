[fuzzy_matching]
# Confidence thresholds for automated decision making
high_confidence_threshold = 95
moderate_confidence_threshold = 80
low_confidence_threshold = 60
auto_approve_threshold = 95.0
silent_accept_threshold = 80.0
reject_threshold = 40.0

# Year tolerance for matching (±years)
year_tolerance = 3
movie_year_tolerance = 3
tv_year_tolerance = 5

# Alternative title settings
use_alternative_titles = true
max_alternative_title_requests = 3
enable_alternative_titles = true
alternative_title_score_threshold = 80.0
max_alternative_title_candidates = 3

# Popularity and tiebreaking
popularity_tiebreaker_threshold = 3

# User interaction settings
enable_interactive_confirmation = false
log_fuzzy_decisions = true

# Performance optimizations
enable_fast_path = true
enable_caching = true
cache_ttl_days = 7
max_tmdb_api_calls_per_query = 5

# Spell checking and normalization
enable_spell_checking = true
skip_spell_check_for_non_english = true
apply_common_typo_fixes = true

# Variant generation limits
max_title_variants = 10
enable_abbreviation_expansion = true
enable_franchise_detection = true

# Automation behavior
batch_automation_mode = true
auto_proceed_on_low_confidence = true
log_all_decisions = true

[tmdb_optimization]
# TMDb API optimization settings
search_concurrency_limit = 5
cache_search_results = true
cache_detail_results = true
cache_timeout_hours = 24
max_retry_attempts = 3
retry_delay_seconds = 2

[franchise_preferences]
# Franchise-specific rules for handling multiple versions
# Format: franchise_keyword = preferred_years:avoid_years:bonus_multiplier:future_penalty

fantastic = 2005:2015,2025:2.0:50
spider = 2002,2021::1.2:0
batman = 1989,2008::1.3:0
star_wars = 1977,1980,1983::1.4:0
matrix = 1999:2023,2025:1.5:30
superman = 1978,2006::1.2:0
x_men = 2000,2003::1.3:0

[performance_tuning]
# Performance monitoring and optimization
enable_performance_logging = true
log_timing_details = true
warn_on_slow_matches_seconds = 10.0

# Memory management
max_cache_entries = 10000
cache_cleanup_interval_hours = 24

[advanced_matching]
# Advanced fuzzy matching parameters
token_set_ratio_weight = 0.3
token_sort_ratio_weight = 0.25
simple_ratio_weight = 0.2
partial_ratio_weight = 0.25

# Bonus scoring
exact_word_match_bonus = 10
substring_match_bonus = 15
year_in_title_bonus = 20
popularity_bonus_max = 20
rating_bonus_max = 15

# Penalty scoring
year_mismatch_penalty_per_year = 5
year_mismatch_penalty_max = 50
future_release_penalty = 50

[logging]
# Logging configuration for fuzzy matching
log_level = INFO
log_cache_operations = false
log_fast_path_decisions = true
log_slow_path_details = true
log_confidence_decisions = true
log_performance_metrics = true

# Log file rotation
max_log_file_size_mb = 50
max_log_files = 5

[debug]
# Debug mode settings
enable_debug_mode = false
save_intermediate_results = false
