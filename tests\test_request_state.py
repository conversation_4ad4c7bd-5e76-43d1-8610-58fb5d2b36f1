import os
import sys
import tempfile
from pathlib import Path

# Ensure test DB is isolated and import path includes repo root
os.environ['REQUEST_STATE_DB'] = str(Path(tempfile.gettempdir()) / 'request_state_test.sqlite3')
REPO_ROOT = Path(__file__).resolve().parents[1]
sys.path.insert(0, str(REPO_ROOT))

from _internal.src import request_state as rs


def setup_function(func):
    # Fresh DB per test
    try:
        if Path(os.environ['REQUEST_STATE_DB']).exists():
            Path(os.environ['REQUEST_STATE_DB']).unlink()
    except Exception:
        pass
    rs.init_db()


def teardown_function(func):
    rs.close()
    try:
        if Path(os.environ['REQUEST_STATE_DB']).exists():
            Path(os.environ['REQUEST_STATE_DB']).unlink()
    except Exception:
        pass


def test_movie_group_lifecycle():
    gid = rs.get_or_create_group('movie', 'Inception', 2010)
    cid = rs.add_child_item(gid, 'movie', title='Inception')
    assert isinstance(cid, int)

    rs.set_child_status(gid, None, None, 'COMPLETE', library_path='/media/movies/Inception (2010)/Inception.mkv')
    assert rs.all_children_terminal(gid)

    rs.mark_group_organized(gid)
    pending = rs.pending_groups()
    assert all(g.id != gid for g in pending)


def test_tv_episodes_and_completion():
    gid = rs.get_or_create_group('tv', 'Chernobyl', 2019)
    # Add three episodes
    for e in (1, 2, 3):
        rs.add_child_item(gid, 'episode', season=1, episode=e, title='Chernobyl')
    # Mark two complete, one failed
    rs.set_child_status(gid, 1, 1, 'COMPLETE', library_path='/tv/Chernobyl/Season 01/S01E01.mkv')
    rs.set_child_status(gid, 1, 2, 'COMPLETE', library_path='/tv/Chernobyl/Season 01/S01E02.mkv')
    rs.set_child_status(gid, 1, 3, 'FAILED')

    assert rs.all_children_terminal(gid)
    rs.mark_group_organized(gid)
    assert all(g.id != gid for g in rs.pending_groups())

