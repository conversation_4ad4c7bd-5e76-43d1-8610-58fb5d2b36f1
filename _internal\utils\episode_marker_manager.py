#!/usr/bin/env python3
"""
Enhanced episode-aware marker system for TV shows.

This module provides utilities for tracking specific episodes within seasons
instead of just marking entire seasons as complete.
"""

import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Set, Any, Union


class EpisodeMarkerManager:
    """
    Manages episode-aware markers for TV shows.
    
    Instead of simple .organized markers, this tracks which specific episodes
    have been processed in each season.
    """
    
    def __init__(self, base_path: Path, marker_name: str = ".organized"):
        """
        Initialize episode marker manager.
        
        Args:
            base_path: Base directory for the TV show (season level)
            marker_name: Name of the marker file (e.g., .organized, .mkv_complete)
        """
        self.base_path = Path(base_path)
        self.marker_name = marker_name
        self.marker_path = self.base_path / marker_name
        
    def load_episode_data(self) -> Dict[str, List[int]]:
        """
        Load existing episode tracking data from marker file.
        
        Returns:
            Dictionary mapping season keys (S01, S02) to lists of episode numbers
        """
        episode_data = {}
        
        if self.marker_path.exists():
            try:
                with open(self.marker_path, 'r') as f:
                    content = f.read().strip()
                    if content:
                        episode_data = json.loads(content)
            except (json.JSONDecodeError, Exception):
                # Old format or corrupted - start fresh
                episode_data = {}
                
        return episode_data
    
    def save_episode_data(self, episode_data: Dict[str, List[int]]) -> bool:
        """
        Save episode tracking data to marker file.
        
        Args:
            episode_data: Dictionary mapping season keys to episode lists
            
        Returns:
            True if saved successfully, False otherwise
        """
        try:
            # Ensure directory exists
            self.marker_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(self.marker_path, 'w') as f:
                json.dump(episode_data, f, indent=2)
            return True
        except Exception:
            return False
    
    def add_episode(self, season: int, episode: int, logger: Optional[logging.Logger] = None, metadata: Optional[Dict[str, Any]] = None) -> bool:
        """
        Add an episode to the tracking data with optional metadata.
        
        Args:
            season: Season number
            episode: Episode number
            logger: Optional logger for output
            metadata: Optional metadata about the episode
            
        Returns:
            True if added successfully, False otherwise
        """
        try:
            episode_data = self.load_episode_data()
            season_key = f"S{season:02d}"
            
            if season_key not in episode_data:
                episode_data[season_key] = {}
            
            # Support both old format (list) and new format (dict with metadata)
            if isinstance(episode_data[season_key], list):
                # Convert old format to new format
                old_episodes = episode_data[season_key]
                episode_data[season_key] = {}
                for ep in old_episodes:
                    episode_data[season_key][f"E{ep:02d}"] = {
                        "episode_number": ep,
                        "tracked_at": datetime.now().isoformat()
                    }
            
            episode_key = f"E{episode:02d}"
            if episode_key not in episode_data[season_key]:
                episode_info = {
                    "episode_number": episode,
                    "tracked_at": datetime.now().isoformat()
                }
                
                # Add metadata if provided
                if metadata:
                    episode_info.update(metadata)
                
                episode_data[season_key][episode_key] = episode_info
                
                if self.save_episode_data(episode_data):
                    if logger:
                        logger.info(f"📝 Updated {self.marker_name} - {season_key}{episode_key} with metadata")
                    return True
            else:
                if logger:
                    logger.debug(f"Episode {season_key}{episode_key} already tracked in {self.marker_name}")
                return True
                
        except Exception as e:
            if logger:
                logger.warning(f"Failed to add episode to {self.marker_name}: {e}")
            
        return False
    
    def is_episode_complete(self, season: int, episode: int) -> bool:
        """
        Check if a specific episode is marked as complete.
        Alias for has_episode for compatibility.
        
        Args:
            season: Season number
            episode: Episode number
            
        Returns:
            bool: True if episode is complete
        """
        return self.has_episode(season, episode)

    def has_episode(self, season: int, episode: int) -> bool:
        """
        Check if a specific episode is tracked as complete.
        
        Args:
            season: Season number
            episode: Episode number
            
        Returns:
            True if episode is tracked, False otherwise
        """
        episode_data = self.load_episode_data()
        season_key = f"S{season:02d}"
        
        return season_key in episode_data and episode in episode_data[season_key]
    
    def get_completed_episodes(self, season: int) -> List[int]:
        """
        Get list of completed episodes for a season.
        
        Args:
            season: Season number
            
        Returns:
            List of completed episode numbers for the season
        """
        episode_data = self.load_episode_data()
        season_key = f"S{season:02d}"
        
        return episode_data.get(season_key, [])
    
    def get_all_completed_episodes(self) -> Dict[int, List[int]]:
        """
        Get all completed episodes across all seasons.
        
        Returns:
            Dictionary mapping season numbers to lists of episode numbers
        """
        episode_data = self.load_episode_data()
        result = {}
        
        for season_key, episodes in episode_data.items():
            try:
                season_num = int(season_key[1:])  # Extract number from S01, S02, etc.
                result[season_num] = episodes
            except (ValueError, IndexError):
                continue
                
        return result
    
    def has_any_episodes(self, season: int) -> bool:
        """
        Check if any episodes are tracked for a season.
        
        Args:
            season: Season number
            
        Returns:
            True if any episodes are tracked for the season
        """
        return len(self.get_completed_episodes(season)) > 0
    
    def remove_episode(self, season: int, episode: int, logger: Optional[logging.Logger] = None) -> bool:
        """
        Remove an episode from tracking (useful for reprocessing).
        
        Args:
            season: Season number
            episode: Episode number
            logger: Optional logger for output
            
        Returns:
            True if removed successfully, False otherwise
        """
        try:
            episode_data = self.load_episode_data()
            season_key = f"S{season:02d}"
            
            if season_key in episode_data and episode in episode_data[season_key]:
                episode_data[season_key].remove(episode)
                
                # Remove season key if no episodes left
                if not episode_data[season_key]:
                    del episode_data[season_key]
                
                if self.save_episode_data(episode_data):
                    if logger:
                        logger.info(f"📝 Removed {season_key}E{episode:02d} from {self.marker_name}")
                    return True
                    
        except Exception as e:
            if logger:
                logger.warning(f"Failed to remove episode from {self.marker_name}: {e}")
                
        return False
    
    def create_simple_fallback(self) -> bool:
        """
        Create a simple marker file (fallback for legacy compatibility).
        
        Returns:
            True if created successfully, False otherwise
        """
        try:
            self.marker_path.touch(exist_ok=True)
            return True
        except Exception:
            return False


def get_episode_marker_manager(tv_show_path: Path, marker_type: str) -> EpisodeMarkerManager:
    """
    Get an episode marker manager for a specific marker type.
    
    Args:
        tv_show_path: Path to the TV show season directory
        marker_type: Type of marker (.organized, .mkv_complete, etc.)
        
    Returns:
        EpisodeMarkerManager instance
    """
    return EpisodeMarkerManager(tv_show_path, marker_type)


# Usage example:
# manager = get_episode_marker_manager(Path("/tv_shows/1080p/The Office (2005)/Season 01"), ".organized")
# manager.add_episode(season=1, episode=1, logger=logger)
# manager.add_episode(season=1, episode=5, logger=logger)
# 
# # Later check what's completed:
# completed = manager.get_completed_episodes(season=1)  # Returns [1, 5]
# has_ep_3 = manager.has_episode(season=1, episode=3)  # Returns False
