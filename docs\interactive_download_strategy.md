# Interactive Download Strategy Choice

## Overview

Added interactive choice system for handling TV show downloads, allowing users to select between different strategies for each show before downloads begin.

## New Features

### Interactive Strategy Selection

For each TV show being processed, the user is now prompted with three options:

1. **🔬 Preflight Analysis** (Recommended)
   - Carefully analyzes all available releases before downloading
   - Uses preflight analyzer to determine quality and acceptability
   - Only downloads releases that meet strict criteria
   - Prevents unwanted downloads

2. **⚡ Sonarr Auto-Grab** (Fast)
   - Lets Sonarr immediately search and grab based on quality profiles
   - Uses Sonarr's built-in logic for release selection
   - Faster but less selective
   - Good for shows where you trust <PERSON>ar<PERSON>'s judgment

3. **⏭️ Skip Downloads** (Manual Control)
   - Adds show to Sonarr but doesn't start any downloads
   - Allows manual control later through Sonarr UI
   - Good for shows you want to handle individually

### Race Condition Fix

**Problem Solved:** Previously, shows were being downloaded before preflight analysis could complete its evaluation.

**Solution:** 
- Disabled immediate search triggers during Sonarr series addition
- Only trigger searches when user explicitly selects auto-grab mode
- Preflight analysis now has full control over when downloads start

### Technical Changes

1. **Modified `process_selected_tv_shows()` function:**
   - Added interactive choice prompt after successful Sonarr addition
   - Implemented three distinct handling paths based on user choice

2. **Updated `_configure_specific_season_monitoring()` function:**
   - Added `disable_immediate_search` parameter
   - Prevents race condition by skipping immediate episode searches during initial setup

3. **Enhanced monitoring control:**
   - Preflight mode: Waits for analysis before enabling monitoring
   - Auto-grab mode: Immediately enables monitoring and triggers searches
   - Skip mode: Leaves series unmonitored for manual control

## Usage

When running the script and processing TV shows:

1. Select your TV shows as usual
2. For each show, you'll be prompted to choose a download strategy
3. Enter `1`, `2`, or `3` based on your preference for that specific show
4. The script will handle the rest according to your choice

## Benefits

- **Flexibility:** Choose the right strategy for each show
- **Control:** No more unwanted early downloads
- **Transparency:** Clear indication of what will happen
- **Reliability:** Preflight analysis can complete without interference

## Example Output

```
🤔 Download Strategy Choice for: Gravity Falls
Choose how you want to handle downloads for this show:
1. 🔬 Preflight Analysis - Carefully analyze releases before downloading (recommended)
2. ⚡ Sonarr Auto-Grab - Let Sonarr immediately search and grab based on quality profiles
3. ⏭️  Skip - Add to Sonarr but don't start any downloads yet

Enter your choice (1/2/3): 1
🔬 Using Preflight Analysis for Gravity Falls
```

This ensures that shows like Gravity Falls will only start downloading after the preflight analyzer has completed its thorough evaluation of available releases.
