﻿using System.Text;

namespace seconv.libse.SubtitleFormats
{
    public class F4Rtf : F4Text
    {
        public override string Extension => ".rtf";

        public override string Name => "F4 Rich Text Format";

        public override bool IsMine(List<string> lines, string fileName)
        {
            if (fileName != null && !fileName.EndsWith(Extension, StringComparison.OrdinalIgnoreCase))
            {
                return false;
            }

            return base.IsMine(lines, fileName);
        }

        public override string ToText(Subtitle subtitle, string title)
        {
            return ToF4Text(subtitle).ToRtf();
        }

        public override void LoadSubtitle(Subtitle subtitle, List<string> lines, string fileName)
        {
            _errorCount = 0;
            var sb = new StringBuilder();
            foreach (string line in lines)
            {
                sb.AppendLine(line);
            }

            string rtf = sb.ToString().Trim();
            if (!rtf.StartsWith("{\\rtf", StringComparison.Ordinal))
            {
                return;
            }

            LoadF4TextSubtitle(subtitle, rtf.FromRtf());
        }

    }
}
