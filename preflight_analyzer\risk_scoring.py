from dataclasses import dataclass
from typing import Dict

@dataclass
class RiskComponents:
    age_risk: float
    poster_risk: float
    group_risk: float
    redundancy_buffer: float
    probe_missing_ratio: float
    probe_error_ratio: float
    obfuscation_penalty: float

class RiskScorer:
    def __init__(self, weights: Dict[str, float] | None = None):
        self.weights: Dict[str, float] = weights if weights is not None else {
            'age_risk': 0.20,
            'poster_risk': 0.10,
            'group_risk': 0.10,
            'redundancy_buffer': -0.20,  # negative weight reduces risk
            'probe_missing_ratio': 0.25,
            'probe_error_ratio': 0.25,   # treat pure errors as high risk / unknown -> conservative
            'obfuscation_penalty': 0.10,
        }

    def score(self, components: RiskComponents) -> float:
        s = 0.0
        for k, w in self.weights.items():
            v = getattr(components, k, 0.0)
            s += v * w
        return max(0.0, min(1.0, s))

    def classify(self, score: float) -> str:
        if score < 0.3:
            return 'low'
        if score < 0.6:
            return 'medium'
        return 'high'

    def build_components(self, *, age_days: int, retention_days: int, poster_fail_rate: float, group_fail_rate: float,
                          redundancy_blocks: int, data_segments: int, probe_missing_ratio: float, probe_error_ratio: float,
                          obfuscated: bool) -> RiskComponents:
        age_risk = 0.0
        if age_days > retention_days:
            age_risk = 1.0
        else:
            age_risk = (age_days / retention_days) ** 1.5  # bias toward older
        poster_risk = poster_fail_rate
        group_risk = group_fail_rate
        redundancy_buffer = 0.0
        if data_segments > 0:
            redundancy_buffer = min(1.0, redundancy_blocks / max(1, data_segments))
        obfuscation_penalty = 0.2 if obfuscated else 0.0
        return RiskComponents(
            age_risk=age_risk,
            poster_risk=poster_risk,
            group_risk=group_risk,
            redundancy_buffer=redundancy_buffer,
            probe_missing_ratio=probe_missing_ratio,
            probe_error_ratio=probe_error_ratio,
            obfuscation_penalty=obfuscation_penalty
        )
