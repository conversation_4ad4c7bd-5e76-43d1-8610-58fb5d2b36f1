"""Shared logic between episode and movie preflight selectors.

Centralises:
 - Candidate normalization
 - Ranking & scoring
 - Artifact emission helper
 - Common TypedDict structures

This reduces duplication between `integrated_selector.py` and
`movie_preflight_selector.py`.
"""
from __future__ import annotations

from dataclasses import dataclass
from pathlib import Path
from typing import Any, Callable, Dict, Iterable, List, Optional, Sequence, Tuple, cast
import json
import time


@dataclass
class BasicRelease:
    guid: str
    title: str
    size: int
    indexer: str | None = None
    score: float = 0.0
    quality: str | None = None
    reason: str | None = None
    raw: Dict[str, Any] | None = None

    def to_dict(self) -> Dict[str, Any]:
        return {
            "guid": self.guid,
            "title": self.title,
            "size": self.size,
            "indexer": self.indexer,
            "score": self.score,
            "quality": self.quality,
            "reason": self.reason,
        }


def _default_rank_key(r: BasicRelease) -> <PERSON>ple[float, int]:
    # Higher score first, then smaller size (heuristic) to prefer efficient releases
    return (-r.score, r.size)


def rank_releases(
    candidates: Sequence[BasicRelease],
    key: Optional[Callable[[BasicRelease], Tuple[Any, ...]]] = None,
    limit: int | None = None,
) -> List[BasicRelease]:
    key = key or _default_rank_key
    ranked = sorted(candidates, key=key)
    if limit is not None:
        ranked = ranked[:limit]
    return ranked


def write_decision_artifact(
    artifact_dir: Path,
    base_name: str,
    payload: Dict[str, Any],
) -> Path:
    artifact_dir.mkdir(parents=True, exist_ok=True)
    ts = time.strftime("%Y%m%d-%H%M%S")
    path = artifact_dir / f"{ts}_{base_name}.json"
    with path.open("w", encoding="utf-8") as f:
        json.dump(payload, f, indent=2, sort_keys=True)
    return path


def normalize_raw_candidates(
    raw_items: Iterable[Dict[str, Any]],
    guid_field: str = "guid",
    title_field: str = "title",
    size_field: str = "size",
) -> List[BasicRelease]:
    out: List[BasicRelease] = []
    for item in raw_items:
        try:
            guid = str(item.get(guid_field) or item.get("guid") or item["id"])
            title = str(item.get(title_field) or item.get("name") or guid)
            size = int(item.get(size_field) or item.get("size", 0) or 0)
        except Exception:  # noqa: BLE001 - broad to skip bad rows safely
            continue
        out.append(
            BasicRelease(
                guid=guid,
                title=title,
                size=size,
                indexer=str(item.get("indexer") or item.get("indexerId") or ""),
                raw=item,
            )
        )
    return out


def parse_newznab_json(body: str) -> List[Dict[str, Any]]:
    """Extract list of release dicts from Newznab JSON body (best-effort)."""
    try:
        js_any: Any = json.loads(body)
    except Exception:
        return []
    if not isinstance(js_any, dict):
        return []
    js: Dict[str, Any] = cast(Dict[str, Any], js_any)
    channel_any = js.get('channel')
    if not isinstance(channel_any, dict):
        return []
    channel: Dict[str, Any] = cast(Dict[str, Any], channel_any)
    items_any = channel.get('item')
    if not isinstance(items_any, list):
        return []
    list_items: List[Any] = cast(List[Any], items_any)
    out: List[Dict[str, Any]] = []
    for elem in list_items:
        if isinstance(elem, dict):
            out.append(cast(Dict[str, Any], elem))
    return out
