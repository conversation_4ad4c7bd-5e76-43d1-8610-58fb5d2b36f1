import json
import sys
from pathlib import Path
import sqlite3

REPO_ROOT = Path(__file__).resolve().parents[1]
sys.path.insert(0, str(REPO_ROOT))

from _internal.src.telemetry_dashboard import write_sqlite


def test_sqlite_e2e(tmp_path):
    summary = {
        'by_type': {'file.organized': 5, 'download.completed': 2},
        'by_series': {str((7, 'Series Title')): 3},
        'by_movie': {str((42, 'Movie Title')): 2},
        'by_year': {'2021': 3},
        'by_title': {'Series Title': 3, 'Movie Title': 2},
        'by_resolution': {'1080p': 4, '4k': 1},
        'by_day': {'2025-01-01': 4, '2025-01-02': 3}
    }
    db_path = tmp_path / 'metrics.db'
    write_sqlite(summary, db_path)

    conn = sqlite3.connect(str(db_path))
    cur = conn.cursor()
    cur.execute('SELECT COUNT(*) FROM metrics')
    rowcount = cur.fetchone()[0]
    # We expect rows across all metrics; ensure > 0 and roughly matching dict sizes
    assert rowcount >= sum(len(v) for v in summary.values())
    conn.close()

