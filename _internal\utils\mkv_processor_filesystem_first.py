#!/usr/bin/env python3
"""
Filesystem-First MKV Processor for PlexMovieAutomator.

This script demonstrates how to refactor pipeline scripts to use the new 
filesystem-first state management architecture:

1. Discovers movies ready for MKV processing by scanning directories
2. Uses marker files to track processing state
3. Stores only metadata in the database
4. Implements idempotent operations for safe retries
"""

import asyncio
import logging
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any, Optional

# Import the new filesystem-first managers
from _internal.utils.filesystem_first_state_manager import FilesystemFirstStateManager, MetadataOnlyDatabase


async def run_mkv_processor_stage_filesystem_first(settings: dict, main_logger: logging.Logger, mcp_manager=None) -> bool:
    """
    Filesystem-first MKV processing stage.
    
    This function demonstrates the new architecture:
    - Discovers work by scanning filesystem
    - Uses marker files for state tracking
    - Stores only metadata in database
    - Implements idempotent operations
    
    Args:
        settings: Pipeline settings
        main_logger: Logger instance
        mcp_manager: MCP manager instance (optional)
        
    Returns:
        bool: True if stage completed successfully
    """
    global logger
    logger = main_logger
    
    logger.info("===== Starting Filesystem-First MKV Processing Stage =====")
    
    # Initialize managers
    workspace_root = Path.cwd()
    filesystem_manager = FilesystemFirstStateManager(workspace_root)
    metadata_db = MetadataOnlyDatabase(workspace_root)
    
    try:
        # Step 1: Discover movies ready for MKV processing
        logger.info("🔍 Discovering movies ready for MKV processing...")
        movies_by_stage = filesystem_manager.discover_movies_by_stage()
        
        # Find movies ready for processing
        ready_movies = movies_by_stage.get('mkv_processing_pending', [])
        interrupted_movies = movies_by_stage.get('mkv_processing_interrupted', [])
        
        logger.info(f"Found {len(ready_movies)} movies ready for MKV processing")
        logger.info(f"Found {len(interrupted_movies)} interrupted movies to resume")
        
        # Process new movies first, then interrupted ones
        all_movies_to_process = ready_movies + interrupted_movies
        
        if not all_movies_to_process:
            logger.info("No movies found ready for MKV processing")
            return True
        
        # Step 2: Process each movie
        processed_count = 0
        failed_count = 0
        
        for movie in all_movies_to_process:
            movie_id = filesystem_manager.generate_movie_identifier(movie)
            movie_dir = Path(movie.get('movie_directory', ''))
            
            logger.info(f"🎬 Processing movie: {movie_id}")
            
            # Get metadata if available
            metadata = metadata_db.get_movie_metadata(movie_id)
            
            # Process the movie
            success = await _process_single_movie_filesystem_first(
                movie, movie_dir, filesystem_manager, metadata_db, settings, mcp_manager
            )
            
            if success:
                processed_count += 1
                logger.info(f"✅ Successfully processed: {movie_id}")
            else:
                failed_count += 1
                logger.error(f"❌ Failed to process: {movie_id}")
        
        # Step 3: Report results
        logger.info(f"📊 MKV Processing Results:")
        logger.info(f"   - Processed: {processed_count}")
        logger.info(f"   - Failed: {failed_count}")
        logger.info(f"   - Total: {len(all_movies_to_process)}")
        
        return failed_count == 0
        
    except Exception as e:
        logger.error(f"❌ MKV processing stage failed: {e}")
        return False
    
    finally:
        # Clean up connections
        metadata_db.close()


async def _process_single_movie_filesystem_first(
    movie: Dict[str, Any], 
    movie_dir: Path, 
    filesystem_manager: FilesystemFirstStateManager,
    metadata_db: MetadataOnlyDatabase,
    settings: dict,
    mcp_manager=None
) -> bool:
    """
    Process a single movie using filesystem-first approach.
    
    This demonstrates idempotent operations and marker file management.
    """
    movie_id = filesystem_manager.generate_movie_identifier(movie)
    
    try:
        # Step 1: Set processing marker (indicates work in progress)
        logger.debug(f"Setting processing marker for {movie_id}")
        filesystem_manager.set_stage_marker(movie_dir, 'mkv_processing', {
            'started_at': datetime.now().isoformat(),
            'process_id': f"mkv_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        })
        
        # Step 2: Check if work is already done (idempotent check)
        if _is_mkv_processing_complete(movie_dir):
            logger.info(f"MKV processing already complete for {movie_id}, skipping")
            filesystem_manager.clear_stage_marker(movie_dir, 'mkv_processing')
            filesystem_manager.set_stage_marker(movie_dir, 'mkv_complete', {
                'completed_at': datetime.now().isoformat(),
                'result': 'already_complete'
            })
            return True
        
        # Step 3: Perform the actual MKV processing
        logger.info(f"Starting MKV processing for {movie_id}")
        
        # Get user preferences from metadata database
        metadata = metadata_db.get_movie_metadata(movie_id)
        audio_lang = metadata.get('audio_lang', 'eng') if metadata else 'eng'
        subtitle_lang = metadata.get('subtitle_lang', 'eng') if metadata else 'eng'
        keep_commentary = metadata.get('keep_commentary', False) if metadata else False
        
        # Find the main movie file
        main_file = _find_main_movie_file(movie_dir)
        if not main_file:
            raise Exception("No main movie file found")
        
        # Process the MKV file
        success = await _perform_mkv_processing(
            main_file, movie_dir, audio_lang, subtitle_lang, keep_commentary, settings
        )
        
        if not success:
            raise Exception("MKV processing failed")
        
        # Step 4: Mark as complete
        filesystem_manager.clear_stage_marker(movie_dir, 'mkv_processing')
        filesystem_manager.set_stage_marker(movie_dir, 'mkv_complete', {
            'completed_at': datetime.now().isoformat(),
            'processed_file': str(main_file),
            'settings_used': {
                'audio_lang': audio_lang,
                'subtitle_lang': subtitle_lang,
                'keep_commentary': keep_commentary
            }
        })
        
        # Step 5: Update metadata with processing results
        if metadata:
            processing_metadata = metadata.get('metadata', {})
            processing_metadata['mkv_processing'] = {
                'completed_at': datetime.now().isoformat(),
                'processed_file': str(main_file)
            }
            metadata_db.save_movie_metadata(
                unique_id=movie_id,
                title=metadata['title'],
                year=metadata.get('year'),
                tmdb_id=metadata.get('tmdb_id'),
                imdb_id=metadata.get('imdb_id'),
                audio_lang=audio_lang,
                subtitle_lang=subtitle_lang,
                keep_commentary=keep_commentary,
                metadata=processing_metadata
            )
        
        logger.info(f"✅ MKV processing completed for {movie_id}")
        return True
        
    except Exception as e:
        logger.error(f"❌ MKV processing failed for {movie_id}: {e}")
        
        # Set error marker
        filesystem_manager.clear_stage_marker(movie_dir, 'mkv_processing')
        filesystem_manager.set_stage_marker(movie_dir, 'error', {
            'error_type': 'mkv_processing_error',
            'error_message': str(e),
            'failed_at': datetime.now().isoformat()
        })
        
        return False


def _is_mkv_processing_complete(movie_dir: Path) -> bool:
    """Check if MKV processing is already complete (idempotent check)."""
    # Check for existing processed content
    processed_dir = movie_dir / "_Processed_VideoAudio"
    if processed_dir.exists() and any(processed_dir.glob("*.mkv")):
        return True
    
    # Could add other completion checks here
    return False


def _find_main_movie_file(movie_dir: Path) -> Optional[Path]:
    """Find the main movie file in the directory using recursive search."""
    # Use recursive search to handle nested folders (e.g., "Movie Name/Movie Name/file.mkv")
    from _internal.src.fs_helpers import find_video_files
    video_files = find_video_files(movie_dir)
    
    if not video_files:
        return None
    
    # If multiple files, find the largest file (usually the main feature)
    if len(video_files) == 1:
        return video_files[0]
    
    try:
        largest_file = max(video_files, key=lambda f: f.stat().st_size if f.exists() else 0)
        return largest_file
    except Exception:
        return video_files[0] if video_files else None


async def _perform_mkv_processing(
    main_file: Path,
    movie_dir: Path, 
    audio_lang: str,
    subtitle_lang: str,
    keep_commentary: bool,
    settings: dict
) -> bool:
    """
    Perform the actual MKV processing.
    
    This is a placeholder for the actual processing logic.
    In a real implementation, this would:
    1. Extract video/audio tracks
    2. Process subtitles
    3. Create optimized MKV
    """
    logger.info(f"Processing MKV file: {main_file}")
    logger.info(f"Settings: audio={audio_lang}, subtitle={subtitle_lang}, commentary={keep_commentary}")
    
    # Create output directory
    output_dir = movie_dir / "_Processed_VideoAudio"
    output_dir.mkdir(exist_ok=True)
    
    # Simulate processing time
    await asyncio.sleep(2)
    
    # Create a placeholder processed file
    processed_file = output_dir / f"{main_file.stem}_processed.mkv"
    
    # In real implementation, this would be actual ffmpeg/mkvtoolnix commands
    try:
        # Placeholder: copy file to show "processing"
        import shutil
        shutil.copy2(main_file, processed_file)
        
        logger.info(f"Created processed file: {processed_file}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to process MKV: {e}")
        return False


# Example of how to integrate with existing orchestrator
def create_filesystem_first_mkv_processor():
    """Factory function to create the filesystem-first MKV processor."""
    return run_mkv_processor_stage_filesystem_first


if __name__ == "__main__":
    # Example usage
    import asyncio
    
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    settings = {
        # Default settings for testing
        'default_audio_language': 'eng',
        'default_subtitle_language': 'eng',
        'keep_commentary': False
    }
    
    # Run the stage
    success = asyncio.run(run_mkv_processor_stage_filesystem_first(settings, logger))
    
    if success:
        print("✅ MKV processing stage completed successfully!")
    else:
        print("❌ MKV processing stage failed!")
