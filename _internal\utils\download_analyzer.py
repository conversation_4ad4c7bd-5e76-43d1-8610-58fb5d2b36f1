"""
Download Analyzer - Extract ALL information BEFORE any renaming
================

This module analyzes raw download folders to extract and preserve ALL metadata
BEFORE any file/folder renaming occurs that would destroy the information.

Key principle: ANALYZE FIRST, PRESERVE INFO, THEN RENAME
"""

import re
from pathlib import Path
from typing import Dict, Any, Optional
import logging
from dataclasses import dataclass


@dataclass
class DownloadAnalysis:
    """Complete analysis results for a download folder"""
    # Original info
    original_folder_name: str
    
    # Content classification
    content_type: str  # 'tv_show' or 'movie'
    confidence: float
    
    # Title and year
    title: str
    year: Optional[int]
    
    # TV-specific info
    season: Optional[int] = None
    episode: Optional[int] = None
    episode_range: Optional[str] = None
    
    # Resolution info
    resolution: Optional[str] = None  # '4k', '1080p', '720p'
    width: Optional[int] = None
    height: Optional[int] = None
    resolution_source: str = 'unknown'  # 'folder_name', 'ffprobe', 'default'
    
    # Quality indicators
    quality_tags: list = None
    
    def __post_init__(self):
        if self.quality_tags is None:
            self.quality_tags = []


class DownloadAnalyzer:
    """Analyzes download folders to extract metadata before any renaming"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # TV show patterns (strong indicators)
        self.tv_patterns = [
            r'S\d{1,2}(?!E)',           # S01, S02 (season without episode)
            r'S\d{1,2}E\d{1,2}',       # S01E01, S02E05
            r'Season[\s\.]\d{1,2}',    # Season 1, Season.01
            r'\b\d{1,2}x\d{1,2}\b',    # 1x01, 2x05
            r'Complete[\s\.]Series',    # Complete Series, Complete.Series
            r'Complete[\s\.]Season',    # Complete Season
        ]
        
        # Movie patterns (weaker, used for disambiguation)
        self.movie_patterns = [
            r'\b(19|20)\d{2}\b',       # Year like 1999, 2023
            r'\b(BluRay|BDRip|DVDRip|WEBRip|WEB-DL)\b',
            r'\b(REMASTERED|EXTENDED|DIRECTORS\.CUT)\b'
        ]
        
        # Resolution patterns
        self.resolution_patterns = {
            '4k': [r'2160p', r'4k', r'uhd', r'3840x2160'],
            '1080p': [r'1080p', r'fhd', r'1920x1080'],
            '720p': [r'720p', r'hd', r'1280x720']
        }
    
    def analyze_download(self, download_path: Path, main_video_file: Optional[Path] = None) -> DownloadAnalysis:
        """
        Completely analyze a download folder BEFORE any renaming.
        
        Args:
            download_path: Path to the original download folder
            main_video_file: Optional path to main video file for ffprobe
            
        Returns:
            Complete analysis with all extracted metadata
        """
        original_name = download_path.name
        self.logger.info(f"🔍 Analyzing download: {original_name}")
        
        # Step 1: Determine content type
        content_type, confidence = self._detect_content_type(original_name)
        
        # Step 2: Extract title and year
        title, year = self._extract_title_and_year(original_name, content_type)
        
        # Step 3: Extract TV-specific info (if applicable)
        season, episode, episode_range = self._extract_tv_info(original_name) if content_type == 'tv_show' else (None, None, None)
        
        # Step 4: Extract resolution info
        resolution, width, height, resolution_source = self._extract_resolution_info(original_name, main_video_file)
        
        # Step 5: Extract quality tags
        quality_tags = self._extract_quality_tags(original_name)
        
        analysis = DownloadAnalysis(
            original_folder_name=original_name,
            content_type=content_type,
            confidence=confidence,
            title=title,
            year=year,
            season=season,
            episode=episode,
            episode_range=episode_range,
            resolution=resolution,
            width=width,
            height=height,
            resolution_source=resolution_source,
            quality_tags=quality_tags
        )
        
        self.logger.info(f"📊 Analysis complete: {content_type} - {title} ({year}) - {resolution}")
        return analysis
    
    def _detect_content_type(self, folder_name: str) -> tuple[str, float]:
        """Detect if content is TV show or movie based on folder name patterns"""
        name_lower = folder_name.lower()
        
        tv_score = 0
        movie_score = 0
        
        # Check for TV patterns
        for pattern in self.tv_patterns:
            if re.search(pattern, folder_name, re.IGNORECASE):
                tv_score += 1
                self.logger.debug(f"TV pattern matched: {pattern}")
        
        # Check for movie patterns  
        for pattern in self.movie_patterns:
            if re.search(pattern, folder_name, re.IGNORECASE):
                movie_score += 0.5  # Weaker evidence
        
        # Additional logic
        if 'season' in name_lower or 'episode' in name_lower:
            tv_score += 1
        
        if tv_score > movie_score:
            confidence = min(0.9, 0.5 + (tv_score * 0.2))
            return 'tv_show', confidence
        else:
            confidence = min(0.9, 0.5 + (movie_score * 0.1))
            return 'movie', confidence
    
    def _extract_title_and_year(self, folder_name: str, content_type: str) -> tuple[str, Optional[int]]:
        """Extract clean title and year from folder name"""
        # Remove common release group patterns
        clean_name = re.sub(r'-[A-Z0-9]+$', '', folder_name)  # Remove -GROUP at end
        
        # Extract year
        year_match = re.search(r'\b(19|20)(\d{2})\b', clean_name)
        year = int(year_match.group()) if year_match else None
        
        # Extract title (everything before quality/format indicators)
        title_match = re.match(r'^([^.]+(?:\.[^.]+)*?)', clean_name)
        title = title_match.group(1) if title_match else folder_name
        
        # Clean up title
        if year:
            title = re.sub(rf'\b{year}\b', '', title)
        
        # Remove TV-specific patterns from title
        if content_type == 'tv_show':
            title = re.sub(r'\.S\d{1,2}(?:E\d{1,2})?.*$', '', title, flags=re.IGNORECASE)
            title = re.sub(r'\.Season\.\d{1,2}.*$', '', title, flags=re.IGNORECASE)
        
        # Convert dots to spaces and clean
        title = title.replace('.', ' ').strip()
        title = re.sub(r'\s+', ' ', title)
        
        return title, year
    
    def _extract_tv_info(self, folder_name: str) -> tuple[Optional[int], Optional[int], Optional[str]]:
        """Extract season/episode information from TV show folder name"""
        # Season patterns
        season_match = re.search(r'S(\d{1,2})', folder_name, re.IGNORECASE)
        season = int(season_match.group(1)) if season_match else None
        
        # Episode patterns
        episode_match = re.search(r'E(\d{1,2})', folder_name, re.IGNORECASE)
        episode = int(episode_match.group(1)) if episode_match else None
        
        # Episode range (e.g., E01-E05)
        range_match = re.search(r'E(\d{1,2})-E(\d{1,2})', folder_name, re.IGNORECASE)
        episode_range = f"E{range_match.group(1)}-E{range_match.group(2)}" if range_match else None
        
        return season, episode, episode_range
    
    def _extract_resolution_info(self, folder_name: str, main_video_file: Optional[Path] = None) -> tuple[Optional[str], Optional[int], Optional[int], str]:
        """Extract resolution info from folder name first, then ffprobe as fallback"""
        
        # PRIORITY 1: Folder name analysis
        name_lower = folder_name.lower()
        
        for resolution, patterns in self.resolution_patterns.items():
            for pattern in patterns:
                if re.search(pattern, name_lower):
                    width_map = {'4k': 3840, '1080p': 1920, '720p': 1280}
                    height_map = {'4k': 2160, '1080p': 1080, '720p': 720}
                    return resolution, width_map[resolution], height_map[resolution], 'folder_name'
        
        # PRIORITY 2: ffprobe analysis (if video file provided)
        if main_video_file and main_video_file.exists():
            try:
                width, height = self._get_video_resolution_ffprobe(main_video_file)
                if width and height:
                    if width >= 3840:
                        return '4k', width, height, 'ffprobe'
                    elif width >= 1920:
                        return '1080p', width, height, 'ffprobe'
                    elif width >= 1280:
                        return '720p', width, height, 'ffprobe'
            except Exception as e:
                self.logger.warning(f"ffprobe analysis failed: {e}")
        
        # Default fallback
        return '1080p', 1920, 1080, 'default'
    
    def _get_video_resolution_ffprobe(self, video_file: Path) -> tuple[Optional[int], Optional[int]]:
        """Use ffprobe to get actual video resolution"""
        import subprocess
        
        try:
            command = [
                'ffprobe', '-v', 'error', '-select_streams', 'v:0',
                '-show_entries', 'stream=width,height', '-of', 'csv=s=x:p=0', str(video_file)
            ]
            
            result = subprocess.run(command, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0 and result.stdout:
                # Handle potential extra separators
                output_parts = [part for part in result.stdout.strip().split('x') if part.strip()]
                if len(output_parts) >= 2:
                    width = int(output_parts[0])
                    height = int(output_parts[1])
                    return width, height
        except Exception as e:
            self.logger.error(f"ffprobe error: {e}")
        
        return None, None
    
    def _extract_quality_tags(self, folder_name: str) -> list[str]:
        """Extract quality and format tags from folder name"""
        tags = []
        
        quality_patterns = {
            'BluRay': r'\bBluRay\b|\bBDRip\b',
            'WEB-DL': r'\bWEB-DL\b',
            'WEBRip': r'\bWEBRip\b',
            'REMUX': r'\bREMUX\b',
            'HDR': r'\bHDR\b|\bDoVi\b',
            'HEVC': r'\bHEVC\b|\bx265\b',
            'x264': r'\bx264\b'
        }
        
        for tag, pattern in quality_patterns.items():
            if re.search(pattern, folder_name, re.IGNORECASE):
                tags.append(tag)
        
        return tags
