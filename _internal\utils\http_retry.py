#!/usr/bin/env python3
"""
Async HTTP retry helpers for aiohttp with exponential backoff and jitter.

Provides small helpers to standardize resilient network calls across the
pipeline (Radarr/Sonarr/SABnzbd integrations, etc.).
"""
from __future__ import annotations

import asyncio
import random
from typing import Any, Dict, Mapping, Optional, Tuple, Union

import aiohttp

JsonType = Union[Dict[str, Any], list, str, int, float, bool, None]


def _should_retry(status: int | None) -> bool:
    if status is None:
        return True
    # Retry on typical transient statuses
    if status in (408, 409, 425, 429):
        return True
    if 500 <= status < 600:
        return True
    return False


async def async_request_json(
    session: aiohttp.ClientSession,
    method: str,
    url: str,
    *,
    headers: Optional[Mapping[str, str]] = None,
    params: Optional[Mapping[str, Any]] = None,
    json: Optional[JsonType] = None,
    data: Any = None,
    timeout: Optional[aiohttp.ClientTimeout] = None,
    max_attempts: int = 3,
    base_backoff: float = 0.5,
    backoff_factor: float = 2.0,
    max_backoff: float = 8.0,
) -> Tuple[int, Optional[JsonType]]:
    """
    Perform an HTTP request expecting JSON response with retries.

    Returns a tuple of (status_code, parsed_json_or_none).
    Never raises for HTTP error statuses; instead, returns (status, None) when
    non-JSON or error after exhausting retries. Network/timeout errors trigger retries.
    """
    attempt = 0
    last_status: Optional[int] = None

    while attempt < max_attempts:
        attempt += 1
        try:
            async with session.request(
                method.upper(), url, headers=headers, params=params, json=json, data=data, timeout=timeout
            ) as resp:
                status = resp.status
                last_status = status
                # Try decode JSON regardless of status; some APIs include error JSON
                try:
                    payload = await resp.json(content_type=None)
                except Exception:
                    payload = None

                if status < 400:
                    return status, payload

                # For non-success, decide whether to retry
                if not _should_retry(status) or attempt >= max_attempts:
                    return status, payload
        except (aiohttp.ClientError, asyncio.TimeoutError):
            # Fall through to retry
            pass

        # Backoff with jitter
        sleep_for = min(max_backoff, base_backoff * (backoff_factor ** (attempt - 1)))
        sleep_for += random.uniform(0.0, 0.25 * sleep_for)
        await asyncio.sleep(sleep_for)

    # Exhausted attempts
    return last_status or 0, None


async def async_delete(
    session: aiohttp.ClientSession,
    url: str,
    *,
    headers: Optional[Mapping[str, str]] = None,
    timeout: Optional[aiohttp.ClientTimeout] = None,
    max_attempts: int = 3,
    base_backoff: float = 0.5,
    backoff_factor: float = 2.0,
    max_backoff: float = 8.0,
) -> int:
    """
    Perform a DELETE with retries. Returns final HTTP status (0 if network error).
    """
    status, _ = await async_request_json(
        session,
        "DELETE",
        url,
        headers=headers,
        timeout=timeout,
        max_attempts=max_attempts,
        base_backoff=base_backoff,
        backoff_factor=backoff_factor,
        max_backoff=max_backoff,
    )
    return status

