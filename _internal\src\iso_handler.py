#!/usr/bin/env python3
"""
ISO Handler for PlexAutomator

<PERSON>les extraction of .iso disk image files to extract the main movie file.
Specifically designed for Blu-ray disk images containing BDMV/STREAM directories.
Uses 7-Zip for extraction only.
"""

import os
import subprocess
import logging
from pathlib import Path
from typing import Optional


class ISOHandler:
    """<PERSON><PERSON> extracting files from .iso disk images using 7-Zip."""

    def __init__(self, logger: logging.Logger):
        self.logger = logger
        self.seven_zip_path: Optional[str] = None

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """No cleanup needed for 7-Zip extraction method."""
        pass

    def _find_7zip(self) -> Optional[str]:
        """Find 7-Zip executable on the system."""
        if self.seven_zip_path:
            return self.seven_zip_path
            
        possible_paths = [
            r"C:\Program Files\7-Zip\7z.exe",
            r"C:\Program Files (x86)\7-Zip\7z.exe",
            "7z",  # If in PATH
            "7za",  # Standalone version
        ]
        
        for path in possible_paths:
            try:
                result = subprocess.run([path, "--help"], 
                                      capture_output=True, 
                                      text=True, 
                                      timeout=5)
                if result.returncode == 0:
                    self.logger.info(f"Found 7-Zip at: {path}")
                    self.seven_zip_path = path
                    return path
            except (subprocess.SubprocessError, FileNotFoundError):
                continue
        
        self.logger.warning("7-Zip not found. Please install 7-Zip for ISO extraction.")
        return None

    def _list_iso_contents_7zip(self, iso_path: Path) -> list[str]:
        """List the contents of an ISO file using 7-Zip."""
        seven_zip_path = self._find_7zip()
        if not seven_zip_path:
            return []
            
        try:
            result = subprocess.run(
                [seven_zip_path, "l", str(iso_path)],
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.returncode != 0:
                self.logger.error(f"Failed to list ISO contents: {result.stderr}")
                return []
            
            # Parse the output to get file paths
            lines = result.stdout.split('\n')
            files = []
            
            # Look for lines that contain file information
            # 7-Zip output format: Date Time Attr Size Compressed Name
            parsing_files = False
            for line in lines:
                line = line.strip()
                if line.startswith("---"):
                    parsing_files = True
                    continue
                elif line.startswith("---") and parsing_files:
                    break
                elif parsing_files and line:
                    # Extract the filename (last part after size info)
                    parts = line.split()
                    if len(parts) >= 6:
                        filename = " ".join(parts[5:])
                        files.append(filename)
            
            return files
        
        except subprocess.SubprocessError as e:
            self.logger.error(f"Error listing ISO contents: {e}")
            return []

    def _find_main_m2ts_in_iso_7zip(self, iso_path: Path) -> Optional[str]:
        """Find the largest .m2ts file in the BDMV/STREAM directory using 7-Zip."""
        seven_zip_path = self._find_7zip()
        if not seven_zip_path:
            return None
            
        try:
            # List all files in the ISO
            files = self._list_iso_contents_7zip(iso_path)
            
            # Filter for .m2ts files in BDMV/STREAM directory
            m2ts_files = []
            for file_path in files:
                if (file_path.upper().endswith('.M2TS') and 
                    'BDMV' in file_path.upper() and 
                    'STREAM' in file_path.upper()):
                    m2ts_files.append(file_path)
            
            if not m2ts_files:
                self.logger.warning(f"No .m2ts files found in BDMV/STREAM directory of {iso_path}")
                return None
            
            # Get file sizes to find the largest one
            largest_file = None
            largest_size = 0
            
            for m2ts_file in m2ts_files:
                try:
                    # Get detailed info for this specific file
                    result = subprocess.run(
                        [seven_zip_path, "l", str(iso_path), m2ts_file],
                        capture_output=True,
                        text=True,
                        timeout=30
                    )
                    
                    if result.returncode == 0:
                        # Parse size from output
                        lines = result.stdout.split('\n')
                        for line in lines:
                            if m2ts_file in line:
                                parts = line.split()
                                if len(parts) >= 4:
                                    try:
                                        size = int(parts[3])
                                        if size > largest_size:
                                            largest_size = size
                                            largest_file = m2ts_file
                                    except (ValueError, IndexError):
                                        continue
                                break
                
                except subprocess.SubprocessError:
                    continue
            
            if largest_file:
                size_gb = largest_size / (1024**3)
                self.logger.info(f"Found main video file: {largest_file} ({size_gb:.2f} GB)")
                return largest_file
            else:
                self.logger.warning("Could not determine file sizes, using first .m2ts file found")
                return m2ts_files[0] if m2ts_files else None
        
        except Exception as e:
            self.logger.error(f"Error finding main .m2ts file: {e}")
            return None

    def _extract_using_windows_mounting(self, iso_path: Path, output_dir: Path) -> Optional[Path]:
        """Extract main movie file from ISO using Windows native mounting."""
        try:
            import shutil
            
            self.logger.info("Attempting Windows native ISO mounting...")
            
            # Mount the ISO using PowerShell with better drive letter extraction
            # Convert to absolute path for PowerShell
            abs_iso_path = iso_path.resolve()
            mount_result = subprocess.run(
                ["powershell", "-Command", f"$mount = Mount-DiskImage -ImagePath '{abs_iso_path}' -PassThru; $mount | Get-Volume | Select-Object -ExpandProperty DriveLetter"],
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if mount_result.returncode != 0:
                self.logger.error(f"Failed to mount ISO: {mount_result.stderr}")
                return None
            
            # Extract drive letter from mount output
            mount_output = mount_result.stdout.strip()
            drive_letter = None
            
            # The command should return just the drive letter
            if mount_output and len(mount_output.strip()) >= 1:
                # Take the first character if it's a letter
                potential_drive = mount_output.strip()[0].upper()
                if potential_drive.isalpha():
                    drive_letter = potential_drive
                    self.logger.info(f"Detected drive letter: {drive_letter}")
            
            # Fallback: try a different PowerShell approach
            if not drive_letter:
                self.logger.info("Trying alternative drive letter detection...")
                alt_result = subprocess.run(
                    ["powershell", "-Command", f"Get-DiskImage -ImagePath '{abs_iso_path}' | Get-Disk | Get-Partition | Get-Volume | Select-Object -ExpandProperty DriveLetter"],
                    capture_output=True,
                    text=True,
                    timeout=15
                )
                
                if alt_result.returncode == 0 and alt_result.stdout.strip():
                    potential_drive = alt_result.stdout.strip()[0].upper()
                    if potential_drive.isalpha():
                        drive_letter = potential_drive
                        self.logger.info(f"Alternative detection found drive letter: {drive_letter}")
            
            if not drive_letter:
                self.logger.error("Could not determine mounted drive letter")
                return None
            
            mounted_path = Path(f"{drive_letter}:")
            self.logger.info(f"ISO mounted at drive {drive_letter}:")
            
            # Find BDMV structure
            bdmv_path = mounted_path / "BDMV"
            if not bdmv_path.exists():
                self.logger.error("BDMV directory not found in mounted ISO")
                return None
            
            stream_path = bdmv_path / "STREAM"
            if not stream_path.exists():
                self.logger.error("STREAM directory not found in BDMV")
                return None
            
            # Find largest .m2ts file
            m2ts_files = list(stream_path.glob("*.m2ts"))
            if not m2ts_files:
                self.logger.error("No .m2ts files found in STREAM directory")
                return None
            
            # Find the largest file (main movie)
            largest_file = None
            largest_size = 0
            
            for m2ts_file in m2ts_files:
                try:
                    size = m2ts_file.stat().st_size
                    if size > largest_size:
                        largest_size = size
                        largest_file = m2ts_file
                except Exception as e:
                    self.logger.warning(f"Could not get size for {m2ts_file}: {e}")
                    continue
            
            if not largest_file:
                self.logger.error("Could not find largest .m2ts file")
                return None
            
            # Generate output filename
            iso_stem = iso_path.stem
            output_filename = f"{iso_stem}.m2ts"
            output_path = output_dir / output_filename
            
            # Copy the file
            self.logger.info(f"Copying main video file: {largest_file.name} ({largest_size / (1024**3):.2f} GB)")
            shutil.copy2(largest_file, output_path)
            
            # Verify copy succeeded
            if output_path.exists():
                copied_size = output_path.stat().st_size
                if copied_size == largest_size:
                    self.logger.info(f"Successfully copied using Windows mounting: {output_path}")
                    return output_path
                else:
                    self.logger.error(f"File copy incomplete: {copied_size} != {largest_size}")
                    return None
            else:
                self.logger.error("Copied file not found")
                return None
                
        except Exception as e:
            self.logger.error(f"Error in Windows mounting extraction: {e}")
            return None
        
        finally:
            # Always try to unmount the ISO
            try:
                abs_iso_path = iso_path.resolve()
                unmount_result = subprocess.run(
                    ["powershell", "-Command", f"Dismount-DiskImage -ImagePath '{abs_iso_path}'"],
                    capture_output=True,
                    text=True,
                    timeout=30
                )
                if unmount_result.returncode == 0:
                    self.logger.info("Successfully unmounted ISO")
                else:
                    self.logger.warning(f"Failed to unmount ISO: {unmount_result.stderr}")
            except Exception as e:
                self.logger.warning(f"Error during ISO unmount: {e}")

    def _extract_file_from_iso_7zip(self, iso_path: Path, file_path: str, output_dir: Path) -> Optional[Path]:
        """Extract a specific file from ISO using 7-Zip."""
        seven_zip_path = self._find_7zip()
        if not seven_zip_path:
            return None
            
        try:
            # Create output directory if it doesn't exist
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # Generate output filename based on ISO name
            iso_stem = iso_path.stem
            output_filename = f"{iso_stem}.m2ts"
            output_path = output_dir / output_filename
            
            # Always extract/overwrite - don't check for existing files
            self.logger.info(f"Extracting {file_path} from {iso_path}")
            
            result = subprocess.run(
                [seven_zip_path, "e", str(iso_path), file_path, f"-o{output_dir}", "-y"],
                capture_output=True,
                text=True,
                timeout=300  # 5 minutes timeout for extraction
            )
            
            if result.returncode != 0:
                self.logger.error(f"Extraction failed: {result.stderr}")
                return None
            
            # The extracted file will have its original name
            extracted_original = output_dir / Path(file_path).name
            
            # Rename to match ISO name if different
            if extracted_original.exists() and extracted_original != output_path:
                # Remove existing file if it exists before renaming
                if output_path.exists():
                    output_path.unlink()
                extracted_original.rename(output_path)
            
            if output_path.exists():
                size_gb = output_path.stat().st_size / (1024**3)
                self.logger.info(f"Successfully extracted main video to: {output_path} ({size_gb:.2f} GB)")
                return output_path
            else:
                self.logger.error("Extraction completed but output file not found")
                return None
        
        except subprocess.SubprocessError as e:
            self.logger.error(f"Error during extraction: {e}")
            return None
        except Exception as e:
            self.logger.error(f"Unexpected error during extraction: {e}")
            return None

    def extract_main_movie_from_iso(self, iso_path: Path, output_dir: Path) -> Optional[Path]:
        """
        Extract the main movie file from an ISO image using 7-Zip first, then Windows mounting as backup.
        
        Args:
            iso_path: Path to the .iso file
            output_dir: Directory to extract the movie file to
            
        Returns:
            Path to the extracted movie file, or None if extraction failed
        """
        if not iso_path.exists():
            self.logger.error(f"ISO file not found: {iso_path}")
            return None

        if not output_dir.exists():
            output_dir.mkdir(parents=True, exist_ok=True)

        # Try 7-Zip method first
        self.logger.info("Attempting ISO extraction using 7-Zip method...")
        
        # Find the main .m2ts file using 7-Zip
        main_video_file = self._find_main_m2ts_in_iso_7zip(iso_path)
        if main_video_file:
            # Extract the file
            extracted_path = self._extract_file_from_iso_7zip(iso_path, main_video_file, output_dir)
            if extracted_path:
                self.logger.info(f"Successfully extracted using 7-Zip: {extracted_path}")
                return extracted_path
            else:
                self.logger.warning("7-Zip extraction failed, attempting Windows mounting backup...")
        else:
            self.logger.warning("7-Zip could not find main video file, attempting Windows mounting backup...")

        # Fallback to Windows native mounting
        return self._extract_using_windows_mounting(iso_path, output_dir)


def process_iso_file(iso_path: Path, output_dir: Path, logger: logging.Logger) -> Optional[Path]:
    """
    Convenience function to process an ISO file and extract the main movie.
    
    Args:
        iso_path: Path to the .iso file
        output_dir: Directory to extract the movie file to
        logger: Logger instance
        
    Returns:
        Path to the extracted movie file, or None if extraction failed
    """
    with ISOHandler(logger) as iso_handler:
        return iso_handler.extract_main_movie_from_iso(iso_path, output_dir)


if __name__ == "__main__":
    # Test the ISO handler
    import sys
    
    if len(sys.argv) != 3:
        print("Usage: python iso_handler.py <iso_file> <output_directory>")
        sys.exit(1)
    
    iso_file = Path(sys.argv[1])
    output_directory = Path(sys.argv[2])
    
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    result = process_iso_file(iso_file, output_directory, logger)
    
    if result:
        print(f"Success: {result}")
    else:
        print("Failed to process ISO file")
        sys.exit(1)
