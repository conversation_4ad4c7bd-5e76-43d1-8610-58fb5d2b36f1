import os
import sys
from pathlib import Path
import asyncio
import types
from unittest.mock import patch, AsyncMock

# Ensure imports work
REPO_ROOT = Path(__file__).resolve().parents[1]
sys.path.insert(0, str(REPO_ROOT))

from _internal.src.file_organizer import organize_tv_show
from _internal.src.event_queue import get_event_queue
from _internal.utils.tv_show_naming import TVShowNamingHelper


async def _create_fake_video_files(base: Path, count: int = 3):
    base.mkdir(parents=True, exist_ok=True)
    # Create filenames typical for a season pack with some unlabeled
    names = [
        'Show.Name.S01E01.mkv',
        'Show.Name.S01E02.mkv',
        'Show.Name.S01E03.mkv',
    ]
    for n in names[:count]:
        f = (base / n)
        f.write_text('fake', encoding='utf-8')
        # Make files appear larger than any potential sample filter
        try:
            f.write_bytes(b'0' * 1024 * 1024)
        except Exception:
            pass


def test_season_pack_triggers_sonarr_rescan(tmp_path, monkeypatch, capsys):
    # Arrange
    download_dir = tmp_path / 'Show.Name.S01.1080p.WEB-DL'
    asyncio.run(_create_fake_video_files(download_dir, count=3))

    # Settings with Sonarr config and EventQueue to temp dir
    out_dir = tmp_path / 'events'
    settings = {
        'Sonarr': {'url': 'http://localhost:8989', 'api_key': 'TESTKEY'},
        'EventQueue': {'enabled': True, 'dir': str(out_dir)}
    }

    content_info = {
        'title': 'Show Name',
        'year': 2020,
        'seriesId': 1234,
    }

    class DummyLogger:
        def info(self, *a, **k):
            pass
        def warning(self, *a, **k):
            pass
        def error(self, *a, **k):
            pass

    # Patch EventQueue to capture publishes and SonarrClient.issue_command for rescan
    captured = []
    class DummyQueue:
        async def publish(self, event_type, data):
            captured.append((event_type, data))
    with patch('_internal.src.event_queue.get_event_queue', return_value=DummyQueue()):
        with patch('_internal.src.sonarr_integration.SonarrClient.issue_command', new_callable=AsyncMock) as mock_issue:
            # Act
            ok = asyncio.run(organize_tv_show(content_info, str(download_dir / 'Show.Name.S01E01.mkv'), download_dir, tmp_path, '1080p', TVShowNamingHelper(), DummyLogger(), settings))
            # Assert
            assert ok is True
            assert mock_issue.await_count >= 1
            # Check at least one call is RescanSeries with seriesId 1234
            found_rescan = any((call.kwargs.get('command_payload', {}) or (call.args[1] if len(call.args) > 1 else {})).get('name') == 'RescanSeries' for call in mock_issue.mock_calls)
            assert found_rescan

