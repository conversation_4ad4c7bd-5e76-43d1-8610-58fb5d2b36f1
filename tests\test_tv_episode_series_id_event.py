import sys
from pathlib import Path
import asyncio
from unittest.mock import patch

REPO_ROOT = Path(__file__).resolve().parents[1]
sys.path.insert(0, str(REPO_ROOT))

from _internal.src.file_organizer import organize_tv_show
from _internal.utils.tv_show_naming import TVShowNamingHelper


class DummyLogger:
    def info(self, *a, **k): pass
    def warning(self, *a, **k): pass
    def error(self, *a, **k): pass


def test_episode_event_includes_series_id(tmp_path):
    download_dir = tmp_path / 'My.Show.S01.1080p.WEB-DL'
    download_dir.mkdir(parents=True, exist_ok=True)
    main = download_dir / 'My.Show.S01E01.mkv'
    main.write_bytes(b'0' * 1024 * 1024)

    settings = {'EventQueue': {'enabled': True, 'dir': str(tmp_path / 'events')}, 'Sonarr': {'url': 'http://localhost:8989', 'api_key': 'X'}}
    content_info = {'title': 'My Show', 'year': 2022, 'seriesId': 555}

    captured = []
    class DummyQueue:
        async def publish(self, event_type, data):
            captured.append((event_type, data))

    with patch('_internal.src.file_organizer.get_event_queue', return_value=DummyQueue()):
        ok = asyncio.run(organize_tv_show(content_info, str(main), download_dir, tmp_path, '1080p', TVShowNamingHelper(), DummyLogger(), settings))
        assert ok is True

    # Assert the file.organized event for episodes includes series_id
    episode_events = [data for evt, data in captured if evt == 'file.organized' and data.get('type') == 'episode']
    assert any(evt.get('series_id') == 555 for evt in episode_events)

