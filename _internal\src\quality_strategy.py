#!/usr/bin/env python3
from __future__ import annotations

from typing import Dict, Any
import logging


def determine_movie_quality_profile_by_year(year: int, logger: logging.Logger, settings: Dict[str, Any] = None) -> Dict[str, Any]:
    """Movie quality profile strategy (Radarr)."""
    settings = settings or {}
    radarr_preferences = settings.get("Radarr", {})
    hd_profile_id = radarr_preferences.get("hd_quality_profile_id", 6)
    uhd_profile_id = radarr_preferences.get("uhd_quality_profile_id", 7)

    if year <= 2009:
        strategy = {
            "profiles": [hd_profile_id],
            "strategy": "1080p_only",
            "description": f"≤2009 movie: Using 1080p only (Profile {hd_profile_id}) - largest file preferred"
        }
        logger.info(f"🎯 Quality Strategy for {year}: {strategy['description']}")
        return strategy
    elif 2010 <= year <= 2015:
        strategy = {
            "profiles": [hd_profile_id, uhd_profile_id],
            "strategy": "both_1080p_and_4k",
            "description": f"2010-2015 movie: Will download BOTH 1080p (Profile {hd_profile_id}) and 4K (Profile {uhd_profile_id}) versions"
        }
        logger.info(f"🎯 Quality Strategy for {year}: {strategy['description']}")
        return strategy
    else:
        strategy = {
            "profiles": [uhd_profile_id],
            "strategy": "4k_only",
            "description": f"2016+ movie: Using 4K only (Profile {uhd_profile_id}) - largest file preferred"
        }
        logger.info(f"🎯 Quality Strategy for {year}: {strategy['description']}")
        return strategy


def determine_tv_quality_profile_by_year(year: int, logger: logging.Logger, settings: Dict[str, Any] = None) -> Dict[str, Any]:
    """TV quality strategy (Sonarr) with adaptive mode support."""
    settings = settings or {}
    sonarr_preferences = settings.get("Sonarr", {})

    adaptive_enabled = sonarr_preferences.get("adaptive_quality_enabled", False)
    tv_inclusive_profile_id = sonarr_preferences.get("tv_inclusive_quality_profile_id", 6)

    if adaptive_enabled:
        strategy = {
            "profiles": [tv_inclusive_profile_id],
            "strategy": "adaptive_inclusive",
            "description": f"Adaptive Quality: Using inclusive profile (ID {tv_inclusive_profile_id}) - prevents episode skipping, allows best available quality selection"
        }
        logger.info(f"📺 TV Quality Strategy (ADAPTIVE): {strategy['description']}")
        logger.info(f"📺 Year-based preference hint: {year} (used for internal logic, not restrictions)")
        return strategy


    tv_720p_profile_id = sonarr_preferences.get("tv_720p_quality_profile_id", 3)
    tv_hd_profile_id = sonarr_preferences.get("tv_hd_quality_profile_id", 4)
    tv_uhd_profile_id = sonarr_preferences.get("tv_uhd_quality_profile_id", 5)

    if year <= 2009:
        strategy = {
            "profiles": [tv_hd_profile_id],
            "strategy": "1080p_only",
            "description": f"≤2009 TV show: Using 1080p only (Profile {tv_hd_profile_id}) - largest file preferred"
        }
        logger.info(f"📺 TV Quality Strategy for {year}: {strategy['description']}")
        return strategy
    elif 2010 <= year <= 2015:
        strategy = {
            "profiles": [tv_hd_profile_id, tv_uhd_profile_id],
            "strategy": "both_1080p_and_4k",
            "description": f"2010-2015 TV show: Will download BOTH 1080p (Profile {tv_hd_profile_id}) and 4K (Profile {tv_uhd_profile_id}) versions"
        }
        logger.info(f"📺 TV Quality Strategy for {year}: {strategy['description']}")
        return strategy
    else:
        strategy = {
            "profiles": [tv_uhd_profile_id],
            "strategy": "4k_only",
            "description": f"2016+ TV show: Using 4K only (Profile {tv_uhd_profile_id}) - largest file preferred"
        }
        logger.info(f"📺 TV Quality Strategy for {year}: {strategy['description']}")
        return strategy

