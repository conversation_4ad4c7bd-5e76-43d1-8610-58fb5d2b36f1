"""Pack search helper encapsulating tvsearch JSON + XML fallback for season packs.

Public function:
    search_season_packs(indexers_cfg, season_number, series_title, pack_category_ids, timeout, parser, existing_guids) -> list[dict]

Returns list of candidate dicts with at least keys: guid, indexer, title, size (if available).
"""
from __future__ import annotations
from typing import Any, Dict, Iterable, List, Sequence
import urllib.request
import urllib.parse
import re
from .shared_logic import parse_newznab_json
 # (NZB fetch not needed at candidate gathering stage)

PACK_TITLE_TEMPLATE = r"(?i)(complete|full).+season|season\s*0*{s}\b|\bS0*{s}(?!E)"

def gather_pack_candidates(indexers_cfg: Dict[str, Dict[str, Any]], *,
                            season_number: int,
                            series_title: str | None,
                            pack_category_ids: Sequence[int] | None,
                            indexer_timeout: float,
                            parser: Any,
                            existing_guids: Iterable[str]) -> List[Dict[str, Any]]:
    pattern = re.compile(PACK_TITLE_TEMPLATE.format(s=season_number))
    existing_set = set(existing_guids)
    results: List[Dict[str, Any]] = []
    for idx_name, idx in indexers_cfg.items():
        try:
            params: Dict[str, Any] = {
                't': 'tvsearch',
                'season': season_number,
                'extended': 1,
                'apikey': idx['api_key'],
                'o': 'json'
            }
            if series_title:
                params['q'] = series_title
            if pack_category_ids:
                params['cat'] = ','.join(str(c) for c in pack_category_ids)
            url = f"{idx['base_url'].rstrip('/')}/api?{urllib.parse.urlencode(params)}"
            req = urllib.request.Request(url, headers={'User-Agent': 'PreflightAnalyzer/0.1'})
            with urllib.request.urlopen(req, timeout=indexer_timeout) as resp:  # nosec
                body = resp.read().decode('utf-8', 'replace')
            parsed_candidates = parse_newznab_json(body)
            if not parsed_candidates:
                titles = re.findall(r'<title>(.*?)</title>', body)
                guids = re.findall(r'<guid.*?>(.*?)</guid>', body)
                parsed_candidates = [{'title': t, 'guid': g} for t, g in zip(titles, guids)]
            for cand in parsed_candidates:
                title = str(cand.get('title') or '')
                guid = str(cand.get('guid') or '')
                if not title or not guid:
                    continue
                if guid in existing_set:
                    continue
                if not pattern.search(title):
                    continue
                results.append({'guid': guid, 'indexer': idx_name, 'title': title, 'size': cand.get('size')})
        except Exception:
            continue
    # Deduplicate by guid keeping first occurrence
    seen: set[str] = set()
    deduped: List[Dict[str, Any]] = []
    for r in results:
        if r['guid'] in seen:
            continue
        seen.add(r['guid'])
        deduped.append(r)
    return deduped

__all__ = ["gather_pack_candidates"]
