# Enhanced Fuzzy Metadata Validation for PlexAutomator

This implementation provides sophisticated fuzzy string matching capabilities for improved metadata accuracy in PlexAutomator. It intelligently handles title variations, year discrepancies, and ambiguous matches to ensure accurate content identification.

## Overview

The Enhanced Fuzzy Metadata Validation system consists of four core modules:

- **`fuzzy_matching.py`**: Core fuzzy matching algorithms and logic
- **`fuzzy_config.py`**: Configuration management for fuzzy matching settings
- **`metadata_cache.py`**: TMDb API optimization with caching and rate limiting
- **`user_interaction.py`**: User confirmation handling for ambiguous matches

## Features

### 🎯 Intelligent Matching
- **RapidFuzz Integration**: High-performance fuzzy string matching using `token_set_ratio` and `token_sort_ratio`
- **Confidence Scoring**: Automatic confidence calculation with configurable thresholds
- **Year Tolerance**: ±3 year tolerance for metadata matching (configurable)
- **Multiple Algorithm Support**: Uses best-of-breed fuzzy matching algorithms

### 🚦 Confidence-Based Decision Making
- **95%+ confidence**: Auto-approve and proceed
- **80-95% confidence**: Silent correction with logging
- **60-80% confidence**: User confirmation required
- **<60% confidence**: Automatic rejection

### 🔧 TMDb API Optimization
- **Intelligent Caching**: Two-tier caching (memory + file) with TTL support
- **Rate Limiting**: Configurable rate limiting (50 req/sec default)
- **Concurrent Requests**: Semaphore-controlled concurrency (50 concurrent default)
- **Automatic Cleanup**: Cache cleanup and maintenance

### 👥 User Interaction
- **Interactive Mode**: Real-time user confirmation for ambiguous matches
- **Non-Interactive Mode**: Batch processing with predefined rules
- **Detailed Reporting**: Performance metrics and matching statistics
- **Fallback Handling**: Graceful degradation when user input unavailable

## Installation

### Prerequisites

The fuzzy matching system requires the RapidFuzz library:

```bash
pip install rapidfuzz
```

### Module Structure

All fuzzy matching modules are located in `_internal/utils/`:

```
_internal/utils/
├── fuzzy_matching.py      # Core fuzzy matching logic
├── fuzzy_config.py        # Configuration management
├── metadata_cache.py      # API caching system
└── user_interaction.py    # User confirmation handling
```

## Configuration

The system uses `config/fuzzy_matching_config.ini` for configuration:

```ini
[Confidence]
auto_approve_threshold = 95
silent_correct_threshold = 80
user_confirm_threshold = 60
reject_threshold = 0

[Year Tolerance]
movie_year_tolerance = 3
tv_year_tolerance = 3

[TMDb API]
cache_ttl_hours = 24
max_concurrent_requests = 50
rate_limit_requests_per_second = 45
enable_caching = true

[User Interface]
enable_interactive_confirmation = true
confirmation_timeout_seconds = 30
show_confidence_scores = true
show_alternatives = true
max_alternatives_shown = 3

[Performance]
enable_fuzzy_matching = true
use_enhanced_algorithms = true
cache_fuzzy_results = true
log_performance_metrics = true
```

## Usage

### Basic Fuzzy Matching

```python
from _internal.utils.fuzzy_matching import FuzzyMatcher, compute_fuzzy_score

# Initialize matcher
matcher = FuzzyMatcher()

# Compute similarity score
score = compute_fuzzy_score("The Matrix", "Matrix")
print(f"Similarity: {score:.1f}%")  # Output: Similarity: 85.7%

# Find best match from candidates
candidates = [
    {"title": "The Matrix", "year": 1999},
    {"title": "Matrix Reloaded", "year": 2003}
]

result = matcher.select_best_match(candidates, "Matrix", 1999)
if result:
    print(f"Best match: {result['match']['title']} (confidence: {result['confidence']:.1f}%)")
```

### Enhanced Metadata Fetching

The fuzzy matching is automatically integrated into the metadata fetching functions:

#### Movies
```python
from _internal.utils.metadata_apis import _fetch_movie_metadata_for_intake_sync

# Enhanced movie metadata with fuzzy matching
result = _fetch_movie_metadata_for_intake_sync("Matrix")

if result and not result.get("error"):
    print(f"Title: {result['title']}")
    print(f"Year: {result['year']}")
    print(f"Confidence: {result.get('fuzzy_confidence', 'N/A')}")
```

#### TV Shows
```python
from _internal.src.metadata_fetcher import fetch_tv_metadata_for_intake

# Enhanced TV metadata with fuzzy matching
result = await fetch_tv_metadata_for_intake(
    show_title="The Office",
    year=2005,
    request_type="full_series"
)

if result and result.get("success"):
    print(f"Show: {result['show_title']}")
    print(f"Year: {result['year']}")
    print(f"Confidence: {result.get('fuzzy_confidence', 'N/A')}")
```

### User Interaction

```python
from _internal.utils.user_interaction import MatchConfirmationHandler

# Interactive confirmation
handler = MatchConfirmationHandler(interactive=True)

# Mock match data
best_match = {"title": "The Matrix", "year": 1999, "confidence": 75.0}
alternatives = [{"title": "Matrix Reloaded", "year": 2003, "confidence": 40.0}]

# Request user confirmation
confirmed = handler.confirm_match("Matrix", best_match, alternatives)
```

### Cache Management

```python
from _internal.utils.metadata_cache import MetadataCache

# Initialize cache
cache = MetadataCache()

# Store data
cache.set("movie_12345", {"title": "The Matrix", "year": 1999})

# Retrieve data
data = cache.get("movie_12345")

# Check if cached
if cache.has("movie_12345"):
    print("Data is cached")
```

## Algorithm Details

### Fuzzy Matching Algorithm

The system uses a multi-stage fuzzy matching approach:

1. **Title Normalization**: 
   - Remove special characters and extra whitespace
   - Convert to lowercase
   - Handle common abbreviations and variations

2. **Fuzzy Scoring**:
   - Primary: `token_set_ratio` (handles word order differences)
   - Secondary: `token_sort_ratio` (handles word arrangement)
   - Combined scoring with weighted averages

3. **Year Validation**:
   - ±3 year tolerance (configurable)
   - Bonus scoring for exact year matches
   - Penalty for year mismatches outside tolerance

4. **Confidence Calculation**:
   - Weighted combination of fuzzy score and year match
   - Normalization to 0-100% scale
   - Threshold-based decision making

### Performance Optimizations

- **Caching**: Aggressive caching of API responses and fuzzy match results
- **Rate Limiting**: Intelligent rate limiting to prevent API throttling
- **Concurrent Processing**: Async support with semaphore-controlled concurrency
- **Early Termination**: Stop processing when high-confidence match found

## Integration Points

### Existing Functions Enhanced

The following existing functions have been enhanced with fuzzy matching:

#### Movie Metadata (`_internal/utils/metadata_apis.py`)
- `_fetch_movie_metadata_for_intake_sync()`: Enhanced with fuzzy TMDb search

#### TV Metadata (`_internal/src/metadata_fetcher.py`)
- `fetch_tv_metadata_for_intake()`: Enhanced with fuzzy TVDB/TMDb search

### New Functions Added

#### Core Fuzzy Matching (`_internal/utils/fuzzy_matching.py`)
- `normalize_title()`: Title normalization for consistent matching
- `compute_fuzzy_score()`: Core fuzzy scoring algorithm
- `FuzzyMatcher.select_best_match()`: Main matching logic
- `FuzzyMatcher.enhance_matching_with_alternatives()`: Alternative suggestion system

#### Configuration Management (`_internal/utils/fuzzy_config.py`)
- `FuzzyMatchingConfig`: Configuration loading and access
- `create_default_config()`: Default configuration creation

#### Cache System (`_internal/utils/metadata_cache.py`)
- `MetadataCache`: Local caching with TTL support
- `ConcurrentTMDbClient`: Rate-limited API client

#### User Interaction (`_internal/utils/user_interaction.py`)
- `MatchConfirmationHandler`: Interactive/non-interactive confirmation
- `FuzzyMatchingReporter`: Performance reporting and statistics

## Testing

Run the comprehensive test suite:

```bash
python test_fuzzy_matching.py
```

The test suite covers:
- Module imports and initialization
- Configuration system
- Title normalization
- Fuzzy scoring algorithms
- Best match selection
- Cache functionality
- User interaction handling
- Enhanced metadata functions

## Performance Monitoring

The system includes built-in performance monitoring:

```python
from _internal.utils.user_interaction import FuzzyMatchingReporter

reporter = FuzzyMatchingReporter()

# Track match attempt
reporter.record_match_attempt("The Matrix", {"title": "Matrix", "confidence": 85.0}, 0.12)

# Generate report
report = reporter.generate_report()
print(f"Total matches: {report['total_attempts']}")
print(f"Average confidence: {report['average_confidence']:.1f}%")
print(f"Average processing time: {report['average_processing_time']:.3f}s")
```

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure RapidFuzz is installed: `pip install rapidfuzz`
2. **Configuration Issues**: Check `config/fuzzy_matching_config.ini` exists and is readable
3. **Cache Issues**: Clear cache directory `_internal/utils/cache/` if needed
4. **Performance Issues**: Adjust cache TTL and concurrency limits in configuration

### Logging

Enable detailed logging by setting log level in configuration:

```ini
[Logging]
log_level = DEBUG
log_fuzzy_decisions = true
log_cache_hits = true
log_performance_stats = true
```

### Debug Mode

For debugging, enable verbose output:

```python
import logging
logging.basicConfig(level=logging.DEBUG)

# Your fuzzy matching code here
```

## Future Enhancements

Potential improvements for future versions:

1. **Machine Learning**: Train models on user correction patterns
2. **Language Detection**: Enhanced support for non-English titles
3. **Semantic Matching**: Use embeddings for semantic similarity
4. **Auto-Learning**: Automatically adjust thresholds based on success rates
5. **Advanced Caching**: Distributed caching for multi-instance deployments

## Contributing

When contributing to the fuzzy matching system:

1. Maintain backward compatibility with existing metadata functions
2. Add comprehensive tests for new functionality
3. Update configuration schema if adding new settings
4. Document performance impact of changes
5. Follow existing code style and patterns

## License

This fuzzy matching implementation is part of PlexAutomator and follows the same license terms.
