#!/usr/bin/env python3
"""
Real-Time Telemetry System for *Arr and SABnzbd Queue Monitoring

This module provides accurate, real-time tracking of download progress across
Sonarr, Radarr, and SABnzbd. It replaces static "download started" messages
with verified queue monitoring and continuous progress updates.

Key Features:
- Multi-source monitoring (Sonarr/Radarr/SABnzbd)
- Accurate state tracking (downloading/completed/failed/awaiting_postproc)
- Real-time CLI output + structured logging
- Retry & verification logic
- Persistent state for resilience
"""

import logging
import time
import json
import asyncio
import aiohttp
from dataclasses import dataclass, asdict, field
from typing import Dict, List, Optional, Any, Union
from pathlib import Path
import urllib.parse
from datetime import datetime, timedelta

@dataclass
class DownloadJob:
    """Represents a tracked download job with all relevant status information."""
    title: str
    id: str                    # unique ID (e.g., "radarr_12345", "sonarr_5678", "sabnzbd_ABC123")
    source: str                # "radarr", "sonarr", or "sabnzbd"
    status: str                # "pending", "downloading", "paused", "completed", "failed", "awaiting_postproc"
    progress: float            # 0.0 to 1.0
    size_total: int            # total size in bytes
    size_downloaded: int       # downloaded bytes so far
    speed: float               # current download speed (bytes/sec)
    eta: str                   # human-readable ETA or None
    started_at: float          # timestamp when tracking started
    completed_at: Optional[float] = None

    # Additional tracking fields
    radarr_id: Optional[int] = None
    sonarr_id: Optional[int] = None
    episode_id: Optional[int] = None
    sab_nzo_id: Optional[str] = None
    download_client_id: Optional[str] = None
    quality: Optional[str] = None
    indexer: Optional[str] = None
    nzb_filename: Optional[str] = None  # Actual NZB filename for display
    nzb_filename: Optional[str] = None  # Actual NZB filename for better display
    last_updated: float = field(default_factory=time.time)
    verification_attempts: int = 0
    max_verification_attempts: int = 6  # 6 attempts over ~30 seconds
    is_fallback_workflow: bool = False  # True if this job requires extra verification time (remove/re-add, etc.)
    error_message: Optional[str] = None
    
    # Hybrid stall detection fields
    last_progress: float = 0.0
    last_progress_time: float = field(default_factory=time.time)


class RealTimeTelemetry:
    """
    Main telemetry system for monitoring downloads across Sonarr, Radarr, and SABnzbd.

    Provides real-time status updates, verification of download starts, and continuous
    monitoring until completion or failure.
    """

    def __init__(self, config: dict, logger: Optional[logging.Logger] = None):
        """
        Initialize telemetry system with configuration.

        Args:
            config: Dictionary containing API credentials and settings
            logger: Optional logger instance for structured logging
        """
        self.logger = logger or logging.getLogger("telemetry")
        self.config = config
        self.active_jobs: Dict[str, DownloadJob] = {}
        self.completed_jobs: Dict[str, DownloadJob] = {}
        self.state_file = Path(config.get("telemetry_state_path", "_internal/state/telemetry_state.json"))
        self._running = False
        self._session: Optional[aiohttp.ClientSession] = None

        # Telemetry separate log file support
        self._telemetry_log_file = None
        self._telemetry_started = False
        self._original_stdout = None

        # API configuration
        self.sonarr_config = config.get('Sonarr', {})
        self.radarr_config = config.get('Radarr', {})
        self.sabnzbd_config = config.get('SABnzbd', {})
        # Telemetry state/behavior
        self.state_max_age_sec = int(config.get('telemetry_state_max_age_sec', 600))  # default 10 minutes
        self.session_only = bool(config.get('session_only', True))
        self.clear_on_start = bool(config.get('clear_on_start', True))
        self.update_interval_sec = int(config.get('update_interval_sec', 5))
        self.verbose_mode = bool(config.get('verbose_mode', False))  # Dashboard vs verbose display

        # ========== INTELLIGENT FALLBACK INTEGRATION ==========
        # Add failure detection and fallback capabilities
        self.previous_job_status = {}  # Track previous job status for failure detection
        self.movie_failures = {}       # Track failure history per radarr_id
        self.tv_failures = {}          # Track failure history per sonarr_id
        self.movie_candidates = {}     # Store candidate info for fallback
        self.tv_candidates = {}        # Store TV candidate info for fallback
        self.fallback_triggered = {}   # Prevent duplicate fallback triggers per radarr_id
        self.fallback_system = None    # Will be initialized when needed
        
        # RACE CONDITION FIX: Add mutex for fallback operations
        self._fallback_mutex = asyncio.Lock()
        # =====================================================

        # Ensure state directory exists
        self.state_file.parent.mkdir(parents=True, exist_ok=True)

        # Load any persisted state
        if self.clear_on_start:
            # start clean
            try:
                if self.state_file.exists():
                    self.state_file.unlink()
            except Exception:
                pass
        else:
            self._load_state()

        self.logger.info("🔄 Real-time telemetry system initialized")

    async def __aenter__(self):
        """Async context manager entry."""
        self._session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30)
        )
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        self._stop_telemetry_logging()  # Clean up telemetry logging
        if self._session:
            await self._session.close()

    def track_new_download(self, title: str, radarr_id: Optional[int] = None,
                          sonarr_id: Optional[int] = None, episode_id: Optional[int] = None,
                          quality: Optional[str] = None, indexer: Optional[str] = None,
                          is_fallback_workflow: bool = False, monitored_status: bool = True,
                          nzb_filename: Optional[str] = None) -> str:
        """
        Add a new item to telemetry tracking.

        Args:
            title: Human-readable title of the content
            radarr_id: Radarr movie ID if applicable
            sonarr_id: Sonarr series ID if applicable
            episode_id: Sonarr episode ID if applicable
            quality: Quality profile name
            indexer: Indexer name
            is_fallback_workflow: True if this is a remove/re-add fallback (requires extended verification)
            monitored_status: Whether the item is monitored in *arr (affects queue visibility)
            nzb_filename: Actual NZB filename for display instead of movie title

        Returns:
            job_id: Unique identifier for this download job
        """
        # Generate unique job ID
        if radarr_id:
            job_id = f"radarr_{radarr_id}"
            source = "radarr"
        elif sonarr_id:
            job_id = f"sonarr_{sonarr_id}_{episode_id or 'series'}"
            source = "sonarr"
        else:
            # Fallback to title-based ID
            safe_title = "".join(c for c in title[:20] if c.isalnum() or c in (' ', '-', '_')).strip()
            job_id = f"manual_{safe_title}_{int(time.time())}"
            source = "unknown"

        # Check if job already exists for this radarr_id/sonarr_id
        if job_id in self.active_jobs:
            existing_job = self.active_jobs[job_id]
            self.logger.info(f"🔄 Updating existing telemetry job: {job_id}")
            
            # Update the existing job with new information
            existing_job.title = title
            existing_job.quality = quality or existing_job.quality
            existing_job.indexer = indexer or existing_job.indexer
            existing_job.nzb_filename = nzb_filename or existing_job.nzb_filename
            existing_job.is_fallback_workflow = is_fallback_workflow or existing_job.is_fallback_workflow
            existing_job.last_updated = time.time()
            
            # Reset status if this is a new fallback attempt
            if is_fallback_workflow and existing_job.status in ["failed", "completed"]:
                existing_job.status = "pending"
                existing_job.progress = 0.0
                existing_job.size_downloaded = 0
                existing_job.speed = 0.0
                existing_job.eta = "Unknown"
                existing_job.error_message = None
                existing_job.verification_attempts = 0
                self.logger.info(f"🔄 Reset job status for fallback: {job_id}")
            
            # Reset fallback flag on new download attempt
            if radarr_id and radarr_id in self.fallback_triggered:
                del self.fallback_triggered[radarr_id]
                self.logger.info(f"🔄 Reset fallback flag for radarr_id {radarr_id}")
            
            self._persist_state()
            
            # Output update message
            display_name = existing_job.nzb_filename if existing_job.nzb_filename else existing_job.title
            if is_fallback_workflow:
                print(f"🔄 Fallback download queued: \"{display_name}\"")
                self._log_structured_event("fallback_queued", existing_job)
            else:
                print(f"📝 Updated existing download: \"{display_name}\"")
                self._log_structured_event("download_updated", existing_job)
            
            return job_id

        job = DownloadJob(
            title=title,
            id=job_id,
            source=source,
            status="pending",
            progress=0.0,
            size_total=0,
            size_downloaded=0,
            speed=0.0,
            eta="Unknown",
            started_at=time.time(),
            radarr_id=radarr_id,
            sonarr_id=sonarr_id,
            episode_id=episode_id,
            quality=quality,
            indexer=indexer,
            nzb_filename=nzb_filename,
            is_fallback_workflow=is_fallback_workflow
        )

        self.active_jobs[job_id] = job
        
        # Reset fallback flag on new download attempt
        if radarr_id and radarr_id in self.fallback_triggered:
            del self.fallback_triggered[radarr_id]
            self.logger.info(f"🔄 Reset fallback flag for radarr_id {radarr_id}")
        
        self._persist_state()

        # Output initial tracking message - prefer NZB filename over movie title for clarity
        display_name = nzb_filename if nzb_filename else title
        print(f"📥 Queued \"{display_name}\" for download...")
        self._log_structured_event("download_queued", job)

        return job_id

    def update_nzb_filename(self, job_id: str, nzb_filename: str) -> bool:
        """
        Update the NZB filename for an existing job after preflight analysis.
        
        Args:
            job_id: The job ID to update
            nzb_filename: The actual NZB filename selected during preflight
            
        Returns:
            True if update successful, False if job not found
        """
        if job_id not in self.active_jobs:
            self.logger.error(f"Cannot update NZB filename - job {job_id} not found")
            return False
        
        job = self.active_jobs[job_id]
        old_filename = job.nzb_filename
        job.nzb_filename = nzb_filename
        job.last_updated = time.time()
        
        self._persist_state()
        
        # Log the update
        if old_filename != nzb_filename:
            display_old = old_filename or job.title
            self.logger.info(f"📝 Updated NZB filename for {job_id}: '{display_old}' → '{nzb_filename}'")
            
        return True

    def update_fallback_candidate(self, radarr_id: int, new_candidate_info: dict) -> bool:
        """
        Update telemetry to show new candidate information when a fallback download is triggered.
        
        Args:
            radarr_id: The radarr ID for the movie
            new_candidate_info: Dict containing new candidate details (title, guid, size, etc.)
            
        Returns:
            True if update successful, False if job not found
        """
        job_id = f"radarr_{radarr_id}"
        
        if job_id not in self.active_jobs:
            self.logger.error(f"Cannot update fallback candidate - job {job_id} not found")
            return False
        
        job = self.active_jobs[job_id]
        
        # Store old information for logging
        old_filename = job.nzb_filename or job.title
        
        # Update job with new candidate information
        if 'title' in new_candidate_info and new_candidate_info['title']:
            job.nzb_filename = new_candidate_info['title']
        if 'size' in new_candidate_info:
            job.size_total = new_candidate_info['size']
        if 'indexer' in new_candidate_info:
            job.indexer = new_candidate_info['indexer']
        
        # Reset download progress for new candidate
        job.status = "downloading"
        job.progress = 0.0
        job.size_downloaded = 0
        job.speed = 0.0
        job.eta = "calc..."  # Changed from "Unknown" to "calc..." to match your dashboard
        job.error_message = None
        job.last_updated = time.time()
        
        # CRITICAL: Reset SABnzbd tracking information so it can find the new download
        job.sab_nzo_id = None
        job.download_client_id = None
        
        # Reset verification attempts for fresh start
        job.verification_attempts = 0
        
        self._persist_state()
        
        # Log and announce the fallback candidate update
        new_filename = job.nzb_filename or job.title
        self.logger.info(f"🔄 FALLBACK CANDIDATE UPDATE for radarr_id {radarr_id}")
        self.logger.info(f"   📛 Old: {old_filename}")
        self.logger.info(f"   ✨ New: {new_filename}")
        print(f"🔄 Fallback download starting: \"{new_filename}\"")
        
        # Update dashboard immediately to show new candidate
        if hasattr(self, '_telemetry_log_file') and self._telemetry_log_file:
            timestamp = datetime.now().strftime("%H:%M:%S")
            fallback_update = f"[{timestamp}] 🔄 FALLBACK CANDIDATE: {new_filename}"
            self._telemetry_log_file.write(fallback_update + "\n")
            self._telemetry_log_file.flush()
        
        # IMPORTANT: Force an immediate queue check to find the new download
        # Schedule this to run in the background to avoid blocking
        if hasattr(self, '_session') and self._session:
            import asyncio
            async def find_new_download():
                # Wait a brief moment for the download to appear in SABnzbd
                await asyncio.sleep(2)
                # Force check SABnzbd queue for the new download
                await self._check_sabnzbd_queue(job)
                self.logger.info(f"🔍 Performed immediate queue search for fallback candidate: {new_filename}")
            
            # Run in background
            asyncio.create_task(find_new_download())
        
        return True

    def check_progress_thresholds(self, job: DownloadJob) -> bool:
        """
        Check if a job has hit critical progress thresholds that indicate failure.
        
        Args:
            job: The download job to check
            
        Returns:
            True if job status was updated, False otherwise
        """
        # Hybrid stall detection with smart dynamic thresholding
        STALL_TIMEOUT = 300  # 5 minutes of no progress = stall
        
        # Smart Dynamic Thresholding based on file size
        MAX_SIZE_GB = 100.0  # Maximum expected file size for scaling
        size_gb = job.size_total / (1024**3) if job.size_total > 0 else 0
        
        # Dynamic threshold: 50% for small files, up to 70% for very large files
        dynamic_threshold = 0.5 + 0.2 * min(size_gb / MAX_SIZE_GB, 1.0)
        
        self.logger.debug(f"Dynamic threshold for {job.title}: {dynamic_threshold:.1%} (size: {size_gb:.1f}GB)")
        
        if job.progress >= dynamic_threshold and job.status == 'downloading':
            current_time = time.time()
            
            # Check if progress has changed since last check
            if job.progress == job.last_progress:
                # Progress hasn't changed - check if we've exceeded stall timeout
                time_stalled = current_time - job.last_progress_time
                if time_stalled > STALL_TIMEOUT:
                    job.status = 'failed'
                    job.error_message = f"Download stalled at {job.progress:.1%} progress for {int(time_stalled/60)} minutes (threshold: {dynamic_threshold:.1%})"
                    self.logger.warning(f"🚨 Download failed: {job.title} stalled at {job.progress:.1%} for {int(time_stalled/60)} minutes (size: {size_gb:.1f}GB, threshold: {dynamic_threshold:.1%})")
                    self._log_structured_event("download_stalled", job)
                    self._persist_state()
                    return True
            else:
                # Progress has changed - update tracking
                job.last_progress = job.progress
                job.last_progress_time = current_time
        
        return False

    async def verify_queue_entry(self, job: DownloadJob, immediate: bool = True) -> bool:
        """
        Verify that a newly submitted download appears in the appropriate queues.

        Args:
            job: The download job to verify
            immediate: Whether to check immediately or schedule for later

        Returns:
            True if verification successful, False otherwise
        """
        if not self._session:
            self.logger.error("Session not initialized - use async context manager")
            return False

        job.verification_attempts += 1

        try:
            # Check the appropriate *arr service queue first
            found_in_arr = False
            if job.source == "radarr" and job.radarr_id:
                found_in_arr = await self._check_radarr_queue(job)
            elif job.source == "sonarr" and (job.sonarr_id or job.episode_id):
                found_in_arr = await self._check_sonarr_queue(job)

            # Check SABnzbd queue for the actual download
            found_in_sab = await self._check_sabnzbd_queue(job)

            if found_in_sab:
                job.status = "downloading"
                job.last_updated = time.time()
                print(f"✅ Download verified: \"{job.title}\" is now downloading")
                self._log_structured_event("download_verified", job)
                self._persist_state()
                return True
            elif found_in_arr:
                # Found in *arr but not SABnzbd yet - might be processing
                print(f"🔎 Download queued in {job.source.title()}, waiting for download client...")
                self._log_structured_event("arr_queued", job)
                # Schedule retry
                if job.verification_attempts < job.max_verification_attempts:
                    return False  # Will retry
                else:
                    job.status = "failed"
                    job.error_message = "Download not found in SABnzbd after multiple attempts"
                    print(f"❌ Download verification failed: \"{job.title}\" - not found in download client")
                    self._log_structured_event("verification_failed", job)
                    return False
            else:
                # Not found in either queue - check history for grab events before giving up
                found_in_history = False
                
                # Check *arr history for grab events (positive confirmation that grab succeeded)
                if job.source == "radarr" and job.radarr_id:
                    found_in_history = await self._check_radarr_grab_in_history(job, job.started_at)
                elif job.source == "sonarr" and (job.sonarr_id or job.episode_id):
                    found_in_history = await self._check_sonarr_grab_in_history(job, job.started_at)
                
                if found_in_history:
                    # Found grab in history, but not in current queues - check SAB history
                    sab_history = await self._get_sabnzbd_history()
                    found_in_sab_history = await self._check_sabnzbd_history_by_title(job, sab_history)
                    
                    if found_in_sab_history:
                        # Found in SAB history - job handled elsewhere
                        return True
                    else:
                        # Grab confirmed but not in SAB yet - continue monitoring
                        print(f"🔎 Grab confirmed in {job.source.title()} history, waiting for download client...")
                        self._log_structured_event("grab_confirmed", job)
                        
                        # For fallback workflows or when grab is confirmed, be more patient
                        max_attempts = job.max_verification_attempts
                        if job.is_fallback_workflow or found_in_history:
                            max_attempts = 15  # Be more patient when grab is confirmed
                        
                        if job.verification_attempts < max_attempts:
                            return False  # Will retry
                        else:
                            job.status = "failed"
                            job.error_message = "Grab confirmed but download not found in SABnzbd"
                            print(f"⚠️ Download grab confirmed but not found in download client: \"{job.title}\"")
                            self._log_structured_event("verification_failed", job)
                            return False
                
                # No grab found in history either - likely submission failed
                max_attempts = job.max_verification_attempts
                if job.is_fallback_workflow:
                    max_attempts = 12  # Double the attempts for fallback workflows (remove/re-add, etc.)
                
                if job.verification_attempts < max_attempts:
                    wait_time = 5 + (job.verification_attempts * 2)  # Increasing wait time
                    workflow_note = " (fallback workflow)" if job.is_fallback_workflow else ""
                    print(f"🔎 Verifying download in queue for \"{job.title}\" (attempt {job.verification_attempts}/{max_attempts}){workflow_note}...")
                    return False  # Will retry
                else:
                    job.status = "failed"
                    job.error_message = f"Download not found in {job.source.title()} queues or history"
                    print(f"❌ Download verification failed: \"{job.title}\" - submission likely failed")
                    self._log_structured_event("verification_failed", job)
                    return False

        except Exception as e:
            self.logger.error(f"Error verifying queue entry for {job.title}: {e}")
            job.error_message = str(e)
            return False

    async def _check_radarr_queue(self, job: DownloadJob) -> bool:
        """Check if job appears in Radarr's download queue."""
        if not self.radarr_config.get('enabled', False):
            return False

        try:
            url = f"{self.radarr_config['url']}/api/v3/queue"
            headers = {'X-Api-Key': self.radarr_config['api_key']}

            async with self._session.get(url, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    records = data.get('records', [])

                    for record in records:
                        movie = record.get('movie', {})
                        if movie.get('id') == job.radarr_id:
                            # Found the movie in queue - update job with download client info
                            job.download_client_id = record.get('downloadId', '')
                            job.size_total = record.get('size', 0)
                            job.size_downloaded = record.get('size', 0) - record.get('sizeleft', 0)
                            job.progress = max(0.0, min(1.0, job.size_downloaded / job.size_total if job.size_total > 0 else 0.0))
                            
                            # Check for failure thresholds
                            self.check_progress_thresholds(job)
                            
                            return True
                else:
                    self.logger.warning(f"Failed to check Radarr queue: HTTP {response.status}")
        except Exception as e:
            self.logger.error(f"Error checking Radarr queue: {e}")

        return False

    async def _check_sonarr_queue(self, job: DownloadJob) -> bool:
        """Check if job appears in Sonarr's download queue."""
        if not self.sonarr_config.get('enabled', False):
            return False

        try:
            url = f"{self.sonarr_config['url']}/api/v3/queue"
            headers = {'X-Api-Key': self.sonarr_config['api_key']}

            async with self._session.get(url, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    records = data.get('records', [])

                    for record in records:
                        # Check by episode ID if available
                        if job.episode_id and record.get('episodeId') == job.episode_id:
                            job.download_client_id = record.get('downloadId', '')
                            job.size_total = record.get('size', 0)
                            job.size_downloaded = record.get('size', 0) - record.get('sizeleft', 0)
                            job.progress = max(0.0, min(1.0, job.size_downloaded / job.size_total if job.size_total > 0 else 0.0))
                            
                            # Check for failure thresholds
                            self.check_progress_thresholds(job)
                            
                            return True
                        # Fallback: check by series ID
                        elif job.sonarr_id and record.get('seriesId') == job.sonarr_id:
                            job.download_client_id = record.get('downloadId', '')
                            job.size_total = record.get('size', 0)
                            job.size_downloaded = record.get('size', 0) - record.get('sizeleft', 0)
                            job.progress = max(0.0, min(1.0, job.size_downloaded / job.size_total if job.size_total > 0 else 0.0))
                            
                            # Check for failure thresholds
                            self.check_progress_thresholds(job)
                            
                            return True
                else:
                    self.logger.warning(f"Failed to check Sonarr queue: HTTP {response.status}")
        except Exception as e:
            self.logger.error(f"Error checking Sonarr queue: {e}")

        return False

    async def _check_sabnzbd_queue(self, job: DownloadJob) -> bool:
        """Check if job appears in SABnzbd's download queue."""
        if not self.sabnzbd_config.get('enabled', False):
            return False

        try:
            url = f"{self.sabnzbd_config['base_url']}/api"
            params = {
                'mode': 'queue',
                'apikey': self.sabnzbd_config['api_key'],
                'output': 'json'
            }

            async with self._session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    queue = data.get('queue', {})
                    slots = queue.get('slots', [])

                    # Look for download by title matching or download ID
                    title_words = set(job.title.lower().replace('.', ' ').replace('-', ' ').replace("'", '').replace(':', '').split())
                    # Remove common words and year for better matching
                    title_words = {word for word in title_words if word not in ['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by']}
                    # Remove year and parentheses (including year in parentheses like "(2016)")
                    title_words = {word for word in title_words if not (word.isdigit() and len(word) == 4)}
                    title_words = {word for word in title_words if not word.startswith('(') and not word.endswith(')')}

                    for slot in slots:
                        slot_filename = slot.get('filename', '').lower()
                        slot_nzo_id = slot.get('nzo_id', '')

                        # Match by download client ID if available
                        if job.download_client_id and job.download_client_id in slot_nzo_id:
                            job.sab_nzo_id = slot_nzo_id
                            self._update_job_from_sab_slot(job, slot)
                            return True

                        # Enhanced title similarity matching 
                        slot_words = set(slot_filename.replace('.', ' ').replace('-', ' ').replace('_', ' ').replace("'", '').replace(':', '').split())
                        slot_words = {word for word in slot_words if word not in ['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by']}
                        slot_words = {word for word in slot_words if not (word.isdigit() and len(word) == 4)}
                        
                        # Calculate similarity - need at least 60% of title words to match
                        if len(title_words) > 0:
                            similarity = len(title_words & slot_words) / len(title_words)
                            if similarity >= 0.6:
                                self.logger.info(f"📦 SABnzbd match found: '{job.title}' -> '{slot_filename}' (similarity: {similarity:.2f})")
                                job.sab_nzo_id = slot_nzo_id
                                job.download_client_id = slot_nzo_id  # Store for future reference
                                self._update_job_from_sab_slot(job, slot)
                                return True
                else:
                    self.logger.warning(f"Failed to check SABnzbd queue: HTTP {response.status}")
        except Exception as e:
            self.logger.error(f"Error checking SABnzbd queue: {e}")

        return False

    def _update_job_from_sab_slot(self, job: DownloadJob, slot: dict):
        """Update job information from SABnzbd queue slot data."""
        try:
            # Parse size information
            mb_total = float(slot.get('mb', 0))
            mb_left = float(slot.get('mbleft', 0))
            mb_downloaded = mb_total - mb_left

            job.size_total = int(mb_total * 1024 * 1024)  # Convert MB to bytes
            job.size_downloaded = int(mb_downloaded * 1024 * 1024)
            job.progress = max(0.0, min(1.0, mb_downloaded / mb_total if mb_total > 0 else 0.0))

            # Check for failure thresholds
            self.check_progress_thresholds(job)

            # Parse speed (comes as string like "5.2 MB/s")
            speed_str = slot.get('speed', '0')
            try:
                if 'MB/s' in speed_str:
                    speed_mb = float(speed_str.replace('MB/s', '').strip())
                    job.speed = speed_mb * 1024 * 1024  # Convert to bytes/sec
                elif 'KB/s' in speed_str:
                    speed_kb = float(speed_str.replace('KB/s', '').strip())
                    job.speed = speed_kb * 1024
                else:
                    job.speed = 0.0
            except (ValueError, AttributeError):
                job.speed = 0.0

            # ETA
            job.eta = slot.get('timeleft', 'Unknown')

            # Status normalization
            sab_status = slot.get('status', '').lower()
            if sab_status in ['downloading', 'fetching', 'running']:
                job.status = 'downloading'
            elif sab_status in ['paused', 'queued']:
                job.status = 'paused'
            elif sab_status in ['verifying', 'repairing', 'unpacking']:
                job.status = 'downloading'  # Still considered downloading
            else:
                job.status = 'downloading'  # Default for active queue items

            job.last_updated = time.time()

        except Exception as e:
            self.logger.error(f"Error updating job from SAB slot: {e}")

    async def start_monitoring(self, interval: int = 5) -> None:
        """
        Begin continuous monitoring of all tracked downloads.

        Args:
            interval: Polling interval in seconds
        """
        self.logger.info(f"🔄 Starting real-time download monitoring (interval: {self.update_interval_sec}s)")
        self._running = True

        # Initial verification for any pending jobs
        pending_jobs = [job for job in self.active_jobs.values() if job.status == "pending"]
        for job in pending_jobs:
            await self.verify_queue_entry(job)

        # Main monitoring loop
        while self._running and self.active_jobs:
            try:
                await self.poll_queues()
                
                # ========== INTELLIGENT FALLBACK INTEGRATION ==========
                # Check for download failures and trigger fallbacks
                await self._check_for_failures_and_trigger_fallbacks()
                # =====================================================
                
                # Use dashboard or verbose display based on configuration
                if self.verbose_mode:
                    self.report_status_grouped()  # Original verbose line-by-line output
                else:
                    self.report_status_dashboard()  # New compact dashboard format

                # Check for jobs that need verification retry
                for job in list(self.active_jobs.values()):
                    if job.status == "pending" and job.verification_attempts < job.max_verification_attempts:
                        # Wait longer between retry attempts
                        time_since_last = time.time() - job.last_updated
                        if time_since_last >= (5 + job.verification_attempts * 2):
                            await self.verify_queue_entry(job)

                await asyncio.sleep(interval)

            except KeyboardInterrupt:
                self.logger.info("Monitoring interrupted by user")
                break
            except Exception as e:
                self.logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(interval)

        self._running = False

        if not self.active_jobs:
            print("🎉 All downloads completed successfully!")
            self.logger.info("All download monitoring completed")
        else:
            self.logger.info(f"Monitoring stopped with {len(self.active_jobs)} active jobs remaining")

    async def poll_queues(self) -> None:
        """Poll all services for current download status updates."""
        if not self._session:
            return

        try:
            # Get current data from all services
            sab_queue = await self._get_sabnzbd_queue()
            sab_history = await self._get_sabnzbd_history()
            sonarr_queue = await self._get_sonarr_queue()
            radarr_queue = await self._get_radarr_queue()

            # Update each active job
            for job_id, job in list(self.active_jobs.items()):
                if job.status in ["completed", "failed"]:
                    continue  # Skip already finished jobs

                updated = False

                # Update from SABnzbd queue if job has SAB ID
                if job.sab_nzo_id:
                    sab_slot = sab_queue.get(job.sab_nzo_id)
                    if sab_slot:
                        self._update_job_from_sab_slot(job, sab_slot)
                        updated = True
                    else:
                        # Not in queue - check history for completion/failure
                        history_item = sab_history.get(job.sab_nzo_id)
                        if history_item:
                            await self._handle_sab_history_item(job, history_item, sonarr_queue, radarr_queue)
                            updated = True
                else:
                    # Job doesn't have SAB ID yet - try to find it in queue or history by title matching
                    if not updated:
                        # First try to find in current queue
                        found_in_queue = await self._check_sabnzbd_queue(job)
                        if found_in_queue:
                            updated = True
                        else:
                            # Not in queue - try to find in history by title matching
                            found_in_history = await self._check_sabnzbd_history_by_title(job, sab_history)
                            if found_in_history:
                                updated = True
                            else:
                                # Debug logging when not found anywhere
                                self.logger.info(f"🔍 Download not found in SABnzbd: '{job.title}' (ID: {job.download_client_id})")

                # Update from *arr queue status
                if job.source == "radarr" and job.radarr_id:
                    arr_item = radarr_queue.get(job.radarr_id)
                    if arr_item:
                        self._update_job_from_arr_queue(job, arr_item)
                        updated = True
                    elif job.status == "awaiting_postproc":
                        # No longer in Radarr queue - import completed
                        job.status = "completed"
                        job.completed_at = time.time()
                        updated = True

                elif job.source == "sonarr" and (job.sonarr_id or job.episode_id):
                    # For Sonarr, check by episode ID first, then series ID
                    arr_item = None
                    if job.episode_id:
                        arr_item = next((item for item in sonarr_queue.values()
                                       if item.get('episodeId') == job.episode_id), None)
                    if not arr_item and job.sonarr_id:
                        arr_item = next((item for item in sonarr_queue.values()
                                       if item.get('seriesId') == job.sonarr_id), None)

                    if arr_item:
                        self._update_job_from_arr_queue(job, arr_item)
                        updated = True
                    elif job.status == "awaiting_postproc":
                        # No longer in Sonarr queue - import completed
                        job.status = "completed"
                        job.completed_at = time.time()
                        updated = True

                if updated:
                    job.last_updated = time.time()

        except Exception as e:
            self.logger.error(f"Error polling queues: {e}")

    async def _get_sabnzbd_queue(self) -> Dict[str, dict]:
        """Get SABnzbd queue data, indexed by nzo_id."""
        if not self.sabnzbd_config.get('enabled', False):
            return {}

        try:
            url = f"{self.sabnzbd_config['base_url']}/api"
            params = {
                'mode': 'queue',
                'apikey': self.sabnzbd_config['api_key'],
                'output': 'json'
            }

            async with self._session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    queue = data.get('queue', {})
                    slots = queue.get('slots', [])
                    return {slot.get('nzo_id'): slot for slot in slots if slot.get('nzo_id')}

        except Exception as e:
            self.logger.error(f"Error getting SABnzbd queue: {e}")

        return {}

    async def _get_sabnzbd_history(self, limit: int = 50) -> Dict[str, dict]:
        """Get SABnzbd history data, indexed by nzo_id."""
        if not self.sabnzbd_config.get('enabled', False):
            return {}

        try:
            url = f"{self.sabnzbd_config['base_url']}/api"
            params = {
                'mode': 'history',
                'apikey': self.sabnzbd_config['api_key'],
                'output': 'json',
                'limit': limit
            }

            async with self._session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    history = data.get('history', {})
                    slots = history.get('slots', [])
                    return {slot.get('nzo_id'): slot for slot in slots if slot.get('nzo_id')}

        except Exception as e:
            self.logger.error(f"Error getting SABnzbd history: {e}")

        return {}

    async def _check_sabnzbd_history_by_title(self, job: DownloadJob, sab_history: Dict[str, dict]) -> bool:
        """Check if job appears in SABnzbd's history by title matching."""
        if not self.sabnzbd_config.get('enabled', False):
            return False

        try:
            # Look for download by title matching
            title_words = set(job.title.lower().replace('.', ' ').replace('-', ' ').split())

            for nzo_id, history_item in sab_history.items():
                item_name = history_item.get('name', '').lower()

                # Match by title similarity (70% of title words appear in filename)
                item_words = set(item_name.replace('.', ' ').replace('-', ' ').split())
                if len(title_words & item_words) >= len(title_words) * 0.7:
                    job.sab_nzo_id = nzo_id
                    job.download_client_id = nzo_id  # Store for future reference

                    # Check for duplicate detection
                    status = history_item.get('status', '').lower()
                    if status == 'failed':
                        fail_message = history_item.get('fail_message', '').lower()
                        if 'duplicate' in fail_message or 'already downloaded' in fail_message:
                            print(f"⚠️ Duplicate detected: \"{job.title}\" - may have been downloaded before")
                            job.status = "failed"
                            job.error_message = f"Duplicate detection: {history_item.get('fail_message', 'Already downloaded')}"
                            self._log_structured_event("duplicate_detected", job)
                            return True

                    # Handle the history item (completion/failure)
                    await self._handle_sab_history_item(job, history_item, {}, {})
                    return True

        except Exception as e:
            self.logger.error(f"Error checking SABnzbd history by title: {e}")

        return False

    async def _get_sonarr_queue(self) -> Dict[str, dict]:
        """Get Sonarr queue data."""
        if not self.sonarr_config.get('enabled', False):
            return {}

        try:
            url = f"{self.sonarr_config['url']}/api/v3/queue"
            headers = {'X-Api-Key': self.sonarr_config['api_key']}

            async with self._session.get(url, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    records = data.get('records', [])
                    # Index by both episode ID and series ID for flexibility
                    queue_dict = {}
                    for record in records:
                        if record.get('episodeId'):
                            queue_dict[f"episode_{record['episodeId']}"] = record
                        if record.get('seriesId'):
                            queue_dict[f"series_{record['seriesId']}"] = record
                    return queue_dict

        except Exception as e:
            self.logger.error(f"Error getting Sonarr queue: {e}")

        return {}

    async def _get_radarr_queue(self) -> Dict[str, dict]:
        """Get Radarr queue data, indexed by movie ID."""
        if not self.radarr_config.get('enabled', False):
            return {}

        try:
            url = f"{self.radarr_config['url']}/api/v3/queue"
            headers = {'X-Api-Key': self.radarr_config['api_key']}

            async with self._session.get(url, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    records = data.get('records', [])
                    return {record.get('movie', {}).get('id'): record
                           for record in records
                           if record.get('movie', {}).get('id')}

        except Exception as e:
            self.logger.error(f"Error getting Radarr queue: {e}")

        return {}

    async def _get_radarr_history(self, movie_id: Optional[int] = None, limit: int = 50) -> Dict[str, dict]:
        """
        Get Radarr history data for verification of grab events.
        
        Args:
            movie_id: If specified, filter history to this movie ID only
            limit: Maximum number of history entries to retrieve
            
        Returns:
            Dictionary of history entries indexed by ID
        """
        if not self.radarr_config.get('enabled', False):
            return {}

        try:
            url = f"{self.radarr_config['url']}/api/v3/history"
            headers = {'X-Api-Key': self.radarr_config['api_key']}
            
            params = {'pageSize': limit, 'sortKey': 'date', 'sortDirection': 'descending'}
            if movie_id:
                params['movieId'] = movie_id

            async with self._session.get(url, headers=headers, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    records = data.get('records', [])
                    return {record.get('id'): record for record in records if record.get('id')}

        except Exception as e:
            self.logger.error(f"Error getting Radarr history: {e}")

        return {}

    async def _check_radarr_grab_in_history(self, job: DownloadJob, since_timestamp: Optional[float] = None) -> bool:
        """
        Check if a grab event appears in Radarr history for the specified movie.
        
        Args:
            job: The download job to check
            since_timestamp: Only check grab events after this timestamp
            
        Returns:
            True if a grab event was found, False otherwise
        """
        if not job.radarr_id:
            return False
            
        try:
            # Get recent history for this movie
            history = await self._get_radarr_history(movie_id=job.radarr_id, limit=20)
            
            # Look for grab events
            for history_entry in history.values():
                event_type = history_entry.get('eventType', '').lower()
                if event_type == 'grabbed':
                    # Check timestamp if provided
                    if since_timestamp:
                        # Parse Radarr's date format (ISO 8601)
                        date_str = history_entry.get('date', '')
                        try:
                            from datetime import datetime
                            event_time = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
                            event_timestamp = event_time.timestamp()
                            if event_timestamp < since_timestamp:
                                continue  # Too old
                        except (ValueError, AttributeError):
                            # If we can't parse the date, assume it's recent
                            pass
                    
                    # Found a recent grab event
                    download_id = history_entry.get('downloadId', '')
                    if download_id:
                        job.download_client_id = download_id
                        self.logger.info(f"✅ Found grab event in Radarr history for {job.title} (downloadId: {download_id})")
                        return True
                        
        except Exception as e:
            self.logger.error(f"Error checking Radarr grab history for {job.title}: {e}")
            
        return False

    async def _get_sonarr_history(self, series_id: Optional[int] = None, episode_id: Optional[int] = None, limit: int = 50) -> Dict[str, dict]:
        """
        Get Sonarr history data for verification of grab events.
        
        Args:
            series_id: If specified, filter history to this series ID
            episode_id: If specified, filter history to this episode ID  
            limit: Maximum number of history entries to retrieve
            
        Returns:
            Dictionary of history entries indexed by ID
        """
        if not self.sonarr_config.get('enabled', False):
            return {}

        try:
            url = f"{self.sonarr_config['url']}/api/v3/history"
            headers = {'X-Api-Key': self.sonarr_config['api_key']}
            
            params = {'pageSize': limit, 'sortKey': 'date', 'sortDirection': 'descending'}
            if series_id:
                params['seriesId'] = series_id
            if episode_id:
                params['episodeId'] = episode_id

            async with self._session.get(url, headers=headers, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    records = data.get('records', [])
                    return {record.get('id'): record for record in records if record.get('id')}

        except Exception as e:
            self.logger.error(f"Error getting Sonarr history: {e}")

        return {}

    async def _check_sonarr_grab_in_history(self, job: DownloadJob, since_timestamp: Optional[float] = None) -> bool:
        """
        Check if a grab event appears in Sonarr history for the specified episode/series.
        
        Args:
            job: The download job to check
            since_timestamp: Only check grab events after this timestamp
            
        Returns:
            True if a grab event was found, False otherwise
        """
        if not (job.sonarr_id or job.episode_id):
            return False
            
        try:
            # Get recent history for this series/episode
            history = await self._get_sonarr_history(
                series_id=job.sonarr_id, 
                episode_id=job.episode_id, 
                limit=20
            )
            
            # Look for grab events
            for history_entry in history.values():
                event_type = history_entry.get('eventType', '').lower()
                if event_type == 'grabbed':
                    # Check timestamp if provided
                    if since_timestamp:
                        # Parse Sonarr's date format (ISO 8601)
                        date_str = history_entry.get('date', '')
                        try:
                            from datetime import datetime
                            event_time = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
                            event_timestamp = event_time.timestamp()
                            if event_timestamp < since_timestamp:
                                continue  # Too old
                        except (ValueError, AttributeError):
                            # If we can't parse the date, assume it's recent
                            pass
                    
                    # Found a recent grab event
                    download_id = history_entry.get('downloadId', '')
                    if download_id:
                        job.download_client_id = download_id
                        self.logger.info(f"✅ Found grab event in Sonarr history for {job.title} (downloadId: {download_id})")
                        return True
                        
        except Exception as e:
            self.logger.error(f"Error checking Sonarr grab history for {job.title}: {e}")
            
        return False

        return {}

    async def _handle_sab_history_item(self, job: DownloadJob, history_item: dict,
                                     sonarr_queue: dict, radarr_queue: dict):
        """Handle SABnzbd history item to determine if download completed or failed."""
        status = history_item.get('status', '').lower()

        if status == 'completed':
            # Check if still in *arr queue (awaiting post-processing)
            still_in_arr = False
            if job.source == "radarr" and job.radarr_id:
                still_in_arr = job.radarr_id in radarr_queue
            elif job.source == "sonarr":
                if job.episode_id:
                    still_in_arr = f"episode_{job.episode_id}" in sonarr_queue
                elif job.sonarr_id:
                    still_in_arr = f"series_{job.sonarr_id}" in sonarr_queue

            if still_in_arr:
                job.status = "awaiting_postproc"
                job.progress = 1.0
                job.speed = 0.0
                job.eta = "Post-processing..."
            else:
                job.status = "completed"
                job.completed_at = time.time()
                job.progress = 1.0

                # Trigger post-processing for completed downloads
                await self._trigger_post_processing(job)

        elif status in ['failed', 'aborted']:
            job.status = "failed"
            job.completed_at = time.time()
            job.error_message = history_item.get('fail_message', 'Download failed')

    async def _trigger_post_processing(self, job: DownloadJob):
        """Trigger post-processing pipeline when a download completes."""
        try:
            import subprocess
            import sys
            from pathlib import Path

            # Path to the main orchestrator
            project_root = Path(__file__).parent.parent.parent
            orchestrator_path = project_root / "02_download_and_organize.py"

            if not orchestrator_path.exists():
                # Try alternative paths
                orchestrator_path = project_root / "src" / "main_pipeline_orchestrator.py"
                if not orchestrator_path.exists():
                    orchestrator_path = project_root / "main_pipeline_orchestrator.py"

            if not orchestrator_path.exists():
                self.logger.error(f"Orchestrator not found - cannot trigger post-processing for {job.title}")
                return False

            # Run the download and organize script directly with --all argument for automatic mode
            cmd = [
                sys.executable,
                str(orchestrator_path),
                "--all"  # Process both movies and TV shows in command-line mode (non-interactive)
            ]

            self.logger.info(f"🔄 Triggering post-processing for completed download: {job.title}")

            # Run the pipeline stage asynchronously (don't wait for completion)
            process = subprocess.Popen(
                cmd,
                cwd=str(project_root),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            self.logger.info(f"✅ Post-processing triggered for: {job.title} (PID: {process.pid})")
            return True

        except Exception as e:
            self.logger.error(f"❌ Failed to trigger post-processing for {job.title}: {e}")
            return False

    def _update_job_from_arr_queue(self, job: DownloadJob, arr_item: dict):
        """Update job from *arr queue item data."""
        try:
            # Update size and progress info from *arr
            size_total = arr_item.get('size', 0)
            size_left = arr_item.get('sizeleft', 0)
            size_downloaded = max(0, size_total - size_left)

            if size_total > 0:
                job.size_total = max(job.size_total, size_total)  # Use larger value
                job.progress = max(0.0, min(1.0, size_downloaded / size_total))

            # Check for errors or specific statuses
            arr_status = arr_item.get('status', '').lower()
            if 'error' in arr_status or 'failed' in arr_status:
                job.status = "failed"
                job.error_message = arr_item.get('errorMessage', 'Download failed in *arr')
                job.completed_at = time.time()

        except Exception as e:
            self.logger.error(f"Error updating job from *arr queue: {e}")

    def report_status(self):
        """Output current status of all active jobs via CLI and structured log."""
        if not self.active_jobs:
            return

        current_time = time.time()

        for job in list(self.active_jobs.values()):
            # Format CLI status line
            cli_line = self._format_cli_status(job)

            # Use \r for single job to overwrite line, \n for multiple jobs
            # Always print in one line per job and overwrite in place to reduce clutter
            print(f"\r{job.source.upper():6} | {cli_line}", end='', flush=True)

            # Log structured status update
            self._log_structured_event("progress_update", job)

            # Move completed/failed jobs to completed list
            if job.status in ("completed", "failed"):
                if not job.completed_at:
                    job.completed_at = current_time

                # Final status message
                if job.status == "completed":
                    print(f"\n✅ {job.title} - Download completed and imported!")
                    self._log_structured_event("download_completed", job)
                else:
                    error_msg = f" ({job.error_message})" if job.error_message else ""
                    print(f"\n❌ {job.title} - Download failed{error_msg}")
                    self._log_structured_event("download_failed", job)

                # Move to completed jobs
                self.completed_jobs[job.id] = job
                del self.active_jobs[job.id]

        # Persist state after any changes
        if self.active_jobs or self.completed_jobs:
            self._persist_state()

    def _format_cli_status(self, job: DownloadJob) -> str:
        """Format a CLI-friendly status line for a download job."""
        if job.status == "pending":
            return f"🔎 Verifying: {job.title} (attempt {job.verification_attempts}/{job.max_verification_attempts})"
        elif job.status == "failed":
            error_part = f" - {job.error_message}" if job.error_message else ""
            return f"❌ Failed: {job.title}{error_part}"
        elif job.status == "completed":
            duration = ""
            if job.completed_at and job.started_at:
                duration_sec = int(job.completed_at - job.started_at)
                duration = f" (took {duration_sec // 60}m {duration_sec % 60}s)"
            return f"✅ Completed: {job.title}{duration}"
        elif job.status == "awaiting_postproc":
            return f"📦 Processing: {job.title} - Download complete, importing..."
        elif job.status == "paused":
            size_str = self._format_size(job.size_total) if job.size_total > 0 else "unknown size"
            return f"⏸️ Paused: {job.title} - {job.progress:.1%} of {size_str}"
        else:  # downloading
            # Format progress information
            progress_pct = f"{job.progress:.1%}"
            size_str = self._format_size(job.size_total) if job.size_total > 0 else "unknown size"
            speed_str = self._format_speed(job.speed) if job.speed > 0 else "0 B/s"
            eta_str = job.eta if job.eta and job.eta != "Unknown" else "calculating..."

            return f"⏳ Downloading: {job.title} — {progress_pct} of {size_str} at {speed_str} (ETA {eta_str})"

    def report_status_dashboard(self):
        """Display download status in a clean dashboard format instead of verbose line-by-line output."""
        if not self.active_jobs:
            return
            
        # Start telemetry logging if not already started
        if not self._telemetry_started:
            self._start_telemetry_logging()
        
        # Redirect dashboard output to telemetry log file
        import sys
        if self._telemetry_log_file:
            original_stdout = sys.stdout
            sys.stdout = self._telemetry_log_file
        
        # Clear screen properly for PowerShell and other terminals
        import os
        if os.name == 'nt':  # Windows
            os.system('cls')
        else:  # Unix/Linux
            os.system('clear')
        
        # Group jobs by type
        downloading_jobs = []
        verification_jobs = []
        other_jobs = []
        
        for job in self.active_jobs.values():
            if job.status == "pending":
                verification_jobs.append(job)
            elif job.status in ["downloading", "paused"]:
                downloading_jobs.append(job)
            else:
                other_jobs.append(job)
        
        # Dashboard header
        print("┌─ DOWNLOAD PROGRESS DASHBOARD ─┐")
        
        # Download progress section
        if downloading_jobs:
            max_title_len = min(45, max(len(job.nzb_filename or job.title) for job in downloading_jobs))
            for job in downloading_jobs:
                # Use NZB filename if available, fallback to title
                display_name = job.nzb_filename or job.title
                title = display_name[:max_title_len] + "..." if len(display_name) > max_title_len else display_name
                title = title.ljust(max_title_len)
                
                progress = f"{job.progress:.1%}".rjust(6)
                size = self._format_size(job.size_total).rjust(8) if job.size_total > 0 else "Unknown ".rjust(8)
                
                if job.eta and job.eta != "Unknown":
                    eta = f"ETA: {job.eta}".rjust(12)
                else:
                    eta = "ETA: calc...".rjust(12)
                
                print(f"│ {title} │ {progress} │ {size} │ {eta} │")
        else:
            print("│ No active downloads                                                   │")
        
        # Verification section
        if verification_jobs:
            print("├─ VERIFICATION QUEUE ─────────────────────────────┤")
            for job in verification_jobs:
                # Use NZB filename if available, fallback to title
                display_name = job.nzb_filename or job.title
                title = display_name[:35] + "..." if len(display_name) > 35 else display_name
                title = title.ljust(40)
                max_attempts = job.max_verification_attempts
                if job.is_fallback_workflow:
                    max_attempts = 12  # Double for fallback workflows
                status = f"Pending (attempt {job.verification_attempts}/{max_attempts})".ljust(25)
                print(f"│ {title} │ {status} │")
        
        # Other jobs section
        if other_jobs:
            print("├─ OTHER STATUS ───────────────────────────────────┤")
            for job in other_jobs:
                # Use NZB filename if available, fallback to title
                display_name = job.nzb_filename or job.title
                title = display_name[:35] + "..." if len(display_name) > 35 else display_name
                title = title.ljust(40)
                
                if job.status == "completed":
                    status = "✅ Completed".ljust(25)
                elif job.status == "failed":
                    status = "❌ Failed".ljust(25)
                elif job.status == "awaiting_postproc":
                    status = "📦 Processing".ljust(25)
                else:
                    status = job.status.title().ljust(25)
                
                print(f"│ {title} │ {status} │")
        
        print("└───────────────────────────────────────────────────┘")
        
        # Restore stdout if we redirected it
        if self._telemetry_log_file and 'original_stdout' in locals():
            sys.stdout = original_stdout
        
        # Persist state after rendering
        self._persist_state()

    def report_status_grouped(self):
        """Render compact grouped status: Radarr first, then Sonarr, single-line per job."""
        if not self.active_jobs:
            return
        groups = {"radarr": [], "sonarr": [], "other": []}
        for job in self.active_jobs.values():
            grp = job.source if job.source in groups else "other"
            groups[grp].append(job)
        # Order: Radarr, Sonarr
        for src in ("radarr", "sonarr"):
            jobs = groups[src]
            if not jobs:
                continue
            print(f"\n[{src.upper()}]")
            for job in jobs:
                cli_line = self._format_cli_status(job)
                print(f"{src.upper():6} | {cli_line}")
        # Persist after rendering
        self._persist_state()

    def _format_size(self, bytes_size: int) -> str:
        """Format byte size in human-readable format."""
        if bytes_size < 1024:
            return f"{bytes_size} B"
        elif bytes_size < 1024 * 1024:
            return f"{bytes_size / 1024:.1f} KB"
        elif bytes_size < 1024 * 1024 * 1024:
            return f"{bytes_size / (1024 * 1024):.1f} MB"
        else:
            return f"{bytes_size / (1024 * 1024 * 1024):.1f} GB"

    def _format_speed(self, bytes_per_sec: float) -> str:
        """Format download speed in human-readable format."""
        if bytes_per_sec < 1024:
            return f"{bytes_per_sec:.0f} B/s"
        elif bytes_per_sec < 1024 * 1024:
            return f"{bytes_per_sec / 1024:.1f} KB/s"
        else:
            return f"{bytes_per_sec / (1024 * 1024):.1f} MB/s"

    def _log_structured_event(self, event_type: str, job: DownloadJob, extra_data: Optional[dict] = None):
        """Log a structured event for the download job."""
        log_data = {
            "timestamp": datetime.now().isoformat(),
            "event": event_type,
            "job_id": job.id,
            "title": job.title,
            "source": job.source,
            "status": job.status,
            "progress": job.progress,
            "size_total": job.size_total,
            "size_downloaded": job.size_downloaded,
            "speed_bps": job.speed,
            "eta": job.eta,
        }

        # Add IDs if available
        if job.radarr_id:
            log_data["radarr_id"] = job.radarr_id
        if job.sonarr_id:
            log_data["sonarr_id"] = job.sonarr_id
        if job.episode_id:
            log_data["episode_id"] = job.episode_id
        if job.sab_nzo_id:
            log_data["sab_nzo_id"] = job.sab_nzo_id
        if job.quality:
            log_data["quality"] = job.quality
        if job.indexer:
            log_data["indexer"] = job.indexer
        if job.error_message:
            log_data["error_message"] = job.error_message

        # Add duration for completed jobs
        if job.completed_at and job.started_at:
            log_data["duration_seconds"] = int(job.completed_at - job.started_at)

        # Add any extra data
        if extra_data:
            log_data.update(extra_data)

        self.logger.info(json.dumps(log_data))

    def _persist_state(self):
        """Save current state to disk for resilience."""
        try:
            # Helper function to serialize datetime objects
            def serialize_datetime(obj):
                if isinstance(obj, datetime):
                    return obj.isoformat()
                elif isinstance(obj, dict):
                    return {k: serialize_datetime(v) for k, v in obj.items()}
                elif isinstance(obj, list):
                    return [serialize_datetime(item) for item in obj]
                else:
                    return obj
            
            state_data = {
                "active_jobs": {job_id: asdict(job) for job_id, job in self.active_jobs.items()},
                "completed_jobs": {job_id: asdict(job) for job_id, job in self.completed_jobs.items()},
                # 🔧 FIX: Persist candidate information and fallback tracking
                # This was missing and caused fallback detection to fail
                "movie_candidates": serialize_datetime(self.movie_candidates),
                "tv_candidates": serialize_datetime(self.tv_candidates),
                "movie_failures": serialize_datetime(self.movie_failures),
                "tv_failures": serialize_datetime(self.tv_failures),
                "fallback_triggered": self.fallback_triggered,
                "last_updated": time.time()
            }

            with open(self.state_file, 'w') as f:
                json.dump(state_data, f, indent=2)

        except Exception as e:
            self.logger.error(f"Error persisting state: {e}")

    def _load_state(self):
        """Load persisted state from disk."""
        try:
            if self.state_file.exists():
                with open(self.state_file, 'r') as f:
                    state_data = json.load(f)

                # 🔥 SPAM FIX: Filter out stale test data and old entries
                cutoff_time = time.time() - self.state_max_age_sec  # configurable retention window (default 10 min)

                # Restore active jobs that weren't completed and aren't test data
                for job_id, job_data in state_data.get("active_jobs", {}).items():
                    job = DownloadJob(**job_data)

                    # Skip test data entries that cause spam
                    if any(test_name in job.title for test_name in ["Test Movie", "Example Movie", "Test Show"]):
                        self.logger.info(f"Skipping test data entry: {job.title}")
                        continue

                    # Skip very old entries that are likely stale
                    if job.last_updated < cutoff_time:
                        self.logger.info(f"Skipping old entry: {job.title} (last update {(time.time() - job.last_updated)/60:.1f} minutes ago)")
                        continue

                    # Only restore jobs that are still potentially active
                    if job.status not in ["completed", "failed"]:
                        self.active_jobs[job_id] = job

                # Restore recent completed jobs (limit to prevent memory bloat)
                completed_jobs = state_data.get("completed_jobs", {})
                # Keep only recent completed jobs (same retention as active)
                for job_id, job_data in completed_jobs.items():
                    job = DownloadJob(**job_data)
                    if job.completed_at and job.completed_at > cutoff_time:
                        # Skip test data in completed jobs too
                        if not any(test_name in job.title for test_name in ["Test Movie", "Example Movie", "Test Show"]):
                            self.completed_jobs[job_id] = job

                if self.active_jobs:
                    self.logger.info(f"Restored {len(self.active_jobs)} active download jobs from state")
                else:
                    self.logger.info("No valid active jobs found in state file")

                # 🔧 FIX: Restore candidate information and fallback tracking
                # This was missing and is critical for intelligent fallback detection
                
                # Restore movie candidates (with integer key conversion)
                movie_candidates_data = state_data.get("movie_candidates", {})
                for radarr_id_str, candidate_info in movie_candidates_data.items():
                    radarr_id = int(radarr_id_str)  # Convert string keys back to int
                    self.movie_candidates[radarr_id] = candidate_info
                
                # Restore TV candidates
                tv_candidates_data = state_data.get("tv_candidates", {})
                for sonarr_id_str, candidate_info in tv_candidates_data.items():
                    sonarr_id = int(sonarr_id_str)
                    self.tv_candidates[sonarr_id] = candidate_info
                
                # Restore failure tracking
                movie_failures_data = state_data.get("movie_failures", {})
                for radarr_id_str, failures in movie_failures_data.items():
                    radarr_id = int(radarr_id_str)
                    self.movie_failures[radarr_id] = failures
                
                tv_failures_data = state_data.get("tv_failures", {})
                for sonarr_id_str, failures in tv_failures_data.items():
                    sonarr_id = int(sonarr_id_str)
                    self.tv_failures[sonarr_id] = failures
                
                # Restore fallback trigger status
                fallback_triggered_data = state_data.get("fallback_triggered", {})
                for media_id_str, triggered in fallback_triggered_data.items():
                    media_id = int(media_id_str)
                    self.fallback_triggered[media_id] = triggered
                
                # Log what was restored
                if self.movie_candidates:
                    self.logger.info(f"✅ Restored {len(self.movie_candidates)} movie candidates from state")
                    for radarr_id, candidate in self.movie_candidates.items():
                        self.logger.info(f"   🎬 ID {radarr_id}: {candidate.get('movie_title', 'Unknown')}")
                else:
                    self.logger.info("No movie candidates found in state file")
                
                if self.tv_candidates:
                    self.logger.info(f"✅ Restored {len(self.tv_candidates)} TV candidates from state")
                
                # Log fallback status
                active_fallbacks = {k: v for k, v in self.fallback_triggered.items() if v}
                if active_fallbacks:
                    self.logger.info(f"📋 Active fallback statuses: {active_fallbacks}")
                    
                # 🧹 AUTO-CLEANUP: Remove excessive duplicates if found
                self._cleanup_duplicate_candidates()

        except Exception as e:
            self.logger.error(f"Error loading state: {e}")

    def stop(self):
        """Stop the monitoring loop."""
        self._running = False
        self._stop_telemetry_logging()  # Clean up telemetry logging
        self.logger.info("Telemetry monitoring stopped")

    def _start_telemetry_logging(self):
        """Create separate log file for telemetry output and redirect console output."""
        if self._telemetry_started:
            return
            
        # Create timestamped telemetry log filename with readable format
        from datetime import datetime
        import sys
        
        timestamp = datetime.now().strftime("%Y-%m-%d_%I-%M-%S-%p")
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        telemetry_log_path = log_dir / f"telemetry_dashboard_{timestamp}.txt"
        
        # Store original stdout before redirecting
        self._original_stdout = sys.stdout
        
        # Create telemetry log file
        self._telemetry_log_file = open(telemetry_log_path, 'w', encoding='utf-8', buffering=1)
        
        # Write header to telemetry log
        header = f"""=== REAL-TIME TELEMETRY DASHBOARD LOG ===
Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Log File: {telemetry_log_path}
==================================================

"""
        self._telemetry_log_file.write(header)
        self._telemetry_log_file.flush()
        
        # Log to main console about the split
        print(f"\n📊 Real-time telemetry monitoring started")
        print(f"📄 Telemetry dashboard log: {telemetry_log_path}")
        print(f"   (Dashboard output will be written to separate file to keep main log clean)\n")
        
        self._telemetry_started = True

    def _stop_telemetry_logging(self):
        """Stop telemetry logging and restore original stdout."""
        if not self._telemetry_started:
            return
            
        if self._telemetry_log_file:
            # Write footer
            from datetime import datetime
            footer = f"""

==================================================
=== TELEMETRY DASHBOARD LOG END ===
Ended: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
==================================================
"""
            self._telemetry_log_file.write(footer)
            self._telemetry_log_file.flush()
            self._telemetry_log_file.close()
            
        # Log to main console that telemetry is done
        if self._original_stdout:
            print("\n📊 Telemetry monitoring completed - dashboard log saved")
            
        self._telemetry_started = False

    def clear_stale_state(self):
        """Clear all stale test data and old entries from state."""
        original_count = len(self.active_jobs)

        # Remove test data and old entries
        cutoff_time = time.time() - (2 * 60 * 60)  # 2 hours
        stale_job_ids = []

        for job_id, job in self.active_jobs.items():
            # Remove test data
            if any(test_name in job.title for test_name in ["Test Movie", "Example Movie", "Test Show"]):
                stale_job_ids.append(job_id)
                continue

            # Remove old entries
            if job.started_at < cutoff_time:
                stale_job_ids.append(job_id)
                continue

        # Remove stale jobs
        for job_id in stale_job_ids:
            del self.active_jobs[job_id]

        # Save cleaned state
        self._persist_state()

        removed_count = len(stale_job_ids)
        self.logger.info(f"Cleared {removed_count} stale entries from telemetry state")
        return removed_count

    def get_summary(self) -> dict:
        """Get a summary of current download status."""
        return {
            "active_downloads": len(self.active_jobs),
            "completed_downloads": len(self.completed_jobs),
            "active_jobs": [
                {
                    "title": job.title,
                    "status": job.status,
                    "progress": job.progress,
                    "speed": job.speed
                }
                for job in self.active_jobs.values()
            ]
        }

    async def wait_for_completion(self, timeout: Optional[int] = None) -> bool:
        """
        Wait for all active downloads to complete.

        Args:
            timeout: Maximum time to wait in seconds (None for no timeout)

        Returns:
            True if all downloads completed, False if timeout reached
        """
        start_time = time.time()

        while self.active_jobs:
            if timeout and (time.time() - start_time) > timeout:
                return False

            await asyncio.sleep(5)
            await self.poll_queues()
            self.report_status()

        return True

    # ========== INTELLIGENT FALLBACK INTEGRATION METHODS ==========
    
    def store_movie_candidate_info(self, radarr_id: int, candidate_details: dict, 
                                  user_selection_index: Optional[int] = None,
                                  original_system_recommendation_index: Optional[int] = None):
        """Store candidate information for potential fallback."""
        
        movie_title = candidate_details.get('title', 'Unknown')
        
        # 🔧 PREVENT DUPLICATION: Check if we already have this movie
        existing_entries = []
        for existing_id, existing_candidate in self.movie_candidates.items():
            if existing_candidate.get('movie_title') == movie_title:
                existing_entries.append((existing_id, existing_candidate))
        
        if existing_entries:
            # Remove old entries for the same movie
            for old_id, old_candidate in existing_entries:
                del self.movie_candidates[old_id]
                self.logger.info(f"🗑️ Removed duplicate entry for '{movie_title}' (old ID: {old_id})")
        
        self.movie_candidates[radarr_id] = {
            "candidate_details": candidate_details,
            "user_selection_index": user_selection_index,
            "original_system_recommendation_index": original_system_recommendation_index,
            "stored_at": datetime.now().timestamp(),  # Use timestamp for JSON serialization
            "movie_title": movie_title  # Store title for lookup and deduplication
        }
        
        self.logger.info(f"📄 Stored candidate info for Radarr ID {radarr_id}")
        self.logger.info(f"   🎯 Candidate: {movie_title}")
        self.logger.info(f"   👤 User selection index: {user_selection_index}")
        if existing_entries:
            self.logger.info(f"   🧹 Replaced {len(existing_entries)} previous entries for same movie")
    
    def update_movie_radarr_id(self, old_radarr_id: int, new_radarr_id: int):
        """Update stored movie candidate info when Radarr ID changes (e.g., during quality fallback)."""
        if old_radarr_id in self.movie_candidates:
            # Move candidate info to new ID
            self.movie_candidates[new_radarr_id] = self.movie_candidates[old_radarr_id]
            del self.movie_candidates[old_radarr_id]
            
            # Update fallback tracking
            if old_radarr_id in self.fallback_triggered:
                self.fallback_triggered[new_radarr_id] = self.fallback_triggered[old_radarr_id]
                del self.fallback_triggered[old_radarr_id]
            
            # Update failure tracking
            if old_radarr_id in self.movie_failures:
                self.movie_failures[new_radarr_id] = self.movie_failures[old_radarr_id]
                del self.movie_failures[old_radarr_id]
            
            self.logger.info(f"🔄 Updated movie candidate info: Radarr ID {old_radarr_id} → {new_radarr_id}")
            return True
        else:
            self.logger.warning(f"⚠️ No candidate info found for old Radarr ID {old_radarr_id}")
            return False
    
    async def _check_for_failures_and_trigger_fallbacks(self):
        """Check for download failures and trigger intelligent fallbacks."""
        
        # Initialize fallback system if needed
        if not self.fallback_system:
            try:
                # Fix import path to work from current directory structure
                import sys
                import os
                sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))
                from _internal.src.intelligent_fallback_system import IntelligentFallbackSystem
                self.fallback_system = IntelligentFallbackSystem(self.config, self.logger, self)
                self.logger.info("🛡️ Intelligent fallback system initialized with telemetry integration")
            except ImportError as e:
                self.logger.warning(f"Could not import fallback system: {e}")
                return
        
        # Check each active job for failure patterns (Patterns 1 & 2)
        for job_id, job in list(self.active_jobs.items()):
            radarr_id = job.radarr_id
            
            if not radarr_id:
                continue
            
            # Get previous status for comparison
            prev_job = self.previous_job_status.get(job_id)
            
            if prev_job:
                # Pattern 1: Job was downloading with progress, now failed/disappeared
                if (prev_job.progress > 0.1 and prev_job.progress < 1.0 and 
                    job.status in ['failed', 'error', 'cancelled']):
                    
                    await self._handle_detected_failure(
                        job, f"failed_at_{prev_job.progress*100:.1f}%", radarr_id
                    )
                
                # Pattern 2: Job status changed to failed
                elif (job.status in ['failed', 'error'] and 
                      prev_job.status not in ['failed', 'error']):
                    
                    await self._handle_detected_failure(
                        job, "status_changed_to_failed", radarr_id
                    )
        
        # Pattern 3: Check for jobs that disappeared from queue (CRITICAL: Must be outside active_jobs loop!)
        for prev_job_id, prev_job in self.previous_job_status.items():
            if (prev_job_id not in self.active_jobs and 
                prev_job.radarr_id and 
                prev_job.progress > 0.1 and prev_job.progress < 1.0):
                
                self.logger.warning(f"🚨 BATCH MODE BUG FIX: Detected disappeared job {prev_job.title}")
                await self._handle_detected_failure(
                    prev_job, "job_disappeared_from_queue", prev_job.radarr_id
                )
        
        # Update previous status for next check
        self.previous_job_status = {job_id: job for job_id, job in self.active_jobs.items()}
    
    async def _handle_detected_failure(self, failed_job: DownloadJob, failure_reason: str, radarr_id: int):
        """Handle detected failure with intelligent fallback."""
        
        # RACE CONDITION FIX: Use mutex to prevent concurrent fallback operations
        async with self._fallback_mutex:
            # Check if fallback already triggered for this movie
            if radarr_id in self.fallback_triggered and self.fallback_triggered[radarr_id]:
                self.logger.info(f"🔄 Fallback already triggered for radarr_id {radarr_id}, skipping duplicate")
                return
            
            movie_title = failed_job.title
            progress = getattr(failed_job, 'progress', 0) * 100
            
            self.logger.error(f"💥 DOWNLOAD FAILURE DETECTED: {movie_title}")
            self.logger.error(f"   📊 Progress when failed: {progress:.1f}%") 
            self.logger.error(f"   🚨 Failure reason: {failure_reason}")
            self.logger.error(f"   🎬 Radarr ID: {radarr_id}")
            
            # Record failure
            if radarr_id not in self.movie_failures:
                self.movie_failures[radarr_id] = []
            
            failure_record = {
                "failed_at": datetime.now(),
                "failure_reason": failure_reason,
                "progress_when_failed": progress,
                "candidate_guid": getattr(failed_job, 'download_client_id', 'unknown'),
                "job_id": failed_job.id
            }
            
            self.movie_failures[radarr_id].append(failure_record)
            
            # Get stored candidate info for fallback
            candidate_info = self.movie_candidates.get(radarr_id)
        
        if candidate_info and self.fallback_system:
            try:
                self.logger.info(f"🔄 Triggering intelligent fallback for {movie_title}")
                
                # RACE CONDITION FIX: Use dedicated session for each fallback to prevent conflicts
                async with aiohttp.ClientSession() as dedicated_session:
                    success = await self.fallback_system.handle_download_failure(
                        movie_title=movie_title,
                        failed_guid=failure_record["candidate_guid"],
                        radarr_id=radarr_id,
                        session=dedicated_session,
                        user_selection_index=candidate_info.get("user_selection_index"),
                        original_system_recommendation_index=candidate_info.get("original_system_recommendation_index")
                    )
                
                if success:
                    # Only mark fallback as triggered when it SUCCEEDS
                    self.fallback_triggered[radarr_id] = True
                    self.logger.info(f"✅ Intelligent fallback triggered successfully for {movie_title}")
                    
                    # Update dashboard to show fallback triggered
                    if hasattr(self, '_telemetry_log_file') and self._telemetry_log_file:
                        timestamp = datetime.now().strftime("%H:%M:%S")
                        fallback_message = f"[{timestamp}] 🔄 FALLBACK TRIGGERED: {movie_title} (Attempt #{len(self.movie_failures[radarr_id])})"
                        self._telemetry_log_file.write(fallback_message + "\n")
                        self._telemetry_log_file.flush()
                else:
                    self.logger.error(f"❌ Intelligent fallback failed for {movie_title}")
                    # Don't mark as triggered - allow retries since this attempt failed
                    
            except Exception as e:
                self.logger.error(f"Error triggering fallback for {movie_title}: {e}")
        else:
            self.logger.warning(f"⚠️  No candidate info stored for {movie_title} - cannot trigger fallback")
            if not candidate_info:
                self.logger.warning("   💡 Make sure to call store_movie_candidate_info() when adding movies")
    
    def get_failure_history(self, radarr_id: int) -> List[dict]:
        """Get failure history for a specific movie."""
        return self.movie_failures.get(radarr_id, [])
    
    def get_attempt_count(self, radarr_id: int) -> int:
        """Get the number of attempts made for a specific movie."""
        return len(self.movie_failures.get(radarr_id, [])) + 1  # +1 for current attempt

    def _cleanup_duplicate_candidates(self):
        """
        Remove duplicate candidate entries for the same movie title.
        Keeps only the most recent entry per movie.
        """
        from collections import defaultdict
        
        # Group by movie title
        title_groups = defaultdict(list)
        for radarr_id, candidate in self.movie_candidates.items():
            title = candidate.get("movie_title", "Unknown")
            title_groups[title].append((radarr_id, candidate))
        
        # Find and remove duplicates
        duplicates_removed = 0
        for title, entries in title_groups.items():
            if len(entries) > 1:
                # Sort by stored_at timestamp (or radarr_id if no timestamp), keep latest
                entries.sort(key=lambda x: (
                    x[1].get("stored_at", 0),  # timestamp first
                    x[0]  # then radarr_id as fallback
                ), reverse=True)
                
                # Keep the first (most recent), remove the rest
                to_keep = entries[0]
                to_remove = entries[1:]
                
                for radarr_id, candidate in to_remove:
                    del self.movie_candidates[radarr_id]
                    duplicates_removed += 1
                
                if duplicates_removed > 0:
                    self.logger.info(f"🧹 Auto-cleanup: Removed {len(to_remove)} duplicate entries for '{title}'")
                    self.logger.info(f"   ✅ Kept ID {to_keep[0]} (most recent)")
        
        if duplicates_removed > 0:
            self.logger.info(f"🧹 Total duplicates auto-removed: {duplicates_removed}")
            # Persist the cleaned state
            self._persist_state()
    
    # ============================================================


# Example usage function
async def example_usage():
    """Example of how to use the real-time telemetry system."""

    # Configuration (would come from settings.ini)
    config = {
        "Sonarr": {
            "enabled": True,
            "url": "http://localhost:8989",
            "api_key": "your_sonarr_api_key"
        },
        "Radarr": {
            "enabled": True,
            "url": "http://localhost:7878",
            "api_key": "your_radarr_api_key"
        },
        "SABnzbd": {
            "enabled": True,
            "base_url": "http://localhost:8080",
            "api_key": "your_sabnzbd_api_key"
        },
        "telemetry_state_path": "_internal/state/telemetry_state.json"
    }

    # Set up logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger("telemetry")

    # Use telemetry system
    async with RealTimeTelemetry(config, logger) as telemetry:
        # Track some downloads
        job1_id = telemetry.track_new_download("Example Movie (2023)", radarr_id=123)
        job2_id = telemetry.track_new_download("Show Name S02E05", sonarr_id=456, episode_id=789)

        # Start monitoring
        await telemetry.start_monitoring(interval=5)

        # Get final summary
        summary = telemetry.get_summary()
        print(f"Final summary: {summary}")


if __name__ == "__main__":
    asyncio.run(example_usage())
