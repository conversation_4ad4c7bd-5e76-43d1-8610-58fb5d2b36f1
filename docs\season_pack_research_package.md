# Season Pack Download Failure Research Package

## Problem Statement

**CRITICAL ISSUE**: Season pack downloads are failing with **HTTP 400 errors** due to invalid/expired download URLs. This is preventing efficient bulk episode downloads for TV series.

## What We're Trying to Accomplish

1. **Season Pack Detection**: Identify when a season pack (complete season in one file) is available and better than individual episodes
2. **Season Pack Download**: Successfully download season packs via Sonarr API
3. **Fallback Logic**: If season pack fails, fall back to individual episode downloads
4. **URL Validation**: Ensure download URLs are valid before attempting downloads

## Current Architecture

### Core Components:
- **Preflight Analyzer**: Analyzes and ranks releases (individual episodes vs season packs)
- **Sonarr Integration**: Handles TV series management and downloads via Sonarr API
- **Download Logic**: Manages the actual download process through Sonarr → SABnzbd
- **Indexer Integration**: Fetches releases from multiple indexers (NZBHydra2, etc.)

### Current Flow:
1. **Analysis Phase**: Preflight analyzer evaluates all available releases
2. **Decision Phase**: Chooses between season pack or individual episodes
3. **Download Phase**: Attempts to download chosen releases via Sonarr API
4. **Monitoring Phase**: Tracks download progress via SABnzbd

## The Problem in Detail

### HTTP 400 Error Context:
- **Where**: Season pack downloads via `POST /api/v3/release` or `POST /api/v3/release/push`
- **When**: After preflight analyzer identifies a good season pack
- **Error**: HTTP 400 with various error messages (invalid GUID, expired URL, etc.)
- **Impact**: Falls back to individual episodes, losing efficiency benefits

### Suspected Root Causes:
1. **Stale Download URLs**: URLs expire between analysis and download attempt
2. **Invalid GUID Format**: GUID structure doesn't match Sonarr expectations
3. **Missing Indexer Mapping**: Incorrect indexer ID or configuration
4. **API Payload Issues**: Malformed request data for season pack downloads
5. **Release Metadata Problems**: Missing or incorrect release metadata

## Current Implementation Files

### Core Analysis Files:
- `preflight_analyzer/integrated_selector.py` - Main analysis and decision logic
- `preflight_analyzer/sonarr_client.py` - Sonarr API interactions
- `preflight_analyzer/shared_logic.py` - Release ranking and comparison

### Download Implementation:
- `01_intake_and_nzb_search.py` - Main download orchestration (lines 4000-4100)
- `_internal/mcp/sonarr_integration.py` - Sonarr MCP service
- `_internal/utils/telemetry_integration.py` - Download tracking

### Configuration:
- `_internal/config/settings.ini` - Sonarr/SABnzbd/Indexer settings
- `config/preflight_config.json` - Preflight analyzer settings

## Key Technical Details

### Sonarr API Endpoints Used:
- `GET /api/v3/release?episodeId={id}` - Fetch available releases
- `POST /api/v3/release/push` - Push release to download client (preferred)
- `POST /api/v3/release` - Alternative download method
- `POST /api/v3/command` - Trigger searches

### Download Payload Structure:
```json
{
  "title": "Series.Name.S01.1080p.WEB-DL.x264-GROUP",
  "downloadUrl": "https://indexer.com/api?t=get&id=12345&apikey=...",
  "protocol": "usenet",
  "publishDate": "2024-01-01T00:00:00Z",
  "guid": "12345-67890-abcdef",
  "indexer": "IndexerName",
  "size": ***********,
  "indexerId": 1
}
```

### Current Season Pack Detection Logic:
- Size thresholds (typically >5GB for season packs)
- Title pattern matching (S01, Season 1, etc.)
- Episode count estimation
- Quality comparison vs individual episodes

## Debugging Information Needed

### 1. **Release Metadata Analysis**
- How are season pack GUIDs generated/retrieved?
- What's the indexer mapping between preflight and Sonarr?
- Are download URLs being properly preserved?

### 2. **API Request/Response Debugging**
- Exact HTTP request payload that fails
- Complete HTTP response including headers
- Sonarr logs during failed attempts

### 3. **Timing Issues**
- Time between analysis and download attempt
- URL expiration timeframes
- Session/token management

### 4. **Indexer Integration**
- How does NZBHydra2 generate download URLs?
- Are there indexer-specific URL formats?
- How does indexer ID mapping work?

## Previous Attempts and Failures

### Attempts Made:
1. **URL Refresh**: Tried re-fetching URLs before download - still failed
2. **GUID Validation**: Validated GUID formats - didn't solve HTTP 400
3. **Indexer Mapping**: Verified indexer IDs - still problematic
4. **Payload Structure**: Adjusted request payloads - limited success
5. **Timing Adjustments**: Added delays between analysis and download

### Patterns Observed:
- Individual episode downloads work fine with same indexers
- Season packs fail consistently across different series
- HTTP 400 occurs regardless of indexer used
- Fallback to individual episodes succeeds

## Research Questions for Analysis

### 1. **Sonarr API Best Practices**
- What's the correct way to download season packs via Sonarr API?
- Are there specific requirements for season pack payloads?
- How should indexer integration work for bulk downloads?

### 2. **URL and GUID Management**
- How to ensure download URLs remain valid?
- What's the proper GUID format for season packs?
- How to handle URL expiration gracefully?

### 3. **Error Handling and Debugging**
- How to get more detailed error information from Sonarr?
- What logging should be implemented for debugging?
- How to detect and handle different failure modes?

### 4. **Alternative Approaches**
- Should we use different API endpoints for season packs?
- Would triggering automatic searches be more reliable?
- How do other automation tools handle season pack downloads?

## Expected Outcome

A robust season pack download implementation that:
1. **Reliably downloads season packs** when available and beneficial
2. **Handles URL expiration** and other failure modes gracefully
3. **Provides detailed error information** for debugging
4. **Falls back cleanly** to individual episodes when needed
5. **Integrates seamlessly** with existing preflight analyzer logic

## Success Metrics

- **Season pack success rate** >90% for valid releases
- **Clear error reporting** for failed attempts
- **Efficient fallback** to individual episodes
- **No HTTP 400 errors** for properly formatted requests
- **Improved download efficiency** for multi-episode requests

---

**Note**: This issue has been challenging to resolve due to the complexity of the Sonarr API, indexer integration, and timing-sensitive download URLs. A fresh analytical approach is needed to identify the root cause and implement a robust solution.
