# Key Files for Season Pack Analysis

## <PERSON><PERSON><PERSON><PERSON> FILES TO ANALYZE

### 1. Core Preflight Logic
- `preflight_analyzer/integrated_selector.py` (Lines 1-489)
  - Main analysis logic, season pack vs episode decision
  - Contains the `preflight_for_season()` function
  - Season pack ranking and selection logic

### 2. Sonarr API Integration  
- `preflight_analyzer/sonarr_client.py` (Full file)
  - Direct Sonarr API calls
  - Release fetching and processing
  - May contain season pack specific logic

### 3. Main Download Orchestration
- `01_intake_and_nzb_search.py` (Lines 4000-4100)
  - Season pack download implementation
  - HTTP 400 error likely occurs here
  - Contains the download payload construction

### 4. Sonarr MCP Service
- `_internal/mcp/sonarr_integration.py` (Lines 338-450)
  - Alternative Sonarr integration approach
  - May have different season pack handling
  - Could provide insights on proper API usage

### 5. Release Ranking Logic
- `preflight_analyzer/shared_logic.py` (Full file if exists)
  - Release comparison and ranking
  - Season pack vs episode scoring
  - Quality assessment logic

### 6. Configuration Files
- `_internal/config/settings.ini` (Sonarr/SABnzbd sections)
  - API endpoints and keys
  - Indexer configuration
  - May affect download URL generation

- `config/preflight_config.json` (Full file)
  - Preflight analyzer settings
  - Season pack preferences
  - Quality thresholds

### 7. Current Error Examples
- Any recent log files showing HTTP 400 errors
- Failed season pack download attempts
- Working individual episode download examples for comparison

## SPECIFIC SECTIONS TO FOCUS ON

### In `integrated_selector.py`:
- Lines 370-489: Season pack decision logic
- Season pack candidate processing
- GUID handling and URL management
- API payload construction for season packs

### In `01_intake_and_nzb_search.py`:
- Lines 4045-4070: Season pack download implementation  
- HTTP request construction for season packs
- Error handling and fallback logic
- Download URL and GUID processing

### In `sonarr_integration.py`:
- Season pack specific download methods
- API endpoint selection (push vs release)
- Payload formatting for different release types

## DEBUGGING INFORMATION NEEDED

### 1. HTTP Request Details
- Exact JSON payload sent to Sonarr
- Complete HTTP headers
- Request URL and method

### 2. HTTP Response Details  
- Full HTTP 400 response body
- Response headers
- Sonarr error messages

### 3. Release Metadata
- Season pack GUID format and source
- Download URL structure and expiration
- Indexer ID mapping

### 4. Timing Analysis
- Time between release analysis and download attempt
- URL generation timestamp vs download timestamp
- Session/authentication token validity

## QUESTIONS FOR ANALYSIS

1. **API Usage**: Is the Sonarr API being used correctly for season pack downloads?
2. **Payload Format**: Are there missing or incorrect fields in the download payload?
3. **GUID Handling**: Is the GUID format correct for season pack releases?
4. **URL Management**: How can download URL expiration be prevented or handled?
5. **Indexer Integration**: Is the indexer ID mapping correct for season packs?
6. **Alternative Approaches**: Should different API endpoints be used for season packs?

## EXPECTED DELIVERABLES

1. **Root Cause Analysis**: Exact reason for HTTP 400 errors
2. **Technical Solution**: Corrected API usage and payload format
3. **Implementation Plan**: Step-by-step fix instructions
4. **Testing Strategy**: How to verify the fix works
5. **Error Handling**: Improved error detection and fallback logic

---

**Priority**: This is a high-impact issue affecting download efficiency. Season packs can reduce 20+ individual downloads to 1 bulk download, significantly improving automation speed and reducing indexer load.
