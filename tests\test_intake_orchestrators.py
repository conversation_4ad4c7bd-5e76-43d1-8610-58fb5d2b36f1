import asyncio
import types


def test_movie_orchestrator_missing_key():
    from _internal.src.intake_movie_orchestrator import add_movie

    # settings missing key
    settings = {"Radarr": {}}

    async def run():
        return await add_movie({"cleaned_title": "Test", "year": 2020}, settings, logger=types.SimpleNamespace(info=print, error=print))

    result = asyncio.run(run())
    assert result["success"] is False
    assert result["reason"] == "missing_api_key"


def test_tv_orchestrator_missing_key():
    from _internal.src.intake_tv_orchestrator import add_tv_show

    settings = {"Sonarr": {}}

    async def run():
        return await add_tv_show({"cleaned_title": "Test TV", "year": 2020}, settings, logger=types.SimpleNamespace(info=print, error=print))

    result = asyncio.run(run())
    assert result["success"] is False
    assert result["reason"] == "missing_api_key"

