#!/usr/bin/env python3
"""
Season Progression Daemon (maintenance)

Merged functionality:
 - Original continuous season progression advancement
 - Optional light-weight season pack queue scanning / cleanup (legacy feature; generally superseded by preflight analyzer)

Environment / behavior controls:
  PACK_MONITOR_MODE:
      "none"        (default) only remove season packs immediately after advancing a season (original behavior)
      "scan_log"    log any season packs found in queue every scan interval (no removal)
      "scan_remove" actively remove & blocklist season packs on each scan (NOT recommended if preflight analyzer evaluates packs)
  PACK_SCAN_INTERVAL: integer number of main loop iterations between queue scans (default 10)

Rationale: Preflight analyzer now chooses between individual episodes and packs based on structural health; blanket
removal of packs is discouraged. The proactive removal modes are legacy / diagnostic only.
"""
from __future__ import annotations
import asyncio
import aiohttp
import logging
import os
import sys
import re
from pathlib import Path

sys.path.append(str(Path(__file__).parent.parent))  # project root

from _internal.utils.tv_season_progression_manager import TVSeasonProgressionManager  # type: ignore
from _internal.utils.common_helpers import get_setting, load_settings  # type: ignore

SEASON_PACK_PATTERNS = [
    r'\bS\d{1,2}(?![\dE])',
    r'\bS\d{1,2}\b(?!.*E\d)',
    r'\bSeason[\s\._-]*\d{1,2}',
    r'\bComplete[\s\._-]*Season',
    r'\bFull[\s\._-]*Season',
    r'\bS\d{1,2}\.',
    r'\bS\d{1,2}\s+\d{4}p',
    r'FLCL\.S\d{1,2}\.',
    r'\bS\d{1,2}\.\d{3,4}p',
]


async def scan_queue_for_season_packs(session: aiohttp.ClientSession, sonarr_url: str, headers: dict, logger: logging.Logger, mode: str) -> None:
    if mode == 'none':
        return
    try:
        async with session.get(f"{sonarr_url}/api/v3/queue", headers=headers) as resp:
            if resp.status != 200:
                logger.debug(f"Season pack scan skipped: queue HTTP {resp.status}")
                return
            queue = await resp.json()
            records = queue.get('records', [])
    except Exception as e:  # noqa
        logger.debug(f"Queue scan error: {e}")
        return

    season_packs = []
    for item in records:
        title = item.get('title', '')
        for pat in SEASON_PACK_PATTERNS:
            if re.search(pat, title, re.IGNORECASE):
                season_packs.append(item)
                break

    if not season_packs:
        return
    if mode == 'scan_log':
        for sp in season_packs:
            logger.info(f"📦 Season pack present (log only): {sp.get('title')}")
        return

    # scan_remove
    for sp in season_packs:
        title = sp.get('title', '')
        item_id = sp.get('id')
        try:
            params = {'removeFromClient': 'true', 'blocklist': 'true'}
            async with session.delete(f"{sonarr_url}/api/v3/queue/{item_id}", headers=headers, params=params) as dr:
                if dr.status in (200, 204):
                    logger.warning(f"🗑️ Removed season pack (scan_remove mode): {title}")
                else:
                    logger.warning(f"Failed to remove season pack {title}: HTTP {dr.status}")
        except Exception as e:  # noqa
            logger.warning(f"Error removing season pack {title}: {e}")


async def remove_season_packs_for_series(session, sonarr_url: str, headers: dict, series_id: int, logger: logging.Logger):
    """One-shot cleanup after advancing a season (legacy behavior)."""
    try:
        async with session.get(f"{sonarr_url}/api/v3/queue", headers=headers) as resp:
            if resp.status != 200:
                return
            queue = await resp.json()
            records = queue.get('records', [])
    except Exception:
        return
    for item in records:
        if item.get('seriesId') != series_id:
            continue
        title = item.get('title', '')
        for pat in SEASON_PACK_PATTERNS:
            if re.search(pat, title, re.IGNORECASE):
                try:
                    params = {'removeFromClient': 'true', 'blocklist': 'true'}
                    async with session.delete(f"{sonarr_url}/api/v3/queue/{item.get('id')}", headers=headers, params=params) as dr:
                        if dr.status in (200, 204):
                            logger.info(f"🗑️ Removed season pack after advancement: {title}")
                except Exception:
                    pass
                break


async def continuous_progression_monitor():
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    logger = logging.getLogger('progression')

    settings_dict = load_settings()
    sonarr_url = get_setting('Sonarr', 'url', settings_dict=settings_dict, default='http://localhost:8989')
    sonarr_api_key = get_setting('Sonarr', 'api_key', settings_dict=settings_dict)
    if not sonarr_api_key:
        logger.error("Sonarr API key not configured")
        return

    pack_mode = os.environ.get('PACK_MONITOR_MODE', 'none').lower()
    scan_interval = int(os.environ.get('PACK_SCAN_INTERVAL', '10') or '10')

    progression_mgr = TVSeasonProgressionManager(logger)
    sequential_file = Path('config/sequential_series.txt')  # Optional list; if absent loop still runs
    logger.info("🚀 Progression daemon started (pack monitor mode=%s)" % pack_mode)

    check_count = 0
    headers = {'X-Api-Key': sonarr_api_key}

    try:
        while True:
            check_count += 1
            opt_in_titles = set()
            if sequential_file.exists():
                try:
                    opt_in_titles = {ln.strip() for ln in sequential_file.read_text(encoding='utf-8').splitlines() if ln.strip() and not ln.startswith('#')}
                except Exception as e:  # noqa
                    logger.warning(f"Failed reading config/sequential_series.txt: {e}")

            timeout = aiohttp.ClientTimeout(total=30)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                # Optional pack scan (global) every scan_interval loops
                if pack_mode != 'none' and (check_count % scan_interval == 0):
                    await scan_queue_for_season_packs(session, sonarr_url, headers, logger, pack_mode)

                # fetch all series once per loop
                try:
                    async with session.get(f"{sonarr_url}/api/v3/series", headers=headers) as resp:
                        if resp.status != 200:
                            logger.warning(f"Series list HTTP {resp.status}")
                            await asyncio.sleep(30)
                            continue
                        all_series = await resp.json()
                except Exception as e:  # noqa
                    logger.error(f"Series fetch error: {e}")
                    await asyncio.sleep(30)
                    continue

                for series_obj in all_series:
                    series_title = series_obj.get('title')
                    series_year = series_obj.get('year')
                    display = f"{series_title} ({series_year})" if series_year else series_title
                    if opt_in_titles and display not in opt_in_titles:
                        continue
                    try:
                        series_id = series_obj.get('id')
                        async with session.get(f"{sonarr_url}/api/v3/episode?seriesId={series_id}", headers=headers) as ep_resp:
                            if ep_resp.status != 200:
                                continue
                            episodes = await ep_resp.json()
                        key = await progression_mgr.init_if_needed(series_obj, episodes, opt_in_titles)
                        if not key:
                            continue
                        state = progression_mgr.load(key)
                        current_season = state.get('current_season', 1)
                        if check_count % 20 == 1:
                            logger.info(f"📺 {display}: Season {current_season}")
                        if await progression_mgr.is_current_season_complete(key, sonarr_url, sonarr_api_key):
                            logger.info(f"✅ {display}: Season {current_season} complete")
                            await progression_mgr.advance(key, sonarr_url, sonarr_api_key)
                            await remove_season_packs_for_series(session, sonarr_url, headers, series_id, logger)
                            new_state = progression_mgr.load(key)
                            if new_state.get('series_complete'):
                                logger.info(f"🎉 {display}: SERIES COMPLETE")
                            else:
                                logger.info(f"⏭️ {display}: Advanced to Season {new_state.get('current_season')}")
                    except Exception as e:  # noqa
                        logger.error(f"Error processing {display}: {e}")

            await asyncio.sleep(30)
    except KeyboardInterrupt:
        logger.info("Stopping progression daemon (keyboard interrupt)")
    except Exception as e:  # noqa
        logger.error(f"Fatal progression daemon error: {e}")


if __name__ == '__main__':
    asyncio.run(continuous_progression_monitor())
