#!/usr/bin/env python3
"""Quick viewer for recent preflight decision artifacts.
Usage: python -m preflight_analyzer.preflight_decision_viewer --limit 5
"""
from __future__ import annotations
import argparse
import json
from pathlib import Path

def main():
    p = argparse.ArgumentParser()
    p.add_argument('--dir', default='workspace/preflight_decisions', help='Decision artifacts directory')
    p.add_argument('--limit', type=int, default=5, help='How many most recent artifacts to show')
    args = p.parse_args()
    d = Path(args.dir)
    if not d.exists():
        print(f"No decision directory: {d}")
        return
    files = sorted(d.glob('*.json'), key=lambda f: f.stat().st_mtime, reverse=True)[:args.limit]
    for f in files:
        print(f"=== {f.name} ===")
        try:
            data = json.loads(f.read_text(encoding='utf-8'))
            stats = data.get('stats', {})
            strategy = data.get('strategy')
            print(f"Strategy: {strategy} Episodes accepted: {stats.get('episodes_accepted')}/{stats.get('episodes_total')} ({stats.get('accept_fraction'):.2% if stats.get('episodes_total') else 0})")
            best = data.get('best') or {}
            if best:
                print(f"Best: {best.get('title')} decision={best.get('decision')} risk={best.get('risk_score')} missing={best.get('probe_missing_ratio')}")
            plan = data.get('plan') or {}
            if plan:
                print(f"Plan episodes: {len(plan.get('episodes') or [])} pack: {bool(plan.get('pack'))}")
        except Exception as e:  # noqa
            print(f"Failed to parse {f.name}: {e}")

if __name__ == '__main__':
    main()
