import math
import random
from dataclasses import dataclass
from typing import List
from .nzb_parser import NZBMetadata, NZBFile

@dataclass
class SamplePlan:
    total_population: int
    sample_size: int
    selected_indices: List[int]

DEFAULT_CONFIDENCE_Z = 1.96  # 95%

class SegmentSampler:
    def __init__(self, expected_proportion: float = 0.99, margin_error: float = 0.01, z_score: float = DEFAULT_CONFIDENCE_Z):
        self.p = expected_proportion
        self.e = margin_error
        self.z = z_score

    def compute_sample_size(self, population: int) -> int:
        # Standard proportion sample size formula with finite population correction
        if population <= 0:
            return 0
        numerator = (self.z ** 2) * self.p * (1 - self.p)
        denom = (self.e ** 2)
        n0 = numerator / denom
        n = (n0 * population) / (n0 + population - 1)
        return min(population, max(10, int(n)))

    def plan(self, metadata: NZBMetadata) -> SamplePlan:
        population = metadata.data_segments
        sample_size = self.compute_sample_size(population)
        # build index list of data segments (exclude par2 segments for availability sampling)
        data_indices = []
        idx = 0
        for f in metadata.files:
            if f.is_par2:
                idx += len(f.segments)
                continue
            for _ in f.segments:
                data_indices.append(idx)
                idx += 1
        if not data_indices:
            return SamplePlan(total_population=0, sample_size=0, selected_indices=[])
        if sample_size >= len(data_indices):
            selected = data_indices
        else:
            # stratified: always first, middle, last
            selected = {data_indices[0], data_indices[len(data_indices)//2], data_indices[-1]}
            while len(selected) < sample_size:
                selected.add(random.choice(data_indices))
            selected = sorted(selected)
        return SamplePlan(total_population=population, sample_size=len(selected), selected_indices=selected)
