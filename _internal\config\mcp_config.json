{"global": {"enabled": true, "max_concurrent_connections": 10, "default_timeout": 30, "retry_backoff_factor": 2.0, "health_check_interval": 300, "log_level": "INFO", "cache_enabled": true, "cache_ttl_seconds": 3600}, "servers": {"sequential_thinking": {"name": "sequential_thinking", "server_type": "sequential_thinking", "enabled": true, "connection_url": "", "api_key": "", "api_secret": "", "additional_config": {"max_steps": 50, "step_timeout": 60, "auto_breakdown": true}, "max_retries": 3, "timeout_seconds": 30, "rate_limit_per_minute": 60, "priority": 1}, "memory_manager": {"name": "memory_manager", "server_type": "memory", "enabled": true, "connection_url": "", "api_key": "", "api_secret": "", "additional_config": {"max_memories": 1000, "memory_ttl_days": 30, "auto_cleanup": true}, "max_retries": 3, "timeout_seconds": 30, "rate_limit_per_minute": 60, "priority": 2}, "github_integration": {"name": "github_integration", "server_type": "github", "enabled": true, "connection_url": "", "api_key": "****************************************", "api_secret": "", "additional_config": {"repository": "charleso99/MCPlex-plex", "auto_create_issues": true, "issue_labels": ["bug", "automation", "pipeline"]}, "max_retries": 3, "timeout_seconds": 30, "rate_limit_per_minute": 60, "priority": 3}, "notion_database": {"name": "notion_database", "server_type": "notion", "enabled": true, "connection_url": "", "api_key": "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", "api_secret": "", "additional_config": {"database_id": "", "auto_sync": true, "sync_interval": 300}, "max_retries": 3, "timeout_seconds": 30, "rate_limit_per_minute": 60, "priority": 4}, "firecrawl_scraper": {"name": "firecrawl_scraper", "server_type": "firecrawl", "enabled": true, "connection_url": "", "api_key": "fc-b0a6491abc87429d902a0ca06b27912d", "api_secret": "", "additional_config": {"max_pages": 10, "respect_robots": true, "delay_ms": 1000}, "max_retries": 3, "timeout_seconds": 30, "rate_limit_per_minute": 60, "priority": 5}, "image_processor": {"name": "image_processor", "server_type": "imagesorcery", "enabled": true, "connection_url": "", "api_key": "", "api_secret": "", "additional_config": {"max_image_size": "2048x2048", "quality": 85, "formats": ["jpg", "png", "webp"]}, "max_retries": 3, "timeout_seconds": 30, "rate_limit_per_minute": 60, "priority": 6}, "radarr_integration": {"name": "radarr_integration", "server_type": "radarr", "enabled": true, "connection_url": "http://localhost:7878", "api_key": "6ba9b679124f4414b3c4b93756decab8", "api_secret": "", "additional_config": {"quality_profiles": {"pre_2010": "Pre-2010 Movies", "golden_era": "Golden Era Movies", "modern": "Modern Movies"}, "download_client": "sabnzbd", "auto_download": true, "monitor_downloads": true}, "max_retries": 3, "timeout_seconds": 30, "rate_limit_per_minute": 60, "priority": 7}, "sonarr_integration": {"name": "sonarr_integration", "server_type": "sonarr", "enabled": true, "connection_url": "http://localhost:8989", "api_key": "745e39af03d3443c989632c27a0fcd47", "api_secret": "", "additional_config": {"quality_profiles": {"tv_720p": "TV 720p Quality", "tv_hd": "TV HD Quality", "tv_uhd": "TV Ultra-HD Quality"}, "year_based_quality_profiles": {"tv_720p_quality_profile_id": 3, "tv_hd_quality_profile_id": 4, "tv_uhd_quality_profile_id": 5}, "download_client": "sabnzbd", "auto_monitor": true, "season_folders": true, "year_based_quality_enabled": true}, "max_retries": 3, "timeout_seconds": 30, "rate_limit_per_minute": 60, "priority": 8}, "download_monitor": {"name": "download_monitor", "server_type": "download_monitor", "enabled": true, "connection_url": "", "api_key": "", "api_secret": "", "additional_config": {"monitor_interval": 30, "max_history": 100, "auto_start": true, "sabnzbd_url": "http://localhost:8080", "sabnzbd_api_key": "91f5bd2c786d4886bd0a1072856719c9"}, "max_retries": 3, "timeout_seconds": 30, "rate_limit_per_minute": 60, "priority": 9}, "notification_system": {"name": "notification_system", "server_type": "notification_system", "enabled": true, "connection_url": "", "api_key": "", "api_secret": "", "additional_config": {"channels": {"discord": {"enabled": false, "webhook_url": "https://discord.com/api/webhooks/YOUR_WEBHOOK_URL"}, "slack": {"enabled": false, "webhook_url": "https://hooks.slack.com/services/YOUR_WEBHOOK_URL"}, "email": {"enabled": false, "smtp_server": "smtp.gmail.com", "smtp_port": 587, "username": "<EMAIL>", "password": "your_app_password", "to_emails": ["<EMAIL>"]}, "desktop": {"enabled": true, "timeout": 10}}}, "max_retries": 3, "timeout_seconds": 30, "rate_limit_per_minute": 60, "priority": 10}}, "last_updated": "2025-07-14T11:45:01.123734+00:00", "version": "1.0.0"}