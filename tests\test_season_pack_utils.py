import unittest
from pathlib import Path

from _internal.src.season_pack_utils import group_by_folder, detect_season_pack, validate_episodes
from _internal.utils.tv_show_naming import TVShowNamingHelper


class TestSeasonPackUtils(unittest.TestCase):
    def setUp(self):
        self.h = TVShowNamingHelper()

    def test_group_by_folder(self):
        files = [
            Path("/tmp/Show.S01/Show.S01E01.mkv"),
            Path("/tmp/Show.S01/Show.S01E02.mkv"),
            Path("/tmp/Other/Other.S01E01.mkv"),
        ]
        grouped = group_by_folder(files)
        self.assertEqual(len(grouped), 2)
        self.assertEqual(len(grouped[Path("/tmp/Show.S01")]), 2)
        self.assertEqual(len(grouped[Path("/tmp/Other")]), 1)

    def test_detect_season_pack_true(self):
        files = [
            Path("/tmp/FLCL.S01/FLCL.S01E01.mkv"),
            Path("/tmp/FLCL.S01/FLCL.S01E02.mkv"),
            Path("/tmp/FLCL.S01/FLCL.S01E03.mkv"),
        ]
        self.assertTrue(detect_season_pack(files, self.h))

    def test_detect_season_pack_false_single(self):
        files = [Path("/tmp/FLCL.S01/FLCL.S01E01.mkv")]
        self.assertFalse(detect_season_pack(files, self.h))

    def test_validate_single_season(self):
        files = [
            Path("/tmp/Show/Show.S01E01.mkv"),
            Path("/tmp/Show/Show.S01E02.mkv"),
            Path("/tmp/Show/Show.S01E04.mkv"),
        ]
        valid, mode = validate_episodes(files, self.h)
        self.assertTrue(valid)
        self.assertEqual(mode, "single_season")

    def test_validate_multi_season(self):
        files = [
            Path("/tmp/Show/Show.S01E01.mkv"),
            Path("/tmp/Show/Show.S02E01.mkv"),
        ]
        valid, mode = validate_episodes(files, self.h)
        self.assertTrue(valid)
        self.assertEqual(mode, "multi_season")

    def test_validate_insufficient(self):
        files = [
            Path("/tmp/Show/Show.S01E01.mkv"),
            Path("/tmp/Show/sample.mkv"),
        ]
        valid, mode = validate_episodes(files, self.h)
        self.assertFalse(valid)
        self.assertEqual(mode, "insufficient")

