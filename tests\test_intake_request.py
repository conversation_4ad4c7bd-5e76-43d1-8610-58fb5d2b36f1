import asyncio
import unittest

from _internal.src.intake_request import process_new_requests


class DummyLogger:
    def __getattr__(self, name):
        def _log(*args, **kwargs):
            pass
        return _log


class TestIntakeRequest(unittest.TestCase):
    def test_process_empty_requests(self):
        logger = DummyLogger()
        res = asyncio.run(process_new_requests([], {}, logger))
        self.assertEqual(res["movies"]["success"], 0)
        self.assertEqual(res["tv"]["success"], 0)
        self.assertEqual(len(res["details"]), 0)


if __name__ == "__main__":
    unittest.main()

