from __future__ import annotations
from datetime import datetime, timezone
from typing import Any, Dict, List

from .nzb_parser import NZBMetadata
from .segment_sampler import SegmentSampler
from .async_nntp_probe import AsyncNNTPProbe, NNTPServerConfig
from .risk_scoring import RiskScorer

async def analyze_metadata(meta: NZBMetadata, *, servers: List[NNTPServerConfig], retention_days: int,
                     dry_run: bool, verbose: bool = False, sample_cap: int = 150) -> Dict[str, Any]:
    """Analyze already-parsed NZB metadata (no disk IO)."""
    sampler = SegmentSampler()
    sample_plan = sampler.plan(meta)

    # Build flattened segment list for sampling indices
    message_ids: List[str] = []
    if not dry_run:
        current_index = 0
        for f in meta.files:
            if f.is_par2:
                current_index += len(f.segments)
                continue
            for seg in f.segments:
                if current_index in sample_plan.selected_indices:
                    message_ids.append(seg.message_id)
                current_index += 1
                if len(message_ids) >= sample_cap:
                    break
            if len(message_ids) >= sample_cap:
                break

    if dry_run:
        probe_results: Dict[str, Any] = {}
        missing_ratio = 0.0
        error_ratio = 0.0
    else:
        probe = AsyncNNTPProbe(servers, verbose=verbose)
        raw_results = await probe.probe_sample(message_ids)
        probe_results = {k: v.__dict__ for k, v in raw_results.items()}
        miss_ratios = [pr['missing'] / pr['total'] for pr in probe_results.values() if pr['total']]
        err_ratios = [pr['errors'] / pr['total'] for pr in probe_results.values() if pr['total']]
        missing_ratio = min(1.0, sum(miss_ratios)/len(miss_ratios)) if miss_ratios else 0.0
        error_ratio = min(1.0, sum(err_ratios)/len(err_ratios)) if err_ratios else 0.0

    poster = meta.files[0].poster if meta.files else ''
    groups = meta.files[0].groups if meta.files else []

    first_date = meta.files[0].date if meta.files else 0
    age_days = int((datetime.now(timezone.utc) - datetime.fromtimestamp(first_date, tz=timezone.utc)).days) if first_date else 0

    obfuscated = False
    for f in meta.files:
        if f.filename_hint and len(f.filename_hint) > 0:
            if sum(c.isalnum() for c in f.filename_hint) / max(1, len(f.filename_hint)) > 0.9 and f.filename_hint.count('.') < 2:
                obfuscated = True
                break

    redundancy_blocks = meta.estimated_parity_blocks
    data_segments = meta.data_segments
    scorer = RiskScorer()
    components = scorer.build_components(
        age_days=age_days,
        retention_days=retention_days,
        poster_fail_rate=0.0,
        group_fail_rate=0.0,
        redundancy_blocks=redundancy_blocks,
        data_segments=data_segments,
        probe_missing_ratio=missing_ratio,
        probe_error_ratio=error_ratio,
        obfuscated=obfuscated,
    )
    risk_score = scorer.score(components)
    sample_total = sample_missing = sample_available = 0
    if probe_results:
        first_pr = next(iter(probe_results.values()))
        sample_total = first_pr.get('total', 0)
        sample_available = first_pr.get('available', 0)
        sample_missing = first_pr.get('missing', 0)
    estimated_missing_segments = int(round(missing_ratio * data_segments)) if data_segments else 0
    if components.probe_error_ratio >= 0.5:
        decision = 'RETRY_PROBE'
    elif missing_ratio >= 0.3 and redundancy_blocks == 0:
        decision = 'REJECT_INCOMPLETE'
    elif missing_ratio >= 0.15 and redundancy_blocks < data_segments * 0.01:
        decision = 'RISKY_LOW_PARITY'
    else:
        decision = 'ACCEPT'
    return {
        'file_count': meta.file_count,
        'data_segments': data_segments,
        'par2_segments': meta.par2_segments,
        'estimated_parity_blocks': redundancy_blocks,
        'sample_size': sample_plan.sample_size,
        'probe_missing_ratio': missing_ratio,
        'probe_error_ratio': error_ratio,
        'risk_score': risk_score,
        'risk_level': scorer.classify(risk_score),
        'age_days': age_days,
        'poster': poster,
        'groups': groups,
        'probe_results': probe_results,
        'sample_total': sample_total,
        'sample_available': sample_available,
        'sample_missing': sample_missing,
        'estimated_missing_segments': estimated_missing_segments,
        'decision': decision,
        'components': components.__dict__,
        'dry_run': dry_run,
    }
