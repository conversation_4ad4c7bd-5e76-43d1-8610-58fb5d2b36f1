# PlexAutomator Analysis Improvement Plan
## Addressing Top Gun Maverick Download Failure Issues

### Current Issues Identified

#### 1. **Limited Candidate Analysis Coverage**
- **Problem**: Only scanning 25 out of 98 available releases for Top Gun Maverick
- **Impact**: Missing potentially better releases in remaining 73 candidates
- **Root Cause**: Fixed `max_candidates=25` limit regardless of available pool size

#### 2. **Missing Information During Analysis Phase**
- **Problem**: Analysis output only shows risk scores, not missing file percentages
- **Impact**: User cannot see why releases are rejected (e.g., "REJECT_INCOMPLETE" without context)
- **Root Cause**: Display logic doesn't include `probe_missing_ratio` during analysis

#### 3. **Probe Sampling vs Actual File Completeness Mismatch**
- **Problem**: Analysis showed 0.0% missing, but SABnzbd reported 56 missing files
- **Impact**: False confidence leading to download failures
- **Root Cause**: NNTP segment sampling doesn't always reflect actual file availability

#### 4. **Size Reporting Discrepancy**
- **Problem**: 108GB reported in analysis vs 56GB actual size in SABnzbd
- **Impact**: Inaccurate storage planning and quality assessment
- **Root Cause**: NZB metadata size vs actual importable content size difference

### Proposed Solutions

#### Solution 1: Dynamic Candidate Scanning
**Implementation**: Scan all available releases within quality filter instead of fixed limit

```ini
[SEARCH]
# Dynamic candidate analysis configuration
enable_dynamic_scanning = true
# Scan all candidates when pool size is reasonable (≤100)
max_dynamic_scan_threshold = 100
# Fallback to top candidates when pool is very large (>100)
large_pool_candidate_limit = 50
# Minimum candidates to analyze even for small pools
min_candidates_to_analyze = 10
```

#### Solution 2: Enhanced Analysis Display
**Implementation**: Show missing percentages and detailed rejection reasons

```python
# Enhanced output during analysis phase:
💾 18:43:20 Cache hit: Release.Name → REJECT_INCOMPLETE (risk: 0.0878, missing: 12.3%)
💾 18:43:20 Cache hit: Release.Name → RISKY_LOW_PARITY (risk: 0.0766, missing: 19.4%, parity: 2.1%)
```

#### Solution 3: Improved Probe Sampling
**Implementation**: Enhanced sampling strategy with validation

```python
# Multiple sampling strategies:
1. Stratified sampling across file segments
2. Critical file validation (largest files, main movie)
3. Cross-server probe verification
4. Post-download size validation
```

#### Solution 4: Multi-Stage Validation
**Implementation**: Add post-analysis validation steps

```python
# Validation pipeline:
1. NNTP probe analysis (current)
2. NZB file structure validation
3. Expected vs actual size estimation
4. Historical success rate for release group
```

### Implementation Priority

#### **Phase 1: Quick Wins (Immediate)**
1. ✅ Add SEARCH configuration section
2. ✅ Enable dynamic candidate scanning
3. ✅ Enhance analysis output display
4. ✅ Show missing percentages during analysis

#### **Phase 2: Analysis Improvements (1-2 weeks)**
1. 🔄 Implement stratified segment sampling
2. 🔄 Add file structure validation
3. 🔄 Cross-server probe verification
4. 🔄 Release group success tracking

#### **Phase 3: Deep Analysis (Research Phase)**
1. 📋 Machine learning risk prediction
2. 📋 Historical pattern analysis
3. 📋 Advanced size estimation algorithms
4. 📋 Release group reputation scoring

### Expected Outcomes

#### **Short Term (Phase 1)**
- ✅ Analyze all 98 available Top Gun candidates instead of just 25
- ✅ Clear visibility into why releases are rejected
- ✅ Better informed selection decisions
- ✅ Reduced false positives in acceptance

#### **Medium Term (Phase 2)**
- 🎯 Improved probe accuracy matching actual download success
- 🎯 Better size estimation accuracy
- 🎯 Reduced download failures
- 🎯 Enhanced release quality scoring

#### **Long Term (Phase 3)**
- 🚀 Predictive analysis preventing failures before download
- 🚀 Adaptive learning from success/failure patterns
- 🚀 Optimized release selection across all content types
- 🚀 Near-zero false positive acceptance rate

### Success Metrics

1. **Coverage**: Analyze >90% of quality-filtered releases when pool ≤100
2. **Accuracy**: Reduce false positive acceptance rate by >50%
3. **Transparency**: Show missing% and detailed rejection reasons for all candidates
4. **Success Rate**: Increase successful downloads from current ~95% to >98%

### Next Steps

1. **Implement Phase 1 changes** (dynamic scanning + enhanced display)
2. **Test with Top Gun Maverick** using all 98 candidates
3. **Validate improvements** with current movie queue
4. **Gather data** for Phase 2 analysis improvements
5. **Research Phase 3** advanced techniques if needed
