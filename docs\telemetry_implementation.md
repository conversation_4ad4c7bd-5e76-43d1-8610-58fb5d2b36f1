# Telemetry Implementation for Radarr Queue API Delays

## Overview

This implementation addresses the known issues with Radarr queue API delays and provides enhanced tracking for fallback download workflows. The key improvements include:

### Core Problems Addressed

1. **Radarr Queue API Delays**: Radarr's queue refresh runs approximately every 60-90 seconds, causing newly grabbed releases to not appear immediately in `/api/v3/queue`.

2. **Fallback Download Workflows**: Remove/re-add scenarios (4K → 1080p fallback) can result in new movie IDs and extended verification times.

3. **SABnzbd Duplicate Detection**: Downloads may skip the queue entirely if SABnzbd detects duplicates, going straight to history.

4. **Monitoring Status Issues**: Unmonitored movies/series don't appear in *arr queues, causing false negatives.

## Implementation Components

### 1. Enhanced Real-Time Telemetry (`real_time_telemetry.py`)

**New Features Added:**
- Radarr/Sonarr history API checking for grab event verification
- Extended verification attempts for fallback workflows (12-15 attempts vs 6)
- SABnzbd duplicate detection in history checking
- Exponential backoff retry logic with increasing intervals

**Key Methods Enhanced:**
```python
async def verify_queue_entry(self, job: DownloadJob, immediate: bool = True) -> bool:
    # Now includes history checking if queue verification fails
    # Extended patience for fallback workflows
    # Grab confirmation via *arr history APIs
```

### 2. Telemetry Integration Layer (`telemetry_integration.py`)

**New Parameters Added:**
- `is_fallback_workflow`: Flag for remove/re-add scenarios
- `monitored_status`: Verification of monitoring status
- Extended verification timeouts for fallback workflows

**Enhanced Methods:**
```python
def track_movie_download(self, title: str, radarr_id: int, quality: Optional[str] = None, 
                        is_fallback_workflow: bool = False) -> str:
    # Now supports fallback workflow flagging
    
async def check_monitoring_status(self, radarr_id: Optional[int] = None) -> bool:
    # Verifies that items are properly monitored in *arr
```

### 3. Download Tracker (`download_tracker.py`)

**High-Level Interface:**
```python
async with DownloadTracker(settings_dict, logger) as tracker:
    # Track initial attempt
    job_id = await tracker.track_movie_with_fallback_support(
        title="Movie (2023)",
        radarr_id=123,
        quality="Ultra-HD",
        is_fallback_workflow=False
    )
    
    # Verify with extended patience
    success = await tracker.verify_grab_with_extended_patience(
        job_id, max_wait_minutes=3
    )
```

## Integration into Existing Scripts

### For 01_intake_and_nzb_search.py

Replace static success messages with robust tracking:

**Before:**
```python
print("✅ Successfully added to Radarr!")
```

**After:**
```python
from _internal.utils.download_tracker import track_movie

# For normal downloads
success = await track_movie(
    settings_dict=settings_dict,
    title=movie_title,
    radarr_id=radarr_id,
    quality=quality_profile_name,
    is_fallback=False,
    logger=logger_instance
)

# For fallback workflows (4K → 1080p)
fallback_success = await track_movie(
    settings_dict=settings_dict,
    title=movie_title,
    radarr_id=new_radarr_id,  # ID after re-add
    quality="HD-1080p",
    is_fallback=True,
    original_id=original_radarr_id,
    logger=logger_instance
)
```

### For Fallback Workflow Integration

```python
async def handle_4k_fallback_workflow(movie_data, settings_dict, logger):
    """Example fallback workflow with enhanced tracking."""
    
    async with DownloadTracker(settings_dict, logger) as tracker:
        # Step 1: Try 4K first
        initial_job = await tracker.track_movie_with_fallback_support(
            title=movie_data["title"],
            radarr_id=movie_data["radarr_id"],
            quality="Ultra-HD",
            is_fallback_workflow=False
        )
        
        # Step 2: Wait reasonable time for 4K grab
        success = await tracker.verify_grab_with_extended_patience(
            initial_job, max_wait_minutes=2
        )
        
        if not success:
            logger.info("🔄 4K unavailable, initiating 1080p fallback...")
            
            # Step 3: Remove and re-add with 1080p profile
            new_radarr_id = await remove_and_readd_movie(
                movie_data, "HD-1080p", settings_dict
            )
            
            # Step 4: Track fallback with extended patience
            fallback_job = await tracker.track_movie_with_fallback_support(
                title=movie_data["title"],
                radarr_id=new_radarr_id,
                quality="HD-1080p",
                is_fallback_workflow=True,
                original_radarr_id=movie_data["radarr_id"],
                fallback_reason="4k_unavailable"
            )
            
            # Step 5: Extended verification for fallback
            return await tracker.verify_grab_with_extended_patience(
                fallback_job, max_wait_minutes=3
            )
        
        return success
```

## Configuration Options

### In settings.ini:

```ini
[Telemetry]
# Extended verification for fallback workflows
fallback_max_attempts = 15
fallback_verification_interval_sec = 10
normal_max_attempts = 8

# Queue delay tolerance
radarr_queue_delay_tolerance_sec = 120
enable_history_verification = true

[Radarr_Robust]
check_monitoring_status = true
warn_on_unmonitored = true
fallback_workflow_patience_multiplier = 1.5
```

## Best Practices Recommendations

### 1. Monitored Status Verification
Always ensure movies are monitored before expecting queue entries:
```python
is_monitored = await tracker.telemetry.check_monitoring_status(radarr_id=movie_id)
if not is_monitored:
    logger.warning("Movie not monitored - downloads may not appear in queue")
```

### 2. Webhook Integration (Future Enhancement)
Set up Radarr/Sonarr webhooks for instant grab notifications:
- URL: `http://your-system:8765/webhook/grab`
- Events: On Grab, On Import, On Health Issue
- Eliminates polling delays entirely

### 3. Quality Profile Strategy
Consider combined quality profiles instead of remove/re-add:
```ini
# Radarr Quality Profile: "4K with 1080p Fallback"
# - Preferred: Ultra-HD (4K)
# - Allowed: HD-1080p, Ultra-HD
# - Delay Profile: Wait 12 hours for 4K, then allow 1080p
```

### 4. SABnzbd Duplicate Handling
Configure SABnzbd appropriately:
- Duplicate Prevention: "Discard" or "Pause"
- Always check history if downloads don't appear in queue

## Troubleshooting Common Issues

### Issue: "Download verification failed - not found in queues"
**Likely Causes:**
1. Movie is not monitored in Radarr
2. Radarr queue hasn't refreshed yet (wait 60-90 seconds)
3. SABnzbd detected duplicate and moved to history
4. Grab actually failed but Radarr returned success

**Solutions:**
1. Check monitoring status: `await tracker.telemetry.check_monitoring_status(radarr_id)`
2. Enable history verification in config
3. Check SABnzbd history for duplicate detection
4. Review Radarr logs for grab failures

### Issue: "Fallback workflow timeout"
**Likely Causes:**
1. New movie ID after re-add isn't being tracked correctly
2. Quality profile mismatch
3. Indexer issues preventing grab

**Solutions:**
1. Verify the new movie ID is correct after re-add
2. Ensure fallback quality profile exists and is accessible
3. Check indexer health in Radarr

### Issue: "Downloads appear to start but disappear quickly"
**Likely Causes:**
1. SABnzbd duplicate detection
2. Authentication issues with indexer
3. Download client connectivity problems

**Solutions:**
1. Check SABnzbd history for failure reasons
2. Verify indexer credentials in Radarr
3. Test download client connectivity

## Performance Considerations

1. **API Rate Limiting**: The system includes exponential backoff to prevent API rate limit issues.

2. **Polling Frequency**: Default 10-second intervals balance responsiveness with API load.

3. **History Lookups**: Limited to recent entries (50 max) to minimize API overhead.

4. **Concurrent Downloads**: System scales to handle multiple simultaneous downloads.

## Logging and Monitoring

Enable detailed logging for troubleshooting:
```ini
[Logging_Enhanced]
log_verification_attempts = true
log_history_checks = true
log_monitoring_status_checks = true
log_verification_timing = true
```

This provides detailed insights into:
- Verification attempt timing
- History API check results  
- Monitoring status verification
- Queue polling performance

## Migration from Existing Code

### Minimal Change Integration
For existing scripts with minimal changes:
```python
# Replace this:
print("✅ Movie added to Radarr!")

# With this:
from _internal.utils.download_tracker import track_movie
success = await track_movie(settings_dict, title, radarr_id, quality)
if success:
    print("✅ Movie grab verified!")
else:
    print("⚠️ Movie grab could not be verified - check logs")
```

### Full Integration
For comprehensive monitoring:
```python
async with DownloadTracker(settings_dict, logger) as tracker:
    # Show best practices reminder
    tracker.log_best_practices_reminder()
    
    # Track multiple downloads
    for movie in movie_queue:
        job_id = await tracker.track_movie_with_fallback_support(...)
        
    # Monitor all to completion
    all_completed = await tracker.monitor_until_completion()
```

This implementation provides a robust solution for handling the inherent delays and complexities of *arr queue management while maintaining compatibility with existing code structures.
