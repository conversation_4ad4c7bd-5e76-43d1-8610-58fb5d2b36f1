#!/usr/bin/env python3
from __future__ import annotations

import aiohttp
from typing import Any, Dict, List, Mapping, Optional, Tuple

from _internal.utils.http_retry import async_request_json, async_delete


class SonarrClient:
    def __init__(self, base_url: str, api_key: str):
        self.base_url = base_url.rstrip('/')
        self.headers = {'X-Api-Key': api_key}

    def _url(self, path: str) -> str:
        return f"{self.base_url}{path}"

    async def series_lookup(self, session: aiohttp.ClientSession, term: str) -> List[Dict[str, Any]]:
        status, payload = await async_request_json(session, 'GET', self._url('/api/v3/series/lookup'), headers=self.headers, params={'term': term})
        if status == 200 and isinstance(payload, list):
            return payload
        return []

    async def get_series(self, session: aiohttp.ClientSession) -> List[Dict[str, Any]]:
        status, payload = await async_request_json(session, 'GET', self._url('/api/v3/series'), headers=self.headers)
        if status == 200 and isinstance(payload, list):
            return payload
        return []

    async def get_queue(self, session: aiohttp.ClientSession) -> Dict[str, Any]:
        status, payload = await async_request_json(session, 'GET', self._url('/api/v3/queue'), headers=self.headers)
        if status == 200 and isinstance(payload, dict):
            return payload
        return {"records": []}

    async def get_queue_page(self, session: aiohttp.ClientSession, page: int = 1, page_size: int = 100) -> Dict[str, Any]:
        status, payload = await async_request_json(session, 'GET', self._url('/api/v3/queue'), headers=self.headers, params={"page": page, "pageSize": page_size})
        if status == 200 and isinstance(payload, dict):
            return payload
        return {"records": []}

    async def get_series_by_id(self, session: aiohttp.ClientSession, series_id: int) -> Optional[Dict[str, Any]]:
        status, payload = await async_request_json(session, 'GET', self._url(f'/api/v3/series/{series_id}'), headers=self.headers)
        if status == 200 and isinstance(payload, dict):
            return payload
        return None

    async def get_episodes_by_series(self, session: aiohttp.ClientSession, series_id: int) -> List[Dict[str, Any]]:
        status, payload = await async_request_json(session, 'GET', self._url('/api/v3/episode'), headers=self.headers, params={"seriesId": series_id})
        if status == 200 and isinstance(payload, list):
            return payload
        return []

    async def get_indexers(self, session: aiohttp.ClientSession) -> List[Dict[str, Any]]:
        status, payload = await async_request_json(session, 'GET', self._url('/api/v3/indexer'), headers=self.headers)
        if status == 200 and isinstance(payload, list):
            return payload
        return []

    async def get_releases(self, session: aiohttp.ClientSession, episode_id: int) -> List[Dict[str, Any]]:
        status, payload = await async_request_json(session, 'GET', self._url('/api/v3/release'), headers=self.headers, params={"episodeId": episode_id})
        if status == 200 and isinstance(payload, list):
            return payload
        return []

    async def get_quality_definitions(self, session: aiohttp.ClientSession) -> List[Dict[str, Any]]:
        status, payload = await async_request_json(session, 'GET', self._url('/api/v3/qualitydefinition'), headers=self.headers)
        if status == 200 and isinstance(payload, list):
            return payload
        return []

    async def get_rootfolders(self, session: aiohttp.ClientSession) -> List[Dict[str, Any]]:
        status, payload = await async_request_json(session, 'GET', self._url('/api/v3/rootfolder'), headers=self.headers)
        if status == 200 and isinstance(payload, list):
            return payload
        return []

    async def get_commands(self, session: aiohttp.ClientSession) -> List[Dict[str, Any]]:
        status, payload = await async_request_json(session, 'GET', self._url('/api/v3/command'), headers=self.headers)
        if status == 200 and isinstance(payload, list):
            return payload
        return []

    async def get_command_by_id(self, session: aiohttp.ClientSession, command_id: int) -> Optional[Dict[str, Any]]:
        status, payload = await async_request_json(session, 'GET', self._url(f'/api/v3/command/{command_id}'), headers=self.headers)
        if status == 200 and isinstance(payload, dict):
            return payload
        return None

    async def update_series(self, session: aiohttp.ClientSession, series_obj: Dict[str, Any]) -> bool:
        status, payload = await async_request_json(session, 'PUT', self._url('/api/v3/series'), headers=self.headers, json=series_obj)
        return status in (200, 202)

    async def set_episodes_monitor_state(self, session: aiohttp.ClientSession, episode_ids: List[int], monitored: bool) -> bool:
        status, _ = await async_request_json(session, 'PUT', self._url('/api/v3/episode/monitor'), headers=self.headers, json={"episodeIds": episode_ids, "monitored": monitored})
        return status in (200, 202)

    async def issue_command(self, session: aiohttp.ClientSession, command_payload: Dict[str, Any]) -> Dict[str, Any]:
        status, payload = await async_request_json(session, 'POST', self._url('/api/v3/command'), headers=self.headers, json=command_payload)
        if status in (200, 201) and isinstance(payload, dict):
            return payload
        return {"success": False, "status": status}

    async def delete_queue_item(self, session: aiohttp.ClientSession, queue_id: int, remove_from_client: bool = True, blocklist: bool = True) -> bool:
        qs = f"?removeFromClient={'true' if remove_from_client else 'false'}&blocklist={'true' if blocklist else 'false'}"
        status = await async_delete(session, self._url(f"/api/v3/queue/{queue_id}{qs}"), headers=self.headers)
        return status in (200, 204)

