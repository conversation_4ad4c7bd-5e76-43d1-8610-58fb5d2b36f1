﻿using System.Collections.ObjectModel;
using System.Drawing;

namespace seconv.libse.Common
{
    public static class SccPositionAndStyleTable
    {
        public static readonly ReadOnlyCollection<SccPositionAndStyle> SccPositionAndStyles =
            new ReadOnlyCollection<SccPositionAndStyle>(new List<SccPositionAndStyle>
        {
            //NO x-coordinate?
            new SccPositionAndStyle(Color.White, SccFontStyle.Regular, 01, 0, "1140"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Regular, 02, 0, "1160"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Regular, 03, 0, "1240"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Regular, 04, 0, "1260"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Regular, 05, 0, "1540"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Regular, 06, 0, "1560"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Regular, 07, 0, "1640"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Regular, 08, 0, "1660"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Regular, 09, 0, "1740"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Regular, 10, 0, "1760"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Regular, 11, 0, "1040"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Regular, 12, 0, "1340"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Regular, 13, 0, "1360"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Regular, 14, 0, "1440"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Regular, 15, 0, "1460"),

            new SccPositionAndStyle(Color.White, SccFontStyle.Underline, 01, 0, "1141"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Underline, 02, 0, "1161"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Underline, 03, 0, "1241"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Underline, 04, 0, "1261"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Underline, 05, 0, "1541"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Underline, 06, 0, "1561"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Underline, 07, 0, "1641"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Underline, 08, 0, "1661"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Underline, 09, 0, "1741"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Underline, 10, 0, "1761"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Underline, 11, 0, "1041"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Underline, 12, 0, "1341"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Underline, 13, 0, "1361"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Underline, 14, 0, "1441"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Underline, 15, 0, "1461"),

            new SccPositionAndStyle(Color.Green, SccFontStyle.Regular, 01, 0, "1142"),
            new SccPositionAndStyle(Color.Green, SccFontStyle.Regular, 02, 0, "1162"),
            new SccPositionAndStyle(Color.Green, SccFontStyle.Regular, 03, 0, "1242"),
            new SccPositionAndStyle(Color.Green, SccFontStyle.Regular, 04, 0, "1262"),
            new SccPositionAndStyle(Color.Green, SccFontStyle.Regular, 05, 0, "1542"),
            new SccPositionAndStyle(Color.Green, SccFontStyle.Regular, 06, 0, "1562"),
            new SccPositionAndStyle(Color.Green, SccFontStyle.Regular, 07, 0, "1642"),
            new SccPositionAndStyle(Color.Green, SccFontStyle.Regular, 08, 0, "1662"),
            new SccPositionAndStyle(Color.Green, SccFontStyle.Regular, 09, 0, "1742"),
            new SccPositionAndStyle(Color.Green, SccFontStyle.Regular, 10, 0, "1762"),
            new SccPositionAndStyle(Color.Green, SccFontStyle.Regular, 11, 0, "1042"),
            new SccPositionAndStyle(Color.Green, SccFontStyle.Regular, 12, 0, "1342"),
            new SccPositionAndStyle(Color.Green, SccFontStyle.Regular, 13, 0, "1362"),
            new SccPositionAndStyle(Color.Green, SccFontStyle.Regular, 14, 0, "1442"),
            new SccPositionAndStyle(Color.Green, SccFontStyle.Regular, 15, 0, "1462"),

            new SccPositionAndStyle(Color.Green, SccFontStyle.Underline, 01, 0, "1143"),
            new SccPositionAndStyle(Color.Green, SccFontStyle.Underline, 02, 0, "1163"),
            new SccPositionAndStyle(Color.Green, SccFontStyle.Underline, 03, 0, "1243"),
            new SccPositionAndStyle(Color.Green, SccFontStyle.Underline, 04, 0, "1263"),
            new SccPositionAndStyle(Color.Green, SccFontStyle.Underline, 05, 0, "1543"),
            new SccPositionAndStyle(Color.Green, SccFontStyle.Underline, 06, 0, "1563"),
            new SccPositionAndStyle(Color.Green, SccFontStyle.Underline, 07, 0, "1643"),
            new SccPositionAndStyle(Color.Green, SccFontStyle.Underline, 08, 0, "1663"),
            new SccPositionAndStyle(Color.Green, SccFontStyle.Underline, 09, 0, "1743"),
            new SccPositionAndStyle(Color.Green, SccFontStyle.Underline, 10, 0, "1763"),
            new SccPositionAndStyle(Color.Green, SccFontStyle.Underline, 11, 0, "1043"),
            new SccPositionAndStyle(Color.Green, SccFontStyle.Underline, 12, 0, "1343"),
            new SccPositionAndStyle(Color.Green, SccFontStyle.Underline, 13, 0, "1363"),
            new SccPositionAndStyle(Color.Green, SccFontStyle.Underline, 14, 0, "1443"),
            new SccPositionAndStyle(Color.Green, SccFontStyle.Underline, 15, 0, "1463"),

            new SccPositionAndStyle(Color.Blue, SccFontStyle.Regular, 01, 0, "1144"),
            new SccPositionAndStyle(Color.Blue, SccFontStyle.Regular, 02, 0, "1164"),
            new SccPositionAndStyle(Color.Blue, SccFontStyle.Regular, 03, 0, "1244"),
            new SccPositionAndStyle(Color.Blue, SccFontStyle.Regular, 04, 0, "1264"),
            new SccPositionAndStyle(Color.Blue, SccFontStyle.Regular, 05, 0, "1544"),
            new SccPositionAndStyle(Color.Blue, SccFontStyle.Regular, 06, 0, "1564"),
            new SccPositionAndStyle(Color.Blue, SccFontStyle.Regular, 07, 0, "1644"),
            new SccPositionAndStyle(Color.Blue, SccFontStyle.Regular, 08, 0, "1664"),
            new SccPositionAndStyle(Color.Blue, SccFontStyle.Regular, 09, 0, "1744"),
            new SccPositionAndStyle(Color.Blue, SccFontStyle.Regular, 10, 0, "1764"),
            new SccPositionAndStyle(Color.Blue, SccFontStyle.Regular, 11, 0, "1044"),
            new SccPositionAndStyle(Color.Blue, SccFontStyle.Regular, 12, 0, "1344"),
            new SccPositionAndStyle(Color.Blue, SccFontStyle.Regular, 13, 0, "1364"),
            new SccPositionAndStyle(Color.Blue, SccFontStyle.Regular, 14, 0, "1444"),
            new SccPositionAndStyle(Color.Blue, SccFontStyle.Regular, 15, 0, "1464"),

            new SccPositionAndStyle(Color.Blue, SccFontStyle.Underline, 01, 0, "1145"),
            new SccPositionAndStyle(Color.Blue, SccFontStyle.Underline, 02, 0, "1165"),
            new SccPositionAndStyle(Color.Blue, SccFontStyle.Underline, 03, 0, "1245"),
            new SccPositionAndStyle(Color.Blue, SccFontStyle.Underline, 04, 0, "1265"),
            new SccPositionAndStyle(Color.Blue, SccFontStyle.Underline, 05, 0, "1545"),
            new SccPositionAndStyle(Color.Blue, SccFontStyle.Underline, 06, 0, "1565"),
            new SccPositionAndStyle(Color.Blue, SccFontStyle.Underline, 07, 0, "1645"),
            new SccPositionAndStyle(Color.Blue, SccFontStyle.Underline, 08, 0, "1665"),
            new SccPositionAndStyle(Color.Blue, SccFontStyle.Underline, 09, 0, "1745"),
            new SccPositionAndStyle(Color.Blue, SccFontStyle.Underline, 10, 0, "1765"),
            new SccPositionAndStyle(Color.Blue, SccFontStyle.Underline, 11, 0, "1045"),
            new SccPositionAndStyle(Color.Blue, SccFontStyle.Underline, 12, 0, "1345"),
            new SccPositionAndStyle(Color.Blue, SccFontStyle.Underline, 13, 0, "1365"),
            new SccPositionAndStyle(Color.Blue, SccFontStyle.Underline, 14, 0, "1445"),
            new SccPositionAndStyle(Color.Blue, SccFontStyle.Underline, 15, 0, "1465"),

            new SccPositionAndStyle(Color.Cyan, SccFontStyle.Regular, 01, 0, "1146"),
            new SccPositionAndStyle(Color.Cyan, SccFontStyle.Regular, 02, 0, "1166"),
            new SccPositionAndStyle(Color.Cyan, SccFontStyle.Regular, 03, 0, "1246"),
            new SccPositionAndStyle(Color.Cyan, SccFontStyle.Regular, 04, 0, "1266"),
            new SccPositionAndStyle(Color.Cyan, SccFontStyle.Regular, 05, 0, "1546"),
            new SccPositionAndStyle(Color.Cyan, SccFontStyle.Regular, 06, 0, "1566"),
            new SccPositionAndStyle(Color.Cyan, SccFontStyle.Regular, 07, 0, "1646"),
            new SccPositionAndStyle(Color.Cyan, SccFontStyle.Regular, 08, 0, "1666"),
            new SccPositionAndStyle(Color.Cyan, SccFontStyle.Regular, 09, 0, "1746"),
            new SccPositionAndStyle(Color.Cyan, SccFontStyle.Regular, 10, 0, "1766"),
            new SccPositionAndStyle(Color.Cyan, SccFontStyle.Regular, 11, 0, "1046"),
            new SccPositionAndStyle(Color.Cyan, SccFontStyle.Regular, 12, 0, "1346"),
            new SccPositionAndStyle(Color.Cyan, SccFontStyle.Regular, 13, 0, "1366"),
            new SccPositionAndStyle(Color.Cyan, SccFontStyle.Regular, 14, 0, "1446"),
            new SccPositionAndStyle(Color.Cyan, SccFontStyle.Regular, 15, 0, "1466"),

            new SccPositionAndStyle(Color.Cyan, SccFontStyle.Underline, 01, 0, "1147"),
            new SccPositionAndStyle(Color.Cyan, SccFontStyle.Underline, 02, 0, "1167"),
            new SccPositionAndStyle(Color.Cyan, SccFontStyle.Underline, 03, 0, "1247"),
            new SccPositionAndStyle(Color.Cyan, SccFontStyle.Underline, 04, 0, "1267"),
            new SccPositionAndStyle(Color.Cyan, SccFontStyle.Underline, 05, 0, "1547"),
            new SccPositionAndStyle(Color.Cyan, SccFontStyle.Underline, 06, 0, "1567"),
            new SccPositionAndStyle(Color.Cyan, SccFontStyle.Underline, 07, 0, "1647"),
            new SccPositionAndStyle(Color.Cyan, SccFontStyle.Underline, 08, 0, "1667"),
            new SccPositionAndStyle(Color.Cyan, SccFontStyle.Underline, 09, 0, "1747"),
            new SccPositionAndStyle(Color.Cyan, SccFontStyle.Underline, 10, 0, "1767"),
            new SccPositionAndStyle(Color.Cyan, SccFontStyle.Underline, 11, 0, "1047"),
            new SccPositionAndStyle(Color.Cyan, SccFontStyle.Underline, 12, 0, "1347"),
            new SccPositionAndStyle(Color.Cyan, SccFontStyle.Underline, 13, 0, "1367"),
            new SccPositionAndStyle(Color.Cyan, SccFontStyle.Underline, 14, 0, "1447"),
            new SccPositionAndStyle(Color.Cyan, SccFontStyle.Underline, 15, 0, "1467"),

            new SccPositionAndStyle(Color.Red, SccFontStyle.Regular, 01, 0, "1148"),
            new SccPositionAndStyle(Color.Red, SccFontStyle.Regular, 02, 0, "1168"),
            new SccPositionAndStyle(Color.Red, SccFontStyle.Regular, 03, 0, "1248"),
            new SccPositionAndStyle(Color.Red, SccFontStyle.Regular, 04, 0, "1268"),
            new SccPositionAndStyle(Color.Red, SccFontStyle.Regular, 05, 0, "1548"),
            new SccPositionAndStyle(Color.Red, SccFontStyle.Regular, 06, 0, "1568"),
            new SccPositionAndStyle(Color.Red, SccFontStyle.Regular, 07, 0, "1648"),
            new SccPositionAndStyle(Color.Red, SccFontStyle.Regular, 08, 0, "1668"),
            new SccPositionAndStyle(Color.Red, SccFontStyle.Regular, 09, 0, "1748"),
            new SccPositionAndStyle(Color.Red, SccFontStyle.Regular, 10, 0, "1768"),
            new SccPositionAndStyle(Color.Red, SccFontStyle.Regular, 11, 0, "1048"),
            new SccPositionAndStyle(Color.Red, SccFontStyle.Regular, 12, 0, "1348"),
            new SccPositionAndStyle(Color.Red, SccFontStyle.Regular, 13, 0, "1368"),
            new SccPositionAndStyle(Color.Red, SccFontStyle.Regular, 14, 0, "1448"),
            new SccPositionAndStyle(Color.Red, SccFontStyle.Regular, 15, 0, "1468"),

            new SccPositionAndStyle(Color.Red, SccFontStyle.Underline, 01, 0, "1149"),
            new SccPositionAndStyle(Color.Red, SccFontStyle.Underline, 02, 0, "1169"),
            new SccPositionAndStyle(Color.Red, SccFontStyle.Underline, 03, 0, "1249"),
            new SccPositionAndStyle(Color.Red, SccFontStyle.Underline, 04, 0, "1269"),
            new SccPositionAndStyle(Color.Red, SccFontStyle.Underline, 05, 0, "1549"),
            new SccPositionAndStyle(Color.Red, SccFontStyle.Underline, 06, 0, "1569"),
            new SccPositionAndStyle(Color.Red, SccFontStyle.Underline, 07, 0, "1649"),
            new SccPositionAndStyle(Color.Red, SccFontStyle.Underline, 08, 0, "1669"),
            new SccPositionAndStyle(Color.Red, SccFontStyle.Underline, 09, 0, "1749"),
            new SccPositionAndStyle(Color.Red, SccFontStyle.Underline, 10, 0, "1769"),
            new SccPositionAndStyle(Color.Red, SccFontStyle.Underline, 11, 0, "1049"),
            new SccPositionAndStyle(Color.Red, SccFontStyle.Underline, 12, 0, "1349"),
            new SccPositionAndStyle(Color.Red, SccFontStyle.Underline, 13, 0, "1369"),
            new SccPositionAndStyle(Color.Red, SccFontStyle.Underline, 14, 0, "1449"),
            new SccPositionAndStyle(Color.Red, SccFontStyle.Underline, 15, 0, "1469"),

            new SccPositionAndStyle(Color.Yellow, SccFontStyle.Regular, 01, 0, "114a"),
            new SccPositionAndStyle(Color.Yellow, SccFontStyle.Regular, 02, 0, "116a"),
            new SccPositionAndStyle(Color.Yellow, SccFontStyle.Regular, 03, 0, "124a"),
            new SccPositionAndStyle(Color.Yellow, SccFontStyle.Regular, 04, 0, "126a"),
            new SccPositionAndStyle(Color.Yellow, SccFontStyle.Regular, 05, 0, "154a"),
            new SccPositionAndStyle(Color.Yellow, SccFontStyle.Regular, 06, 0, "156a"),
            new SccPositionAndStyle(Color.Yellow, SccFontStyle.Regular, 07, 0, "164a"),
            new SccPositionAndStyle(Color.Yellow, SccFontStyle.Regular, 08, 0, "166a"),
            new SccPositionAndStyle(Color.Yellow, SccFontStyle.Regular, 09, 0, "174a"),
            new SccPositionAndStyle(Color.Yellow, SccFontStyle.Regular, 10, 0, "176a"),
            new SccPositionAndStyle(Color.Yellow, SccFontStyle.Regular, 11, 0, "104a"),
            new SccPositionAndStyle(Color.Yellow, SccFontStyle.Regular, 12, 0, "134a"),
            new SccPositionAndStyle(Color.Yellow, SccFontStyle.Regular, 13, 0, "136a"),
            new SccPositionAndStyle(Color.Yellow, SccFontStyle.Regular, 14, 0, "144a"),
            new SccPositionAndStyle(Color.Yellow, SccFontStyle.Regular, 15, 0, "146a"),

            new SccPositionAndStyle(Color.Yellow, SccFontStyle.Underline, 01, 0, "114b"),
            new SccPositionAndStyle(Color.Yellow, SccFontStyle.Underline, 02, 0, "116b"),
            new SccPositionAndStyle(Color.Yellow, SccFontStyle.Underline, 03, 0, "124b"),
            new SccPositionAndStyle(Color.Yellow, SccFontStyle.Underline, 04, 0, "126b"),
            new SccPositionAndStyle(Color.Yellow, SccFontStyle.Underline, 05, 0, "154b"),
            new SccPositionAndStyle(Color.Yellow, SccFontStyle.Underline, 06, 0, "156b"),
            new SccPositionAndStyle(Color.Yellow, SccFontStyle.Underline, 07, 0, "164b"),
            new SccPositionAndStyle(Color.Yellow, SccFontStyle.Underline, 08, 0, "166b"),
            new SccPositionAndStyle(Color.Yellow, SccFontStyle.Underline, 09, 0, "174b"),
            new SccPositionAndStyle(Color.Yellow, SccFontStyle.Underline, 10, 0, "176b"),
            new SccPositionAndStyle(Color.Yellow, SccFontStyle.Underline, 11, 0, "104b"),
            new SccPositionAndStyle(Color.Yellow, SccFontStyle.Underline, 12, 0, "134b"),
            new SccPositionAndStyle(Color.Yellow, SccFontStyle.Underline, 13, 0, "136b"),
            new SccPositionAndStyle(Color.Yellow, SccFontStyle.Underline, 14, 0, "144b"),
            new SccPositionAndStyle(Color.Yellow, SccFontStyle.Underline, 15, 0, "146b"),

            new SccPositionAndStyle(Color.Magenta, SccFontStyle.Regular, 01, 0, "114c"),
            new SccPositionAndStyle(Color.Magenta, SccFontStyle.Regular, 02, 0, "116c"),
            new SccPositionAndStyle(Color.Magenta, SccFontStyle.Regular, 03, 0, "124c"),
            new SccPositionAndStyle(Color.Magenta, SccFontStyle.Regular, 04, 0, "126c"),
            new SccPositionAndStyle(Color.Magenta, SccFontStyle.Regular, 05, 0, "154c"),
            new SccPositionAndStyle(Color.Magenta, SccFontStyle.Regular, 06, 0, "156c"),
            new SccPositionAndStyle(Color.Magenta, SccFontStyle.Regular, 07, 0, "164c"),
            new SccPositionAndStyle(Color.Magenta, SccFontStyle.Regular, 08, 0, "166c"),
            new SccPositionAndStyle(Color.Magenta, SccFontStyle.Regular, 09, 0, "174c"),
            new SccPositionAndStyle(Color.Magenta, SccFontStyle.Regular, 10, 0, "176c"),
            new SccPositionAndStyle(Color.Magenta, SccFontStyle.Regular, 11, 0, "104c"),
            new SccPositionAndStyle(Color.Magenta, SccFontStyle.Regular, 12, 0, "134c"),
            new SccPositionAndStyle(Color.Magenta, SccFontStyle.Regular, 13, 0, "136c"),
            new SccPositionAndStyle(Color.Magenta, SccFontStyle.Regular, 14, 0, "144c"),
            new SccPositionAndStyle(Color.Magenta, SccFontStyle.Regular, 15, 0, "146c"),

            new SccPositionAndStyle(Color.Magenta, SccFontStyle.Underline, 01, 0, "114d"),
            new SccPositionAndStyle(Color.Magenta, SccFontStyle.Underline, 02, 0, "116d"),
            new SccPositionAndStyle(Color.Magenta, SccFontStyle.Underline, 03, 0, "124d"),
            new SccPositionAndStyle(Color.Magenta, SccFontStyle.Underline, 04, 0, "126d"),
            new SccPositionAndStyle(Color.Magenta, SccFontStyle.Underline, 05, 0, "154d"),
            new SccPositionAndStyle(Color.Magenta, SccFontStyle.Underline, 06, 0, "156d"),
            new SccPositionAndStyle(Color.Magenta, SccFontStyle.Underline, 07, 0, "164d"),
            new SccPositionAndStyle(Color.Magenta, SccFontStyle.Underline, 08, 0, "166d"),
            new SccPositionAndStyle(Color.Magenta, SccFontStyle.Underline, 09, 0, "174d"),
            new SccPositionAndStyle(Color.Magenta, SccFontStyle.Underline, 10, 0, "176d"),
            new SccPositionAndStyle(Color.Magenta, SccFontStyle.Underline, 11, 0, "104d"),
            new SccPositionAndStyle(Color.Magenta, SccFontStyle.Underline, 12, 0, "134d"),
            new SccPositionAndStyle(Color.Magenta, SccFontStyle.Underline, 13, 0, "136d"),
            new SccPositionAndStyle(Color.Magenta, SccFontStyle.Underline, 14, 0, "144d"),
            new SccPositionAndStyle(Color.Magenta, SccFontStyle.Underline, 15, 0, "146d"),

            new SccPositionAndStyle(Color.White, SccFontStyle.Italic, 01, 0, "114e"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Italic, 02, 0, "116e"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Italic, 03, 0, "124e"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Italic, 04, 0, "126e"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Italic, 05, 0, "154e"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Italic, 06, 0, "156e"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Italic, 07, 0, "164e"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Italic, 08, 0, "166e"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Italic, 09, 0, "174e"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Italic, 10, 0, "176e"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Italic, 11, 0, "104e"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Italic, 12, 0, "134e"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Italic, 13, 0, "136e"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Italic, 14, 0, "144e"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Italic, 15, 0, "146e"),

            new SccPositionAndStyle(Color.White, SccFontStyle.Underline | SccFontStyle.Italic, 01, 0, "114f"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Underline | SccFontStyle.Italic, 02, 0, "116f"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Underline | SccFontStyle.Italic, 03, 0, "124f"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Underline | SccFontStyle.Italic, 04, 0, "126f"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Underline | SccFontStyle.Italic, 05, 0, "154f"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Underline | SccFontStyle.Italic, 06, 0, "156f"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Underline | SccFontStyle.Italic, 07, 0, "164f"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Underline | SccFontStyle.Italic, 08, 0, "166f"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Underline | SccFontStyle.Italic, 09, 0, "174f"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Underline | SccFontStyle.Italic, 10, 0, "176f"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Underline | SccFontStyle.Italic, 11, 0, "104f"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Underline | SccFontStyle.Italic, 12, 0, "134f"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Underline | SccFontStyle.Italic, 13, 0, "136f"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Underline | SccFontStyle.Italic, 14, 0, "144f"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Underline | SccFontStyle.Italic, 15, 0, "146f"),

            new SccPositionAndStyle(Color.White, SccFontStyle.Regular, 1, 0, "91d0"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Underline, 1, 0,"9151"),
            new SccPositionAndStyle(Color.Green, SccFontStyle.Regular, 1, 0, "91c2"),
            new SccPositionAndStyle(Color.Green, SccFontStyle.Underline, 1, 0,"9143"),
            new SccPositionAndStyle(Color.Blue, SccFontStyle.Regular, 1, 0,"91c4"),
            new SccPositionAndStyle(Color.Blue, SccFontStyle.Underline, 1, 0, "9145"),
            new SccPositionAndStyle(Color.Cyan, SccFontStyle.Regular, 1, 0,"9146"),
            new SccPositionAndStyle(Color.Cyan, SccFontStyle.Underline, 1, 0,"91c7"),
            new SccPositionAndStyle(Color.Red, SccFontStyle.Regular, 1, 0,"91c8"),
            new SccPositionAndStyle(Color.Red, SccFontStyle.Underline, 1, 0,"9149"),
            new SccPositionAndStyle(Color.Yellow, SccFontStyle.Regular, 1, 0,"914a"),
            new SccPositionAndStyle(Color.Yellow, SccFontStyle.Underline, 1, 0,"91cb"),
            new SccPositionAndStyle(Color.Magenta, SccFontStyle.Regular, 1, 0,"914c"),
            new SccPositionAndStyle(Color.Magenta, SccFontStyle.Underline, 1, 0, "91cd"),

            new SccPositionAndStyle(Color.White, SccFontStyle.Regular, 2, 0, "9170"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Underline, 2, 0,"91f1"),
            new SccPositionAndStyle(Color.Green, SccFontStyle.Regular, 2, 0, "9162"),
            new SccPositionAndStyle(Color.Green, SccFontStyle.Underline, 2, 0, "91e3"),
            new SccPositionAndStyle(Color.Blue, SccFontStyle.Regular, 2, 0, "9164"),
            new SccPositionAndStyle(Color.Blue, SccFontStyle.Underline, 2, 0, "91e5"),
            new SccPositionAndStyle(Color.Cyan, SccFontStyle.Regular, 2, 0, "91e6"),
            new SccPositionAndStyle(Color.Cyan, SccFontStyle.Underline, 2, 0, "9167"),
            new SccPositionAndStyle(Color.Red, SccFontStyle.Regular, 2, 0, "9168"),
            new SccPositionAndStyle(Color.Red, SccFontStyle.Underline, 2, 0, "91e9"),
            new SccPositionAndStyle(Color.Yellow, SccFontStyle.Regular, 2, 0, "91ea"),
            new SccPositionAndStyle(Color.Yellow, SccFontStyle.Underline, 2, 0, "916b"),
            new SccPositionAndStyle(Color.Magenta, SccFontStyle.Regular, 2, 0, "91ec"),
            new SccPositionAndStyle(Color.Magenta, SccFontStyle.Underline, 2, 0, "916d"),

            new SccPositionAndStyle(Color.White, SccFontStyle.Regular, 3, 0, "92d0"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Underline, 3, 0, "9251"),
            new SccPositionAndStyle(Color.Green, SccFontStyle.Regular, 3, 0, "92c2"),
            new SccPositionAndStyle(Color.Green, SccFontStyle.Underline, 3, 0, "9243"),
            new SccPositionAndStyle(Color.Blue, SccFontStyle.Regular, 3, 0, "92c4"),
            new SccPositionAndStyle(Color.Blue, SccFontStyle.Underline, 3, 0, "9245"),
            new SccPositionAndStyle(Color.Cyan, SccFontStyle.Regular, 3, 0, "9246"),
            new SccPositionAndStyle(Color.Cyan, SccFontStyle.Underline, 3, 0, "92c7"),
            new SccPositionAndStyle(Color.Red, SccFontStyle.Regular, 3, 0, "92c8"),
            new SccPositionAndStyle(Color.Red, SccFontStyle.Underline, 3, 0, "9249"),
            new SccPositionAndStyle(Color.Yellow, SccFontStyle.Regular, 3, 0, "924a"),
            new SccPositionAndStyle(Color.Yellow, SccFontStyle.Underline, 3, 0, "92cb"),
            new SccPositionAndStyle(Color.Magenta, SccFontStyle.Regular, 3, 0, "924c"),
            new SccPositionAndStyle(Color.Magenta, SccFontStyle.Underline, 3, 0, "92cd"),

            new SccPositionAndStyle(Color.White, SccFontStyle.Regular, 4, 0, "9270"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Underline, 4, 0, "92f1"),
            new SccPositionAndStyle(Color.Green, SccFontStyle.Regular, 4, 0, "9262"),
            new SccPositionAndStyle(Color.Green, SccFontStyle.Underline, 4, 0, "92e3"),
            new SccPositionAndStyle(Color.Blue, SccFontStyle.Regular, 4, 0, "9264"),
            new SccPositionAndStyle(Color.Blue, SccFontStyle.Underline, 4, 0, "92e5"),
            new SccPositionAndStyle(Color.Cyan, SccFontStyle.Regular, 4, 0, "92e6"),
            new SccPositionAndStyle(Color.Cyan, SccFontStyle.Underline, 4, 0, "9267"),
            new SccPositionAndStyle(Color.Red, SccFontStyle.Regular, 4, 0, "9268"),
            new SccPositionAndStyle(Color.Red, SccFontStyle.Underline, 4, 0, "92e9"),
            new SccPositionAndStyle(Color.Yellow, SccFontStyle.Regular, 4, 0, "92ea"),
            new SccPositionAndStyle(Color.Yellow, SccFontStyle.Underline, 4, 0, "926b"),
            new SccPositionAndStyle(Color.Magenta, SccFontStyle.Regular, 4, 0, "92ec"),
            new SccPositionAndStyle(Color.Magenta, SccFontStyle.Underline, 4, 0, "926d"),

            new SccPositionAndStyle(Color.White, SccFontStyle.Regular, 5, 0, "15d0"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Underline, 5, 0, "1551"),
            new SccPositionAndStyle(Color.Green, SccFontStyle.Regular, 5, 0, "15c2"),
            //                case "1543": return new SCCPositionAndStyle(Color.Green, SccFontStyle.Underline, 5, 0, ""),
            new SccPositionAndStyle(Color.Blue, SccFontStyle.Regular, 5, 0, "15c4"),
            //                case "1545": return new SCCPositionAndStyle(Color.Blue, SccFontStyle.Underline, 5, 0, ""),
            //                case "1546": return new SCCPositionAndStyle(Color.Cyan, SccFontStyle.Regular, 5, 0, ""),
            new SccPositionAndStyle(Color.Cyan, SccFontStyle.Underline, 5, 0, "15c7"),
            new SccPositionAndStyle(Color.Red, SccFontStyle.Regular, 5, 0, "15c8"),
            //case "1549": return new SCCPositionAndStyle(Color.Red, SccFontStyle.Underline, 5, 0, ""),
            //case "154a": return new SCCPositionAndStyle(Color.Yellow, SccFontStyle.Regular, 5, 0, ""),
            new SccPositionAndStyle(Color.Yellow, SccFontStyle.Underline, 5, 0, "15cb"),
            //case "154c": return new SCCPositionAndStyle(Color.Magenta, SccFontStyle.Regular, 5, 0, ""),
            new SccPositionAndStyle(Color.Magenta, SccFontStyle.Underline, 5, 0, "15cd"),

            new SccPositionAndStyle(Color.White, SccFontStyle.Regular, 6, 0, "1570"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Underline, 6, 0, "15f1"),
            //case "1562": return new SCCPositionAndStyle(Color.Green, SccFontStyle.Regular, 6, 0, ""),
            //case "15e3": return new SCCPositionAndStyle(Color.Green, SccFontStyle.Underline, 6, 0, ""),
            //case "1564": return new SCCPositionAndStyle(Color.Blue, SccFontStyle.Regular, 6, 0, ""),
            new SccPositionAndStyle(Color.Blue, SccFontStyle.Underline, 6, 0, "15e5"),
            new SccPositionAndStyle(Color.Cyan, SccFontStyle.Regular, 6, 0, "15e6"),
            //case "1567": return new SCCPositionAndStyle(Color.Cyan, SccFontStyle.Underline, 6, 0, ""),
            //case "1568": return new SCCPositionAndStyle(Color.Red, SccFontStyle.Regular, 6, 0, ""),
            new SccPositionAndStyle(Color.Red, SccFontStyle.Underline, 6, 0, "15e9"),
            new SccPositionAndStyle(Color.Yellow, SccFontStyle.Regular, 6, 0, "15ea"),
            //case "156b": return new SCCPositionAndStyle(Color.Yellow, SccFontStyle.Underline, 6, 0, ""),
            new SccPositionAndStyle(Color.Magenta, SccFontStyle.Regular, 6, 0, "15ec"),
            //case "156d": return new SCCPositionAndStyle(Color.Magenta, SccFontStyle.Underline, 6, 0, ""),

            new SccPositionAndStyle(Color.White, SccFontStyle.Regular, 7, 0, "16d0"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Underline, 7, 0, "1651"),
            new SccPositionAndStyle(Color.Green, SccFontStyle.Regular, 7, 0, "16c2"),
            //case "1643": return new SCCPositionAndStyle(Color.Green, SccFontStyle.Underline, 7, 0, ""),
            new SccPositionAndStyle(Color.Blue, SccFontStyle.Regular, 7, 0, "16c4"),
            //case "1645": return new SCCPositionAndStyle(Color.Blue, SccFontStyle.Underline, 7, 0, ""),
            //case "1646": return new SCCPositionAndStyle(Color.Cyan, SccFontStyle.Regular, 7, 0, ""),
            new SccPositionAndStyle(Color.Cyan, SccFontStyle.Underline, 7, 0, "16c7"),
            new SccPositionAndStyle(Color.Red, SccFontStyle.Regular, 7, 0, "16c8"),
            //case "1649": return new SCCPositionAndStyle(Color.Red, SccFontStyle.Underline, 7, 0, ""),
            //case "164a": return new SCCPositionAndStyle(Color.Yellow, SccFontStyle.Regular, 7, 0, ""),
            new SccPositionAndStyle(Color.Yellow, SccFontStyle.Underline, 7, 0, "16cb"),
            //case "164c": return new SCCPositionAndStyle(Color.Magenta, SccFontStyle.Regular, 7, 0, ""),
            new SccPositionAndStyle(Color.Magenta, SccFontStyle.Underline, 7, 0, "16cd"),

            new SccPositionAndStyle(Color.White, SccFontStyle.Regular, 8, 0, "1670"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Underline, 8, 0, "16f1"),
            //case "1662": return new SCCPositionAndStyle(Color.Green, SccFontStyle.Regular, 8, 0, ""),
            new SccPositionAndStyle(Color.Green, SccFontStyle.Underline, 8, 0, "16e3"),
            //case "1664": return new SCCPositionAndStyle(Color.Blue, SccFontStyle.Regular, 8, 0, ""),
            new SccPositionAndStyle(Color.Blue, SccFontStyle.Underline, 8, 0, "16e5"),
            new SccPositionAndStyle(Color.Cyan, SccFontStyle.Regular, 8, 0, "16e6"),
            //case "1667": return new SCCPositionAndStyle(Color.Cyan, SccFontStyle.Underline, 8, 0, ""),
            //case "1668": return new SCCPositionAndStyle(Color.Red, SccFontStyle.Regular, 8, 0, ""),
            new SccPositionAndStyle(Color.Red, SccFontStyle.Underline, 8, 0, "16e9"),
            new SccPositionAndStyle(Color.Yellow, SccFontStyle.Regular, 8, 0, "16ea"),
            //case "166b": return new SCCPositionAndStyle(Color.Yellow, SccFontStyle.Underline, 8, 0, ""),
            new SccPositionAndStyle(Color.Magenta, SccFontStyle.Regular, 8, 0, "16ec"),
            //case "166d": return new SCCPositionAndStyle(Color.Magenta, SccFontStyle.Underline, 8, 0, ""),

            new SccPositionAndStyle(Color.White, SccFontStyle.Regular, 9, 0, "97d0"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Underline, 9, 0, "9751"),
            new SccPositionAndStyle(Color.Green, SccFontStyle.Regular, 9, 0, "97c2"),
            new SccPositionAndStyle(Color.Green, SccFontStyle.Underline, 9, 0, "9743"),
            new SccPositionAndStyle(Color.Blue, SccFontStyle.Regular, 9, 0, "97c4"),
            new SccPositionAndStyle(Color.Blue, SccFontStyle.Underline, 9, 0, "9745"),
            new SccPositionAndStyle(Color.Cyan, SccFontStyle.Regular, 9, 0, "9746"),
            new SccPositionAndStyle(Color.Cyan, SccFontStyle.Underline, 9, 0, "97c7"),
            new SccPositionAndStyle(Color.Red, SccFontStyle.Regular, 9, 0, "97c8"),
            new SccPositionAndStyle(Color.Red, SccFontStyle.Underline, 9, 0, "9749"),
            new SccPositionAndStyle(Color.Yellow, SccFontStyle.Regular, 9, 0, "974a"),
            new SccPositionAndStyle(Color.Yellow, SccFontStyle.Underline, 9, 0, "97cb"),
            new SccPositionAndStyle(Color.Magenta, SccFontStyle.Regular, 9, 0, "974c"),
            new SccPositionAndStyle(Color.Magenta, SccFontStyle.Underline, 9, 0, "97cd"),

            new SccPositionAndStyle(Color.White, SccFontStyle.Regular, 10, 0, "9770"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Underline, 10, 0, "97f1"),
            new SccPositionAndStyle(Color.Green, SccFontStyle.Regular, 10, 0, "9762"),
            new SccPositionAndStyle(Color.Green, SccFontStyle.Underline, 10, 0, "97e3"),
            new SccPositionAndStyle(Color.Blue, SccFontStyle.Regular, 10, 0, "9764"),
            new SccPositionAndStyle(Color.Blue, SccFontStyle.Underline, 10, 0, "97e5"),
            new SccPositionAndStyle(Color.Cyan, SccFontStyle.Regular, 10, 0, "97e6"),
            new SccPositionAndStyle(Color.Cyan, SccFontStyle.Underline, 10, 0, "9767"),
            new SccPositionAndStyle(Color.Red, SccFontStyle.Regular, 10, 0, "9768"),
            new SccPositionAndStyle(Color.Red, SccFontStyle.Underline, 10, 0, "97e9"),
            new SccPositionAndStyle(Color.Yellow, SccFontStyle.Regular, 10, 0, "97ea"),
            new SccPositionAndStyle(Color.Yellow, SccFontStyle.Underline, 10, 0, "976b"),
            new SccPositionAndStyle(Color.Magenta, SccFontStyle.Regular, 10, 0, "97ec"),
            new SccPositionAndStyle(Color.Magenta, SccFontStyle.Underline, 10, 0, "976d"),

            new SccPositionAndStyle(Color.White, SccFontStyle.Regular, 11, 0, "10d0"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Underline, 11, 0, "1051"),
            new SccPositionAndStyle(Color.Green, SccFontStyle.Regular, 11, 0, "10c2"),
            //case "1043": return new SCCPositionAndStyle(Color.Green, SccFontStyle.Underline, 11, 0, ""),
            new SccPositionAndStyle(Color.Blue, SccFontStyle.Regular, 11, 0, "10c4"),
            //case "1045": return new SCCPositionAndStyle(Color.Blue, SccFontStyle.Underline, 11, 0, ""),
            //case "1046": return new SCCPositionAndStyle(Color.Cyan, SccFontStyle.Regular, 11, 0, ""),
            new SccPositionAndStyle(Color.Cyan, SccFontStyle.Underline, 11, 0, "10c7"),
            new SccPositionAndStyle(Color.Red, SccFontStyle.Regular, 11, 0, "10c8"),
            //case "1049": return new SCCPositionAndStyle(Color.Red, SccFontStyle.Underline, 11, 0, ""),
            //case "104a": return new SCCPositionAndStyle(Color.Yellow, SccFontStyle.Regular, 11, 0, ""),
            new SccPositionAndStyle(Color.Yellow, SccFontStyle.Underline, 11, 0, "10cb"),
            //case "104c": return new SCCPositionAndStyle(Color.Magenta, SccFontStyle.Regular, 11, 0, ""),
            new SccPositionAndStyle(Color.Magenta, SccFontStyle.Underline, 11, 0, "10cd"),

            new SccPositionAndStyle(Color.White, SccFontStyle.Regular, 12, 0, "13d0"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Underline, 12, 0, "1351"),
            new SccPositionAndStyle(Color.Green, SccFontStyle.Regular, 12, 0, "13c2"),
            //case "1343": return new SCCPositionAndStyle(Color.Green, SccFontStyle.Underline, 12, 0, ""),
            new SccPositionAndStyle(Color.Blue, SccFontStyle.Regular, 12, 0, "13c4"),
            //case "1345": return new SCCPositionAndStyle(Color.Blue, SccFontStyle.Underline, 12, 0, ""),
            //case "1346": return new SCCPositionAndStyle(Color.Cyan, SccFontStyle.Regular, 12, 0, ""),
            new SccPositionAndStyle(Color.Cyan, SccFontStyle.Underline, 12, 0, "13c7"),
            new SccPositionAndStyle(Color.Red, SccFontStyle.Regular, 12, 0, "13c8"),
            //case "1349": return new SCCPositionAndStyle(Color.Red, SccFontStyle.Underline, 12, 0, ""),
            //case "134a": return new SCCPositionAndStyle(Color.Yellow, SccFontStyle.Regular, 12, 0, ""),
            new SccPositionAndStyle(Color.Yellow, SccFontStyle.Underline, 12, 0, "13cb"),
            //case "134c": return new SCCPositionAndStyle(Color.Magenta, SccFontStyle.Regular, 12, 0, ""),
            new SccPositionAndStyle(Color.Magenta, SccFontStyle.Underline, 12, 0, "13cd"),

            new SccPositionAndStyle(Color.White, SccFontStyle.Regular, 13, 0, "1370"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Underline, 13, 0, "13f1"),
            //case "1362": return new SCCPositionAndStyle(Color.Green, SccFontStyle.Regular, 13, 0, ""),
            new SccPositionAndStyle(Color.Green, SccFontStyle.Underline, 13, 0, "13e3"),
            //case "1364": return new SCCPositionAndStyle(Color.Blue, SccFontStyle.Regular, 13, 0, ""),
            new SccPositionAndStyle(Color.Blue, SccFontStyle.Underline, 13, 0, "13e5"),
            new SccPositionAndStyle(Color.Cyan, SccFontStyle.Regular, 13, 0, "13e6"),
            //case "1367": return new SCCPositionAndStyle(Color.Cyan, SccFontStyle.Underline, 13, 0, ""),
            //case "1368": return new SCCPositionAndStyle(Color.Red, SccFontStyle.Regular, 13, 0, ""),
            new SccPositionAndStyle(Color.Red, SccFontStyle.Underline, 13, 0, "13e9"),
            new SccPositionAndStyle(Color.Yellow, SccFontStyle.Regular, 13, 0, "13ea"),
            //case "136b": return new SCCPositionAndStyle(Color.Yellow, SccFontStyle.Underline, 13, 0, ""),
            new SccPositionAndStyle(Color.Magenta, SccFontStyle.Regular, 13, 0, "13ec"),
            //case "136d": return new SCCPositionAndStyle(Color.Magenta, SccFontStyle.Underline, 13, 0, ""),

            new SccPositionAndStyle(Color.White, SccFontStyle.Regular, 14, 0, "94d0"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Underline, 14, 0, "9451"),
            new SccPositionAndStyle(Color.Green, SccFontStyle.Regular, 14, 0, "94c2"),
            new SccPositionAndStyle(Color.Green, SccFontStyle.Underline, 14, 0, "9443"),
            new SccPositionAndStyle(Color.Blue, SccFontStyle.Regular, 14, 0, "94c4"),
            new SccPositionAndStyle(Color.Blue, SccFontStyle.Underline, 14, 0, "9445"),
            new SccPositionAndStyle(Color.Cyan, SccFontStyle.Regular, 14, 0, "9446"),
            new SccPositionAndStyle(Color.Cyan, SccFontStyle.Underline, 14, 0, "94c7"),
            new SccPositionAndStyle(Color.Red, SccFontStyle.Regular, 14, 0, "94c8"),
            new SccPositionAndStyle(Color.Red, SccFontStyle.Underline, 14, 0, "9449"),
            new SccPositionAndStyle(Color.Yellow, SccFontStyle.Regular, 14, 0, "944a"),
            new SccPositionAndStyle(Color.Yellow, SccFontStyle.Underline, 14, 0, "94cb"),
            new SccPositionAndStyle(Color.Magenta, SccFontStyle.Regular, 14, 0, "944c"),
            new SccPositionAndStyle(Color.Magenta, SccFontStyle.Underline, 14, 0, "94cd"),

            new SccPositionAndStyle(Color.White, SccFontStyle.Regular, 15, 0, "9470"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Underline, 15, 0, "94f1"),
            new SccPositionAndStyle(Color.Green, SccFontStyle.Regular, 15, 0, "9462"),
            new SccPositionAndStyle(Color.Green, SccFontStyle.Underline, 15, 0, "94e3"),
            new SccPositionAndStyle(Color.Blue, SccFontStyle.Regular, 15, 0, "9464"),
            new SccPositionAndStyle(Color.Blue, SccFontStyle.Underline, 15, 0, "94e5"),
            new SccPositionAndStyle(Color.Cyan, SccFontStyle.Regular, 15, 0, "94e6"),
            new SccPositionAndStyle(Color.Cyan, SccFontStyle.Underline, 15, 0, "9467"),
            new SccPositionAndStyle(Color.Red, SccFontStyle.Regular, 15, 0, "9468"),
            new SccPositionAndStyle(Color.Red, SccFontStyle.Underline, 15, 0, "94e9"),
            new SccPositionAndStyle(Color.Yellow, SccFontStyle.Regular, 15, 0, "94ea"),
            new SccPositionAndStyle(Color.Yellow, SccFontStyle.Underline, 15, 0, "946b"),
            new SccPositionAndStyle(Color.Magenta, SccFontStyle.Regular, 15, 0, "94ec"),
            new SccPositionAndStyle(Color.Magenta, SccFontStyle.Underline, 15, 0, "946d"),

            //Columns 4-28

            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 1, 4, "9152"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 1, 4, "91d3"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 1, 8, "9154"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 1, 8, "91d5"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 1, 12, "91d6"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 1, 12, "9157"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 1, 16, "9158"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 1, 16, "91d9"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 1, 20, "91da"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 1, 20, "915b"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 1, 24, "91dc"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 1, 24, "915d"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 1, 28, "915e"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 1, 28, "91df"),

            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 2, 4, "91f2"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 2, 4, "9173"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 2, 8, "91f4"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 2, 8, "9175"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 2, 12, "9176"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 2, 12, "91f7"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 2, 16, "91f8"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 2, 16, "9179"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 2, 20, "917a"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 2, 20, "91fb"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 2, 24, "917c"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 2, 24, "91fd"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 2, 28, "91fe"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 2, 28, "917f"),

            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 3, 4, "9252"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 3, 4, "92d3"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 3, 8, "9254"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 3, 8, "92d5"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 3, 12, "92d6"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 3, 12, "9257"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 3, 16, "9258"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 3, 16, "92d9"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 3, 20, "92da"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 3, 20, "925b"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 3, 24, "92dc"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 3, 24, "925d"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 3, 28, "925e"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 3, 28, "92df"),

            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 4, 4, "92f2"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 4, 4, "9273"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 4, 8, "92f4"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 4, 8, "9275"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 4, 12, "9276"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 4, 12, "92f7"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 4, 16, "92f8"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 4, 16, "9279"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 4, 20, "927a"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 4, 20, "92fb"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 4, 24, "927c"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 4, 24, "92fd"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 4, 28, "92fe"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 4, 28, "927f"),

            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 5, 4, "1552"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 5, 4, "15d3"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 5, 8, "1554"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 5, 8, "15d5"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 5, 12, "15d6"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 5, 12, "1557"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 5, 16, "1558"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 5, 16, "15d9"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 5, 20, "15da"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 5, 20, "155b"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 5, 24, "15dc"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 5, 24, "155d"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 5, 28, "155e"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 5, 28, "15df"),

            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 6, 4, "15f2"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 6, 4, "1573"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 6, 8, "15f4"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 6, 8, "1575"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 6, 12, "1576"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 6, 12, "15f7"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 6, 16, "15f8"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 6, 16, "1579"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 6, 20, "157a"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 6, 20, "15fb"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 6, 24, "157c"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 6, 24, "15fd"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 6, 28, "15fe"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 6, 28, "157f"),

            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 7, 4, "1652"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 7, 4, "16d3"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 7, 8, "1654"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 7, 8, "16d5"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 7, 12, "16d6"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 7, 12, "1657"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 7, 16, "1658"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 7, 16, "16d9"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 7, 20, "16da"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 7, 20, "165b"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 7, 24, "16dc"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 7, 24, "165d"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 7, 28, "165e"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 7, 28, "16df"),

            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 8, 4, "16f2"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 8, 4, "1673"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 8, 8, "16f4"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 8, 8, "1675"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 8, 12, "1676"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 8, 12, "16f7"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 8, 16, "16f8"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 8, 16, "1679"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 8, 20, "167a"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 8, 20, "16fb"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 8, 24, "167c"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 8, 24, "16fd"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 8, 28, "16fe"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 8, 28, "167f"),

            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 9, 4, "9752"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 9, 4, "97d3"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 9, 8, "9754"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 9, 8, "97d5"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 9, 12, "97d6"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 9, 12, "9757"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 9, 16, "9758"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 9, 16, "97d9"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 9, 20, "97da"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 9, 20, "975b"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 9, 24, "97dc"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 9, 24, "975d"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 9, 28, "975e"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 9, 28, "97df"),

            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 10, 4, "97f2"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 10, 4, "9773"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 10, 8, "97f4"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 10, 8, "9775"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 10, 12, "9776"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 10, 12, "97f7"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 10, 16, "97f8"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 10, 16, "9779"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 10, 20, "977a"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 10, 20, "97fb"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 10, 24, "977c"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 10, 24, "97fd"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 10, 28, "97fe"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 10, 28, "977f"),

            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 11, 4, "1052"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 11, 4, "10d3"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 11, 8, "1054"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 11, 8, "10d5"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 11, 12, "10d6"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 11, 12, "1057"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 11, 16, "1058"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 11, 16, "10d9"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 11, 20, "10da"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 11, 20, "105b"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 11, 24, "10dc"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 11, 24, "105d"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 11, 28, "105e"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 11, 28, "10df"),

            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 12, 4, "1352"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 12, 4, "13d3"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 12, 8, "1354"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 12, 8, "13d5"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 12, 12, "13d6"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 12, 12, "1357"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 12, 16, "1358"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 12, 16, "13d9"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 12, 20, "13da"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 12, 20, "135b"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 12, 24, "13dc"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 12, 24, "135d"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 12, 28, "135e"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 12, 28, "13df"),

            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 13, 4, "13f2"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 13, 4, "1373"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 13, 8, "13f4"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 13, 8, "1375"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 13, 12, "1376"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 13, 12, "13f7"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 13, 16, "13f8"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 13, 16, "1379"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 13, 20, "137a"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 13, 20, "13fb"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 13, 24, "137c"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 13, 24, "13fd"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 13, 28, "13fe"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 13, 28, "137f"),

            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 14, 4, "9452"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 14, 4, "94d3"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 14, 8, "9454"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 14, 8, "94d5"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 14, 12, "94d6"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 14, 12, "9457"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 14, 16, "9458"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 14, 16, "94d9"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 14, 20, "94da"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 14, 20, "945b"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 14, 24, "94dc"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 14, 24, "945d"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 14, 28, "945e"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 14, 28, "94df"),

            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 15, 4, "94f2"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 15, 4, "9473"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 15, 8, "94f4"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 15, 8, "9475"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 15, 12, "9476"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 15, 12, "94f7"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 15, 16, "94f8"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 15, 16, "9479"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 15, 20, "947a"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 15, 20, "94fb"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 15, 24, "947c"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 15, 24, "94fd"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, 15, 28, "94fe"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Underline, 15, 28, "947f"),

            // mid-row commands
            new SccPositionAndStyle(Color.White, SccFontStyle.Regular, -1, -1, "9120"),
            new SccPositionAndStyle(Color.White, SccFontStyle.Underline, -1, -1, "91a1"),
            new SccPositionAndStyle(Color.Green, SccFontStyle.Regular, -1, -1, "91a2"),
            new SccPositionAndStyle(Color.Green, SccFontStyle.Underline, -1, -1, "9123"),
            new SccPositionAndStyle(Color.Blue, SccFontStyle.Regular, -1, -1, "91a4"),
            new SccPositionAndStyle(Color.Blue, SccFontStyle.Underline, -1, -1, "9125"),
            new SccPositionAndStyle(Color.Cyan, SccFontStyle.Regular, -1, -1, "9126"),
            new SccPositionAndStyle(Color.Cyan, SccFontStyle.Underline, -1, -1, "91a7"),
            new SccPositionAndStyle(Color.Red, SccFontStyle.Regular, -1, -1, "91a8"),
            new SccPositionAndStyle(Color.Red, SccFontStyle.Underline, -1, -1, "9129"),
            new SccPositionAndStyle(Color.Yellow, SccFontStyle.Regular, -1, -1, "912a"),
            new SccPositionAndStyle(Color.Yellow, SccFontStyle.Underline, -1, -1, "91ab"),
            new SccPositionAndStyle(Color.Magenta, SccFontStyle.Regular, -1, -1, "912c"),
            new SccPositionAndStyle(Color.Magenta, SccFontStyle.Underline, -1, -1, "91ad"),

            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Italic, -1, -1, "91ae"),
            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Italic | SccFontStyle.Underline, -1, -1, "912f"),

            new SccPositionAndStyle(Color.Transparent, SccFontStyle.Regular, -1, -1, "94a8"), // turn flash on
        });
    }
}
