# 🚨 CRITICAL BUG FIXED: Download Filter Issue

## Problem Identified:
The preflight analyzer was downloading **ALL analyzed episodes** including **REJECTED files**!

```
❌ BEFORE (Bug):
season_episodes = decision.get('episodes', [])  # Gets ALL episodes (ACCEPT + REJECT)
↓
Downloads 25 files (including rejected ones) ← THIS WAS THE PROBLEM!
```

```
✅ AFTER (Fixed):  
all_episodes = decision.get('episodes', [])
season_episodes = [ep for ep in all_episodes if ep.get('decision') in ('ACCEPT', 'RISKY_LOW_PARITY')]
↓
Downloads only 3 accepted files ← PROBLEM SOLVED!
```

## Root Cause:
The preflight analyzer returns ALL episodes it analyzed (including rejected ones) in the `episodes` array. 
The download logic was blindly iterating through ALL episodes instead of filtering for only ACCEPTED ones.

## Impact of Fix:
1. ✅ **No more downloading rejected files** - Only ACCEPT/RISKY_LOW_PARITY episodes are downloaded
2. ✅ **Correct download count** - 3 episodes instead of 25 bogus downloads
3. ✅ **No more REJECT_INCOMPLETE downloads** - Rejected files are filtered out before download
4. ✅ **Sane behavior** - Downloads match what preflight actually recommends

## Testing:
Next FLCL run should show:
- "🚀 Starting downloads for Season 1 immediately..."
- "✅ Started download: [only 3 accepted episodes]"
- No REJECTED files in download list

## Telemetry Note:
The telemetry system we built works perfectly for Sonarr Auto-Grab mode, but preflight has its own direct download mechanism. The telemetry will work when you choose option 2 (⚡ Sonarr Auto-Grab) instead of option 1 (🔬 Preflight Analysis).

---

**Status**: Critical download filtering bug FIXED! 🎉
