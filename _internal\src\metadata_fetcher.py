#!/usr/bin/env python3
from __future__ import annotations

from typing import Any, Dict, Optional
import aiohttp
import logging

from _internal.utils.common_helpers import get_setting
from _internal.utils.http_retry import async_request_json
from _internal.utils.fuzzy_matching import (
    select_best_match, 
    enhance_matching_with_alternatives,
    format_match_result,
    should_auto_approve,
    should_silent_correct,
    should_request_confirmation,
    should_reject,
    extract_candidate_year
)

logger = logging.getLogger(__name__)


async def fetch_tv_metadata_for_intake(title: str, year: Optional[int] = None, settings_dict: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Fetch TV metadata via TMDb with enhanced fuzzy matching and retries.
    Returns {success, cleaned_title, year, tmdb_id, tvdb_id, imdb_id, ...}
    """
    tmdb_api_key = get_setting("APIKeys", "tmdb_api_key", settings_dict=settings_dict)
    if not tmdb_api_key:
        return {"success": False, "error": "TMDb API key not configured"}

    async with aiohttp.ClientSession() as session:
        # Search TMDb TV
        search_url = "https://api.themoviedb.org/3/search/tv"
        
        # Try search with year first if provided
        search_results = []
        if year:
            status, search_data = await async_request_json(session, "GET", search_url, params={
                "api_key": tmdb_api_key,
                "query": title,
                "language": "en-US",
                "first_air_date_year": year
            })
            if status == 200 and isinstance(search_data, dict):
                search_results = search_data.get("results", [])
                logger.info(f"TMDb TV search with year {year}: {len(search_results)} results")
        
        # Fallback search without year if no results
        if not search_results:
            status, search_data = await async_request_json(session, "GET", search_url, params={
                "api_key": tmdb_api_key,
                "query": title,
                "language": "en-US"
            })
            if status != 200 or not isinstance(search_data, dict):
                return {"success": False, "error": f"TMDb API error: {status}"}
            
            search_results = search_data.get("results", [])
            logger.info(f"TMDb TV search without year: {len(search_results)} results")
        
        if not search_results:
            return {"success": False, "error": "No TV show results found"}

        # Enhanced fuzzy matching for TV shows
        try:
            # Use enhanced matching with alternative titles as fallback
            best_match, confidence_score, match_metadata = enhance_matching_with_alternatives(
                query_title=title,
                candidates=search_results,
                api_key=tmdb_api_key,
                user_year=year,
                content_type="tv",
                score_threshold=80.0
            )
            
            if not best_match:
                logger.error(f"No suitable TV match found for '{title}' after fuzzy matching.")
                return {"success": False, "error": "No reliable TV metadata match found"}
            
            # Format the match result with confidence information
            match_result = format_match_result(
                candidate=best_match,
                score=confidence_score,
                match_metadata=match_metadata,
                content_type="tv",
                original_query=f"{title} ({year})" if year else title
            )
            
            # Handle different confidence levels
            if should_reject(confidence_score):
                logger.warning(f"Rejecting low confidence TV match: {match_result['message']}")
                return {
                    "success": False, 
                    "error": match_result.get("error", "Low confidence TV match rejected"),
                    "confidence_score": confidence_score,
                    "best_guess": match_result.get("matched_title")
                }
            
            elif should_request_confirmation(confidence_score):
                logger.warning(f"Ambiguous TV match requires confirmation: {match_result['message']}")
                return {
                    "success": False,
                    "error": f"Ambiguous TV match (confidence {confidence_score:.1f}%). User confirmation needed.",
                    "confidence_score": confidence_score,
                    "best_guess": match_result.get("matched_title"),
                    "suggestion": match_result.get("suggestion")
                }
            
            # Log the matching decision
            if should_auto_approve(confidence_score):
                logger.info(f"Auto-approved TV match: {match_result['message']}")
            elif should_silent_correct(confidence_score):
                logger.info(f"Silent TV correction applied: {match_result['message']}")

            tv_id = best_match.get("id")
            logger.info(f"Selected TV match for '{title}': ID={tv_id}, Name='{best_match.get('name')}', Confidence={confidence_score:.1f}%")

        except Exception as e:
            logger.error(f"Error during TV fuzzy matching for '{title}': {e}")
            # Fallback to original simple matching
            best_match = None
            if year and isinstance(year, int):
                for r in search_results:
                    fy = r.get("first_air_date")
                    if fy and str(year) in str(fy):
                        best_match = r
                        break
            if best_match is None:
                best_match = search_results[0]
            
            tv_id = best_match.get("id")
            confidence_score = 100.0  # Assume high confidence for fallback
            match_result = {"warning": "TV fuzzy matching failed, used fallback selection"}

        # Get detailed TV information
        details_url = f"https://api.themoviedb.org/3/tv/{tv_id}"
        status, details = await async_request_json(session, "GET", details_url, params={
            "api_key": tmdb_api_key,
            "language": "en-US",
            "append_to_response": "external_ids"
        })
        if status != 200 or not isinstance(details, dict):
            return {"success": False, "error": f"TMDb TV details error: {status}"}

        name = details.get("name", title)
        first_air = details.get("first_air_date", "")
        year_final = int(first_air.split("-")[0]) if first_air else year
        external_ids = details.get("external_ids", {}) or {}

        result = {
            "success": True,
            "cleaned_title": name,
            "year": year_final,
            "tmdb_id": str(tv_id),
            "tvdb_id": str(external_ids.get("tvdb_id", "")),
            "imdb_id": external_ids.get("imdb_id", ""),
            "overview": details.get("overview", ""),
            "genres": [g.get("name") for g in details.get("genres", [])],
            "status": details.get("status", ""),
            "in_production": details.get("in_production", False),
            "number_of_seasons": details.get("number_of_seasons", 0),
            "number_of_episodes": details.get("number_of_episodes", 0),
            "networks": [n.get("name") for n in details.get("networks", [])],
            "metadata_source": "TMDb TV API",
            "confidence_score": confidence_score,
            "fuzzy_matching_used": True
        }
        
        # Add warning for moderate confidence matches
        if should_silent_correct(confidence_score):
            result["warning"] = match_result.get("warning", f"TV fuzzy matched with {confidence_score:.1f}% confidence")
        
        # Add matching metadata for debugging/logging
        if "match_metadata" in locals():
            result["match_metadata"] = match_metadata
        
        return result


async def fetch_tvdb_episode_data(tvdb_id: str, settings_dict: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """Fetch detailed episode data from TVDB v4; fallback to TMDb, with retries.
    Returns {success, episodes_by_season, chronological_episodes, metadata_source, ...}
    """
    if not tvdb_id or tvdb_id == "unknown":
        return {"success": False, "error": "No TVDB ID provided"}

    tvdb_api_key = get_setting("APIKeys", "tvdb_api_key", settings_dict=settings_dict) if settings_dict else None
    tmdb_api_key = get_setting("APIKeys", "tmdb_api_key", settings_dict=settings_dict) if settings_dict else None

    # Try TVDB v4 first
    if tvdb_api_key:
        try:
            async with aiohttp.ClientSession() as session:
                status, login_result = await async_request_json(session, "POST", "https://api4.thetvdb.com/v4/login", json={"apikey": tvdb_api_key})
                token = login_result.get("data", {}).get("token") if isinstance(login_result, dict) else None
                if token:
                    headers = {"Authorization": f"Bearer {token}"}
                    status, ep_data = await async_request_json(session, "GET", f"https://api4.thetvdb.com/v4/series/{tvdb_id}/episodes/default", headers=headers)
                    if status == 200 and isinstance(ep_data, dict):
                        data_list = ep_data.get("data", [])
                        episodes_by_season: Dict[int, Dict[str, Any]] = {}
                        chronological_episodes = []
                        for item in data_list:
                            s = item.get("seasonNumber")
                            e = item.get("number")
                            if s and e and s != 0:
                                episodes_by_season.setdefault(s, {"episodes": [], "episode_count": 0})
                                episodes_by_season[s]["episodes"].append({"season": s, "episode": e, "title": item.get("name", "")})
                                episodes_by_season[s]["episode_count"] += 1
                                chronological_episodes.append({"season": s, "episode": e})
                        return {
                            "success": True,
                            "series_title": f"TVDB Series {tvdb_id}",
                            "total_seasons": len(episodes_by_season),
                            "total_episodes": sum(v.get("episode_count", 0) for v in episodes_by_season.values()),
                            "episodes_by_season": episodes_by_season,
                            "chronological_episodes": chronological_episodes,
                            "metadata_source": "TVDB v4 API (AIRED ORDER)",
                            "ordering_type": "AIRED"
                        }
        except Exception:
            pass

    # Fallback to TMDb
    if not tmdb_api_key:
        return {"success": False, "error": "Neither TVDB nor TMDb API key available"}

    base = "https://api.themoviedb.org/3"
    async with aiohttp.ClientSession() as session:
        status, series_data = await async_request_json(session, "GET", f"{base}/tv/{tvdb_id}", params={"api_key": tmdb_api_key})
        if status != 200 or not isinstance(series_data, dict):
            return {"success": False, "error": f"TMDb API error: {status}"}
        num_seasons = series_data.get("number_of_seasons", 0)

        episodes_by_season = {}
        chronological_episodes = []
        total_episodes = 0
        for season_num in range(1, num_seasons + 1):
            status, season_data = await async_request_json(session, "GET", f"{base}/tv/{tvdb_id}/season/{season_num}", params={"api_key": tmdb_api_key})
            if status == 200 and isinstance(season_data, dict):
                eps = season_data.get("episodes", [])
                episodes_by_season[season_num] = {
                    "episodes": [
                        {"season": season_num, "episode": ep.get("episode_number"), "title": ep.get("name", "")}
                        for ep in eps if isinstance(ep, dict)
                    ],
                    "episode_count": len(eps)
                }
                chronological_episodes.extend({"season": season_num, "episode": ep.get("episode_number")} for ep in eps)
                total_episodes += len(eps)
        return {
            "success": True,
            "series_title": f"TMDb Series {tvdb_id}",
            "total_seasons": num_seasons,
            "total_episodes": total_episodes,
            "episodes_by_season": episodes_by_season,
            "chronological_episodes": chronological_episodes,
            "metadata_source": "TMDb TV API (fallback from TVDB)",
            "ordering_type": "AIRED_FALLBACK"
        }

