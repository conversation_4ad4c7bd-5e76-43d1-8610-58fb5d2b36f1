from _internal.src.telemetry_dashboard import aggregate_counts


def test_daily_and_resolution_aggregation():
    events = [
        {'ts': '2025-01-01T00:00:00+00:00', 'type': 'file.organized', 'data': {'type': 'movie', 'title': 'A', 'year': 2021, 'resolution': '1080p'}},
        {'ts': '2025-01-01T12:00:00+00:00', 'type': 'file.organized', 'data': {'type': 'episode', 'title': 'B', 'year': 2021, 'series_id': 5, 'resolution': '4k'}},
        {'ts': '2025-01-02T00:00:00+00:00', 'type': 'download.completed', 'data': {}},
    ]
    summary = aggregate_counts(events)
    assert summary['by_day']['2025-01-01'] == 2
    assert summary['by_resolution']['1080p'] == 1
    assert summary['by_resolution']['4k'] == 1

