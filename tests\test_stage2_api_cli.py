import sys
import json
import asyncio
from pathlib import Path
from unittest.mock import patch

REPO_ROOT = Path(__file__).resolve().parents[1]
sys.path.insert(0, str(REPO_ROOT))

from _internal.src.event_queue import get_event_queue


def test_stage2_api_cli_counts_events(tmp_path, capsys):
    # Prepare EventQueue in temp dir and one sab.postprocess
    eq_dir = tmp_path / 'events'
    eq = get_event_queue({'EventQueue': {'enabled': True, 'dir': str(eq_dir)}})

    dl = tmp_path / 'Some.Movie.2018.1080p.WEB-DL'
    dl.mkdir(parents=True, exist_ok=True)
    main = dl / 'Some.Movie.2018.1080p.WEB-DL.mkv'
    main.write_bytes(b'0' * 1024 * 1024)

    async def _emit():
        await eq.publish('sab.postprocess', {
            'final_folder': str(dl),
            'original_nzb_name': dl.name,
            'clean_job_name': dl.name,
            'status': '0'
        })
    asyncio.run(_emit())

    # Run CLI and capture output
    from _internal.src.stage2_api_cli import main as cli_main
    out_dir = tmp_path / 'organized'
    sys.argv = ['prog', '--events', str(eq_dir), '--output', str(out_dir)]
    cli_main()

    # Run again to ensure cursor advances and processed count returns 0
    sys.argv = ['prog', '--events', str(eq_dir), '--output', str(out_dir)]
    cli_main()

    captured = capsys.readouterr().out
    lines = captured.splitlines()
    assert any('Processed 1 event(s)' in line for line in lines)
    assert any('Processed 0 event(s)' in line for line in lines)

