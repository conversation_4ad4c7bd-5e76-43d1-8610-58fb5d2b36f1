#!/usr/bin/env python3
from __future__ import annotations

# Central list of allowed video extensions (lowercase)
VIDEO_EXTENSIONS = [
    ".mkv",
    ".mp4",
    ".m4v",
    ".avi",
    ".mov",
    ".ts",  # Transport Stream files
    ".m2ts",  # Blu-ray video files
]

# Disk image extensions that need special handling
DISK_IMAGE_EXTENSIONS = [
    ".iso",  # ISO disk image files
]

SUBTITLE_EXTENSIONS = [
    ".srt",
    ".sup",
]

