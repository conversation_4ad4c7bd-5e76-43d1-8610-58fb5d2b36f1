#!/usr/bin/env python3
"""
PlexAutomator/_internal/utils/fuzzy_config.py

Configuration settings for fuzzy metadata matching.
Allows users to customize confidence thresholds and matching behavior.
"""

import configparser
import logging
from pathlib import Path
from typing import Dict, Any, Optional

DEFAULT_CONFIG = {
    "fuzzy_matching": {
        "high_confidence_threshold": "95",
        "moderate_confidence_threshold": "80", 
        "low_confidence_threshold": "60",
        "year_tolerance": "3",
        "use_alternative_titles": "true",
        "max_alternative_title_requests": "3",
        "popularity_tiebreaker_threshold": "3",
        "enable_interactive_confirmation": "false",
        "log_fuzzy_decisions": "true"
    },
    "tmdb_optimization": {
        "search_concurrency_limit": "5",
        "cache_search_results": "true",
        "cache_detail_results": "true",
        "cache_timeout_hours": "24",
        "max_retry_attempts": "3",
        "retry_delay_seconds": "2"
    }
}


class FuzzyMatchingConfig:
    """Configuration manager for fuzzy matching settings."""
    
    def __init__(self, config_path: Optional[Path] = None):
        self.config_path = config_path or Path("config/fuzzy_matching_config.ini")
        self.config = configparser.ConfigParser()
        self._load_config()
    
    def _load_config(self):
        """Load configuration from file, creating defaults if needed."""
        if self.config_path.exists():
            try:
                self.config.read(self.config_path)
            except Exception as e:
                print(f"Warning: Could not read fuzzy matching config: {e}")
                self._create_default_config()
        else:
            self._create_default_config()
    
    def _create_default_config(self):
        """Create default configuration file."""
        self.config.read_dict(DEFAULT_CONFIG)
        
        # Ensure directory exists
        self.config_path.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            with open(self.config_path, 'w') as f:
                self.config.write(f)
            print(f"Created default fuzzy matching config at: {self.config_path}")
        except Exception as e:
            print(f"Warning: Could not create fuzzy matching config file: {e}")
    
    def get_threshold(self, threshold_type: str) -> float:
        """Get confidence threshold value."""
        thresholds = {
            "high": self.config.getfloat("fuzzy_matching", "high_confidence_threshold", fallback=95.0),
            "moderate": self.config.getfloat("fuzzy_matching", "moderate_confidence_threshold", fallback=80.0),
            "low": self.config.getfloat("fuzzy_matching", "low_confidence_threshold", fallback=60.0)
        }
        return thresholds.get(threshold_type, 95.0)
    
    def get_year_tolerance(self) -> int:
        """Get year tolerance for matching."""
        return self.config.getint("fuzzy_matching", "year_tolerance", fallback=3)
    
    def use_alternative_titles(self) -> bool:
        """Check if alternative titles should be used."""
        return self.config.getboolean("fuzzy_matching", "use_alternative_titles", fallback=True)
    
    def max_alternative_title_requests(self) -> int:
        """Get maximum number of alternative title API requests."""
        return self.config.getint("fuzzy_matching", "max_alternative_title_requests", fallback=3)
    
    def popularity_tiebreaker_threshold(self) -> float:
        """Get threshold for using popularity as tiebreaker."""
        return self.config.getfloat("fuzzy_matching", "popularity_tiebreaker_threshold", fallback=3.0)
    
    def enable_interactive_confirmation(self) -> bool:
        """Check if interactive confirmation is enabled."""
        return self.config.getboolean("fuzzy_matching", "enable_interactive_confirmation", fallback=False)
    
    def log_fuzzy_decisions(self) -> bool:
        """Check if fuzzy matching decisions should be logged."""
        return self.config.getboolean("fuzzy_matching", "log_fuzzy_decisions", fallback=True)
    
    def get_search_concurrency_limit(self) -> int:
        """Get concurrency limit for TMDb searches."""
        return self.config.getint("tmdb_optimization", "search_concurrency_limit", fallback=5)
    
    def cache_search_results(self) -> bool:
        """Check if search results should be cached."""
        return self.config.getboolean("tmdb_optimization", "cache_search_results", fallback=True)
    
    def cache_detail_results(self) -> bool:
        """Check if detail results should be cached."""
        return self.config.getboolean("tmdb_optimization", "cache_detail_results", fallback=True)
    
    def get_cache_timeout_hours(self) -> int:
        """Get cache timeout in hours."""
        return self.config.getint("tmdb_optimization", "cache_timeout_hours", fallback=24)
    
    def get_max_retry_attempts(self) -> int:
        """Get maximum retry attempts for API calls."""
        return self.config.getint("tmdb_optimization", "max_retry_attempts", fallback=3)
    
    def get_retry_delay_seconds(self) -> float:
        """Get retry delay in seconds."""
        return self.config.getfloat("tmdb_optimization", "retry_delay_seconds", fallback=2.0)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        return {
            "thresholds": {
                "high_confidence": self.get_threshold("high"),
                "moderate_confidence": self.get_threshold("moderate"), 
                "low_confidence": self.get_threshold("low")
            },
            "year_tolerance": self.get_year_tolerance(),
            "use_alternative_titles": self.use_alternative_titles(),
            "max_alternative_title_requests": self.max_alternative_title_requests(),
            "popularity_tiebreaker_threshold": self.popularity_tiebreaker_threshold(),
            "enable_interactive_confirmation": self.enable_interactive_confirmation(),
            "log_fuzzy_decisions": self.log_fuzzy_decisions(),
            "tmdb_optimization": {
                "search_concurrency_limit": self.get_search_concurrency_limit(),
                "cache_search_results": self.cache_search_results(),
                "cache_detail_results": self.cache_detail_results(),
                "cache_timeout_hours": self.get_cache_timeout_hours(),
                "max_retry_attempts": self.get_max_retry_attempts(),
                "retry_delay_seconds": self.get_retry_delay_seconds()
            }
        }
    
    def update_threshold(self, threshold_type: str, value: float):
        """Update a confidence threshold."""
        threshold_keys = {
            "high": "high_confidence_threshold",
            "moderate": "moderate_confidence_threshold",
            "low": "low_confidence_threshold"
        }
        
        if threshold_type in threshold_keys:
            if not self.config.has_section("fuzzy_matching"):
                self.config.add_section("fuzzy_matching")
            
            self.config.set("fuzzy_matching", threshold_keys[threshold_type], str(value))
            self._save_config()
    
    def _save_config(self):
        """Save configuration to file."""
        try:
            with open(self.config_path, 'w') as f:
                self.config.write(f)
        except Exception as e:
            print(f"Warning: Could not save fuzzy matching config: {e}")


# Global configuration instance
_global_config: Optional[FuzzyMatchingConfig] = None


def get_fuzzy_config(config_path: Optional[Path] = None) -> FuzzyMatchingConfig:
    """Get global fuzzy matching configuration instance."""
    global _global_config
    if _global_config is None:
        _global_config = FuzzyMatchingConfig(config_path)
    return _global_config


def reset_fuzzy_config():
    """Reset global configuration (useful for testing)."""
    global _global_config
    _global_config = None


# ==============================================================================
# ENHANCED FUZZY MATCHING CONFIG (previously optimized_fuzzy_config.py)
# ==============================================================================

# Extended DEFAULT_CONFIG with enhanced settings
ENHANCED_DEFAULT_CONFIG = {
    "fuzzy_matching": {
        "high_confidence_threshold": "95",
        "moderate_confidence_threshold": "80", 
        "low_confidence_threshold": "60",
        "year_tolerance": "3",
        "use_alternative_titles": "true",
        "max_alternative_title_requests": "3",
        "popularity_tiebreaker_threshold": "3",
        "enable_interactive_confirmation": "false",
        "log_fuzzy_decisions": "true",
        
        # Enhanced thresholds
        "auto_approve_threshold": "95.0",
        "silent_accept_threshold": "80.0", 
        "reject_threshold": "40.0",
        "movie_year_tolerance": "3",
        "tv_year_tolerance": "5",
        
        # Performance settings
        "enable_fast_path": "true",
        "enable_caching": "true",
        "cache_ttl_days": "7",
        "max_tmdb_api_calls_per_query": "5",
        
        # Spell checking and normalization
        "enable_spell_checking": "true",
        "skip_spell_check_for_non_english": "true",
        "apply_common_typo_fixes": "true",
        
        # Variant generation
        "max_title_variants": "10",
        "enable_abbreviation_expansion": "true",
        "enable_franchise_detection": "true",
        
        # Alternative titles
        "enable_alternative_titles": "true",
        "alternative_title_score_threshold": "80.0",
        "max_alternative_title_candidates": "3",
        
        # Automation behavior
        "batch_automation_mode": "true",
        "enable_interactive_confirmation": "false",
        "auto_proceed_on_low_confidence": "true",
        "log_all_decisions": "true"
    },
    "tmdb_optimization": {
        "search_concurrency_limit": "5",
        "cache_search_results": "true",
        "cache_detail_results": "true",
        "cache_timeout_hours": "24",
        "max_retry_attempts": "3",
        "retry_delay_seconds": "2"
    },
    "performance_tuning": {
        "enable_performance_logging": "true",
        "log_timing_details": "true",
        "warn_on_slow_matches_seconds": "10.0",
        "max_cache_entries": "10000",
        "cache_cleanup_interval_hours": "24"
    },
    "franchise_preferences": {
        # Examples: preferred_years:avoid_years:bonus_multiplier:future_penalty
        "batman": "1989,2005,2008,2012:1997:1.2:0.1",
        "spider-man": "2002,2004,2007,2012,2017:1977,2018:1.1:0.05"
    }
}


class EnhancedFuzzyMatchingConfig(FuzzyMatchingConfig):
    """Enhanced configuration manager for fuzzy matching with automation settings."""
    
    def __init__(self, config_path: Optional[Path] = None):
        # Call parent constructor
        super().__init__(config_path)
        
        # Merge enhanced defaults with existing config
        self._ensure_enhanced_defaults()
        
    def _ensure_enhanced_defaults(self):
        """Ensure enhanced default values are available."""
        for section_name, section_data in ENHANCED_DEFAULT_CONFIG.items():
            if not self.config.has_section(section_name):
                self.config.add_section(section_name)
            
            for key, value in section_data.items():
                if not self.config.has_option(section_name, key):
                    self.config.set(section_name, key, value)
    
    # Enhanced confidence thresholds
    def get_auto_approve_threshold(self) -> float:
        """Get auto-approve confidence threshold."""
        return self.config.getfloat("fuzzy_matching", "auto_approve_threshold", fallback=95.0)
        
    def get_silent_accept_threshold(self) -> float:
        """Get silent accept confidence threshold."""
        return self.config.getfloat("fuzzy_matching", "silent_accept_threshold", fallback=80.0)
        
    def get_low_confidence_threshold(self) -> float:
        """Get low confidence threshold."""
        return self.config.getfloat("fuzzy_matching", "low_confidence_threshold", fallback=60.0)
        
    def get_reject_threshold(self) -> float:
        """Get rejection threshold."""
        return self.config.getfloat("fuzzy_matching", "reject_threshold", fallback=40.0)
        
    # Enhanced year tolerance
    def get_movie_year_tolerance(self) -> int:
        """Get year tolerance for movies."""
        return self.config.getint("fuzzy_matching", "movie_year_tolerance", fallback=3)
        
    def get_tv_year_tolerance(self) -> int:
        """Get year tolerance for TV shows."""
        return self.config.getint("fuzzy_matching", "tv_year_tolerance", fallback=5)
        
    def get_year_tolerance(self, content_type: str) -> int:
        """Get year tolerance for specific content type."""
        if content_type == "movie":
            return self.get_movie_year_tolerance()
        elif content_type == "tv":
            return self.get_tv_year_tolerance()
        else:
            return 3  # Default fallback
            
    # Performance settings
    def is_fast_path_enabled(self) -> bool:
        """Check if fast path optimization is enabled."""
        return self.config.getboolean("fuzzy_matching", "enable_fast_path", fallback=True)
        
    def is_caching_enabled(self) -> bool:
        """Check if caching is enabled."""
        return self.config.getboolean("fuzzy_matching", "enable_caching", fallback=True)
        
    def get_cache_ttl_days(self) -> int:
        """Get cache TTL in days."""
        return self.config.getint("fuzzy_matching", "cache_ttl_days", fallback=7)
        
    def get_max_tmdb_api_calls(self) -> int:
        """Get maximum TMDb API calls per query."""
        return self.config.getint("fuzzy_matching", "max_tmdb_api_calls_per_query", fallback=5)
        
    # Spell checking and normalization
    def is_spell_checking_enabled(self) -> bool:
        """Check if spell checking is enabled."""
        return self.config.getboolean("fuzzy_matching", "enable_spell_checking", fallback=True)
        
    def skip_spell_check_for_non_english(self) -> bool:
        """Check if spell checking should be skipped for non-English titles."""
        return self.config.getboolean("fuzzy_matching", "skip_spell_check_for_non_english", fallback=True)
        
    def apply_common_typo_fixes(self) -> bool:
        """Check if common typo fixes should be applied."""
        return self.config.getboolean("fuzzy_matching", "apply_common_typo_fixes", fallback=True)
        
    # Variant generation
    def get_max_title_variants(self) -> int:
        """Get maximum number of title variants to generate."""
        return self.config.getint("fuzzy_matching", "max_title_variants", fallback=10)
        
    def is_abbreviation_expansion_enabled(self) -> bool:
        """Check if abbreviation expansion is enabled."""
        return self.config.getboolean("fuzzy_matching", "enable_abbreviation_expansion", fallback=True)
        
    def is_franchise_detection_enabled(self) -> bool:
        """Check if franchise detection is enabled."""
        return self.config.getboolean("fuzzy_matching", "enable_franchise_detection", fallback=True)
        
    # Alternative titles
    def is_alternative_titles_enabled(self) -> bool:
        """Check if alternative title lookup is enabled."""
        return self.config.getboolean("fuzzy_matching", "enable_alternative_titles", fallback=True)
        
    def get_alternative_title_score_threshold(self) -> float:
        """Get threshold for alternative title lookup."""
        return self.config.getfloat("fuzzy_matching", "alternative_title_score_threshold", fallback=80.0)
        
    def get_max_alternative_title_candidates(self) -> int:
        """Get maximum candidates to check for alternative titles."""
        return self.config.getint("fuzzy_matching", "max_alternative_title_candidates", fallback=3)
        
    # Automation behavior
    def is_batch_automation_mode(self) -> bool:
        """Check if batch automation mode is enabled."""
        return self.config.getboolean("fuzzy_matching", "batch_automation_mode", fallback=True)
        
    def is_interactive_confirmation_enabled(self) -> bool:
        """Check if interactive confirmation is enabled."""
        return self.config.getboolean("fuzzy_matching", "enable_interactive_confirmation", fallback=False)
        
    def auto_proceed_on_low_confidence(self) -> bool:
        """Check if system should auto-proceed on low confidence matches."""
        return self.config.getboolean("fuzzy_matching", "auto_proceed_on_low_confidence", fallback=True)
        
    def log_all_decisions(self) -> bool:
        """Check if all matching decisions should be logged."""
        return self.config.getboolean("fuzzy_matching", "log_all_decisions", fallback=True)
        
    # Franchise preferences
    def get_franchise_preferences(self) -> Dict[str, Dict[str, Any]]:
        """Get franchise-specific preferences."""
        preferences = {}
        
        if self.config.has_section("franchise_preferences"):
            for franchise, rule_string in self.config["franchise_preferences"].items():
                try:
                    # Parse rule format: preferred_years:avoid_years:bonus_multiplier:future_penalty
                    parts = rule_string.split(":")
                    if len(parts) >= 4:
                        preferred_years = [int(y.strip()) for y in parts[0].split(",") if y.strip().isdigit()]
                        avoid_years = [int(y.strip()) for y in parts[1].split(",") if y.strip().isdigit()]
                        bonus_multiplier = float(parts[2]) if parts[2] else 1.0
                        future_penalty = float(parts[3]) if parts[3] else 0
                        
                        preferences[franchise] = {
                            'preferred_years': preferred_years,
                            'avoid_years': avoid_years,
                            'bonus_multiplier': bonus_multiplier,
                            'future_penalty': future_penalty
                        }
                except (ValueError, IndexError) as e:
                    logger = logging.getLogger(__name__)
                    logger.warning(f"Invalid franchise preference rule for '{franchise}': {rule_string} - {e}")
                    
        return preferences
        
    # Performance tuning
    def is_performance_logging_enabled(self) -> bool:
        """Check if performance logging is enabled."""
        return self.config.getboolean("performance_tuning", "enable_performance_logging", fallback=True)
        
    def log_timing_details(self) -> bool:
        """Check if timing details should be logged."""
        return self.config.getboolean("performance_tuning", "log_timing_details", fallback=True)
        
    def get_slow_match_warning_threshold(self) -> float:
        """Get threshold for warning about slow matches."""
        return self.config.getfloat("performance_tuning", "warn_on_slow_matches_seconds", fallback=10.0)
        
    def get_max_cache_entries(self) -> int:
        """Get maximum cache entries."""
        return self.config.getint("performance_tuning", "max_cache_entries", fallback=10000)
        
    def get_cache_cleanup_interval_hours(self) -> int:
        """Get cache cleanup interval in hours."""
        return self.config.getint("performance_tuning", "cache_cleanup_interval_hours", fallback=24)


# Enhanced factory function
def get_enhanced_fuzzy_config(config_path: Optional[Path] = None) -> EnhancedFuzzyMatchingConfig:
    """Get enhanced fuzzy matching configuration instance."""
    return EnhancedFuzzyMatchingConfig(config_path)


# Backward compatibility aliases
def get_optimized_fuzzy_config(config_path: Optional[Path] = None) -> EnhancedFuzzyMatchingConfig:
    """Legacy function name for backward compatibility."""
    return get_enhanced_fuzzy_config(config_path)


# Make enhanced version the default
_global_enhanced_config = None

def get_fuzzy_config(config_path: Optional[Path] = None) -> EnhancedFuzzyMatchingConfig:
    """Get fuzzy matching configuration (enhanced version by default)."""
    global _global_enhanced_config
    if _global_enhanced_config is None:
        _global_enhanced_config = EnhancedFuzzyMatchingConfig(config_path)
    return _global_enhanced_config
