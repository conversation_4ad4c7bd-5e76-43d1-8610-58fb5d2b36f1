# Refactor 2: Telemetry Migration Guide

This guide shows exactly how to replace static "download started" messages with real-time telemetry verification in PlexAutomator scripts.

## The Problem (Refactor 2)

**Current Static Messages:**
```python
# 01_intake_and_nzb_search.py
print(f"✅ Successfully added to Radarr!")
print(f"✅ TV show successfully added to Sonarr!")
print(f"🚀 Immediate download started: {title}")

# 02_download_and_organize.py  
# Manual queue polling with no verification of actual download status
```

**The Issue:** These messages don't reflect reality. Adding to Radarr ≠ downloads actually starting.

## The Solution

Replace static messages with **real-time telemetry** that verifies downloads are actually queued and progressing.

## Step 1: Basic Integration Pattern

### Minimal Change Pattern (for 01_intake_and_nzb_search.py)

**Before:**
```python
# Around line 3302
print(f"✅ Successfully added to Radarr!")

# Around line 3795  
print(f"✅ TV show successfully added to Sonarr!")

# Around line 3965
print(f"🚀 Immediate download started: {title}")
```

**After:**
```python
# Add import at top
from _internal.utils.telemetry_integration import TelemetryIntegrator

# Replace static messages with telemetry tracking
async def process_with_telemetry(settings_dict, logger_instance):
    async with TelemetryIntegrator(settings_dict, logger_instance) as integrator:
        
        # Instead of: print(f"✅ Successfully added to Radarr!")
        # Track the movie and verify downloads start
        job_id = integrator.track_movie_download(title, radarr_movie_id, quality_profile)
        print(f"📊 Tracking download: {title} (Job: {job_id[:8]})")
        
        # Instead of: print(f"✅ TV show successfully added to Sonarr!")  
        # Track the episode and verify downloads start
        job_id = integrator.track_episode_download(title, sonarr_series_id, episode_id, quality_profile)
        print(f"📊 Tracking episode download: {title} (Job: {job_id[:8]})")
        
        # Monitor all tracked downloads
        if integrator.get_active_download_count() > 0:
            print(f"\n🔍 Monitoring {integrator.get_active_download_count()} downloads...")
            success = await integrator.monitor_downloads(interval=5, timeout=300)  # 5min timeout
            
            if success:
                print("🎉 All downloads verified and started!")
            else:
                print("⚠️ Some downloads may not have started - check logs")
```

### Drop-in Replacement Functions

**For immediate use without major refactoring:**

```python
# Add these helper functions to your existing script
from _internal.utils.telemetry_integration import TelemetryIntegrator, LegacyBridge

# Global integrator (initialize once at script start)
telemetry_integrator = None

async def init_telemetry(settings_dict, logger):
    global telemetry_integrator
    telemetry_integrator = TelemetryIntegrator(settings_dict, logger)
    await telemetry_integrator.__aenter__()

async def cleanup_telemetry():
    global telemetry_integrator
    if telemetry_integrator:
        await telemetry_integrator.__aexit__(None, None, None)

# Replace existing success messages
def track_movie_success(title, radarr_id, quality=None):
    """Replace: print(f"✅ Successfully added to Radarr!")"""
    if telemetry_integrator:
        job_id = telemetry_integrator.track_movie_download(title, radarr_id, quality)
        print(f"📊 Movie tracked: {title} (Job: {job_id[:8]})")
    else:
        print(f"✅ Successfully added to Radarr: {title}")

def track_episode_success(title, sonarr_id, episode_id=None, quality=None):
    """Replace: print(f"✅ TV show successfully added to Sonarr!")"""
    if telemetry_integrator:
        job_id = telemetry_integrator.track_episode_download(title, sonarr_id, episode_id, quality)
        print(f"📊 Episode tracked: {title} (Job: {job_id[:8]})")
    else:
        print(f"✅ TV show successfully added to Sonarr: {title}")

async def verify_all_downloads():
    """Call at end of script to verify all downloads actually started"""
    if telemetry_integrator and telemetry_integrator.get_active_download_count() > 0:
        print(f"\n🔍 Verifying {telemetry_integrator.get_active_download_count()} downloads...")
        success = await telemetry_integrator.monitor_downloads(interval=5, timeout=300)
        if success:
            print("🎉 All downloads verified!")
        else:
            print("⚠️ Some downloads may not have started properly")
    else:
        print("✅ No downloads to verify")
```

## Step 2: Integration Points

### In 01_intake_and_nzb_search.py

**Key locations to modify:**

1. **Line ~3302** (Movie success): Replace with `track_movie_success(title, movie_id, quality)`
2. **Line ~3795** (TV success): Replace with `track_episode_success(title, series_id, episode_id, quality)`  
3. **Line ~3965** (Download started): Remove static message, telemetry will show real status
4. **End of main()**: Add `await verify_all_downloads()`

### In 02_download_and_organize.py

**Key changes:**

1. **Replace manual queue polling** with telemetry state recovery
2. **Add download completion verification** before organization starts
3. **Integrate with existing SABnzbd monitoring** for unified view

```python
# In 02_download_and_organize.py main function
async def main_with_telemetry():
    async with TelemetryIntegrator(settings_dict, logger) as integrator:
        # Check for any downloads still active from previous scripts
        active_count = integrator.get_active_download_count()
        
        if active_count > 0:
            print(f"📥 Found {active_count} active downloads from previous steps")
            print("🕐 Waiting for downloads to complete before organizing...")
            
            success = await integrator.monitor_downloads()
            if success:
                print("✅ All downloads completed - starting organization")
            else:
                print("⚠️ Some downloads incomplete - organizing available files")
        
        # Continue with existing organization logic
        await organize_completed_downloads()
```

## Step 3: Configuration

**Add to your settings.ini:**

```ini
[Telemetry]
enabled = true
polling_interval = 5
max_wait_time = 1800
state_file = _internal/state/telemetry_state.json
real_time_output = true
```

**The telemetry system will automatically use your existing Sonarr/Radarr/SABnzbd settings.**

## Step 4: Testing

### Test the Integration

1. **Dry Run**: Set `enabled = false` in telemetry config - will show what would be tracked
2. **Single Movie**: Test with one movie request to verify tracking works
3. **Full Integration**: Run normal workflow and verify real-time monitoring

### Expected Output Changes

**Before (Static):**
```
✅ Successfully added to Radarr!
✅ TV show successfully added to Sonarr!
🚀 Immediate download started: Movie Title
```

**After (Telemetry):**
```
📊 Movie tracked: Movie Title (Job: a1b2c3d4)
📊 Episode tracked: Show S01E01 (Job: e5f6g7h8)

🔍 Monitoring 2 downloads...
📥 Job a1b2c3d4: Movie Title -> Queued in SABnzbd
📥 Job e5f6g7h8: Show S01E01 -> Downloading (15% complete)
⏱️  Job a1b2c3d4: Movie Title -> Downloading (3% complete)
✅ Job e5f6g7h8: Show S01E01 -> Download Complete!
✅ Job a1b2c3d4: Movie Title -> Download Complete!

🎉 All downloads verified and completed!
```

## Step 5: Rollback Plan

If telemetry causes issues:

1. **Disable telemetry**: Set `enabled = false` in config
2. **Fallback mode**: Scripts will use original static messages
3. **Remove integration**: Comment out telemetry import lines

## Benefits of This Migration

1. **Truth in Messaging**: Only show "download started" when downloads actually start
2. **Real-time Monitoring**: See actual download progress instead of guessing
3. **Error Detection**: Catch failed queue additions immediately  
4. **Pipeline Verification**: Confirm each step works before proceeding
5. **Debugging**: Structured logs show exactly what happened when

## Example: Complete Integration for One Function

**Before:**
```python
def add_movie_to_radarr(title, tmdb_id):
    # ... existing Radarr API call ...
    if response.status_code == 201:
        print(f"✅ Successfully added to Radarr!")
        return True
    return False
```

**After:**
```python
async def add_movie_to_radarr(title, tmdb_id, integrator):
    # ... existing Radarr API call ...
    if response.status_code == 201:
        movie_data = response.json()
        movie_id = movie_data.get('id')
        quality = movie_data.get('qualityProfileId')
        
        # Track with telemetry instead of static message
        job_id = integrator.track_movie_download(title, movie_id, quality)
        print(f"📊 Movie queued: {title} (Tracking: {job_id[:8]})")
        return True
    return False
```

This migration turns PlexAutomator from "hope it works" to "know it works" with real verification at every step.
