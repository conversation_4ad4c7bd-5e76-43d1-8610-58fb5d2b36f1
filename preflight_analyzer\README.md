# Preflight Analyzer Prototype

Isolated prototype for NZB pre-download risk assessment. Not yet integrated into core Automator.

## Goals (Prototype Phase)
- Parse NZB and derive static risk features (poster, age, group, subject, segment counts, parity heuristic)
- Query indexer (Newznab) for extended metadata (optional placeholder)
- Adaptive STAT sampling against configured NNTP servers (async)
- Compute preliminary risk score (pluggable weights)
- Emit structured JSON report for a single test show/release

## Non-Goals (For Now)
- Full Sonarr/Radarr interception emulation
- Season pack vs individual comparison logic (will follow after core probe validated)
- Persistent DB (using lightweight JSON history cache during prototype)

## Quick Start
Prototype requires Python 3.11+.

1. Create/activate venv (if not already):
```
python -m venv .venv
.venv\Scripts\activate
```
2. Install deps (currently standard library only; add `httpx`, `fastapi`, etc later):
```
python -m pip install --upgrade pip
```
3. Run analyzer on a single NZB (real path required):
```
python -m preflight_analyzer.analyze_release --nzb C:\path\to\release.nzb --server-config preflight_analyzer\sample_servers.json
```

Dry-run static analysis only (no NNTP probing, server config optional):
```
python -m preflight_analyzer.analyze_release --nzb C:\path\to\release.nzb --dry-run
```

Batch process all NZBs in a directory (JSON lines output):
```
python -m preflight_analyzer.analyze_release --nzb C:\Users\<USER>\Downloads\Newshosting --server-config preflight_analyzer\sample_servers.json --jsonl
```

## Next Steps
- Implement NZB parser + parity heuristic
- Implement async STAT sampler
- Implement risk scoring with configurable weights
- Add CLI entrypoint producing JSON output
- Tune probe (currently caps sample to 150 STATs per NZB for speed). Adjust in `analyze_release.py` (SAMPLE_CAP) or integrate adaptive logic (e.g., larger for low redundancy).

## Migration Plan
After validation on a single show, gradually integrate:
1. API wrapper (FastAPI) to accept Sonarr-style POST
2. Decision feedback -> Logging + local history
3. Hook into failure learning system inside Automator

---
Prototype only. Safe to delete or relocate without impacting main pipeline.

## Radarr & Sonarr Preflight Selection (Integration Prototype)

Two orchestration CLIs now exist to preflight-analyze release candidates before grabbing:

- Sonarr episodes: `python -m preflight_analyzer.preflight_select --config preflight_config.json --servers-config sample_servers.json --episode-id 123` (repeat `--episode-id` as needed; add `--manual-search` to trigger Sonarr manual search first).
- Radarr movies: `python -m preflight_analyzer.preflight_select_radarr --config preflight_config.json --servers-config sample_servers.json --movie-id 456` (repeatable; `--manual-search` triggers Radarr movie search).

Both share the same indexer list and NNTP server config; config JSON needs `sonarr` and/or `radarr` blocks plus `indexers` array. Decisions and probe metrics are emitted as JSON with a `best` selection; automatic grabbing is skipped with `--no-grab`.
