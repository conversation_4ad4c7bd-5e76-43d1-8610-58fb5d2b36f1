import sys
from pathlib import Path

REPO_ROOT = Path(__file__).resolve().parents[1]
sys.path.insert(0, str(REPO_ROOT))

from _internal.src.event_queue import get_event_queue


def test_telemetry_cli_settings_infer(tmp_path, capsys):
    # Create temp events dir and one file.organized
    eq_dir = tmp_path / 'events'
    eq = get_event_queue({'EventQueue': {'enabled': True, 'dir': str(eq_dir)}})

    import asyncio
    async def _emit():
        await eq.publish('file.organized', {'type': 'movie', 'title': 'X', 'year': 2021})
    asyncio.run(_emit())

    # Build a settings.ini via loader that points EventQueue to eq_dir
    settings_ini = tmp_path / 'settings.ini'
    settings_ini.write_text('[EventQueue]\ndir=' + str(eq_dir).replace('\\', '/') + '\n', encoding='utf-8')

    # Run CLI with --settings only
    from _internal.src.telemetry_dashboard_cli import main as tele_main
    sys.argv = ['prog', '--settings', str(settings_ini), '--type', 'file.organized']
    tele_main()
    out = capsys.readouterr().out
    assert '"file.organized": 1' in out

