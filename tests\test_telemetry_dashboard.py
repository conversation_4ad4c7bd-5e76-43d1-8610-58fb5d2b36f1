import json
from pathlib import Path

from _internal.src.telemetry_dashboard import aggregate_counts


def test_aggregate_counts_synthetic(tmp_path):
    events = [
        {'type': 'file.organized', 'data': {'type': 'movie', 'title': 'Inception', 'radarr_id': 1}},
        {'type': 'file.organized', 'data': {'type': 'movie', 'title': 'Inception', 'radarr_id': 1}},
        {'type': 'file.organized', 'data': {'type': 'episode', 'title': 'Chernobyl', 'series_id': 10, 'season': 1, 'episode': 1}},
        {'type': 'file.organized', 'data': {'type': 'episode', 'title': 'Chernobyl', 'series_id': 10, 'season': 1, 'episode': 2}},
        {'type': 'download.completed', 'data': {'path': '/tmp/a', 'status': 'success'}},
    ]
    summary = aggregate_counts(events)
    assert summary['by_type']['file.organized'] == 4
    assert summary['by_movie'][str((1, 'Inception'))] == 2
    assert summary['by_series'][str((10, 'Chernobyl'))] == 2

