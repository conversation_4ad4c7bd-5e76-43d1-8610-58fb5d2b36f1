#!/usr/bin/env python3
from __future__ import annotations

"""
Minimal SABnzbd post-process event writer.

Usage in SAB: add as a post-processing script. SAB passes args:
  %1 = Directory
  %2 = NZB name
  %3 = Clean name
  %7 = PP status (0 OK)

This script writes a durable event to the JSONL EventQueue and exits 0 even if
it cannot write the event (to avoid marking SAB job failed).
"""

import os
import sys
from pathlib import Path
from typing import Any, Dict
from datetime import datetime, timezone


def main(argv):
    try:
        download_dir = argv[1] if len(argv) > 1 else None
        nzb_name = argv[2] if len(argv) > 2 else None
        clean_name = argv[3] if len(argv) > 3 else None
        status_code = int(argv[7]) if len(argv) > 7 else 0
    except Exception:
        download_dir, nzb_name, clean_name, status_code = None, None, None, 0

    status = 'success' if status_code == 0 else 'failure'

    # Compose event payload
    payload: Dict[str, Any] = {
        'job_name': clean_name or nzb_name or 'unknown',
        'download_dir': download_dir,
        'sab_status_code': status_code,
        'status': status,
        'sab_env': {
            'SAB_COMPLETE_DIR': os.environ.get('SAB_COMPLETE_DIR'),
            'SAB_CATEGORY': os.environ.get('SAB_CATEGORY'),
        },
    }

    # Append to EventQueue inbox
    try:
        from _internal.src.event_queue import get_event_queue
        eq = get_event_queue({'EventQueue': {'enabled': True}})
        import asyncio
        asyncio.run(eq.publish('sab.postprocess', payload))
        print(f"Enqueued SAB event for {payload['job_name']} ({status})")
    except Exception as e:
        # Best-effort; do not fail SAB job
        print(f"WARN: could not write event: {e}")

    # Always succeed so SAB treats job per its own status
    return 0


if __name__ == '__main__':
    sys.exit(main(sys.argv))

