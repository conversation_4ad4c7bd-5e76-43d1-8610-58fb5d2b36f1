#!/usr/bin/env python3
"""Utilities for robust season-pack detection and validation.

These helpers are intentionally pure (no I/O side-effects) so they can be
unit-tested in isolation. They operate on Paths (names only) and use the
TVShowNamingHelper to parse season/episode information.
"""
from __future__ import annotations

from dataclasses import dataclass
from pathlib import Path
from typing import Dict, Iterable, List, Optional, Tuple
import re

from _internal.utils.tv_show_naming import TVShowNamingHelper


@dataclass(frozen=True)
class EpisodeKey:
    season: Optional[int]
    episode: Optional[int]


def group_by_folder(files: Iterable[Path]) -> Dict[Path, List[Path]]:
    """Group a flat iterable of file paths by their immediate parent folder.

    Returns a dict mapping parent folder Path -> list of files in that folder.
    Maintains the original order of files per folder.
    """
    grouped: Dict[Path, List[Path]] = {}
    for f in files:
        parent = f.parent
        grouped.setdefault(parent, []).append(f)
    return grouped


def _is_likely_sample(name: str) -> bool:
    return bool(re.search(r"\b(sample|trailer|extras?)\b", name, re.IGNORECASE))


def _filter_episode_candidates(files_in_folder: List[Path], min_size_bytes: int | None = None) -> List[Path]:
    """Filter out tiny or sample-like files.

    If min_size_bytes is provided, keep only files meeting that size. If file does
    not exist in tests, size check is skipped and file is kept (unit-test friendly).
    """
    filtered: List[Path] = []
    for f in files_in_folder:
        name = f.name
        if _is_likely_sample(name):
            continue
        if min_size_bytes is not None:
            try:
                if f.exists() and f.stat().st_size < min_size_bytes:
                    continue
            except OSError:
                # If stat fails, keep it so tests using temp files without content still work
                pass
        filtered.append(f)
    return filtered


def detect_season_pack(files_in_folder: List[Path], tv_helper: Optional[TVShowNamingHelper] = None, *, min_size_bytes: int | None = None) -> bool:
    """Return True if the files look like a season pack.

    Criteria:
      - > 1 video file after filtering
      - >= 2 distinct (season, episode) pairs parsed from names

    min_size_bytes defaults to None for testability; callers can pass a threshold in prod.
    """
    if not files_in_folder or len(files_in_folder) <= 1:
        return False

    tv_helper = tv_helper or TVShowNamingHelper()
    files = _filter_episode_candidates(files_in_folder, min_size_bytes=min_size_bytes)
    if len(files) <= 1:
        return False

    distinct: set[EpisodeKey] = set()
    for f in files:
        info = tv_helper.parse_tv_show_filename(f.name)
        if info.season is not None and info.episode is not None:
            distinct.add(EpisodeKey(info.season, info.episode))
    return len(distinct) > 1


def validate_episodes(files_in_folder: List[Path], tv_helper: Optional[TVShowNamingHelper] = None, *, min_size_bytes: int | None = None) -> Tuple[bool, str]:
    """Validate grouped files and classify pack mode.

    Returns (valid, mode) where mode is one of:
      - "single_season" (typical season pack)
      - "multi_season" (episodes from more than one season)
      - "insufficient" (not enough distinct episodes to be a pack)

    valid indicates whether organizing as a pack is sensible.
    """
    tv_helper = tv_helper or TVShowNamingHelper()
    files = _filter_episode_candidates(files_in_folder, min_size_bytes=min_size_bytes)
    if len(files) <= 1:
        return False, "insufficient"

    seasons: set[int] = set()
    distinct_eps: set[EpisodeKey] = set()
    series_names: set[str] = set()

    for f in files:
        info = tv_helper.parse_tv_show_filename(f.name)
        if info.series_title:
            series_names.add(info.series_title.lower())
        if info.season is not None:
            seasons.add(info.season)
        if info.season is not None and info.episode is not None:
            distinct_eps.add(EpisodeKey(info.season, info.episode))

    if len(distinct_eps) <= 1:
        return False, "insufficient"

    # If multiple distinct series names, this is suspicious; not a normal pack
    if len(series_names) > 1:
        return False, "insufficient"

    mode = "multi_season" if len(seasons) > 1 else "single_season"
    return True, mode


__all__ = [
    "group_by_folder",
    "detect_season_pack",
    "validate_episodes",
]

