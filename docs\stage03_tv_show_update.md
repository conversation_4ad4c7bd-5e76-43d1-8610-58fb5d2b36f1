# Stage 03 MKV Processor - TV Show Support Update

## Overview
Successfully updated Stage 03 (MKV Processor) to support the new movies/tv_shows directory structure while maintaining backward compatibility with the legacy structure.

## Changes Made

### 1. Updated FilesystemFirstStateManager
**File**: `_internal/utils/filesystem_first_state_manager.py`

**Key Changes**:
- **discover_movies_by_stage()**: Now scans both movies/ and tv_shows/ subdirectories
- **_scan_content_directory()**: New method to handle content type directories 
- **_scan_content_type_directory()**: Scans movies/ or tv_shows/ for resolution folders
- **_scan_resolution_directory()**: Enhanced to track content type metadata
- **Content Type Detection**: Automatically identifies and tags content as 'movie' or 'tv_show'

**Structure Support**:
```
New Structure (Primary):
workspace/2_downloaded_and_organized/
├── movies/
│   ├── 1080p/
│   │   └── The Matrix (1999)/
│   └── 4k/
│       └── Blade Runner 2049 (2017)/
└── tv_shows/
    ├── 1080p/
    │   ├── Monster (2004)/         # Single season
    │   └── Naruto (2002)/          # Multi-season
    └── 4k/
        └── Breaking Bad (2008)/

Legacy Structure (Backward Compatible):
workspace/2_downloaded_and_organized/
├── 1080p/
│   ├── The Matrix (1999)/
│   └── Monster (2004)/
└── 4k/
    └── Blade Runner 2049 (2017)/
```

### 2. Enhanced Discovery Logic
- **Dual Structure Support**: Automatically detects and handles both new and legacy structures
- **Content Type Tracking**: Adds 'content_type' field ('movie' or 'tv_show') to discovered content
- **Stage Mapping**: Correctly maps directory-based stages to processing stages
- **Error Handling**: Graceful fallbacks for edge cases

## Testing Results

### Discovery Test Results
```
✅ Found 4 content items across different stages
✅ Correctly identified 2 movies and 2 TV shows  
✅ Proper content type tagging (movie/tv_show)
✅ Accurate stage mapping (organized, mkv_processing_complete)
```

### Integration Test Results
```
✅ Stage 03 successfully discovers movies/tv_shows structure
✅ Correctly finds MKV and subtitle files for both content types
✅ Proper file counting (TV shows: multiple episodes, Movies: single file)
✅ Clean test execution with proper cleanup
```

## Pipeline Impact

### What Works Now
- **Stage 03 MKV Processor**: Fully compatible with new structure
- **Content Discovery**: Finds both movies and TV shows automatically
- **Processing Logic**: Existing MKV processing logic works unchanged
- **Backward Compatibility**: Legacy structure still supported

### Next Steps Needed
- **Stage 04 (Video Encoder)**: Update to handle movies/tv_shows structure  
- **Stage 05 (Subtitle Handler)**: Update to handle movies/tv_shows structure
- **Stage 06 (Final Mux)**: Update to handle movies/tv_shows structure
- **Configuration Updates**: Update any hardcoded paths in remaining stages

## Technical Details

### Core Methods Updated
1. **discover_movies_by_stage()**: Main discovery entry point
2. **_scan_content_directory()**: Handles mixed content type/legacy scanning  
3. **_scan_content_type_directory()**: Specific movies/tv_shows folder scanning
4. **_scan_resolution_directory()**: Resolution folder processing with content type awareness

### Metadata Enhancement
All discovered content now includes:
- `content_type`: 'movie' or 'tv_show' 
- `cleaned_title`: Extracted title
- `year`: Extracted year
- `movie_directory`: Full path to content
- `main_movie_file`: Primary MKV file (if found)

### Error Handling
- Graceful handling of missing directories
- Fallback to direct content folder scanning
- Optional parameter support for backward compatibility
- Comprehensive exception handling with logging

## Validation
- **Unit Tests**: Discovery functionality verified
- **Integration Tests**: End-to-end Stage 03 compatibility confirmed
- **Content Type Tests**: Movie/TV show differentiation working
- **Structure Tests**: Both new and legacy structures supported

The Stage 03 MKV Processor is now fully ready to handle TV show content in the new directory structure! 🎉
