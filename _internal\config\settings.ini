# PlexAutomator/config/settings.ini
# This is the central configuration file for the entire pipeline.
# Fill in your user-specific paths and API keys below.

[General]
# SQLite database for reliable state management (replaces JSON files)
sqlite_database_path = _internal/data/pipeline_state.db
# Backward-compatibility JSON paths (maintained during transition)
movies_to_process_json_path = _internal/config/movies_to_process.json
pipeline_state_file = _internal/config/pipeline_state.json
# Path to the master list for original language hints (used by 03_mkv_processor.py)
movies_master_list_path = _internal/config/movies_master_list.txt
# Path to the input file for new movie requests.
new_movie_requests_file = new_movie_requests.txt
# Path to the input file for new TV show requests.
new_tv_requests_file = new_tv_requests.txt
# A list of valid movie file extensions to look for in completed downloads.
movie_file_extensions = [".mkv", ".mp4", ".avi", ".mov", ".wmv", ".flv", ".webm", ".mpg", ".mpeg", ".m4v", ".3gp", ".ts"]

[Database]
# SQLite database configuration
# Enable automatic filesystem synchronization at script startup
auto_sync_filesystem = true
# Enable self-healing validation
auto_heal_discrepancies = true
# Database backup settings
backup_enabled = true
backup_retention_days = 30
backup_directory = _internal/backups
# Health monitoring settings
health_check_enabled = true
health_alert_threshold = 80

[Paths]
# --- Workspace Paths ---
# These are the main working directories for the pipeline.
# They are relative to the project root directory.

# Updated structure with organized content and centralized watch folder
new_requests_dir = workspace/0_new_requests
movie_requests_dir = %(new_requests_dir)s/movies
tv_requests_dir = %(new_requests_dir)s/tv_shows
# Centralized watch folder that SABnzbd monitors (replaces individual subfolder watch folders)
sabnzbd_watch_folder = %(new_requests_dir)s/watched

# Archive folders for completed NZB files
movie_nzb_archive_dir = %(movie_requests_dir)s/nzb_files_launched_archive
tv_nzb_archive_dir = %(tv_requests_dir)s/nzb_files_launched_archive

# Backward compatibility paths
intake_pending_dir = workspace/0_new_movie_requests
nzb_files_for_download_dir = %(intake_pending_dir)s/nzb_files_for_download
nzb_files_launched_archive_dir = %(intake_pending_dir)s/nzb_files_launched_archive

download_client_active_dir = workspace/1_downloading
download_incomplete_dir = %(download_client_active_dir)s/incomplete
download_complete_raw_dir = %(download_client_active_dir)s/complete_raw

mkv_processing_output_dir = workspace/2_downloaded_and_organized

archive_processed_dir = workspace/archive_processed
archive_original_pgs_sup_dir = %(archive_processed_dir)s/original_pgs_sup_files

# Plex media directory (where movies are organized for Plex)
plex_movies_directory = C:/Users/<USER>/Videos/plex
# Plex TV shows directory (where TV shows are organized for Plex)
plex_tv_directory = E:/

# Organization Settings
# Set to false to disable resolution-based subdirectories (720p, 1080p, 4k)
enable_resolution_based_organization = true

issues_hold_dir = workspace/issues_hold
issues_metadata_failed_dir = %(issues_hold_dir)s/metadata_failed
issues_download_failed_dir = %(issues_hold_dir)s/download_failed
issues_mkv_processing_failed_dir = %(issues_hold_dir)s/mkv_processing_failed
issues_subtitle_ocr_failed_dir = %(issues_hold_dir)s/subtitle_ocr_failed
issues_consolidation_error_dir = %(issues_hold_dir)s/consolidation_error
issues_manual_review_dir = %(issues_hold_dir)s/manual_review_needed

[Executables]
# --- Paths to external command-line tools ---
# Ensure these paths are correct for your system, or that the executables are in your system's PATH.
mkvmerge_path = C:/Program Files/MKVToolNix/mkvmerge.exe
mkvpropedit_path = C:/Program Files/MKVToolNix/mkvpropedit.exe
mkvextract_path = C:/Program Files/MKVToolNix/mkvextract.exe
ffprobe_path = C:/ffmpeg/bin/ffprobe.exe
ffmpeg_path = C:/ffmpeg/bin/ffmpeg.exe
# Path to Subtitle Edit executable (for OCR processing)
subtitle_edit_path = C:/Program Files/Subtitle Edit/SubtitleEdit.exe

[APIKeys]
# Your API Keys. It is recommended to use environment variables for these in a production setup.
tmdb_api_key = efd5018b7283ecb6fa1c0c525783bc08

# TVDB API key for accurate TV show episode data with AIRED ORDER
# Get your API key from: https://thetvdb.com/api-information
tvdb_api_key = de490cb4-a4a8-4eda-8fd2-113167fbd455

# Fanart.tv API keys (for community artwork)
# Get your API key from: https://fanart.tv/get-an-api-key/
fanart_api_key = 1d9280fdd12ae59808ac6315f6a5ce5d
# fanart_client_key = your_fanart_client_key_here

# ThePosterDB credentials (for poster repository access)
# Create account at: https://theposterdb.com/register
theposterdb_username = POTUSCUMONFLOTUS
theposterdb_password = 2zAiMj?rXmFQLE?

# Your OpenAI key (if used for future enhancements)
# openai_api_key = sk-proj-sN3hH...

[PosterPreferences]
# Advanced poster selection preferences (implementing PDF requirements)

# Processing mode: 'interactive' for manual selection, 'auto' for AI-based selection
processing_mode = auto

# Poster quality preferences
prefer_textless = false
min_resolution = 1000x1500
max_file_size_mb = 10
require_poster_aspect_ratio = true

# Source preferences (in order of preference)
preferred_sources = TMDB,Fanart.tv,ThePosterDB
blocked_sources =

# Language preferences
allowed_languages = en,null

# AI analysis preferences (implementing PDF requirements)
enable_ai_analysis = true
use_ollama_models = true
use_advanced_analysis = true
ollama_vision_model = qwen2.5vl:latest
ollama_text_model = gemma3:latest
ollama_timeout_seconds = 20
ollama_max_image_size = 768
quality_threshold = 0.6
prefer_ai_over_community_ratings = true
fallback_to_basic_ai_on_timeout = true

# Advanced poster archiving and categorization
enable_permanent_poster_archive = true
archive_all_collected_posters = true
create_categorized_folders = true
save_analysis_metadata = true

# Enhanced filtering preferences
filter_excessive_text_posters = true
max_text_coverage_percent = 40
prefer_high_readability = true
filter_by_visual_style = false
preferred_visual_styles = photographic,illustrated
filter_by_mood_tone = false
preferred_mood_tones = dramatic,action

# Advanced filtering preferences
filter_by_text_presence = false
filter_by_language = true
filter_by_artistic_quality = true
min_artistic_score = 0.5

# Batch processing settings
auto_select_threshold = 0.8
max_posters_to_analyze = 20
enable_parallel_analysis = false

[Radarr]
# Radarr API configuration (replaces Chrome driver NZB scraping)
url = http://localhost:7878
api_key = 6ba9b679124f4414b3c4b93756decab8
# Quality profile ID for downloads (6 = HD - 720p/1080p)
quality_profile_id = 6
# Year-based quality profile IDs for advanced quality selection
# HD profile for 1080p downloads (≤2009 movies and 2010-2015 1080p)
hd_quality_profile_id = 4
# Ultra-HD profile for 4K downloads (2010-2015 4K and 2016+ movies)
uhd_quality_profile_id = 5

[Sonarr]
# Sonarr API configuration for TV show automation
url = http://localhost:8989
api_key = 745e39af03d3443c989632c27a0fcd47
# Year-based quality profile IDs for advanced TV show quality selection
# 720p profile for older TV shows (≤2005 era shows, often poor 4K remastering)
tv_720p_quality_profile_id = 3
# HD profile for 1080p downloads (2006-2015 shows and 2016-2020 1080p)
tv_hd_quality_profile_id = 4
# Ultra-HD profile for 4K downloads (2016-2020 4K and 2021+ shows)
tv_uhd_quality_profile_id = 5
# NEW: Inclusive profile that accepts all quality tiers (SD through 4K) for adaptive selection
# This should be the "TV Complete - 4K Preferred" profile you mentioned
# Configure this profile in Sonarr to accept: SD, 720p, 1080p, 4K with 4K preferred
tv_inclusive_quality_profile_id = 6
# Enable adaptive quality selection (true) or use year-based profiles (false)
# When true, uses tv_inclusive_quality_profile_id instead of year-based strict profiles
adaptive_quality_enabled = true
# Maximum allowed size for a single episode (in GB). Episodes larger than this will be blockedlisted.
# Typical sensible values: 25 (aggressive), 40 (balanced). Default here: 40.
max_episode_size_gb = 40
# Preferred region order for disambiguating duplicate titled series (comma-separated, highest priority first)
region_preference_order = US,UK,CA,AU

[Advanced]
# Season pack support configuration
# When true, allows season pack downloads for efficiency (bulk downloads)
# When false, blocks all season packs and uses individual episode downloads only
enable_season_packs = true
# Prefer season packs for full seasons (6+ episodes)
prefer_season_packs_for_full_seasons = true
# Use season packs when less than this percentage of episodes are individually available
season_pack_fallback_threshold = 0.50
# Maximum age in days for series to be eligible for season pack downloads (0 = no limit)
season_pack_max_age_days = 0

[SEARCH]
# Advanced candidate analysis configuration
# Enable dynamic scanning to analyze all available candidates within reasonable limits
enable_dynamic_scanning = true
# Scan all candidates when pool size is manageable (≤100 releases)
max_dynamic_scan_threshold = 100
# Fallback to top candidates when pool is very large (>100 releases)
large_pool_candidate_limit = 50
# Minimum candidates to analyze even for small pools
min_candidates_to_analyze = 10
# Default max_candidates for fixed scanning mode (fallback)
max_candidates = 50
# Show detailed analysis information including missing percentages
show_detailed_analysis = true
# Enhanced probe sampling for better accuracy
enhanced_probe_sampling = true

[IntakeAndNZBSearch]
# Modernized: direct Radarr/Sonarr API integration replaces manual NZB site automation
nzbfinder_url = https://nzbfinder.ws/


[DownloadAndOrganize]
# 'NewshostingLauncher' uses os.startfile on Windows.
# 'SABnzbdAPI' uses SABnzbd API for direct integration.
# Future options: 'NZBGetAPI', 'JDownloaderAPI'
downloader_client_type = SABnzbdAPI
# Path to the executable for the Newshosting client (used for logging/checking if running)
newshosting_exe_path = C:\Users\<USER>\AppData\Local\Newshosting\3.7.6\newshosting.exe
# SABnzbd API configuration
sabnzbd_url = http://127.0.0.1:8080
sabnzbd_api_key = 91f5bd2c786d4886bd0a1072856719c9
# Parameters for monitoring system activity to pause processing
disk_busy_threshold_mbps = 10
network_busy_threshold_mbps = 5
download_check_interval_seconds = 60
max_wait_for_download_hours = 12

[MKVProcessor]
# Language preferences for track selection
audio_preferred_lang = eng
subtitle_preferred_lang = eng

[SubtitleHandler]
# Subtitle Edit OCR configuration
# OCR method to use in Subtitle Edit. Options: ollama_vision, tesseract, paddle_ocr
ocr_method = ollama_vision
# Ollama model to use for OCR (when using ollama_vision method)
ollama_model = qwen2.5vl:latest
# Language for OCR processing
ocr_language = English
# Set to 'true' to attempt to convert ASS/SSA subtitles to SRT using ffmpeg.
convert_ass_to_srt = true

[PosterAndQCPrep]
# Set to 'true' to automatically download a poster from TMDb.
automate_poster_download = true
# Standard filename for the downloaded poster.
poster_filename = poster.jpg
# Poster size to download from TMDb. Options: w92, w154, w185, w342, w500, w780, original
poster_size = w500

[Orchestrator]
# How often the main pipeline script should run a full cycle, in seconds (for continuous mode).
pipeline_interval_seconds = 300
# Path for the orchestrator's specific log file.
orchestrator_log_path = logs/pipeline_orchestrator.log

[Logging]
# Log levels: DEBUG, INFO, WARNING, ERROR, CRITICAL
file_log_level = INFO
console_log_level = INFO
log_rotation_max_bytes = 10485760 # 10MB
log_rotation_backup_count = 5

[SABnzbd]
# SABnzbd configuration for telemetry system
enabled = true
base_url = http://127.0.0.1:8080
api_key = 91f5bd2c786d4886bd0a1072856719c9