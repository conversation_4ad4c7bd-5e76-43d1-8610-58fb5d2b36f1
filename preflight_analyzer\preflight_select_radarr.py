from __future__ import annotations
import argparse
import json
import sys
from pathlib import Path
from typing import Any, Dict, List, Optional, cast
import asyncio

from .nzb_parser import NZBParser
from .analyze_release import load_servers
from .core_analyzer import analyze_metadata
from .indexer_client import extract_newznab_id, fetch_nzb
from .radarr_client import fetch_releases_for_movie, manual_search_movie, grab_release
from .history_store import DecisionHistory
from .orchestrator_common import load_config, choose_best


 # load_config and choose_best imported from orchestrator_common


async def process(args: argparse.Namespace) -> int:
    cfg = load_config(Path(args.config))
    radarr_cfg = cast(Dict[str, Any], cfg.get('radarr') or {})
    indexers_cfg = {i['name']: i for i in cfg.get('indexers', [])}
    if not radarr_cfg:
        print('Missing radarr config', file=sys.stderr)
        return 2
    if not args.movie_id:
        print('At least one --movie-id required', file=sys.stderr)
        return 2
    servers = load_servers(Path(args.servers_config))
    history = DecisionHistory(Path(args.history))
    all_candidate_reports: List[Dict[str, Any]] = []
    parser_obj = NZBParser()
    for movie_id in args.movie_id:
        if args.manual_search:
            manual_search_movie(radarr_cfg['url'], radarr_cfg['api_key'], movie_id)
        releases = fetch_releases_for_movie(radarr_cfg['url'], radarr_cfg['api_key'], movie_id)[:args.max_candidates]
        for rel in releases:
            guid = rel.get('guid', '')
            idx_name = rel.get('indexer') or rel.get('indexerName') or ''
            key = f"{idx_name}:{guid}"
            if history.get(key):
                continue
            idx_cfg = indexers_cfg.get(idx_name)
            if not idx_cfg:
                continue
            nzb_id = extract_newznab_id(guid)
            if not nzb_id:
                continue
            try:
                raw = fetch_nzb(idx_cfg['base_url'], idx_cfg['api_key'], nzb_id)
                meta = parser_obj.parse_bytes(raw)  # type: ignore
                report = await analyze_metadata(meta, servers=servers, retention_days=4000, dry_run=args.dry_run, verbose=False, sample_cap=args.sample_cap)
                report.update({'guid': guid, 'indexer': idx_name, 'title': rel.get('title'), 'size': rel.get('size'), 'movie_id': movie_id})
                history.put(key, report['decision'], report)
                all_candidate_reports.append(report)
            except Exception as e:  # noqa
                all_candidate_reports.append({'guid': guid, 'indexer': idx_name, 'error': str(e), 'movie_id': movie_id})
    best: Optional[Dict[str, Any]] = choose_best([r for r in all_candidate_reports if 'decision' in r])
    output: Dict[str, Any] = {'candidates': all_candidate_reports, 'best': best}
    print(json.dumps(output, indent=2))
    if best and not args.no_grab and best['decision'] in ('ACCEPT', 'RISKY_LOW_PARITY'):
        try:
            grab_release(radarr_cfg['url'], radarr_cfg['api_key'], best['guid'])
            print(f"Grabbed movie release: {best['guid']}")
        except Exception as e:  # noqa
            print(f"Failed to grab: {e}", file=sys.stderr)
    return 0


def main():  # noqa
    parser = argparse.ArgumentParser(description='Preflight select best Radarr movie release candidates via NZB structural probe.')
    parser.add_argument('--config', required=True, help='Path to config/preflight_config.json')
    parser.add_argument('--movie-id', type=int, action='append', help='Radarr Movie ID (repeatable)')
    parser.add_argument('--manual-search', action='store_true', help='Trigger a Radarr manual search before evaluating releases')
    parser.add_argument('--max-candidates', type=int, default=8, help='Max releases per movie to evaluate')
    parser.add_argument('--servers-config', required=True, help='Path to servers config JSON (same as analyze_release)')
    parser.add_argument('--history', default='data/preflight_history_movies.json', help='Decision history JSON path')
    parser.add_argument('--dry-run', action='store_true', help='Do not actually grab even if acceptable (overridden by --no-grab)')
    parser.add_argument('--sample-cap', type=int, default=320, help='Cap on total sampled segments across files')
    parser.add_argument('--no-grab', action='store_true', help='Never grab automatically (output only)')
    args = parser.parse_args()
    rc = asyncio.run(process(args))
    if rc:
        sys.exit(rc)


if __name__ == '__main__':  # pragma: no cover
    main()
