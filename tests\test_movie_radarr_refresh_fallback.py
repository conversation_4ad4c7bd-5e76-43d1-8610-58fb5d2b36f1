import sys
from pathlib import Path
import asyncio
from unittest.mock import patch, AsyncMock

REPO_ROOT = Path(__file__).resolve().parents[1]
sys.path.insert(0, str(REPO_ROOT))

from _internal.src.file_organizer import organize_movie


class DummyLogger:
    def info(self, *a, **k): pass
    def warning(self, *a, **k): pass
    def error(self, *a, **k): pass


def test_radarr_refresh_fallback_when_no_id(tmp_path):
    download_dir = tmp_path / 'Some.Movie.2019.1080p.WEB-DL'
    download_dir.mkdir(parents=True, exist_ok=True)
    main = download_dir / 'Some.Movie.2019.1080p.WEB-DL.mkv'
    main.write_bytes(b'0' * 1024 * 1024)

    settings = {
        'Radarr': {'url': 'http://localhost:7878', 'api_key': 'TESTKEY'},
        'EventQueue': {'enabled': True, 'dir': str(tmp_path / 'events')}
    }
    content_info = {'title': 'Some Movie', 'year': 2019}

    class DummyQueue:
        async def publish(self, event_type, data):
            pass

    with patch('_internal.src.file_organizer.get_event_queue', return_value=DummyQueue()):
        with patch('_internal.src.radarr_integration.RadarrClient.issue_command', new_callable=AsyncMock) as mock_cmd:
            ok = asyncio.run(organize_movie(content_info, str(main), download_dir, tmp_path, '1080p', DummyLogger(), settings))
            assert ok is True
            # Ensure fallback command RefreshMonitoredDownloads was called
            called_payloads = [(c.kwargs.get('command_payload') or (c.args[1] if len(c.args) > 1 else {})) for c in mock_cmd.mock_calls]
            assert any(p.get('name') == 'RefreshMonitoredDownloads' for p in called_payloads)

