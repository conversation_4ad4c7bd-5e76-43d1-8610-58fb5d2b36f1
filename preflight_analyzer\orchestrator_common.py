from __future__ import annotations
import json
import os
from pathlib import Path
from typing import Any, Dict, List, Optional


def load_config(path: Path) -> Dict[str, Any]:
    """Load JSON config and expand leading-$ env var placeholders for known secret fields."""
    raw: Dict[str, Any] = json.loads(path.read_text())

    def expand(v: Any):
        if isinstance(v, str) and v.startswith('$'):
            return os.environ.get(v[1:], '')
        return v

    indexers_list_any = raw.get('indexers') or []
    if isinstance(indexers_list_any, list):
        for idx in indexers_list_any:
            if isinstance(idx, dict):
                for k in ('api_key', 'base_url'):
                    if k in idx:
                        idx[k] = expand(idx[k])

    for section in ('sonarr', 'radarr'):
        if section in raw and isinstance(raw[section], dict) and 'api_key' in raw[section]:
            raw[section]['api_key'] = expand(raw[section]['api_key'])
        if section in raw and isinstance(raw[section], dict) and 'url' in raw[section]:
            raw[section]['url'] = expand(raw[section]['url'])
    return raw


def choose_best(reports: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
    if not reports:
        return None
    acceptable = [r for r in reports if r.get('decision') in ('ACCEPT', 'RISKY_LOW_PARITY')]
    pool = acceptable if acceptable else reports

    def key(r: Dict[str, Any]) -> tuple[int, float, float, int]:
        decision_mapping: Dict[str, int] = {
            'ACCEPT': 0,
            'RISKY_LOW_PARITY': 1,
            'RETRY_PROBE': 2,
            'REJECT_INCOMPLETE': 3,
        }
        decision_raw = r.get('decision')
        decision = decision_raw if isinstance(decision_raw, str) else ''
        decision_rank = decision_mapping.get(decision, 9)
        risk_score = float(r.get('risk_score', 1.0) or 1.0)
        missing_ratio = float(r.get('probe_missing_ratio', 1.0) or 1.0)
        parity_blocks = int(r.get('estimated_parity_blocks', 0) or 0)
        return (int(decision_rank), risk_score, missing_ratio, -parity_blocks)

    pool.sort(key=key)
    return pool[0]


__all__ = ["load_config", "choose_best"]
