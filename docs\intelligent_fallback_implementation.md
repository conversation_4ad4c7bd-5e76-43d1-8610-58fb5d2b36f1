# Intelligent Fallback System Implementation Guide

## Overview

This implementation solves the problem of external systems (like Radarr) automatically adding unwanted downloads when a chosen file fails. Instead, it implements an intelligent fallback system that:

1. **Prevents Radarr auto-monitoring** by disabling automatic search
2. **Uses preflight analysis rankings** to select the next best candidate
3. **Respects user preference hierarchy** when manual selections are made
4. **Integrates with telemetry** to detect failures and trigger fallbacks

## Key Components

### 1. IntelligentFallbackSystem (`_internal/src/intelligent_fallback_system.py`)
- Core fallback logic
- Manages preflight decision data
- Handles candidate selection hierarchy
- Triggers fallback downloads

### 2. DownloadFailureMonitor (`_internal/src/download_failure_monitor.py`)
- Monitors downloads for failures
- Tracks user selections vs system recommendations
- Integrates with telemetry system
- Maintains download state

### 3. PreflightIntegratedDownloader (`_internal/src/preflight_integrated_downloader.py`)
- Integrates preflight analysis with fallback system
- Provides convenient APIs for download management
- Handles user selections and system recommendations
- Reports to monitoring system

### 4. Modified Movie Orchestrator (`_internal/src/intake_movie_orchestrator.py`)
- **CRITICAL CHANGE**: Sets `"monitored": False` and `"searchForMovie": False`
- Prevents Radarr from automatically searching/downloading

## Integration Steps

### Step 1: Update Preflight Analysis Code

Replace direct Radarr download calls with the integrated downloader:

```python
# OLD CODE (in preflight analysis):
# Direct Radarr API call that allows auto-monitoring
radarr_result = await radarr.issue_command(session, download_payload)

# NEW CODE:
from _internal.src.preflight_integrated_downloader import download_with_fallback_protection

# For user selections
success = await download_with_fallback_protection(
    radarr_id=movie_id,
    selected_candidate=selected_candidate,
    candidates_list=acceptable_candidates,
    settings_dict=settings_dict,
    logger=logger,
    is_user_selection=True
)

# For system recommendations  
success = await download_with_fallback_protection(
    radarr_id=movie_id,
    selected_candidate=best_candidate,
    candidates_list=acceptable_candidates,
    settings_dict=settings_dict,
    logger=logger,
    is_user_selection=False
)
```

### Step 2: Update Telemetry Integration

Add failure/success reporting to the telemetry system:

```python
# In telemetry failure detection
from _internal.src.preflight_integrated_downloader import report_failure_from_telemetry

# When telemetry detects a failure
await report_failure_from_telemetry(radarr_id, failed_guid, settings_dict, logger)

# When telemetry detects success
from _internal.src.preflight_integrated_downloader import report_success_from_telemetry
await report_success_from_telemetry(radarr_id, completed_guid, settings_dict, logger)
```

### Step 3: Start Monitoring Service

Add monitoring startup to your main script:

```python
# In main application startup
from _internal.src.preflight_integrated_downloader import get_integrated_downloader

async def start_monitoring():
    downloader = get_integrated_downloader(settings_dict, logger)
    # Start monitoring in background
    asyncio.create_task(downloader.start_monitoring())
```

## How It Works

## ✅ **CORRECTED INTELLIGENT FALLBACK LOGIC**

### **The Fundamental Understanding:**

Your original requirement was crystal clear, but I initially misunderstood the ranking direction:

> **"if 22 fails you then trigger 21, and if 21 fails you then trigger 20"**  
> **"if 28 fails, you should continue from 28 and choose 27, and if 27 fails then continue with 26"**

This means we move DOWN the list toward **worse** candidates (higher index numbers), NOT up toward better ones.

### User Selection Scenario (CORRECTED)

1. **System recommends candidate #22**
2. **User selects candidate #28** (for their own reasons - maybe #22 was too big, wrong quality, etc.)
3. **If #28 fails** → **Try #29** (next worse candidate, similar to user's choice)
4. **If #29 fails** → **Try #30, #31, #32...** (continuing DOWN toward worse candidates)

### System Recommendation Scenario (CORRECTED)

1. **System recommends best candidate #22** 
2. **User accepts recommendation** (downloads candidate #22)
3. **If #22 fails** → **Try #23** (next worse candidate)
4. **If #23 fails** → **Try #24, #25, #26...** (continuing DOWN toward worse candidates)

### Why This Logic Makes Perfect Sense:

```
User Psychology:
├── User rejected #22 for a REASON (size, quality, source, etc.)
├── User chose #28 because it met their criteria
├── If #28 fails → try something SIMILAR (#29, #30...)
└── NOT #1 (completely different from what user wanted)

Progressive Degradation:
├── Start from user's choice level (#28)
├── Gradually try worse options (#29, #30, #31...)
├── Maintain similar quality/characteristics
└── Avoid sudden jumps to completely different options
```

### Enhanced Blacklisting with Correct Direction

**Example**: User selects #28, original recommendation was #22, #28 fails
1. **#22 immediately blacklisted** (user rejected it for a reason)
2. **#28 gets blacklisted** (it failed)  
3. **Try #29** (next worse candidate, similar to user's choice)
4. **If #29 fails** → **Try #30, #31, #32...** (continuing DOWN)

### Corrected Fallback Logic

```
Blacklisting Rules:
├── Failed candidates → Always blacklisted
├── User-rejected candidates → Always blacklisted  
└── Successful downloads → Clear all blacklists for that movie

Selection Logic:
├── Start from failed candidate's position
├── Move DOWN: #28 fails → try #29, #30, #31...
├── Skip all blacklisted candidates
└── Continue until end of list or successful download

Benefits:
├── 🚫 Never retry failed candidates
├── 🚫 Never try user-rejected candidates  
├── � Gradual quality degradation (not sudden jumps)
└── 🎯 Respects user's original quality preference
```

**CRITICAL INSIGHT**: The system respects your choice level and degrades gradually, rather than jumping to completely different quality tiers.

## Configuration

### Disable Auto-Monitoring (Already Implemented)

The orchestrator now sets:
```python
"monitored": False,           # No automatic monitoring
"searchForMovie": False       # No immediate search
```

### Monitoring Settings

Adjust monitoring intervals in the downloader:
```python
await downloader.start_monitoring(check_interval=30)  # Check every 30 seconds
```

### Fallback Limits

Prevent infinite loops with maximum fallback attempts:
```python
# In IntelligentFallbackSystem
if fallback_count >= 3:  # Max 3 fallback attempts
    logger.error("Maximum fallback attempts reached")
    return False
```

## Testing

Use the demo utility to test the system:

```bash
# List available preflight decisions
python fallback_demo.py list-decisions

# Monitor downloads (run in background)
python fallback_demo.py monitor

# Test manual fallback
python fallback_demo.py trigger-fallback 273 "https://nzbfinder.ws/details/guid"

# Simulate failure for testing
python fallback_demo.py test-failure 273 "https://nzbfinder.ws/details/guid"

# Check system status
python fallback_demo.py status
```

## Benefits

1. **Full Control**: No external system can trigger unwanted downloads
2. **Intelligent Selection**: Uses preflight analysis data for optimal choices
3. **User Preference Respect**: Maintains user selection hierarchy
4. **Telemetry Integration**: Seamlessly works with existing monitoring
5. **Failure Recovery**: Automatic fallback without user intervention
6. **Loop Prevention**: Built-in safeguards against infinite retries

## Migration Guide

### For Existing Preflight Code

1. **Replace direct Radarr calls** with `download_with_fallback_protection()`
2. **Add monitoring startup** to main application
3. **Update telemetry integration** with failure/success reporting
4. **Test with demo utility** before production use

### For Telemetry Integration

1. **Add failure detection** hooks in telemetry system
2. **Call reporting functions** when failures/successes detected
3. **Update tracking logic** to handle fallback downloads

The system is designed to be a drop-in replacement that enhances existing functionality without breaking current workflows.
