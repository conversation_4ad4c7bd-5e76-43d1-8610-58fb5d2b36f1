# CHANGELOG

## Unreleased
### ✨ Enhanced Fuzzy Metadata Validation Implementation
- **NEW**: Comprehensive fuzzy string matching system using RapidFuzz for improved metadata accuracy
- **NEW**: Confidence-based decision making with configurable thresholds (95% auto-approve, 80-95% silent correct, 60-80% user confirm, <60% reject)
- **NEW**: ±3 year tolerance for metadata matching with bonus scoring for exact matches
- **NEW**: TMDb API optimization with intelligent caching, rate limiting (50 req/sec), and concurrent request handling
- **NEW**: Interactive and non-interactive user confirmation system for ambiguous matches
- **NEW**: Performance monitoring and reporting with detailed statistics
- **ENHANCED**: `_fetch_movie_metadata_for_intake_sync()` in `_internal/utils/metadata_apis.py` with fuzzy matching
- **ENHANCED**: `fetch_tv_metadata_for_intake()` in `_internal/src/metadata_fetcher.py` with fuzzy matching
- **ADDED**: Four new core modules:
  - `_internal/utils/fuzzy_matching.py`: Core fuzzy matching algorithms
  - `_internal/utils/fuzzy_config.py`: Configuration management system
  - `_internal/utils/metadata_cache.py`: API caching and optimization
  - `_internal/utils/user_interaction.py`: User confirmation handling
- **ADDED**: `config/fuzzy_matching_config.ini`: Comprehensive configuration file
- **ADDED**: `test_fuzzy_matching.py`: Complete test suite for fuzzy matching functionality
- **ADDED**: `docs/fuzzy_matching_README.md`: Detailed documentation and usage guide

### 🔧 Infrastructure Improvements
- Deprecated and removed legacy season pack monitor (`_background_season_pack_monitor` and `_internal/utils/season_pack_monitor.py`).
- Intake now relies on preflight analyzer hybrid logic (episodes + optional pack) instead of blanket pack blocking.
- Added content-type validation/quarantine gate before organization.
- Introduced decision artifacts + viewer for preflight outcomes.
- Began root cleanup (moved legacy scripts, relocating example request files to docs/examples).
