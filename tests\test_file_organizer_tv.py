import asyncio
import tempfile
from pathlib import Path
import unittest

from _internal.src.file_organizer import organize_tv_show
from _internal.utils.tv_show_naming import TVShowNamingHelper


class DummyLogger:
    def __getattr__(self, name):
        def _log(*args, **kwargs):
            pass
        return _log


class TestTVOrganizer(unittest.TestCase):
    def test_organize_tv_single_episode(self):
        logger = DummyLogger()
        with tempfile.TemporaryDirectory() as tmp:
            tmp_path = Path(tmp)
            organized_base = tmp_path / "workspace" / "2_downloaded_and_organized"
            organized_base.mkdir(parents=True)

            download_dir = tmp_path / "workspace" / "1_downloading" / "complete_raw" / "The.Office.US.S01E01.1080p"
            download_dir.mkdir(parents=True)
            main_file = download_dir / "The.Office.US.S01E01.1080p.mkv"
            main_file.write_bytes(b"data")

            content_info = {"title": "The Office US", "year": 2005}
            resolution = "1080p"
            helper = TVShowNamingHelper()

            ok = asyncio.run(organize_tv_show(content_info, str(main_file), download_dir, organized_base, resolution, helper, logger))
            self.assertTrue(ok)

            dest_dir = organized_base / "tv_shows" / "1080p" / "The Office US (2005)" / "Season 01"
            dest_file = dest_dir / "S01E01.mkv"
            self.assertTrue(dest_file.exists())
            self.assertFalse(main_file.exists())

            marker = dest_dir / ".organized"
            self.assertTrue(marker.exists())


if __name__ == "__main__":
    unittest.main()

