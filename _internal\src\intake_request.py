#!/usr/bin/env python3
from __future__ import annotations

from dataclasses import dataclass
from typing import Any, Dict, List
from pathlib import Path
import importlib.util

from _internal.utils.common_helpers import get_setting
from _internal.src.event_queue import get_event_queue
from _internal.src import request_state


@dataclass
class IntakeResult:
    success: bool
    reason: str
    details: Dict[str, Any]


def _load_stage01_module():
    """Dynamically load the Stage 01 script (filename starts with a digit)."""
    root = Path(__file__).resolve().parents[2]
    stage1_path = root / "01_intake_and_nzb_search.py"
    spec = importlib.util.spec_from_file_location("stage01", stage1_path)
    if spec and spec.loader:
        mod = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(mod)  # type: ignore[attr-defined]
        return mod
    raise ImportError("Unable to load Stage 01 script")


async def process_movie_intake(movie_data: Dict[str, Any], settings: Dict[str, Any], logger) -> IntakeResult:
    """
    Process a single movie intake: delegate to existing Stage 01 function, and emit DownloadAccepted event + record state.
    """
    try:
        radarr_key = get_setting("Radarr", "api_key", expected_type=str, settings_dict=settings)
        if not radarr_key:
            return IntakeResult(False, "missing_api_key", {"component": "radarr"})
        stage01 = _load_stage01_module()
        result = await stage01._add_movie_to_radarr_modern(movie_data, settings, logger)
        # Emit event and create basic state record when accepted/added
        try:
            if result.get('success'):
                title = movie_data.get('cleaned_title') or movie_data.get('title')
                year = movie_data.get('year')
                radarr_id = (result.get('radarr_id') or (result.get('radarr_movie') or {}).get('id'))
                gid = request_state.get_or_create_group('movie', title or 'Unknown', year, metadata={'source': 'intake', 'radarr_id': radarr_id})
                request_state.add_child_item(gid, 'movie', title=title)
                eq = get_event_queue({'EventQueue': {'enabled': True}})
                import asyncio
                await eq.publish('download.accepted', {
                    'type': 'movie',
                    'title': title,
                    'year': year,
                    'radarr_id': radarr_id
                })
        except Exception:
            pass
        return IntakeResult(result.get("success", False), result.get("reason", "unknown"), result)
    except Exception as e:
        logger.error(f"process_movie_intake error: {e}")
        return IntakeResult(False, f"exception_{e}", {})


async def process_tv_intake(tv_data: Dict[str, Any], settings: Dict[str, Any], logger) -> IntakeResult:
    """
    Process a TV intake: delegate to existing Stage 01 function, and emit DownloadAccepted event(s) + record state.
    """
    try:
        sonarr_key = get_setting("Sonarr", "api_key", expected_type=str, settings_dict=settings)
        if not sonarr_key:
            return IntakeResult(False, "missing_api_key", {"component": "sonarr"})
        stage01 = _load_stage01_module()
        result = await stage01._add_tv_show_to_sonarr_modern(tv_data, settings, logger)
        # Emit events and create state entries for requested episodes/seasons
        try:
            if result.get('success'):
                title = tv_data.get('cleaned_title') or tv_data.get('title')
                year = tv_data.get('year')
                sonarr_series_id = result.get('sonarr_id')
                gid = request_state.get_or_create_group('tv', title or 'Unknown', year, metadata={'source': 'intake', 'sonarr_series_id': sonarr_series_id})
                # If specific episodes provided
                episodes = []
                pr = (tv_data.get('parsed_request') or tv_data)
                if isinstance(pr, dict):
                    if pr.get('request_type') == 'specific_episodes':
                        episodes = pr.get('episodes') or []
                    elif pr.get('request_type') == 'specific_season' and pr.get('season'):
                        # Prepopulate season items without episodes; Stage 2 will fill episodes as they complete
                        request_state.add_child_item(gid, 'episode', season=int(pr.get('season')), episode=None, title=title)
                    elif pr.get('request_type') == 'full_series':
                        # Marker for season-level tracking
                        request_state.add_child_item(gid, 'episode', season=1, episode=None, title=title)
                eq = get_event_queue({'EventQueue': {'enabled': True}})
                import asyncio
                for ep in episodes:
                    s = ep.get('season') or ep.get('seasonNumber')
                    e = ep.get('episode') or ep.get('episodeNumber')
                    if s and e:
                        request_state.add_child_item(gid, 'episode', season=int(s), episode=int(e), title=title)
                        await eq.publish('download.accepted', {
                            'type': 'episode',
                            'title': title,
                            'year': year,
                            'season': int(s),
                            'episode': int(e),
                            'sonarr_series_id': sonarr_series_id
                        })
        except Exception:
            pass
        return IntakeResult(result.get("success", False), result.get("reason", "unknown"), result)
    except Exception as e:
        logger.error(f"process_tv_intake error: {e}")
        return IntakeResult(False, f"exception_{e}", {})


async def process_new_requests(requests: List[Dict[str, Any]], settings: Dict[str, Any], logger) -> Dict[str, Any]:
    """
    Entry point used by Stage 01 to process a batch of requests.
    Each request dict should include a 'type' key: 'movie' or 'tv'.
    Returns a summary dict.
    """
    summary: Dict[str, Any] = {"movies": {"success": 0, "fail": 0}, "tv": {"success": 0, "fail": 0}, "details": []}

    for req in requests:
        try:
            rtype = (req.get("type") or "").lower()
            if rtype == "movie":
                res = await process_movie_intake(req, settings, logger)
                summary["movies"]["success" if res.success else "fail"] += 1
                summary["details"].append({"type": rtype, **res.details})
            elif rtype == "tv":
                res = await process_tv_intake(req, settings, logger)
                summary["tv"]["success" if res.success else "fail"] += 1
                summary["details"].append({"type": rtype, **res.details})
            else:
                summary["details"].append({"type": rtype or "unknown", "error": "unsupported_type"})
        except Exception as e:
            logger.warning(f"Request processing error: {e}")
            summary["details"].append({"type": req.get("type", "unknown"), "error": str(e)})

    return summary

