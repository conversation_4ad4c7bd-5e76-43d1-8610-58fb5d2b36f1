# TV Single-Episode Overwrite Policy

Controls how single-episode downloads behave when the destination episode already exists.

Config (settings.ini):

[TV]
# replace  - always replace existing file (default)
# larger   - replace only if the new file is larger (bytes)
# newer    - replace only if the new file has a newer modified time
# skip     - never replace; creates marker only
single_episode_overwrite_policy = replace

Notes:
- Season packs ignore this policy and never overwrite existing episodes (they only organize missing ones).
- When policy prevents replacement (e.g., 'larger' but new is not larger), we still ensure the episode marker exists so the pipeline can progress.

