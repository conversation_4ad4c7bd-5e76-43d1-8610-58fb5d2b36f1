#!/usr/bin/env python3
from __future__ import annotations

import argparse
import json
from pathlib import Path

from _internal.src.telemetry_dashboard import tail_events, aggregate_counts, write_csv, write_sqlite
from _internal.utils.common_helpers import load_settings, get_setting


def main():
    parser = argparse.ArgumentParser(description='Aggregate EventQueue JSONL for dashboard summaries')
    parser.add_argument('--file', '-f', help='Path to events.jsonl (if omitted and --settings provided, inferred)')
    parser.add_argument('--settings', '-s', help='Path to settings.ini to infer EventQueue path')
    parser.add_argument('--max', '-n', type=int, default=1000, help='Max lines to read from the end')
    parser.add_argument('--type', '-t', help='Filter by event type (e.g., file.organized)')
    parser.add_argument('--kind', '-k', choices=['movie', 'episode'], help='Filter by content kind when type is file.organized')
    parser.add_argument('--csv', help='Write CSV summary to this path')
    parser.add_argument('--sqlite', help='Write SQLite summary DB to this path')
    args = parser.parse_args()

    events_path = None
    if args.file:
        events_path = Path(args.file)
    elif args.settings:
        settings = load_settings(args.settings)
        eq_dir = get_setting('EventQueue', 'dir', settings_dict=settings, default='_internal/state/event_queue')
        events_path = Path(eq_dir) / 'events.jsonl'
    else:
        raise SystemExit('Provide --file or --settings to locate events.jsonl')

    events = list(tail_events(events_path, max_lines=args.max))
    # Optional filters
    if args.type:
        events = [e for e in events if e.get('type') == args.type]
    if args.kind and args.type == 'file.organized':
        events = [e for e in events if (e.get('data') or e).get('type') == args.kind]

    summary = aggregate_counts(events)

    if args.csv:
        write_csv(summary, Path(args.csv))
    if args.sqlite:
        write_sqlite(summary, Path(args.sqlite))

    print(json.dumps(summary, indent=2))


if __name__ == '__main__':
    main()

