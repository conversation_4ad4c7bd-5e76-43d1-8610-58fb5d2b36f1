#!/usr/bin/env python3
"""
PlexMovieAutomator/src/utils/metadata_apis.py

Functions for interacting with metadata APIs like TMDb and OMDb.
Enhanced with fuzzy matching for improved metadata validation.
"""
import logging
import requests
import time
import asyncio  # Added for async wrapper
from pathlib import Path
from utils.common_helpers import get_setting, extract_year_from_title # extract_year_from_title used internally
from _internal.utils.fuzzy_matching import (
    select_best_match, 
    enhance_matching_with_alternatives,
    format_match_result,
    should_auto_approve,
    should_silent_correct,
    should_request_confirmation,
    should_reject
)

# Consider using a more robust library like 'tmdbsimple' or 'themoviedb'
# but direct requests are fine for controlled use.
# from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

logger = logging.getLogger(__name__) # Assumes logger is set up by calling script

TMDB_API_BASE_URL = "https://api.themoviedb.org/3"
TMDB_IMAGE_BASE_URL = "https://image.tmdb.org/t/p/" # Add size and path, e.g., w500/poster.jpg

# Basic retry decorator (can be made more specific with tenacity)
def retry_request(max_tries=3, delay_seconds=2, backoff_factor=2):
    def decorator(func):
        def wrapper(*args, **kwargs):
            tries = 0
            while tries < max_tries:
                try:
                    return func(*args, **kwargs)
                except requests.exceptions.RequestException as e:
                    tries += 1
                    logger.warning(f"Request failed (attempt {tries}/{max_tries}): {e}. Retrying in {delay_seconds}s...")
                    if tries >= max_tries:
                        logger.error(f"Max retries reached for {func.__name__}. Last error: {e}")
                        raise # Or return a specific error indicator
                    time.sleep(delay_seconds)
                    delay_seconds *= backoff_factor
        return wrapper
    return decorator

@retry_request()
def _make_tmdb_api_request(endpoint: str, api_key: str, params: dict = None):
    """Makes a GET request to TMDb API, handles common errors and retries."""
    if not api_key:
        logger.error("TMDb API key not provided for TMDb request.")
        return None # Or raise ValueError

    full_url = f"{TMDB_API_BASE_URL}/{endpoint.strip('/')}"
    request_params = {"api_key": api_key}
    if params:
        request_params.update(params)

    logger.debug(f"Requesting TMDb: {full_url} with params: {request_params}")
    response = requests.get(full_url, params=request_params, timeout=15)
    response.raise_for_status() # Will raise HTTPError for 4xx/5xx
    return response.json()

def search_movie_tmdb(title_query: str, year: int = None, api_key: str = None, settings_dict=None) -> list[dict]:
    """Searches TMDb for movies."""
    if not api_key:
        api_key = get_setting("APIKeys", "tmdb_api_key", settings_dict=settings_dict)
    if not api_key:
        logger.error("TMDb API Key is missing for search_movie_tmdb.")
        return []

    params = {"query": title_query, "language": "en-US", "page": 1}
    if year:
        params["year"] = year # TMDb search also supports primary_release_year

    try:
        data = _make_tmdb_api_request("search/movie", api_key, params)
        results = data.get("results", []) if data else []
        logger.info(f"TMDb search for '{title_query}' (Year: {year}) found {len(results)} results.")
        return results
    except requests.exceptions.HTTPError as e:
        logger.error(f"TMDb search HTTP error for '{title_query}': {e.response.status_code} - {e.response.text}")
    except requests.exceptions.RequestException as e:
        logger.error(f"TMDb search request error for '{title_query}': {e}")
    except Exception as e: # Catch any other unexpected error during processing
        logger.error(f"Unexpected error in search_movie_tmdb for '{title_query}': {e}")
    return []


def get_movie_details_tmdb(tmdb_id: str | int, api_key: str = None, settings_dict=None) -> dict | None:
    """Fetches detailed movie information from TMDb including external IDs."""
    if not tmdb_id: return None
    if not api_key:
        api_key = get_setting("APIKeys", "tmdb_api_key", settings_dict=settings_dict)
    if not api_key:
        logger.error("TMDb API Key is missing for get_movie_details_tmdb.")
        return None

    endpoint = f"movie/{tmdb_id}"
    params = {"language": "en-US", "append_to_response": "external_ids,release_dates,credits,keywords"}
    
    try:
        details = _make_tmdb_api_request(endpoint, api_key, params)
        if details:
            logger.info(f"Fetched details for TMDb ID {tmdb_id}: {details.get('title')}")
        return details
    except requests.exceptions.HTTPError as e:
        logger.error(f"TMDb details HTTP error for ID {tmdb_id}: {e.response.status_code} - {e.response.text}")
    except requests.exceptions.RequestException as e:
        logger.error(f"TMDb details request error for ID {tmdb_id}: {e}")
    except Exception as e:
        logger.error(f"Unexpected error in get_movie_details_tmdb for ID {tmdb_id}: {e}")
    return None


# Rename original sync implementation so we can provide an async wrapper with original public name
# (Backward compatible: existing callers already use `await fetch_movie_metadata_for_intake(...)` and raised an error)

def _fetch_movie_metadata_for_intake_sync(raw_title_from_user: str, settings_dict: dict) -> dict:
    """
    Orchestrates metadata fetching: cleans title, searches TMDb, gets details.
    Enhanced with improved fuzzy matching for better accuracy and performance.
    Returns a standardized dictionary for the state manager.
    """
    logger_instance = logging.getLogger(__name__)
    
    tmdb_api_key = get_setting("APIKeys", "tmdb_api_key", settings_dict=settings_dict, required=True)
    if not tmdb_api_key:
        return {"success": False, "error": "TMDb API Key not configured."}

    # Try enhanced approach first, fall back to original if needed
    try:
        # Import and use enhanced fuzzy matcher
        from _internal.utils.fuzzy_matching import create_enhanced_matcher
        
        logger_instance.info(f"Using enhanced fuzzy matching for: '{raw_title_from_user}'")
        
        # Create matcher instance
        matcher = create_enhanced_matcher()
        
        # Create TMDb search function wrapper
        def tmdb_search_wrapper(title, year, content_type):
            return search_movie_tmdb(title, year, tmdb_api_key, settings_dict)
        
        # Create TMDb details function wrapper  
        def tmdb_details_wrapper(tmdb_id, content_type):
            return get_movie_details_tmdb(tmdb_id, tmdb_api_key, settings_dict)
        
        # Perform enhanced matching
        match_result = matcher.match_media_title(
            raw_title=raw_title_from_user,
            content_type="movie",
            tmdb_search_func=tmdb_search_wrapper,
            tmdb_details_func=tmdb_details_wrapper
        )
        
        if not match_result.success:
            logger_instance.warning(f"Enhanced matching failed for '{raw_title_from_user}': {match_result.error}")
            # Fall back to original implementation below
            raise Exception(f"Enhanced matching failed: {match_result.error}")
        
        # Fetch detailed movie information
        logger_instance.info(f"Fetching details for TMDb ID {match_result.tmdb_id}")
        movie_details = get_movie_details_tmdb(match_result.tmdb_id, tmdb_api_key, settings_dict)
        
        if not movie_details:
            logger_instance.error(f"No movie details returned for TMDb ID {match_result.tmdb_id}")
            return {"success": False, "error": "Failed to fetch movie details from TMDb"}
        
        # Build standardized result with enhanced matching metadata
        result = {
            "success": True,
            "tmdb_id": match_result.tmdb_id,
            "title": movie_details.get("title", match_result.title),
            "cleaned_title": movie_details.get("title", match_result.title),  # Add missing cleaned_title field
            "original_title": movie_details.get("original_title"),
            "release_date": movie_details.get("release_date"),
            "year": match_result.year,
            "overview": movie_details.get("overview"),
            "genres": movie_details.get("genres", []),
            "runtime": movie_details.get("runtime"),
            "imdb_id": movie_details.get("external_ids", {}).get("imdb_id"),
            "poster_path": movie_details.get("poster_path"),
            "backdrop_path": movie_details.get("backdrop_path"),
            "vote_average": movie_details.get("vote_average"),
            "vote_count": movie_details.get("vote_count"),
            "popularity": movie_details.get("popularity"),
            
            # Enhanced matching metadata
            "confidence_score": match_result.confidence,
            "from_cache": match_result.from_cache,
            "match_metadata": match_result.metadata,
            "fuzzy_match_warning": match_result.warning,
            "matched_via": "enhanced_fuzzy_matching",
            "original_query": raw_title_from_user
        }
        
        # Log success with performance info
        elapsed_time = match_result.metadata.get('elapsed_time', 0)
        match_path = match_result.metadata.get('match_path', 'unknown')
        logger_instance.info(f"Successfully matched '{raw_title_from_user}' to '{result['title']}' "
                           f"({match_result.confidence:.1f}% confidence) via {match_path} in {elapsed_time:.2f}s")
        
        if match_result.warning:
            logger_instance.warning(f"Match warning: {match_result.warning}")
            
        return result
        
    except Exception as e:
        logger_instance.warning(f"Enhanced matching failed, falling back to original implementation: {e}")
        # Fall back to original implementation below
    
    # Original implementation as fallback
    logger_instance.info(f"Using original fuzzy matching for: '{raw_title_from_user}'")
    
    # 1. Clean raw title and extract year if present
    title_to_search, year_from_title = extract_year_from_title(raw_title_from_user)
    logger_instance.debug(f"Parsed intake: Title='{title_to_search}', Year='{year_from_title}' from '{raw_title_from_user}'")

    # Import fuzzy matching helper for search variants
    from _internal.utils.fuzzy_matching import preprocess_search_query, normalize_title
    
    # Note: Common fixes are now handled in normalize_title() to avoid duplication
    # This ensures consistent handling across all matching scenarios
    
    # 2. Generate search variants for improved matching
    clean_title, extracted_year, title_variants = preprocess_search_query(raw_title_from_user)
    
    # Use extracted year if available, otherwise use year_from_title
    search_year = extracted_year if extracted_year else year_from_title
    
    # Add more search variants based on normalization
    additional_variants = []
    for variant in title_variants:
        normalized = normalize_title(variant)
        if normalized and normalized not in [v.lower() for v in title_variants]:
            additional_variants.append(normalized.title())  # Convert back to title case
    
    all_search_variants = title_variants + additional_variants
    logger_instance.info(f"Generated {len(all_search_variants)} search variants: {all_search_variants}")

    # 3. Search TMDb with multiple strategies and variants
    search_results = []
    
    for variant in all_search_variants:
        # Try with year first if available
        if search_year:
            variant_results = search_movie_tmdb(variant, search_year, tmdb_api_key, settings_dict)
            if variant_results:
                search_results.extend(variant_results)
                logger_instance.info(f"TMDb search '{variant}' with year {search_year}: {len(variant_results)} results")
        
        # Try without year
        variant_results = search_movie_tmdb(variant, None, tmdb_api_key, settings_dict)
        if variant_results:
            search_results.extend(variant_results)
            logger_instance.info(f"TMDb search '{variant}' without year: {len(variant_results)} results")
        
        # Early exit if we have enough results
        if len(search_results) >= 20:  # Reasonable limit
            break
    
    # Remove duplicates by TMDb ID
    seen_ids = set()
    unique_results = []
    for result in search_results:
        tmdb_id = result.get('id')
        if tmdb_id and tmdb_id not in seen_ids:
            seen_ids.add(tmdb_id)
            unique_results.append(result)
    
    search_results = unique_results
    
    if not search_results:
        logger_instance.warning(f"No TMDb search results found for '{raw_title_from_user}' or any variants.")
        return {"success": False, "error": f"No TMDb results found for '{raw_title_from_user}'"}
    
    logger_instance.info(f"Total unique results from all variants: {len(search_results)}")

    # 4. Check for multiple movies with same base title (like Fantastic 4 franchise)
    # Group candidates by normalized base title
    base_title_groups = {}
    for result in search_results:
        title = result.get('title', '')
        # Extract base title (remove year, subtitles, etc.)
        base_title = title.lower()
        # Remove common patterns
        for pattern in [r'\s*\(\d{4}\)', r'\s*:\s*.*', r'\s*-\s*.*', r'\s*(the\s+)?movie$']:
            import re
            base_title = re.sub(pattern, '', base_title).strip()
        
        if base_title not in base_title_groups:
            base_title_groups[base_title] = []
        base_title_groups[base_title].append(result)
    
    # Check if we have multiple versions of the same movie
    multiple_versions = False
    for base_title, candidates in base_title_groups.items():
        if len(candidates) > 1:
            multiple_versions = True
            years = [c.get('release_date', '')[:4] for c in candidates if c.get('release_date')]
            logger_instance.warning(f"Multiple versions found for '{base_title}': years {years}")
            
            # For cases like "Fantastic 4", prefer older versions unless year specified
            if not search_year and len(candidates) > 1:
                # Sort by release date, prefer older movies
                candidates.sort(key=lambda x: x.get('release_date', '9999-01-01'))
                # Put the oldest one first in search_results
                oldest = candidates[0]
                search_results = [oldest] + [r for r in search_results if r.get('id') != oldest.get('id')]
                logger_instance.info(f"Prioritized older version: {oldest.get('title')} ({oldest.get('release_date', 'Unknown')[:4]})")

    # 5. Enhanced fuzzy matching to select best match
    try:
        # Special handling for vague queries with specific years (like "Space Movie 2001")
        if search_year and len(title_to_search.split()) <= 2:  # Vague query with year
            # Prioritize exact year matches for vague queries
            year_matches = [r for r in search_results if r.get('release_date', '').startswith(str(search_year))]
            if year_matches:
                search_results = year_matches + [r for r in search_results if r not in year_matches]
                logger_instance.info(f"Prioritized {len(year_matches)} year matches for vague query with year {search_year}")
        
        # Use enhanced matching with alternative titles as fallback
        best_match, confidence_score, match_metadata = enhance_matching_with_alternatives(
            query_title=raw_title_from_user,  # Use original title for better matching
            candidates=search_results,
            api_key=tmdb_api_key,
            user_year=search_year,
            content_type="movie",
            score_threshold=60.0  # Lower threshold for challenging cases
        )
        
        if not best_match:
            logger_instance.error(f"No suitable match found for '{raw_title_from_user}' after fuzzy matching.")
            return {"success": False, "error": "No reliable metadata match found"}
        
        # Format the match result with confidence information
        match_result = format_match_result(
            candidate=best_match,
            score=confidence_score,
            match_metadata=match_metadata,
            content_type="movie",
            original_query=raw_title_from_user
        )
        
        # Handle different confidence levels with more lenient approach
        if should_reject(confidence_score) and confidence_score < 40.0:  # Only reject very low scores
            logger_instance.warning(f"Rejecting very low confidence match: {match_result['message']}")
            return {
                "success": False, 
                "error": match_result.get("error", "Very low confidence match rejected"),
                "confidence_score": confidence_score,
                "best_guess": match_result.get("matched_title")
            }
        
        elif should_request_confirmation(confidence_score) and confidence_score < 70.0:
            logger_instance.warning(f"Low confidence match (proceeding anyway): {match_result['message']}")
            # For automated processing, proceed with warning rather than failing
            # In a real system, this could prompt the user for confirmation
        
        # Proceed with high or moderate confidence matches
        tmdb_id_found = best_match.get("id")
        if not tmdb_id_found:
            logger_instance.error(f"No TMDb ID found in best match for '{raw_title_from_user}'.")
            return {"success": False, "error": "TMDb ID missing in search result."}

        # Log the matching decision
        if should_auto_approve(confidence_score):
            logger_instance.info(f"Auto-approved match: {match_result['message']}")
        elif should_silent_correct(confidence_score):
            logger_instance.info(f"Silent correction applied: {match_result['message']}")

        logger_instance.info(f"Selected TMDb match for '{raw_title_from_user}': ID={tmdb_id_found}, Title='{best_match.get('title')}', Confidence={confidence_score:.1f}%")

    except Exception as e:
        logger_instance.error(f"Error during fuzzy matching for '{raw_title_from_user}': {e}")
        # Fallback to original simple matching
        best_match = None
        if year_from_title:
            for res in search_results:
                release_date = res.get("release_date", "")
                if release_date and release_date.startswith(str(year_from_title)):
                    best_match = res
                    break
        if not best_match:
            best_match = search_results[0]
        
        tmdb_id_found = best_match.get("id")
        if not tmdb_id_found:
            return {"success": False, "error": "TMDb ID missing in search result."}
        
        confidence_score = 100.0  # Assume high confidence for fallback
        match_result = {"warning": "Fuzzy matching failed, used fallback selection"}

    # 4. Get Full Details from TMDb with validation
    full_details = get_movie_details_tmdb(tmdb_id_found, tmdb_api_key, settings_dict)
    if not full_details:
        logger_instance.error(f"Failed to fetch full details for TMDb ID {tmdb_id_found}.")
        return {"success": False, "error": f"Failed to get details for TMDb ID {tmdb_id_found}."}
    
    # Quick validation of essential fields before processing
    if not full_details.get("title") or not full_details.get("release_date"):
        logger_instance.warning(f"TMDb ID {tmdb_id_found} has missing essential fields, trying next best match...")
        
        # Try to get the next best match if available
        if hasattr(match_metadata, 'get') and len(search_results) > 1:
            # Re-run selection excluding this problematic ID
            filtered_results = [r for r in search_results if r.get("id") != tmdb_id_found]
            if filtered_results:
                try:
                    backup_match, backup_confidence, _ = enhance_matching_with_alternatives(
                        query_title=raw_title_from_user,
                        candidates=filtered_results,
                        api_key=tmdb_api_key,
                        user_year=search_year,
                        content_type="movie",
                        score_threshold=50.0  # Lower threshold for backup
                    )
                    
                    if backup_match:
                        backup_id = backup_match.get("id")
                        backup_details = get_movie_details_tmdb(backup_id, tmdb_api_key, settings_dict)
                        if backup_details and backup_details.get("title") and backup_details.get("release_date"):
                            logger_instance.info(f"Using backup match: ID={backup_id}, Title='{backup_details.get('title')}'")
                            full_details = backup_details
                            tmdb_id_found = backup_id
                            confidence_score = backup_confidence
                        else:
                            logger_instance.error(f"Backup match also has missing metadata for TMDb ID {backup_id}")
                except Exception as backup_error:
                    logger_instance.warning(f"Backup matching failed: {backup_error}")
        
        # Final check - if still missing essential data, fail
        if not full_details.get("title") or not full_details.get("release_date"):
            logger_instance.error(f"Essential metadata (title/year) missing from TMDb details for ID {tmdb_id_found}.")
            return {"success": False, "error": "Title or year missing in TMDb details."}

    # 5. Structure and Return Data
    title = full_details.get("title")
    release_date_str = full_details.get("release_date", "")
    year = int(release_date_str[:4]) if release_date_str and release_date_str[:4].isdigit() else None
    
    # Note: Essential fields already validated above

    # Construct poster URL
    poster_path_segment = full_details.get("poster_path")
    full_poster_url = None
    if poster_path_segment:
         # Poster size can be a setting, e.g., w500
        poster_size = get_setting("TMDb", "poster_default_size", default="w500", settings_dict=settings_dict)
        image_base = get_setting("TMDb", "image_base_url", default="https://image.tmdb.org/t/p/", settings_dict=settings_dict)
        full_poster_url = f"{image_base.strip('/')}/{poster_size.strip('/')}{poster_path_segment}"

    result = {
        "success": True,
        "title": title,  # Add this for consistency
        "cleaned_title": title,
        "year": year,
        "tmdb_id": str(tmdb_id_found), # Ensure string for consistency
        "imdb_id": full_details.get("external_ids", {}).get("imdb_id"),
        "genres": [g["name"] for g in full_details.get("genres", [])],
        "overview": full_details.get("overview"),
        "poster_url_tmdb": full_poster_url,
        "metadata_source": "TMDb",
        "error": None,
        "confidence_score": confidence_score,
        "fuzzy_matching_used": True
    }
    
    # Add warning for moderate confidence matches
    if should_silent_correct(confidence_score):
        result["warning"] = match_result.get("warning", f"Fuzzy matched with {confidence_score:.1f}% confidence")
    
    # Add matching metadata for debugging/logging
    if "match_metadata" in locals():
        result["match_metadata"] = match_metadata
    
    return result

async def fetch_movie_metadata_for_intake(raw_title_from_user: str, settings_dict: dict) -> dict:
    """Async wrapper calling the original sync implementation in a thread pool."""
    loop = asyncio.get_running_loop()
    return await loop.run_in_executor(None, _fetch_movie_metadata_for_intake_sync, raw_title_from_user, settings_dict)

def download_image_from_url(image_url: str, save_path_str: str | Path, logger_instance: logging.Logger = None) -> bool:
    """Downloads an image from a URL and saves it."""
    current_logger = logger_instance if logger_instance else logging.getLogger(__name__)
    if not image_url:
        current_logger.warning("No image URL provided for download.")
        return False
    
    save_path = Path(save_path_str)
    # Ensure directory exists
    save_path.parent.mkdir(parents=True, exist_ok=True)

    try:
        response = requests.get(image_url, stream=True, timeout=20)
        response.raise_for_status()
        with open(save_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        current_logger.info(f"Image downloaded successfully to {save_path}")
        return True
    except requests.exceptions.RequestException as e:
        current_logger.error(f"Failed to download image from {image_url}: {e}")
        return False
    except IOError as e:
        current_logger.error(f"Failed to save image to {save_path}: {e}")
        return False