#!/usr/bin/env python3
from __future__ import annotations

import json
from pathlib import Path
from typing import Any, Dict, Optional
from datetime import datetime, timezone


def compute_movie_key(content: Dict[str, Any]) -> str:
    """
    Compute canonical idempotency key for a movie.
    Preference order: Radarr ID -> TMDB ID -> title-year.
    Returns a stable string key.
    """
    # Radarr movie id
    rid = content.get('id') or content.get('radarr_id') or content.get('movie_id')
    if isinstance(rid, int) and rid > 0:
        return f"movie:radarr:{rid}"

    # TMDB id
    tmdb = content.get('tmdbId') or content.get('tmdb_id')
    if tmdb:
        return f"movie:tmdb:{tmdb}"

    # Title-year fallback
    title = (content.get('cleaned_title') or content.get('title') or 'unknown').strip().lower()
    year = content.get('year') or content.get('Year') or 'unknown'
    return f"movie:titleyear:{title}:{year}"


def compute_episode_key(series_id: Optional[int] = None, season: Optional[int] = None,
                        episode: Optional[int] = None, episode_id: Optional[int] = None) -> Optional[str]:
    """
    Compute canonical idempotency key for a TV episode.
    Preference order: Sonarr episode ID -> seriesId+SxxExx.
    Returns None if insufficient info.
    """
    if episode_id:
        return f"ep:sonarr_ep:{episode_id}"
    if series_id and season is not None and episode is not None:
        return f"ep:sonarr:{series_id}:S{season:02d}E{episode:02d}"
    return None


class IdempotencyIndex:
    """
    Minimal durable index of processed items by idempotency key.
    Stores last known stage and timestamp in a JSON file.
    """
    def __init__(self, base_dir: Optional[Path] = None):
        self.base_dir = Path(base_dir) if base_dir else Path("_internal/state/idempotency")
        self.base_dir.mkdir(parents=True, exist_ok=True)
        self.file_path = self.base_dir / "index.json"
        self._data: Dict[str, Dict[str, Any]] = {}
        self._load()

    def _load(self) -> None:
        try:
            if self.file_path.exists():
                self._data = json.loads(self.file_path.read_text(encoding="utf-8"))
        except Exception:
            self._data = {}

    def _save(self) -> None:
        try:
            self.file_path.write_text(json.dumps(self._data, ensure_ascii=False, indent=2), encoding="utf-8")
        except Exception:
            pass

    def is_already_processed(self, key: str, target_stage: str) -> bool:
        rec = self._data.get(key)
        if not rec:
            return False
        last_stage = rec.get("last_stage")
        if not last_stage:
            return False
        # Simple stage precedence: consider equal or more advanced stages as done
        order = [
            "downloading",
            "download_completed",
            "organized",
            "mkv_processing",
            "mkv_complete",
            "subtitle_processing",
            "subtitle_complete",
            "final_mux_pending",
            "final_mux_complete",
            "completed",
        ]
        try:
            return order.index(last_stage) >= order.index(target_stage)
        except ValueError:
            return False

    def mark_processed(self, key: str, stage: str, metadata: Optional[Dict[str, Any]] = None) -> None:
        self._data[key] = {
            "last_stage": stage,
            "updated_at": datetime.now(timezone.utc).isoformat(),
            "metadata": metadata or {},
        }
        self._save()

