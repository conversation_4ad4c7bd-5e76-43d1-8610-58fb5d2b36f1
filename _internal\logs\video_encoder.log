2025-09-15 05:15:52,643 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-15 05:15:52,643 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-15 05:15:52,643 - INFO - Filesystem-first video encoder initialized
2025-09-15 05:15:52,643 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-15 05:15:52,643 - INFO - Workspace: .
2025-09-15 05:16:13,405 - INFO - Starting filesystem-first video encoding batch processing
2025-09-15 05:16:13,405 - INFO - Discovering movies ready for video encoding...
2025-09-15 05:16:13,500 - INFO - Got actual duration from ffprobe: 5867.9 seconds (97.8 minutes)
2025-09-15 05:16:13,535 - INFO - Got actual duration from ffprobe: 5312.3 seconds (88.5 minutes)
2025-09-15 05:16:13,557 - INFO - Got actual duration from ffprobe: 9133.3 seconds (152.2 minutes)
2025-09-15 05:16:13,578 - INFO - Got actual duration from ffprobe: 7926.4 seconds (132.1 minutes)
2025-09-15 05:16:13,595 - INFO - Got actual duration from ffprobe: 7814.5 seconds (130.2 minutes)
2025-09-15 05:16:13,595 - INFO - Found 5 movies ready for encoding
2025-09-15 05:16:35,709 - INFO - Processing movie: 13 Going on 30 (2004)
2025-09-15 05:16:35,710 - INFO - Original bitrate for 13 Going on 30: 28499.5 kbps (28.5 Mbps)
2025-09-15 05:16:35,710 - INFO - Compressed to 50.0% of original: 14.2 Mbps
2025-09-15 05:17:59,926 - INFO - Output path: workspace\4_ready_for_final_mux\1080p\13 Going on 30 (2004)\13 Going on 30 (2004).encoded.mkv
2025-09-15 05:17:59,926 - INFO - Starting encoding for 13 Going on 30
2025-09-15 05:17:59,926 - INFO - HandBrake command: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe -i "workspace\3_mkv_cleaned_subtitles_extracted\movies\1080p\13 Going on 30 (2004)\13 Going on 30 (2004).processed.mkv" -o "workspace\4_ready_for_final_mux\1080p\13 Going on 30 (2004)\13 Going on 30 (2004).encoded.mkv" -e x265 --encoder-preset fast --encoder-profile main --encoder-level 5.1 -f mkv -b 2000 --multi-pass -T --audio 1 -E aac -B 160 --mixdown stereo --verbose 1
2025-09-15 06:20:40,082 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-15 06:20:40,082 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-15 06:20:40,082 - INFO - Filesystem-first video encoder initialized
2025-09-15 06:20:40,082 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-15 06:20:40,082 - INFO - Workspace: .
2025-09-15 06:20:41,894 - INFO - Starting filesystem-first video encoding batch processing
2025-09-15 06:20:41,894 - INFO - Discovering movies ready for video encoding...
2025-09-15 06:20:41,989 - INFO - Got actual duration from ffprobe: 132.7 seconds (2.2 minutes)
2025-09-15 06:20:42,027 - INFO - Got actual duration from ffprobe: 120.4 seconds (2.0 minutes)
2025-09-15 06:20:42,053 - INFO - Got actual duration from ffprobe: 196.5 seconds (3.3 minutes)
2025-09-15 06:20:42,071 - INFO - Got actual duration from ffprobe: 202.4 seconds (3.4 minutes)
2025-09-15 06:20:42,089 - INFO - Got actual duration from ffprobe: 176.3 seconds (2.9 minutes)
2025-09-15 06:20:42,089 - INFO - Found 5 movies ready for encoding
2025-09-15 06:20:47,228 - INFO - Processing movie: 13 Going on 30 (2004)
2025-09-15 06:20:47,228 - INFO - Original bitrate for 13 Going on 30: 28934.8 kbps (28.9 Mbps)
2025-09-15 06:20:47,228 - INFO - Compressed to 50.0% of original: 14.5 Mbps
2025-09-15 06:21:22,681 - INFO - Output path: workspace\4_ready_for_final_mux\1080p\13 Going on 30 (2004)\13 Going on 30 (2004).encoded.mkv
2025-09-15 06:21:22,681 - INFO - Starting encoding for 13 Going on 30
2025-09-15 06:21:22,681 - INFO - HandBrake command: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe -i "workspace\3_mkv_cleaned_subtitles_extracted\movies\1080p\13 Going on 30 (2004)\13 Going on 30 (2004).processed.mkv" -o "workspace\4_ready_for_final_mux\1080p\13 Going on 30 (2004)\13 Going on 30 (2004).encoded.mkv" -e x265 --encoder-preset fast --encoder-profile main --encoder-level 5.1 -f mkv -b 2000 --multi-pass -T --audio 1 -E aac -B 160 --mixdown stereo --verbose 1
2025-09-15 06:22:45,361 - INFO - Encoding completed successfully for 13 Going on 30 in 1.4 minutes
2025-09-15 06:22:45,361 - INFO - Output file verification passed: 34.4 MB
2025-09-15 06:22:45,369 - WARNING - No _Processed_Audio directory found at: workspace\3_mkv_cleaned_subtitles_extracted\1080p\13 Going on 30 (2004)\_Processed_Audio
2025-09-15 06:22:45,369 - WARNING - Stage 3 movie directory not found: workspace\3_mkv_cleaned_subtitles_extracted\1080p\13 Going on 30 (2004)
2025-09-15 06:22:45,390 - INFO - Saved encoding metadata for 13 Going on 30
2025-09-15 06:22:45,390 - INFO - Successfully processed 13 Going on 30
2025-09-15 06:22:45,390 - INFO - Processing movie: Don't Breathe (2016)
2025-09-15 06:22:45,390 - INFO - Original bitrate for Don't Breathe: 26837.0 kbps (26.8 Mbps)
2025-09-15 06:22:45,390 - INFO - Compressed to 50.0% of original: 13.4 Mbps
2025-09-15 06:23:20,328 - INFO - Output path: workspace\4_ready_for_final_mux\1080p\Don't Breathe (2016)\Dont Breathe (2016).encoded.mkv
2025-09-15 06:23:20,328 - INFO - Starting encoding for Don't Breathe
2025-09-15 06:23:20,328 - INFO - HandBrake command: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe -i "workspace\3_mkv_cleaned_subtitles_extracted\movies\1080p\Don't Breathe (2016)\Don't Breathe (2016).processed.mkv" -o "workspace\4_ready_for_final_mux\1080p\Don't Breathe (2016)\Dont Breathe (2016).encoded.mkv" -e x265 --encoder-preset fast --encoder-profile main --encoder-level 5.1 -f mkv -b 2000 --multi-pass -T --audio 1 -E aac -B 160 --mixdown stereo --verbose 1
2025-09-15 06:24:11,145 - INFO - Encoding completed successfully for Don't Breathe in 0.8 minutes
2025-09-15 06:24:11,145 - INFO - Output file verification passed: 30.9 MB
2025-09-15 06:24:11,166 - WARNING - No _Processed_Audio directory found at: workspace\3_mkv_cleaned_subtitles_extracted\1080p\Don't Breathe (2016)\_Processed_Audio
2025-09-15 06:24:11,166 - WARNING - Stage 3 movie directory not found: workspace\3_mkv_cleaned_subtitles_extracted\1080p\Don't Breathe (2016)
2025-09-15 06:24:11,219 - INFO - Saved encoding metadata for Don't Breathe
2025-09-15 06:24:11,219 - INFO - Successfully processed Don't Breathe
2025-09-15 06:24:11,220 - INFO - Processing movie: The Dark Knight (2008)
2025-09-15 06:24:11,220 - INFO - Original bitrate for The Dark Knight: 22097.1 kbps (22.1 Mbps)
2025-09-15 06:24:11,221 - INFO - Compressed to 50.0% of original: 11.0 Mbps
2025-09-15 06:25:05,124 - INFO - Output path: workspace\4_ready_for_final_mux\1080p\The Dark Knight (2008)\The Dark Knight (2008).encoded.mkv
2025-09-15 06:25:05,124 - INFO - Starting encoding for The Dark Knight
2025-09-15 06:25:05,124 - INFO - HandBrake command: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe -i "workspace\3_mkv_cleaned_subtitles_extracted\movies\1080p\The Dark Knight (2008)\The Dark Knight (2008).processed.mkv" -o "workspace\4_ready_for_final_mux\1080p\The Dark Knight (2008)\The Dark Knight (2008).encoded.mkv" -e x265 --encoder-preset fast --encoder-profile main --encoder-level 5.1 -f mkv -b 2000 --multi-pass -T --audio 1 -E aac -B 160 --mixdown stereo --verbose 1
2025-09-15 06:26:39,772 - INFO - Encoding completed successfully for The Dark Knight in 1.6 minutes
2025-09-15 06:26:39,773 - INFO - Output file verification passed: 50.9 MB
2025-09-15 06:26:39,777 - WARNING - No _Processed_Audio directory found at: workspace\3_mkv_cleaned_subtitles_extracted\1080p\The Dark Knight (2008)\_Processed_Audio
2025-09-15 06:26:39,777 - WARNING - Stage 3 movie directory not found: workspace\3_mkv_cleaned_subtitles_extracted\1080p\The Dark Knight (2008)
2025-09-15 06:26:39,780 - INFO - Saved encoding metadata for The Dark Knight
2025-09-15 06:26:39,781 - INFO - Successfully processed The Dark Knight
2025-09-15 06:26:39,781 - INFO - Processing movie: Star Trek Into Darkness (2013)
2025-09-15 06:26:39,781 - INFO - Original bitrate for Star Trek Into Darkness: 59189.5 kbps (59.2 Mbps)
2025-09-15 06:26:39,781 - INFO - 4K content: using default 20.0 Mbps
2025-09-15 06:28:34,753 - INFO - Output path: workspace\4_ready_for_final_mux\4k\Star Trek Into Darkness (2013)\Star Trek Into Darkness (2013).encoded.mkv
2025-09-15 06:28:34,753 - INFO - Starting encoding for Star Trek Into Darkness
2025-09-15 06:28:34,753 - INFO - HandBrake command: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe -i "workspace\3_mkv_cleaned_subtitles_extracted\movies\4k\Star Trek Into Darkness (2013)\Star Trek Into Darkness (2013).processed.mkv" -o "workspace\4_ready_for_final_mux\4k\Star Trek Into Darkness (2013)\Star Trek Into Darkness (2013).encoded.mkv" -e x265 --encoder-preset fast --encoder-profile main10 --encoder-level 5.1 -f mkv -b 2000 --multi-pass -T --audio 1 -E aac -B 160 --mixdown stereo --verbose 1
2025-09-15 06:31:56,499 - INFO - Encoding completed successfully for Star Trek Into Darkness in 3.4 minutes
2025-09-15 06:31:56,499 - INFO - Output file verification passed: 52.1 MB
2025-09-15 06:31:56,503 - WARNING - No _Processed_Audio directory found at: workspace\3_mkv_cleaned_subtitles_extracted\4k\Star Trek Into Darkness (2013)\_Processed_Audio
2025-09-15 06:31:56,503 - WARNING - Stage 3 movie directory not found: workspace\3_mkv_cleaned_subtitles_extracted\4k\Star Trek Into Darkness (2013)
2025-09-15 06:31:56,548 - INFO - Saved encoding metadata for Star Trek Into Darkness
2025-09-15 06:31:56,549 - INFO - Successfully processed Star Trek Into Darkness
2025-09-15 06:31:56,549 - INFO - Processing movie: Top Gun Maverick (2022)
2025-09-15 06:31:56,549 - INFO - Original bitrate for Top Gun Maverick: 75466.7 kbps (75.5 Mbps)
2025-09-15 06:31:56,549 - INFO - 4K content: using default 20.0 Mbps
2025-09-15 06:31:57,572 - INFO - Batch processing interrupted
2025-09-15 06:31:57,572 - INFO - Batch processing complete: 4 successful, 0 failed, 0 skipped
2025-09-15 06:49:07,487 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-15 06:49:07,488 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-15 06:49:07,488 - INFO - Filesystem-first video encoder initialized
2025-09-15 06:49:07,488 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-15 06:49:07,488 - INFO - Workspace: .
2025-09-15 06:49:09,527 - INFO - Starting filesystem-first video encoding batch processing
2025-09-15 06:49:09,527 - INFO - Discovering movies ready for video encoding...
2025-09-15 06:49:09,607 - INFO - Got actual duration from ffprobe: 132.7 seconds (2.2 minutes)
2025-09-15 06:49:09,643 - INFO - Got actual duration from ffprobe: 120.4 seconds (2.0 minutes)
2025-09-15 06:49:09,669 - INFO - Got actual duration from ffprobe: 196.5 seconds (3.3 minutes)
2025-09-15 06:49:09,687 - INFO - Got actual duration from ffprobe: 202.4 seconds (3.4 minutes)
2025-09-15 06:49:09,706 - INFO - Got actual duration from ffprobe: 176.3 seconds (2.9 minutes)
2025-09-15 06:49:09,706 - INFO - Found 5 movies ready for encoding
2025-09-15 06:49:13,485 - INFO - Processing movie: 13 Going on 30 (2004)
2025-09-15 06:49:13,486 - INFO - Original bitrate for 13 Going on 30: 28934.8 kbps (28.9 Mbps)
2025-09-15 06:49:13,486 - INFO - Compressed to 50.0% of original: 14.5 Mbps
2025-09-15 06:49:54,198 - INFO - Output path: workspace\4_ready_for_final_mux\1080p\13 Going on 30 (2004)\13 Going on 30 (2004).encoded.mkv
2025-09-15 06:49:54,198 - INFO - Starting encoding for 13 Going on 30
2025-09-15 06:49:54,199 - INFO - HandBrake command: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe -i "workspace\3_mkv_cleaned_subtitles_extracted\movies\1080p\13 Going on 30 (2004)\13 Going on 30 (2004).processed.mkv" -o "workspace\4_ready_for_final_mux\1080p\13 Going on 30 (2004)\13 Going on 30 (2004).encoded.mkv" -e x265 --encoder-preset fast --encoder-profile main --encoder-level 5.1 -f mkv -b 2000 --multi-pass -T --audio 1 -E aac -B 160 --mixdown stereo --verbose 1
2025-09-15 06:51:13,403 - INFO - Encoding completed successfully for 13 Going on 30 in 1.3 minutes
2025-09-15 06:51:13,403 - INFO - Output file verification passed: 34.4 MB
2025-09-15 06:51:13,405 - INFO - 🧹 Cleaning up after video_encoding completion for 13 Going on 30 (2004)
2025-09-15 06:51:13,406 - INFO - ✅ Cleanup completed for video_encoding
2025-09-15 06:51:13,406 - INFO - ✅ Cleanup completed after video encoding
2025-09-15 06:51:13,406 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\movies\1080p\13 Going on 30 (2004)\_Processed_Audio
2025-09-15 06:51:13,411 - INFO - ✅ Copied largest audio file: TrueHD_5.1_eng_A_TRUEHD.thd (22.1 MB)
2025-09-15 06:51:13,411 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\movies\1080p\13 Going on 30 (2004)\_Processed_Audio
2025-09-15 06:51:13,411 - INFO -    To: workspace\4_ready_for_final_mux\1080p\13 Going on 30 (2004)
2025-09-15 06:51:13,441 - INFO - 🗑️ Deleted processed MKV file: 13 Going on 30 (2004).processed.mkv
2025-09-15 06:51:13,444 - INFO - 🗑️ Deleted processed audio folder: workspace\3_mkv_cleaned_subtitles_extracted\movies\1080p\13 Going on 30 (2004)\_Processed_Audio
2025-09-15 06:51:13,444 - INFO - ✅ Stage 3 cleanup completed for 13 Going on 30
2025-09-15 06:51:13,447 - INFO - Saved encoding metadata for 13 Going on 30
2025-09-15 06:51:13,447 - INFO - Successfully processed 13 Going on 30
2025-09-15 06:51:13,447 - INFO - Processing movie: Don't Breathe (2016)
2025-09-15 06:51:13,448 - INFO - Original bitrate for Don't Breathe: 26837.0 kbps (26.8 Mbps)
2025-09-15 06:51:13,448 - INFO - Compressed to 50.0% of original: 13.4 Mbps
2025-09-15 06:51:37,464 - INFO - Output path: workspace\4_ready_for_final_mux\1080p\Don't Breathe (2016)\Dont Breathe (2016).encoded.mkv
2025-09-15 06:51:37,464 - INFO - Starting encoding for Don't Breathe
2025-09-15 06:51:37,464 - INFO - HandBrake command: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe -i "workspace\3_mkv_cleaned_subtitles_extracted\movies\1080p\Don't Breathe (2016)\Don't Breathe (2016).processed.mkv" -o "workspace\4_ready_for_final_mux\1080p\Don't Breathe (2016)\Dont Breathe (2016).encoded.mkv" -e x265 --encoder-preset fast --encoder-profile main --encoder-level 5.1 -f mkv -b 2000 --multi-pass -T --audio 1 -E aac -B 160 --mixdown stereo --verbose 1
2025-09-15 06:52:28,462 - INFO - Encoding completed successfully for Don't Breathe in 0.8 minutes
2025-09-15 06:52:28,462 - INFO - Output file verification passed: 30.9 MB
2025-09-15 06:52:28,464 - INFO - 🧹 Cleaning up after video_encoding completion for Don't Breathe (2016)
2025-09-15 06:52:28,464 - INFO - ✅ Cleanup completed for video_encoding
2025-09-15 06:52:28,464 - INFO - ✅ Cleanup completed after video encoding
2025-09-15 06:52:28,464 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\movies\1080p\Don't Breathe (2016)\_Processed_Audio
2025-09-15 06:52:28,475 - INFO - ✅ Copied largest audio file: Track_1_eng_A_DTS.dts (51.6 MB)
2025-09-15 06:52:28,475 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\movies\1080p\Don't Breathe (2016)\_Processed_Audio
2025-09-15 06:52:28,475 - INFO -    To: workspace\4_ready_for_final_mux\1080p\Don't Breathe (2016)
2025-09-15 06:52:28,501 - INFO - 🗑️ Deleted processed MKV file: Don't Breathe (2016).processed.mkv
2025-09-15 06:52:28,507 - INFO - 🗑️ Deleted processed audio folder: workspace\3_mkv_cleaned_subtitles_extracted\movies\1080p\Don't Breathe (2016)\_Processed_Audio
2025-09-15 06:52:28,507 - INFO - ✅ Stage 3 cleanup completed for Don't Breathe
2025-09-15 06:52:28,512 - INFO - Saved encoding metadata for Don't Breathe
2025-09-15 06:52:28,512 - INFO - Successfully processed Don't Breathe
2025-09-15 06:52:28,513 - INFO - Processing movie: The Dark Knight (2008)
2025-09-15 06:52:28,513 - INFO - Original bitrate for The Dark Knight: 22097.1 kbps (22.1 Mbps)
2025-09-15 06:52:28,513 - INFO - Compressed to 50.0% of original: 11.0 Mbps
2025-09-15 06:53:00,250 - INFO - Output path: workspace\4_ready_for_final_mux\1080p\The Dark Knight (2008)\The Dark Knight (2008).encoded.mkv
2025-09-15 06:53:00,250 - INFO - Starting encoding for The Dark Knight
2025-09-15 06:53:00,250 - INFO - HandBrake command: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe -i "workspace\3_mkv_cleaned_subtitles_extracted\movies\1080p\The Dark Knight (2008)\The Dark Knight (2008).processed.mkv" -o "workspace\4_ready_for_final_mux\1080p\The Dark Knight (2008)\The Dark Knight (2008).encoded.mkv" -e x265 --encoder-preset fast --encoder-profile main --encoder-level 5.1 -f mkv -b 2000 --multi-pass -T --audio 1 -E aac -B 160 --mixdown stereo --verbose 1
2025-09-15 06:54:47,464 - INFO - Encoding completed successfully for The Dark Knight in 1.8 minutes
2025-09-15 06:54:47,464 - INFO - Output file verification passed: 50.9 MB
2025-09-15 06:54:47,466 - INFO - 🧹 Cleaning up after video_encoding completion for The Dark Knight (2008)
2025-09-15 06:54:47,466 - INFO - ✅ Cleanup completed for video_encoding
2025-09-15 06:54:47,466 - INFO - ✅ Cleanup completed after video encoding
2025-09-15 06:54:47,466 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\movies\1080p\The Dark Knight (2008)\_Processed_Audio
2025-09-15 06:54:47,472 - INFO - ✅ Copied largest audio file: Track_1_eng_A_TRUEHD.thd (30.0 MB)
2025-09-15 06:54:47,472 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\movies\1080p\The Dark Knight (2008)\_Processed_Audio
2025-09-15 06:54:47,472 - INFO -    To: workspace\4_ready_for_final_mux\1080p\The Dark Knight (2008)
2025-09-15 06:54:47,507 - INFO - 🗑️ Deleted processed MKV file: The Dark Knight (2008).processed.mkv
2025-09-15 06:54:47,515 - INFO - 🗑️ Deleted processed audio folder: workspace\3_mkv_cleaned_subtitles_extracted\movies\1080p\The Dark Knight (2008)\_Processed_Audio
2025-09-15 06:54:47,515 - INFO - ✅ Stage 3 cleanup completed for The Dark Knight
2025-09-15 06:54:47,538 - INFO - Saved encoding metadata for The Dark Knight
2025-09-15 06:54:47,538 - INFO - Successfully processed The Dark Knight
2025-09-15 06:54:47,538 - INFO - Processing movie: Star Trek Into Darkness (2013)
2025-09-15 06:54:47,538 - INFO - Original bitrate for Star Trek Into Darkness: 59189.5 kbps (59.2 Mbps)
2025-09-15 06:54:47,538 - INFO - 4K content: using default 20.0 Mbps
2025-09-15 06:55:14,581 - INFO - Output path: workspace\4_ready_for_final_mux\4k\Star Trek Into Darkness (2013)\Star Trek Into Darkness (2013).encoded.mkv
2025-09-15 06:55:14,581 - INFO - Starting encoding for Star Trek Into Darkness
2025-09-15 06:55:14,581 - INFO - HandBrake command: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe -i "workspace\3_mkv_cleaned_subtitles_extracted\movies\4k\Star Trek Into Darkness (2013)\Star Trek Into Darkness (2013).processed.mkv" -o "workspace\4_ready_for_final_mux\4k\Star Trek Into Darkness (2013)\Star Trek Into Darkness (2013).encoded.mkv" -e x265 --encoder-preset fast --encoder-profile main10 --encoder-level 5.1 -f mkv -b 2000 --multi-pass -T --audio 1 -E aac -B 160 --mixdown stereo --verbose 1
2025-09-15 06:58:26,342 - INFO - Encoding completed successfully for Star Trek Into Darkness in 3.2 minutes
2025-09-15 06:58:26,342 - INFO - Output file verification passed: 52.1 MB
2025-09-15 06:58:26,343 - INFO - 🧹 Cleaning up after video_encoding completion for Star Trek Into Darkness (2013)
2025-09-15 06:58:26,343 - INFO - ✅ Cleanup completed for video_encoding
2025-09-15 06:58:26,344 - INFO - ✅ Cleanup completed after video encoding
2025-09-15 06:58:26,344 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\movies\4k\Star Trek Into Darkness (2013)\_Processed_Audio
2025-09-15 06:58:26,365 - INFO - ✅ Copied largest audio file: Track_1_eng_A_TRUEHD.thd (104.6 MB)
2025-09-15 06:58:26,366 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\movies\4k\Star Trek Into Darkness (2013)\_Processed_Audio
2025-09-15 06:58:26,366 - INFO -    To: workspace\4_ready_for_final_mux\4k\Star Trek Into Darkness (2013)
2025-09-15 06:58:26,459 - INFO - 🗑️ Deleted processed MKV file: Star Trek Into Darkness (2013).processed.mkv
2025-09-15 06:58:26,470 - INFO - 🗑️ Deleted processed audio folder: workspace\3_mkv_cleaned_subtitles_extracted\movies\4k\Star Trek Into Darkness (2013)\_Processed_Audio
2025-09-15 06:58:26,470 - INFO - ✅ Stage 3 cleanup completed for Star Trek Into Darkness
2025-09-15 06:58:26,474 - INFO - Saved encoding metadata for Star Trek Into Darkness
2025-09-15 06:58:26,474 - INFO - Successfully processed Star Trek Into Darkness
2025-09-15 06:58:26,474 - INFO - Processing movie: Top Gun Maverick (2022)
2025-09-15 06:58:26,474 - INFO - Original bitrate for Top Gun Maverick: 75466.7 kbps (75.5 Mbps)
2025-09-15 06:58:26,474 - INFO - 4K content: using default 20.0 Mbps
2025-09-15 06:59:01,344 - INFO - Output path: workspace\4_ready_for_final_mux\4k\Top Gun Maverick (2022)\Top Gun Maverick (2022).encoded.mkv
2025-09-15 06:59:01,344 - INFO - Starting encoding for Top Gun Maverick
2025-09-15 06:59:01,344 - INFO - HandBrake command: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe -i "workspace\3_mkv_cleaned_subtitles_extracted\movies\4k\Top Gun Maverick (2022)\Top Gun Maverick (2022).processed.mkv" -o "workspace\4_ready_for_final_mux\4k\Top Gun Maverick (2022)\Top Gun Maverick (2022).encoded.mkv" -e x265 --encoder-preset slow --encoder-profile main --encoder-level 5.1 -f mkv -b 2000 --multi-pass -T --audio 1 -E aac -B 160 --mixdown stereo --verbose 1
2025-09-15 07:06:05,456 - INFO - Encoding completed successfully for Top Gun Maverick in 7.1 minutes
2025-09-15 07:06:05,456 - INFO - Output file verification passed: 45.7 MB
2025-09-15 07:06:05,458 - INFO - 🧹 Cleaning up after video_encoding completion for Top Gun Maverick (2022)
2025-09-15 07:06:05,458 - INFO - ✅ Cleanup completed for video_encoding
2025-09-15 07:06:05,458 - INFO - ✅ Cleanup completed after video encoding
2025-09-15 07:06:05,458 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\movies\4k\Top Gun Maverick (2022)\_Processed_Audio
2025-09-15 07:06:05,479 - INFO - ✅ Copied largest audio file: Track_1_eng_A_TRUEHD.thd (101.5 MB)
2025-09-15 07:06:05,479 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\movies\4k\Top Gun Maverick (2022)\_Processed_Audio
2025-09-15 07:06:05,480 - INFO -    To: workspace\4_ready_for_final_mux\4k\Top Gun Maverick (2022)
2025-09-15 07:06:05,583 - INFO - 🗑️ Deleted processed MKV file: Top Gun Maverick (2022).processed.mkv
2025-09-15 07:06:05,590 - INFO - 🗑️ Deleted processed audio folder: workspace\3_mkv_cleaned_subtitles_extracted\movies\4k\Top Gun Maverick (2022)\_Processed_Audio
2025-09-15 07:06:05,591 - INFO - ✅ Stage 3 cleanup completed for Top Gun Maverick
2025-09-15 07:06:05,595 - INFO - Saved encoding metadata for Top Gun Maverick
2025-09-15 07:06:05,595 - INFO - Successfully processed Top Gun Maverick
2025-09-15 07:06:05,595 - INFO - Batch processing complete: 5 successful, 0 failed, 0 skipped
2025-09-16 22:13:49,304 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-16 22:13:49,309 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-16 22:13:49,310 - INFO - Filesystem-first video encoder initialized
2025-09-16 22:13:49,310 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-16 22:13:49,310 - INFO - Workspace: .
2025-09-16 22:37:31,071 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-16 22:37:31,071 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-16 22:37:31,072 - INFO - Filesystem-first video encoder initialized
2025-09-16 22:37:31,072 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-16 22:37:31,072 - INFO - Workspace: .
2025-09-16 22:37:33,347 - INFO - Starting filesystem-first TV episode encoding batch processing
2025-09-16 22:37:33,348 - INFO - Discovering TV episodes ready for video encoding...
2025-09-16 22:37:33,348 - INFO - Found 0 episodes ready for encoding
2025-09-16 22:37:33,348 - INFO - No episodes ready for encoding
2025-09-16 22:38:11,888 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-16 22:38:11,888 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-16 22:38:11,888 - INFO - Filesystem-first video encoder initialized
2025-09-16 22:38:11,888 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-16 22:38:11,888 - INFO - Workspace: .
2025-09-16 22:38:13,163 - INFO - Starting filesystem-first TV episode encoding batch processing
2025-09-16 22:38:13,163 - INFO - Discovering TV episodes ready for video encoding...
2025-09-16 22:38:13,164 - INFO - Found 0 episodes ready for encoding
2025-09-16 22:38:13,164 - INFO - No episodes ready for encoding
2025-09-16 23:05:33,394 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-16 23:05:33,395 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-16 23:05:33,396 - INFO - Loaded external tool paths from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
2025-09-16 23:05:33,396 - INFO - Filesystem-first video encoder initialized
2025-09-16 23:05:33,396 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-16 23:05:33,396 - INFO - FFmpeg path: C:\ffmpeg\bin\ffmpeg.exe
2025-09-16 23:05:33,396 - INFO - MKVMerge path: C:\Program Files\MKVToolNix\mkvmerge.exe
2025-09-16 23:05:33,396 - INFO - Workspace: .
2025-09-16 23:05:33,397 - INFO - Discovering movies ready for video encoding...
2025-09-16 23:05:33,423 - INFO - Got actual duration from ffprobe: 5867.9 seconds (97.8 minutes)
2025-09-16 23:05:33,423 - INFO - Found 1 movies ready for encoding
2025-09-16 23:05:33,423 - INFO - Original bitrate for 13 Going on 30: 28499.5 kbps (28.5 Mbps)
2025-09-16 23:05:33,424 - INFO - Compressed to 50.0% of original: 14.2 Mbps
2025-09-16 23:05:38,912 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-16 23:05:38,912 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-16 23:05:38,914 - INFO - Loaded external tool paths from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
2025-09-16 23:05:38,914 - INFO - Filesystem-first video encoder initialized
2025-09-16 23:05:38,914 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-16 23:05:38,914 - INFO - FFmpeg path: C:\ffmpeg\bin\ffmpeg.exe
2025-09-16 23:05:38,914 - INFO - MKVMerge path: C:\Program Files\MKVToolNix\mkvmerge.exe
2025-09-16 23:05:38,914 - INFO - Workspace: .
2025-09-16 23:05:38,914 - INFO - Discovering movies ready for video encoding...
2025-09-16 23:05:38,938 - INFO - Got actual duration from ffprobe: 5867.9 seconds (97.8 minutes)
2025-09-16 23:05:38,939 - INFO - Found 1 movies ready for encoding
2025-09-16 23:05:38,939 - ERROR - Error in queue-based processing: EOF when reading a line
2025-09-16 23:09:57,369 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-16 23:09:57,370 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-16 23:09:57,371 - INFO - Loaded external tool paths from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
2025-09-16 23:09:57,371 - INFO - Filesystem-first video encoder initialized
2025-09-16 23:09:57,371 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-16 23:09:57,371 - INFO - FFmpeg path: C:\ffmpeg\bin\ffmpeg.exe
2025-09-16 23:09:57,371 - INFO - MKVMerge path: C:\Program Files\MKVToolNix\mkvmerge.exe
2025-09-16 23:09:57,371 - INFO - Workspace: .
2025-09-16 23:09:57,371 - INFO - Discovering movies ready for video encoding...
2025-09-16 23:09:57,395 - INFO - Got actual duration from ffprobe: 5867.9 seconds (97.8 minutes)
2025-09-16 23:09:57,395 - INFO - Found 1 movies ready for encoding
2025-09-16 23:09:57,395 - INFO - Original bitrate for 13 Going on 30: 28499.5 kbps (28.5 Mbps)
2025-09-16 23:09:57,395 - INFO - Compressed to 50.0% of original: 14.2 Mbps
2025-09-16 23:10:19,816 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-16 23:10:19,816 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-16 23:10:19,817 - INFO - Loaded external tool paths from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
2025-09-16 23:10:19,818 - INFO - Filesystem-first video encoder initialized
2025-09-16 23:10:19,818 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-16 23:10:19,818 - INFO - FFmpeg path: C:\ffmpeg\bin\ffmpeg.exe
2025-09-16 23:10:19,818 - INFO - MKVMerge path: C:\Program Files\MKVToolNix\mkvmerge.exe
2025-09-16 23:10:19,818 - INFO - Workspace: .
2025-09-16 23:10:19,818 - INFO - Discovering movies ready for video encoding...
2025-09-16 23:10:19,841 - INFO - Got actual duration from ffprobe: 5867.9 seconds (97.8 minutes)
2025-09-16 23:10:19,842 - INFO - Found 1 movies ready for encoding
2025-09-16 23:10:19,842 - ERROR - Error in queue-based processing: EOF when reading a line
2025-09-16 23:13:03,865 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-16 23:13:03,865 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-16 23:13:03,866 - INFO - Loaded external tool paths from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
2025-09-16 23:13:03,866 - INFO - Filesystem-first video encoder initialized
2025-09-16 23:13:03,867 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-16 23:13:03,867 - INFO - FFmpeg path: C:\ffmpeg\bin\ffmpeg.exe
2025-09-16 23:13:03,867 - INFO - MKVMerge path: C:\Program Files\MKVToolNix\mkvmerge.exe
2025-09-16 23:13:03,867 - INFO - Workspace: .
2025-09-16 23:13:03,867 - INFO - Discovering movies ready for video encoding...
2025-09-16 23:13:03,891 - INFO - Got actual duration from ffprobe: 5867.9 seconds (97.8 minutes)
2025-09-16 23:13:03,891 - INFO - Found 1 movies ready for encoding
2025-09-16 23:13:31,910 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-16 23:13:31,911 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-16 23:13:31,912 - INFO - Loaded external tool paths from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
2025-09-16 23:13:31,912 - INFO - Filesystem-first video encoder initialized
2025-09-16 23:13:31,912 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-16 23:13:31,912 - INFO - FFmpeg path: C:\ffmpeg\bin\ffmpeg.exe
2025-09-16 23:13:31,912 - INFO - MKVMerge path: C:\Program Files\MKVToolNix\mkvmerge.exe
2025-09-16 23:13:31,912 - INFO - Workspace: .
2025-09-16 23:13:31,912 - INFO - Discovering movies ready for video encoding...
2025-09-16 23:13:31,936 - INFO - Got actual duration from ffprobe: 5867.9 seconds (97.8 minutes)
2025-09-16 23:13:31,936 - INFO - Found 1 movies ready for encoding
2025-09-16 23:13:59,131 - INFO - Original bitrate for 13 Going on 30: 28499.5 kbps (28.5 Mbps)
2025-09-16 23:13:59,131 - INFO - Compressed to 50.0% of original: 14.2 Mbps
