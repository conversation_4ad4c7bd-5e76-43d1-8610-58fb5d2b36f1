#!/usr/bin/env python3
"""
Quick Pipeline Status - Simplified version for at-a-glance view
"""

import os
from pathlib import Path

def quick_status():
    workspace = Path("workspace")
    
    print("🎬 QUICK PIPELINE STATUS")
    print("=" * 40)
    
    # Count movies and TV shows in each main stage
    stages = [
        ("0_new_requests", "📥 New Requests"),
        ("1_downloading", "⬇️  Downloading"), 
        ("2_downloaded_and_organized", "📁 Downloaded"),
        ("3_mkv_cleaned_subtitles_extracted", "🎬 MKV Processed"),
        ("4_ready_for_final_mux", "🔄 Ready for Mux"),
        ("5_awaiting_poster", "🖼️  Awaiting Poster"),
        ("6_final_plex_ready", "✅ Plex Ready")
    ]
    
    total_in_pipeline = 0
    
    for folder, name in stages:
        count = count_movies_in_stage(workspace / folder)
        if count > 0:
            print(f"{name:<20} : {count:>3} items")
            total_in_pipeline += count
        else:
            print(f"{name:<20} : {count:>3} items")
    
    print("-" * 40)
    
    # Count issues
    issues_path = workspace / "issues_hold"
    total_issues = 0
    
    if issues_path.exists():
        for issue_dir in issues_path.iterdir():
            if issue_dir.is_dir():
                count = count_movies_in_stage(issue_dir)
                if count > 0:
                    print(f"🔴 {issue_dir.name:<17} : {count:>3} items")
                    total_issues += count
    
    print("=" * 40)
    print(f"📊 TOTAL IN PIPELINE: {total_in_pipeline}")
    print(f"⚠️  TOTAL ISSUES: {total_issues}")
    
    if total_issues == 0:
        print("🟢 STATUS: HEALTHY")
    elif total_issues < 10:
        print("🟡 STATUS: MINOR ISSUES")
    else:
        print("🔴 STATUS: NEEDS ATTENTION")
    
    # Pause before closing (so window doesn't disappear immediately)
    print("\n" + "="*40)
    input("Press Enter to exit...")

def count_movies_in_stage(stage_path):
    """Count movies and TV shows in a stage, handling different folder structures"""
    if not stage_path.exists():
        return 0
    
    count = 0
    
    # Special handling for specific stages
    stage_name = stage_path.name
    
    if stage_name == "0_new_movie_requests":
        # Check for new structure first (movies/tv_shows subdirs)
        if (stage_path / "movies").exists() or (stage_path / "tv_shows").exists():
            for content_type in ["movies", "tv_shows"]:
                content_path = stage_path / content_type
                if content_path.exists():
                    for subdir in ["nzb_files_for_download", "nzb_files_launched_archive"]:
                        subpath = content_path / subdir
                        if subpath.exists():
                            count += len([d for d in subpath.iterdir() if d.is_dir()])
        else:
            # Legacy structure - check nzb_files_for_download and nzb_files_launched_archive
            for subdir in ["nzb_files_for_download", "nzb_files_launched_archive"]:
                subpath = stage_path / subdir
                if subpath.exists():
                    count += len([d for d in subpath.iterdir() if d.is_dir()])
        return count
    
    elif stage_name == "1_downloading":
        # Check for new structure first (movies/tv_shows subdirs)
        if (stage_path / "movies").exists() or (stage_path / "tv_shows").exists():
            for content_type in ["movies", "tv_shows"]:
                content_path = stage_path / content_type
                if content_path.exists():
                    for subdir in ["complete_raw", "incomplete"]:
                        subpath = content_path / subdir  
                        if subpath.exists():
                            count += len([d for d in subpath.iterdir() if d.is_dir()])
        else:
            # Legacy structure - check complete_raw and incomplete
            for subdir in ["complete_raw", "incomplete"]:
                subpath = stage_path / subdir  
                if subpath.exists():
                    count += len([d for d in subpath.iterdir() if d.is_dir()])
        return count
    
    # Check for new content type structure (movies/tv_shows subdirectories)
    content_type_dirs = []
    for item in stage_path.iterdir():
        if item.is_dir():
            if item.name in ['movies', 'tv_shows']:
                # New structure: scan content type subdirectories
                for resolution_dir in item.iterdir():
                    if resolution_dir.is_dir() and resolution_dir.name.lower() in ['1080p', '4k', '720p', '2160p']:
                        content_type_dirs.append(resolution_dir)
            elif item.name.lower() in ['1080p', '4k', '720p', '2160p']:
                # Legacy structure: direct resolution directories
                content_type_dirs.append(item)

    if content_type_dirs:
        # Count content in resolution directories
        for resolution_dir in content_type_dirs:
            count += len([d for d in resolution_dir.iterdir() if d.is_dir()])
    else:
        # Direct content folders (fallback)
        count = len([d for d in stage_path.iterdir() if d.is_dir() and not d.name.startswith('.') and d.name not in ['movies', 'tv_shows']])
    
    return count

if __name__ == "__main__":
    quick_status()
