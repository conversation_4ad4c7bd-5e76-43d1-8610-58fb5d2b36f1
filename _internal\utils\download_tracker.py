#!/usr/bin/env python3
"""
Download Tracker for Radarr Queue API Delays and Fallback Workflows

This module provides enhanced tracking capabilities specifically designed to handle:
1. Radarr queue API delays (up to 90+ seconds)
2. Remove/re-add fallback workflows (4K → 1080p)
3. SABnzbd duplicate detection and history verification
4. Real-time grab confirmations via history API

Key improvements:
- Extended verification time for fallback workflows
- History-based grab confirmation 
- Monitoring status verification
- Duplicate detection handling
- Exponential backoff retry logic
"""

import asyncio
import logging
import time
from typing import Optional, Dict, Any, List
from pathlib import Path

from .telemetry_integration import TelemetryIntegrator


class DownloadTracker:
    """
    Enhanced download tracker specifically designed for handling Radarr/Sonarr
    queue delays and fallback download scenarios.
    """
    
    def __init__(self, settings_dict: dict, logger: Optional[logging.Logger] = None):
        """
        Initialize the download tracker.
        
        Args:
            settings_dict: PlexAutomator settings dictionary
            logger: Optional logger instance
        """
        self.settings_dict = settings_dict
        self.logger = logger or logging.getLogger("download_tracker")
        self.telemetry: Optional[TelemetryIntegrator] = None
        self._active_fallback_jobs: Dict[str, dict] = {}
        
    async def __aenter__(self):
        """Async context manager entry."""
        self.telemetry = TelemetryIntegrator(self.settings_dict, self.logger, verbose_mode=False)
        await self.telemetry.__aenter__()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.telemetry:
            await self.telemetry.__aexit__(exc_type, exc_val, exc_tb)
    
    async def track_movie_with_fallback_support(
        self, 
        title: str, 
        radarr_id: int, 
        quality: str,
        is_fallback_workflow: bool = False,
        original_radarr_id: Optional[int] = None,
        fallback_reason: str = "quality_fallback"
    ) -> str:
        """
        Track a movie download with enhanced support for fallback workflows.
        
        Args:
            title: Movie title
            radarr_id: Current Radarr movie ID
            quality: Quality profile name (e.g., "HD-1080p", "Ultra-HD")
            is_fallback_workflow: True if this is a remove/re-add scenario
            original_radarr_id: Original movie ID before remove/re-add
            fallback_reason: Reason for fallback ("4k_unavailable", "quality_fallback", etc.)
            
        Returns:
            job_id: Unique job identifier for tracking
        """
        if not self.telemetry:
            raise RuntimeError("Tracker not initialized - use async context manager")
            
        # First verify the movie is monitored
        is_monitored = await self.telemetry.check_monitoring_status(radarr_id=radarr_id)
        if not is_monitored:
            self.logger.warning(f"⚠️ Movie ID {radarr_id} is not monitored - downloads may not appear in queue")
        
        # Track the download with fallback flags
        job_id = self.telemetry.track_movie_download(
            title=title,
            radarr_id=radarr_id,
            quality=quality,
            is_fallback_workflow=is_fallback_workflow
        )
        
        # Store fallback metadata for enhanced monitoring
        if is_fallback_workflow:
            self._active_fallback_jobs[job_id] = {
                "title": title,
                "radarr_id": radarr_id,
                "original_radarr_id": original_radarr_id,
                "quality": quality,
                "fallback_reason": fallback_reason,
                "started_at": time.time()
            }
            
            workflow_msg = f"🔄 Tracking fallback workflow: {title} → {quality}"
            if original_radarr_id:
                workflow_msg += f" (ID: {original_radarr_id} → {radarr_id})"
            print(workflow_msg)
            
        return job_id
    
    async def verify_grab_with_extended_patience(
        self, 
        job_id: str, 
        max_wait_minutes: int = 3,
        check_interval_seconds: int = 10
    ) -> bool:
        """
        Verify a download grab with extended patience for fallback workflows.
        
        Args:
            job_id: Job ID returned by track_movie_with_fallback_support
            max_wait_minutes: Maximum time to wait for grab verification
            check_interval_seconds: How often to check for updates
            
        Returns:
            True if grab was verified, False if timeout or failed
        """
        if not self.telemetry:
            raise RuntimeError("Tracker not initialized")
            
        max_wait_sec = max_wait_minutes * 60
        start_time = time.time()
        last_status_msg = ""
        
        self.logger.info(f"🔍 Verifying grab for job {job_id} (max wait: {max_wait_minutes} minutes)")
        
        while time.time() - start_time < max_wait_sec:
            # Check if it's verified as downloading
            is_verified = await self.telemetry.verify_grab_by_id(job_id, max_wait_sec=check_interval_seconds)
            
            if is_verified:
                elapsed = time.time() - start_time
                print(f"✅ Grab verified after {elapsed:.1f} seconds: {job_id}")
                
                # Clean up fallback metadata
                if job_id in self._active_fallback_jobs:
                    del self._active_fallback_jobs[job_id]
                    
                return True
            
            # Provide progress updates
            elapsed = time.time() - start_time
            remaining = max_wait_sec - elapsed
            status_msg = f"🔎 Still verifying grab... ({elapsed:.0f}s elapsed, {remaining:.0f}s remaining)"
            
            if status_msg != last_status_msg:
                print(status_msg)
                last_status_msg = status_msg
            
            await asyncio.sleep(check_interval_seconds)
        
        # Timeout reached
        self.logger.warning(f"⏰ Grab verification timeout for job {job_id} after {max_wait_minutes} minutes")
        
        # Clean up fallback metadata
        if job_id in self._active_fallback_jobs:
            fallback_info = self._active_fallback_jobs[job_id]
            self.logger.error(f"❌ Fallback workflow failed: {fallback_info['title']} → {fallback_info['quality']}")
            del self._active_fallback_jobs[job_id]
            
        return False
    
    async def monitor_until_completion(
        self, 
        interval_seconds: int = 10,
        show_progress: bool = True
    ) -> bool:
        """
        Monitor all tracked downloads until completion with progress updates.
        
        Args:
            interval_seconds: How often to check for updates
            show_progress: Whether to show progress updates
            
        Returns:
            True if all downloads completed successfully
        """
        if not self.telemetry:
            raise RuntimeError("Tracker not initialized")
            
        if self.telemetry.get_active_download_count() == 0:
            print("✅ No active downloads to monitor")
            return True
            
        print(f"📊 Monitoring {self.telemetry.get_active_download_count()} active downloads...")
        
        # Set up webhook recommendation
        self.telemetry.add_webhook_support_note()
        
        # Monitor downloads
        success = await self.telemetry.monitor_downloads(interval=interval_seconds)
        
        if success:
            print("🎉 All downloads completed successfully!")
        else:
            print("⚠️ Some downloads may not have completed successfully - check logs")
            
        return success
    
    def get_fallback_status_summary(self) -> Dict[str, Any]:
        """
        Get a summary of active fallback workflows.
        
        Returns:
            Dictionary with fallback workflow status information
        """
        summary = {
            "active_fallback_count": len(self._active_fallback_jobs),
            "fallback_jobs": []
        }
        
        for job_id, job_info in self._active_fallback_jobs.items():
            elapsed = time.time() - job_info["started_at"]
            summary["fallback_jobs"].append({
                "job_id": job_id,
                "title": job_info["title"],
                "quality": job_info["quality"],
                "radarr_id": job_info["radarr_id"],
                "original_radarr_id": job_info.get("original_radarr_id"),
                "reason": job_info["fallback_reason"],
                "elapsed_seconds": elapsed
            })
            
        return summary
    
    async def handle_monitoring_status_issues(self, radarr_id: int, title: str) -> bool:
        """
        Check and potentially fix monitoring status issues.
        
        Args:
            radarr_id: Radarr movie ID to check
            title: Movie title for logging
            
        Returns:
            True if monitoring status is OK, False if there are issues
        """
        if not self.telemetry:
            return False
            
        is_monitored = await self.telemetry.check_monitoring_status(radarr_id=radarr_id)
        
        if not is_monitored:
            self.logger.warning(f"⚠️ Movie '{title}' (ID: {radarr_id}) is not monitored")
            self.logger.warning("   → This may cause downloads to not appear in Radarr's queue")
            self.logger.warning("   → Consider enabling monitoring in Radarr for this movie")
            return False
            
        return True
    
    def log_best_practices_reminder(self):
        """Log best practices for enhanced telemetry with fallback workflows."""
        best_practices = """
📋 ENHANCED TELEMETRY BEST PRACTICES:

1. MONITORING STATUS: Always ensure movies/series are monitored in *arr
   → Unmonitored items won't appear in queue/activity views

2. QUALITY PROFILES: Consider using combined profiles (4K + 1080p) instead of 
   remove/re-add workflows to avoid ID changes and timing issues

3. API DELAYS: Radarr/Sonarr queue refresh runs ~every 60-90 seconds
   → Immediate verification may fail, but grab can still succeed

4. WEBHOOKS: Set up webhooks for instant grab notifications
   → Eliminates polling delays and provides real-time status

5. DUPLICATE DETECTION: SABnzbd may immediately fail duplicate downloads
   → Check history if downloads don't appear in active queue

6. FALLBACK WORKFLOWS: Allow extra time for remove/re-add scenarios
   → Extended verification attempts prevent false negatives
        """
        self.logger.info(best_practices.strip())


# Convenience functions for integration into existing scripts
async def track_movie(
    settings_dict: dict,
    title: str,
    radarr_id: int,
    quality: str,
    is_fallback: bool = False,
    original_id: Optional[int] = None,
    logger: Optional[logging.Logger] = None
) -> bool:
    """
    Convenience function to track a single movie with enhanced monitoring.
    
    Returns True if grab was verified, False otherwise.
    """
    async with DownloadTracker(settings_dict, logger) as tracker:
        # Track the download
        job_id = await tracker.track_movie_with_fallback_support(
            title=title,
            radarr_id=radarr_id,
            quality=quality,
            is_fallback_workflow=is_fallback,
            original_radarr_id=original_id
        )
        
        # Verify grab with extended patience
        max_wait = 3 if is_fallback else 2  # Extra time for fallback workflows
        return await tracker.verify_grab_with_extended_patience(job_id, max_wait_minutes=max_wait)


async def monitor_active_downloads(
    settings_dict: dict,
    logger: Optional[logging.Logger] = None
) -> bool:
    """
    Convenience function to monitor all active downloads until completion.
    
    Returns True if all completed successfully, False otherwise.
    """
    async with DownloadTracker(settings_dict, logger) as tracker:
        return await tracker.monitor_until_completion()


# Integration example for 01_intake_and_nzb_search.py
async def example_fallback_integration():
    """
    Example showing how to integrate robust tracking into fallback workflows.
    """
    settings_dict = {}  # From actual settings
    logger = logging.getLogger("intake")
    
    async with DownloadTracker(settings_dict, logger) as tracker:
        # Track initial 4K attempt
        initial_job = await tracker.track_movie_with_fallback_support(
            title="Example Movie (2023)",
            radarr_id=123,
            quality="Ultra-HD",
            is_fallback_workflow=False
        )
        
        # Wait a reasonable time for 4K
        initial_success = await tracker.verify_grab_with_extended_patience(
            initial_job, max_wait_minutes=2
        )
        
        if not initial_success:
            print("🔄 4K not found, initiating 1080p fallback...")
            
            # Simulate remove/re-add workflow (new ID)
            fallback_job = await tracker.track_movie_with_fallback_support(
                title="Example Movie (2023)",
                radarr_id=124,  # New ID after re-add
                quality="HD-1080p",
                is_fallback_workflow=True,
                original_radarr_id=123,
                fallback_reason="4k_unavailable"
            )
            
            # Allow extra time for fallback verification
            fallback_success = await tracker.verify_grab_with_extended_patience(
                fallback_job, max_wait_minutes=3
            )
            
            if not fallback_success:
                logger.error("❌ Both 4K and 1080p fallback failed")
                return False
        
        # Monitor all downloads to completion
        return await tracker.monitor_until_completion()


if __name__ == "__main__":
    # Quick test
    logging.basicConfig(level=logging.INFO)
    asyncio.run(example_fallback_integration())
