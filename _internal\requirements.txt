# PlexMovieAutomator/requirements.txt - Minimal Dependencies for Subtitle Edit Integration

# Essential dependencies for subtitle processing with Subtitle Edit
pysrt>=1.1.2                # SRT subtitle file generation and parsing
Pillow>=10.0.0              # Basic image processing (for poster optimization only)

# UI automation dependencies for Subtitle Edit integration (fallback)
pyautogui>=0.9.54           # UI automation for clicking Start OCR button
pywin32>=306                # Windows API access for window management

# Direct Ollama Vision integration
aiohttp>=3.8.0              # Async HTTP client for Ollama API calls

# Note: OCR processing is now handled by Subtitle Edit with Ollama Vision
# No more heavy dependencies like torch, onnxruntime-gpu, opencv-python needed!