#!/usr/bin/env python3
from __future__ import annotations

import json
from pathlib import Path
from typing import Any, Dict, Iterable, Optional

from _internal.src.event_queue import EventQueue, get_event_queue


class EventInboxReader:
    """
    Minimal reader for SAB post-process events from the EventQueue JSONL file.
    Filters entries of type 'sab.postprocess'.
    Maintains a cursor file to avoid re-processing the same lines.
    """
    def __init__(self, eq: Optional[EventQueue] = None, cursor_dir: Optional[Path] = None):
        self.eq = eq or get_event_queue({'EventQueue': {'enabled': True}})
        self.events_path: Path = Path(getattr(self.eq, 'file_path', '_internal/state/event_queue/events.jsonl'))
        self.cursor_dir = cursor_dir or Path('_internal/state/event_queue')
        self.cursor_dir.mkdir(parents=True, exist_ok=True)
        self.cursor_path = self.cursor_dir / 'sab_inbox.cursor'

    def _read_cursor(self) -> int:
        try:
            return int(self.cursor_path.read_text().strip())
        except Exception:
            return 0

    def _write_cursor(self, pos: int) -> None:
        try:
            self.cursor_path.write_text(str(pos))
        except Exception:
            pass

    def iter_new_events(self) -> Iterable[Dict[str, Any]]:
        if not self.events_path.exists():
            return []
        pos = self._read_cursor()
        with self.events_path.open('r', encoding='utf-8') as f:
            f.seek(pos)
            while True:
                line = f.readline()
                if not line:
                    break
                try:
                    evt = json.loads(line)
                except Exception:
                    continue
                if evt.get('type') == 'sab.postprocess':
                    yield evt
            self._write_cursor(f.tell())

