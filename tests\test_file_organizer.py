import asyncio
import json
import tempfile
from pathlib import Path
import unittest

from _internal.src.file_organizer import organize_movie


class DummyLogger:
    def __getattr__(self, name):
        def _log(*args, **kwargs):
            pass
        return _log


class TestFileOrganizer(unittest.TestCase):
    def test_organize_movie_happy_path(self):
        logger = DummyLogger()
        with tempfile.TemporaryDirectory() as tmp:
            tmp_path = Path(tmp)
            # simulate organized base
            organized_base = tmp_path / "workspace" / "2_downloaded_and_organized"
            organized_base.mkdir(parents=True)

            # simulate download dir and file
            download_dir = tmp_path / "workspace" / "1_downloading" / "complete_raw" / "Some.Movie.2020.1080p"
            download_dir.mkdir(parents=True)
            main_file = download_dir / "Some.Movie.2020.1080p.mkv"
            main_file.write_bytes(b"data")

            # inputs
            content_info = {"title": "Some.Movie.2020.1080p.BluRay", "year": 2020}
            resolution = "1080p"

            # run
            ok = asyncio.run(organize_movie(content_info, str(main_file), download_dir, organized_base, resolution, logger))
            self.assertTrue(ok)

            # assert file moved
            dest_dir = organized_base / "movies" / "1080p" / "Some Movie (2020)"
            dest_file = dest_dir / "Some Movie (2020).mkv"
            self.assertTrue(dest_file.exists())
            self.assertFalse(main_file.exists())

            # assert marker
            marker = dest_dir / ".organized"
            self.assertTrue(marker.exists())
            data = json.loads(marker.read_text())
            self.assertEqual(data.get("status"), "organized")
            self.assertEqual(data.get("movie_year"), 2020)


if __name__ == "__main__":
    unittest.main()

