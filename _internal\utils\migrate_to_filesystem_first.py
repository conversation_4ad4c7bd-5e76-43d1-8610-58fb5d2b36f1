#!/usr/bin/env python3
"""
Migration script to implement the filesystem-first state management architecture.

This script:
1. Backs up the current SQLite database
2. Migrates metadata to the new metadata-only database
3. Creates marker files based on current movie states
4. Updates all references to use the new filesystem-first manager
"""

import json
import logging
import shutil
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Any

# Import both old and new state managers
from .robust_state_manager import SQLiteStateManager
from .filesystem_first_state_manager import FilesystemFirstStateManager, MetadataOnlyDatabase

logger = logging.getLogger(__name__)


def migrate_to_filesystem_first_architecture(workspace_root: Optional[Path] = None, backup_existing: bool = True):
    """
    Migrate from the current SQLite-based system to filesystem-first architecture.
    
    Args:
        workspace_root: Root directory of the workspace
        backup_existing: Whether to backup existing database before migration
    """
    if workspace_root is None:
        workspace_root = Path.cwd()
    
    workspace_root = Path(workspace_root)
    
    logger.info("🔄 Starting migration to filesystem-first state management architecture")
    
    # Initialize managers
    old_sqlite_manager = SQLiteStateManager(workspace_root)
    filesystem_manager = FilesystemFirstStateManager(workspace_root)
    metadata_db = MetadataOnlyDatabase(workspace_root)
    
    try:
        # Step 1: Backup existing database
        if backup_existing:
            _backup_existing_database(workspace_root)
        
        # Step 2: Get all movies from current system
        logger.info("Reading existing movie data...")
        existing_movies = old_sqlite_manager.get_all_movies()
        logger.info(f"Found {len(existing_movies)} movies in current system")
        
        # Step 3: Migrate metadata to new database
        logger.info("Migrating metadata to new database...")
        metadata_migrated = 0
        for movie in existing_movies:
            success = _migrate_movie_metadata(movie, metadata_db)
            if success:
                metadata_migrated += 1
        
        logger.info(f"Migrated metadata for {metadata_migrated} movies")
        
        # Step 4: Create marker files based on current states
        logger.info("Creating marker files based on current states...")
        markers_created = 0
        for movie in existing_movies:
            success = _create_markers_for_movie(movie, filesystem_manager, workspace_root)
            if success:
                markers_created += 1
        
        logger.info(f"Created markers for {markers_created} movies")
        
        # Step 5: Validate new system
        logger.info("Validating new filesystem-first system...")
        validation_results = _validate_migration(filesystem_manager, metadata_db, existing_movies)
        
        # Step 6: Create migration report
        _create_migration_report(workspace_root, {
            'total_movies': len(existing_movies),
            'metadata_migrated': metadata_migrated,
            'markers_created': markers_created,
            'validation_results': validation_results,
            'migration_timestamp': datetime.now().isoformat()
        })
        
        logger.info("✅ Migration to filesystem-first architecture completed successfully!")
        logger.info(f"   - Migrated {metadata_migrated} movie metadata records")
        logger.info(f"   - Created {markers_created} marker file sets")
        logger.info(f"   - System is now using filesystem as single source of truth")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Migration failed: {e}")
        return False
    
    finally:
        # Clean up connections
        try:
            old_sqlite_manager.close()
            metadata_db.close()
        except:
            pass


def _backup_existing_database(workspace_root: Path):
    """Backup the existing SQLite database."""
    db_path = workspace_root / "_internal" / "data" / "pipeline_state.db"
    
    if db_path.exists():
        backup_dir = workspace_root / "_internal" / "backups" / "filesystem_migration"
        backup_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = backup_dir / f"pipeline_state_backup_{timestamp}.db"
        
        shutil.copy2(db_path, backup_path)
        logger.info(f"Backed up existing database to: {backup_path}")


def _migrate_movie_metadata(movie: Dict[str, Any], metadata_db: MetadataOnlyDatabase) -> bool:
    """Migrate a single movie's metadata to the new database."""
    try:
        unique_id = movie.get('unique_id')
        if not unique_id:
            logger.warning(f"Movie missing unique_id, skipping: {movie.get('title')}")
            return False
        
        # Extract metadata from the old format
        metadata = movie.get('metadata', {})
        
        # Save to new metadata-only database
        success = metadata_db.save_movie_metadata(
            unique_id=unique_id,
            title=movie.get('title', 'Unknown'),
            year=movie.get('year'),
            tmdb_id=movie.get('tmdb_id'),
            imdb_id=movie.get('imdb_id'),
            audio_lang=metadata.get('preferred_audio_language', 'eng'),
            subtitle_lang=metadata.get('preferred_subtitle_language', 'eng'),
            keep_commentary=metadata.get('keep_commentary', False),
            metadata={
                'migration_timestamp': datetime.now().isoformat(),
                'original_metadata': metadata,
                'processing_metrics': movie.get('processing_metrics', {}),
                'quality_metrics': metadata.get('quality_metrics', {})
            }
        )
        
        if success:
            logger.debug(f"Migrated metadata for: {unique_id}")
        
        return success
        
    except Exception as e:
        logger.error(f"Failed to migrate metadata for movie: {e}")
        return False


def _create_markers_for_movie(movie: Dict[str, Any], filesystem_manager: FilesystemFirstStateManager, 
                             workspace_root: Path) -> bool:
    """Create marker files for a movie based on its current status."""
    try:
        unique_id = movie.get('unique_id')
        status = movie.get('status', 'unknown')
        
        if not unique_id:
            return False
        
        # Find the movie's current directory
        movie_dir = _find_movie_directory(movie, workspace_root)
        if not movie_dir or not movie_dir.exists():
            logger.warning(f"Could not find directory for movie: {unique_id}")
            return False
        
        # Create markers based on status
        marker_data = {
            'migrated_from_status': status,
            'migration_timestamp': datetime.now().isoformat(),
            'original_paths': movie.get('paths', {})
        }
        
        # Map old statuses to new marker files
        if status == 'organized':
            filesystem_manager.set_stage_marker(movie_dir, 'organized', marker_data)
        
        elif status in ['mkv_processing_pending', 'mkv_processing_active']:
            filesystem_manager.set_stage_marker(movie_dir, 'organized', marker_data)
            if status == 'mkv_processing_active':
                filesystem_manager.set_stage_marker(movie_dir, 'mkv_processing', marker_data)
        
        elif status in ['mkv_processing_completed', 'subtitle_processing_pending']:
            filesystem_manager.set_stage_marker(movie_dir, 'organized', marker_data)
            filesystem_manager.set_stage_marker(movie_dir, 'mkv_complete', marker_data)
        
        elif status in ['subtitle_processing_active']:
            filesystem_manager.set_stage_marker(movie_dir, 'organized', marker_data)
            filesystem_manager.set_stage_marker(movie_dir, 'mkv_complete', marker_data)
            filesystem_manager.set_stage_marker(movie_dir, 'subtitle_processing', marker_data)
        
        elif status in ['subtitle_processing_completed', 'final_mux_pending']:
            filesystem_manager.set_stage_marker(movie_dir, 'organized', marker_data)
            filesystem_manager.set_stage_marker(movie_dir, 'mkv_complete', marker_data)
            filesystem_manager.set_stage_marker(movie_dir, 'subtitle_complete', marker_data)
        
        elif status == 'completed':
            filesystem_manager.set_stage_marker(movie_dir, 'organized', marker_data)
            filesystem_manager.set_stage_marker(movie_dir, 'mkv_complete', marker_data)
            filesystem_manager.set_stage_marker(movie_dir, 'subtitle_complete', marker_data)
            filesystem_manager.set_stage_marker(movie_dir, 'final_mux_complete', marker_data)
        
        elif 'error' in status:
            filesystem_manager.set_stage_marker(movie_dir, 'error', {
                **marker_data,
                'error_status': status,
                'error_message': movie.get('error_message', 'Unknown error')
            })
        
        logger.debug(f"Created markers for: {unique_id} (status: {status})")
        return True
        
    except Exception as e:
        logger.error(f"Failed to create markers for movie: {e}")
        return False


def _find_movie_directory(movie: Dict[str, Any], workspace_root: Path) -> Optional[Path]:
    """Find a movie's directory based on its paths and unique_id."""
    unique_id = movie.get('unique_id', '')
    paths = movie.get('paths', {})
    
    # Try various path sources
    path_candidates = [
        paths.get('organized_mkv_path'),
        paths.get('final_output_path'),
        paths.get('download_path'),
        movie.get('file_path')
    ]
    
    for path_str in path_candidates:
        if path_str:
            path = Path(path_str)
            if path.exists():
                # If it's a file, get the parent directory
                if path.is_file():
                    return path.parent
                elif path.is_dir():
                    return path
    
    # Try to find by searching workspace directories
    workspace_dirs = [
        workspace_root / "workspace" / "2_downloaded_and_organized",
        workspace_root / "workspace" / "3_mkv_cleaned_subtitles_extracted",
        workspace_root / "workspace" / "4_ready_for_final_mux",
        workspace_root / "workspace" / "issues_hold"
    ]
    
    title = movie.get('title', '')
    year = movie.get('year', '')
    search_pattern = f"{title} ({year})" if year else title
    
    for workspace_dir in workspace_dirs:
        if workspace_dir.exists():
            # Check direct match
            direct_match = workspace_dir / search_pattern
            if direct_match.exists():
                return direct_match
            
            # Check in resolution subfolders
            for item in workspace_dir.iterdir():
                if item.is_dir():
                    subfolder_match = item / search_pattern
                    if subfolder_match.exists():
                        return subfolder_match
            
            # Fuzzy search
            for item in workspace_dir.rglob('*'):
                if item.is_dir() and search_pattern.lower() in item.name.lower():
                    return item
    
    return None


def _validate_migration(filesystem_manager: FilesystemFirstStateManager, 
                       metadata_db: MetadataOnlyDatabase, 
                       original_movies: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Validate that the migration was successful."""
    results = {
        'filesystem_discovery_count': 0,
        'metadata_count': 0,
        'missing_movies': [],
        'extra_movies': [],
        'marker_validation': {'passed': 0, 'failed': 0}
    }
    
    try:
        # Test filesystem discovery
        discovered_movies = filesystem_manager.discover_movies_by_stage()
        total_discovered = sum(len(movies) for movies in discovered_movies.values())
        results['filesystem_discovery_count'] = total_discovered
        
        # Test metadata database
        metadata_movies = metadata_db.get_all_movies()
        results['metadata_count'] = len(metadata_movies)
        
        # Check for missing or extra movies
        original_ids = {movie.get('unique_id') for movie in original_movies if movie.get('unique_id')}
        migrated_ids = {movie.get('unique_id') for movie in metadata_movies if movie.get('unique_id')}
        
        results['missing_movies'] = list(original_ids - migrated_ids)
        results['extra_movies'] = list(migrated_ids - original_ids)
        
        logger.info(f"Validation: Discovered {total_discovered} movies via filesystem")
        logger.info(f"Validation: Found {len(metadata_movies)} movies in metadata DB")
        
        if results['missing_movies']:
            logger.warning(f"Missing movies after migration: {results['missing_movies']}")
        
        if results['extra_movies']:
            logger.warning(f"Extra movies after migration: {results['extra_movies']}")
        
    except Exception as e:
        logger.error(f"Validation failed: {e}")
        results['validation_error'] = str(e)
    
    return results


def _create_migration_report(workspace_root: Path, migration_data: Dict[str, Any]):
    """Create a detailed migration report."""
    report_dir = workspace_root / "_internal" / "reports"
    report_dir.mkdir(parents=True, exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_path = report_dir / f"filesystem_first_migration_report_{timestamp}.json"
    
    report = {
        "migration_summary": {
            "timestamp": migration_data['migration_timestamp'],
            "status": "completed",
            "total_movies_processed": migration_data['total_movies'],
            "metadata_records_migrated": migration_data['metadata_migrated'],
            "marker_files_created": migration_data['markers_created']
        },
        "validation_results": migration_data['validation_results'],
        "architecture_changes": {
            "old_system": "SQLite database with status and paths",
            "new_system": "Filesystem-first with metadata-only database",
            "key_improvements": [
                "Filesystem is single source of truth",
                "Marker files track processing state",
                "Database stores only static metadata",
                "Idempotent operations enable safe retries",
                "Auto-recovery from interruptions"
            ]
        },
        "next_steps": [
            "Verify all pipeline scripts use new filesystem scanning",
            "Test marker file creation and cleanup",
            "Validate auto-recovery functionality",
            "Update documentation and monitoring"
        ]
    }
    
    with open(report_path, 'w') as f:
        json.dump(report, f, indent=2)
    
    logger.info(f"Migration report created: {report_path}")


if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Run migration
    success = migrate_to_filesystem_first_architecture()
    
    if success:
        print("\n✅ Migration completed successfully!")
        print("The system now uses filesystem-first state management.")
        print("Check the migration report for detailed results.")
    else:
        print("\n❌ Migration failed!")
        print("Check the logs for error details.")
