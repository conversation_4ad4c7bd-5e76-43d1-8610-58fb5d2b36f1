#!/usr/bin/env python3
from __future__ import annotations

from typing import Any, Dict, Optional, List
from pathlib import Path
import importlib.util

from _internal.utils.common_helpers import get_setting
from _internal.src.sonarr_integration import SonarrClient
from _internal.src.event_queue import get_event_queue


def _load_stage01_module():
    root = Path(__file__).resolve().parents[2]
    stage1_path = root / "01_intake_and_nzb_search.py"
    spec = importlib.util.spec_from_file_location("stage01", stage1_path)
    if spec and spec.loader:
        mod = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(mod)  # type: ignore[attr-defined]
        return mod
    raise ImportError("Unable to load Stage 01 script")


async def add_tv_show(tv_data: Dict[str, Any], settings: Dict[str, Any], logger) -> Dict[str, Any]:
    sonarr_key = get_setting("Sonarr", "api_key", expected_type=str, settings_dict=settings)
    if not sonarr_key:
        return {"success": False, "reason": "missing_api_key"}

    stage01 = _load_stage01_module()
    result = await stage01._add_tv_show_to_sonarr_modern(tv_data, settings, logger)
    return result


async def configure_specific_episode(session, sonarr_url: str, api_key: str, series_id: int,
                                     season_num: int, episode_num: int, logger,
                                     settings: Optional[Dict[str, Any]] = None,
                                     disable_immediate_search: bool = False) -> bool:
    """
    Modular version of Stage 01 specific-episode monitoring + search trigger.
    Uses SonarrClient for I/O.
    """
    headers = {"X-Api-Key": api_key}
    sonarr = SonarrClient(sonarr_url, api_key)

    # 1) Fetch series and set only target season monitored
    series_data = await sonarr.get_series_by_id(session, series_id)
    if not series_data:
        logger.error(f"Failed to get series details for ID {series_id}")
        return False

    series_data["seasons"] = [{**s, "monitored": (s.get("seasonNumber") == season_num)} for s in series_data.get("seasons", [])]

    ok = await sonarr.update_series(session, series_data)
    if not ok:
        logger.error(f"Failed to update series monitoring for ID {series_id}")
        return False

    # 2) Fetch episodes with small retry until the target is visible
    import asyncio
    episodes: List[Dict[str, Any]] = []
    for attempt in range(1, 4):
        episodes = await sonarr.get_episodes_by_series(session, series_id)
        if any(ep.get("seasonNumber") == season_num and ep.get("episodeNumber") == episode_num for ep in episodes):
            break
        logger.info(f"Episode S{season_num:02d}E{episode_num:02d} not found attempt {attempt}; waiting 2s and retrying")
        await asyncio.sleep(2)

    # 3) Set only the target episode monitored
    target_episode = None
    for ep in episodes:
        if ep.get("seasonNumber") == season_num and ep.get("episodeNumber") == episode_num:
            target_episode = ep
            break

    if not target_episode:
        logger.warning(f"Target episode S{season_num:02d}E{episode_num:02d} not found; cannot monitor")
        return False

    await sonarr.set_episodes_monitor_state(session, [e.get("id") for e in episodes if e.get("seasonNumber") == season_num], False)
    ok = await sonarr.set_episodes_monitor_state(session, [target_episode["id"]], True)
    if not ok:
        return False

    # 4) Trigger EpisodeSearch
    if not disable_immediate_search:
        cmd = await sonarr.issue_command(session, {"name": "EpisodeSearch", "episodeIds": [target_episode["id"]]})
        if cmd and isinstance(cmd, dict) and cmd.get("name"):
            logger.info(f"   🔍 Triggered automatic search for S{season_num:02d}E{episode_num:02d}")
            await get_event_queue().publish("sonarr.episode_search.triggered", {
                "series_id": series_id,
                "season": season_num,
                "episode": episode_num,
                "episode_id": target_episode["id"],
            })
        else:
            logger.warning("   ⚠️ Failed to trigger EpisodeSearch via client")
            await get_event_queue().publish("sonarr.episode_search.failed", {
                "series_id": series_id,
                "season": season_num,
                "episode": episode_num,
                "episode_id": target_episode.get("id"),
            })
    return True


async def configure_multiple_episodes(session, sonarr_url: str, api_key: str, series_id: int,
                                      episodes_list: list, logger, settings: Optional[Dict[str, Any]] = None,
                                      disable_immediate_search: bool = False) -> bool:
    """
    Configure Sonarr to monitor only a given set of episodes and optionally trigger searches.
    Mirrors Stage 01 behavior but through SonarrClient.
    """
    import asyncio
    sonarr = SonarrClient(sonarr_url, api_key)

    logger.info(f"   📺 Configuring monitoring for {len(episodes_list)} specific episodes")

    # Wait for episodes to populate
    max_retries = 15
    retry_delay = 4
    all_episodes: List[Dict[str, Any]] = []
    for attempt in range(max_retries):
        logger.info(f"   🔄 Attempt {attempt + 1}/{max_retries}: Fetching episodes for series {series_id}")
        eps = await sonarr.get_episodes_by_series(session, series_id)
        all_episodes = eps or []
        if all_episodes:
            break
        if attempt < max_retries - 1:
            logger.info(f"   ⏳ Episodes not yet populated, waiting {retry_delay}s (attempt {attempt + 1}/{max_retries})")
            await asyncio.sleep(retry_delay)
            retry_delay = int(retry_delay * 1.5)
    if not all_episodes:
        logger.error("No episodes found after retries")
        return False

    target_episodes = {(ep['season'], ep['episode']) for ep in episodes_list}
    episodes_to_monitor = []
    season_to_ids: Dict[int, List[int]] = {}
    for episode in all_episodes:
        s = episode.get("seasonNumber")
        e = episode.get("episodeNumber")
        if (s, e) in target_episodes:
            episodes_to_monitor.append(episode)
            season_to_ids.setdefault(s, []).append(episode.get("id"))
            logger.info(f"      ✓ Will monitor S{s:02d}E{e:02d}")
    if not episodes_to_monitor:
        logger.warning("None of the requested episodes were found in the series")
        return False

    # Ensure series itself is monitored
    _ = await sonarr.update_series(session, {"id": series_id, "monitored": True, "seasonFolder": True})

    # For each season involved, unmonitor all then re-monitor only the targets
    episodes_by_season = {}
    for ep in all_episodes:
        episodes_by_season.setdefault(ep.get("seasonNumber"), []).append(ep)

    for season_num, eps in episodes_by_season.items():
        # Unmonitor whole season first
        await sonarr.set_episodes_monitor_state(session, [e.get("id") for e in eps], False)
        # Re-monitor only targets in this season
        target_ids = [e.get("id") for e in eps if e.get("id") in set(season_to_ids.get(season_num, []))]
        if target_ids:
            await sonarr.set_episodes_monitor_state(session, target_ids, True)

    logger.info(f"   ✅ Episode monitoring configuration complete")

    if not disable_immediate_search:
        for ep in episodes_to_monitor:
            season_num = ep.get("seasonNumber")
            episode_num = ep.get("episodeNumber")
            cmd = await sonarr.issue_command(session, {"name": "EpisodeSearch", "episodeIds": [ep["id"]]})
            if cmd and isinstance(cmd, dict) and cmd.get("name"):
                logger.info(f"   🔍 Triggered search for S{season_num:02d}E{episode_num:02d}")
                await get_event_queue().publish("sonarr.episode_search.triggered", {
                    "series_id": series_id,
                    "season": season_num,
                    "episode": episode_num,
                    "episode_id": ep["id"],
                })
            else:
                logger.warning(f"Failed to trigger search for S{season_num:02d}E{episode_num:02d}")
                await get_event_queue().publish("sonarr.episode_search.failed", {
                    "series_id": series_id,
                    "season": season_num,
                    "episode": episode_num,
                    "episode_id": ep.get("id"),
                })
    else:
        logger.info(f"   ⏭️ Skipping immediate search triggers for {len(episodes_to_monitor)} episodes")

    return True



async def configure_specific_season(session, sonarr_url: str, api_key: str, series_id: int,
                                    season_num: int, logger, disable_immediate_search: bool = False) -> bool:
    """
    Configure Sonarr to monitor only a specific season and optionally trigger episode searches.
    Mirrors Stage 01 behavior and uses SonarrClient.
    """
    sonarr = SonarrClient(sonarr_url, api_key)

    # Get series
    series_data = await sonarr.get_series_by_id(session, series_id)
    if not series_data:
        logger.error(f"Failed to get series details for ID {series_id}")
        return False

    # Monitor only target season
    for season in series_data.get("seasons", []):
        season["monitored"] = (season.get("seasonNumber") == season_num)

    ok = await sonarr.update_series(session, series_data)
    if not ok:
        return False

    logger.info(f"   🎯 Configured monitoring for Season {season_num}")

    # Get episodes and trigger searches to populate release cache
    episodes = await sonarr.get_episodes_by_series(session, series_id)
    season_episodes = [ep for ep in (episodes or []) if ep.get("seasonNumber") == season_num]

    if season_episodes:
        if not disable_immediate_search:
            logger.info(f"   🔍 Triggering searches for {len(season_episodes)} episodes to populate release cache")
            for ep in season_episodes:
                ep_id = ep.get("id")
                ep_num = ep.get("episodeNumber")
                if ep_id:
                    payload = {"name": "EpisodeSearch", "episodeIds": [ep_id]}
                    try:
                        cmd = await sonarr.issue_command(session, payload)
                        if cmd and isinstance(cmd, dict) and cmd.get('id'):
                            logger.info(f"      ✓ Triggered search for S{season_num:02d}E{ep_num:02d}")
                            await get_event_queue().publish("sonarr.episode_search.triggered", {
                                "series_id": series_id,
                                "season": season_num,
                                "episode": ep_num,
                                "episode_id": ep_id,
                            })
                        else:
                            logger.warning(f"      ⚠️ Failed to trigger search for S{season_num:02d}E{ep_num:02d}")
                            await get_event_queue().publish("sonarr.episode_search.failed", {
                                "series_id": series_id,
                                "season": season_num,
                                "episode": ep_num,
                                "episode_id": ep_id,
                            })
                    except Exception as err:
                        logger.warning(f"      ⚠️ Search trigger error for S{season_num:02d}E{ep_num:02d}: {err}")
                        await get_event_queue().publish("sonarr.episode_search.error", {
                            "series_id": series_id,
                            "season": season_num,
                            "episode": ep_num,
                            "error": str(err),
                        })
            import asyncio
            await asyncio.sleep(2)
            logger.info("   ✅ Episode searches triggered - preflight analyzer can now access release cache")
        else:
            logger.info("   ⏭️ Skipping immediate search triggers (preflight will handle searches)")
    else:
        logger.warning(f"   ⚠️ No episodes found for Season {season_num}")

    return True
