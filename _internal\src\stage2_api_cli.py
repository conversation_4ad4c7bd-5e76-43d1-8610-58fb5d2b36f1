#!/usr/bin/env python3
from __future__ import annotations

import argparse
import asyncio
from pathlib import Path

from _internal.src.stage2_api import run_inbox_processing
from _internal.utils.common_helpers import load_settings, get_setting


class Logger:
    def info(self, *a, **k): print(*a)
    def warning(self, *a, **k): print(*a)
    def error(self, *a, **k): print(*a)


def main():
    parser = argparse.ArgumentParser(description='Run Stage 2 inbox processing over EventQueue events')
    parser.add_argument('--events', '-e', help='EventQueue directory containing events.jsonl')
    parser.add_argument('--settings', '-s', help='Path to settings.ini to load full settings dict (also infers EventQueue dir when --events omitted)')
    parser.add_argument('--output', '-o', help='Organized output base dir for library')
    parser.add_argument('--dry-run', action='store_true', help='Do not move files; only simulate and count')
    args = parser.parse_args()

    logger = Logger()

    # Build settings dict
    if args.settings:
        settings_dict = load_settings(args.settings)
        events_dir = Path(get_setting('EventQueue', 'dir', settings_dict=settings_dict, default='_internal/state/event_queue')) if not args.events else Path(args.events)
        # Ensure EventQueue settings consistent
        settings_dict.setdefault('EventQueue', {})['dir'] = str(events_dir)
        settings_dict['EventQueue']['enabled'] = True
    else:
        events_dir = Path(args.events) if args.events else Path('_internal/state/event_queue')
        settings_dict = {'EventQueue': {'enabled': True, 'dir': str(events_dir)}}

    if args.output:
        settings_dict.setdefault('Paths', {})['mkv_processing_output_dir'] = str(Path(args.output))

    if args.dry_run:
        # Reuse run_inbox_processing to iterate, but do not perform moves; count candidates
        from _internal.src.sab_event_inbox import EventInboxReader
        from _internal.src.fs_helpers import find_video_files
        inbox = EventInboxReader(cursor_dir=events_dir, eq=None)
        count = 0
        for evt in inbox.iter_new_events():
            data = evt.get('data', {})
            ddir = data.get('final_folder') or data.get('download_dir')
            status = data.get('status')
            ok = (str(status).lower() == 'success') or (str(status) == '0')
            if not ddir or not ok: continue
            p = Path(ddir)
            if not p.exists(): continue
            videos = find_video_files(p)
            if not videos: continue
            count += 1
        print(f"Processed {count} event(s)")
        return

    processed = asyncio.run(run_inbox_processing(settings_dict, logger, events_dir=events_dir))
    print(f"Processed {processed} event(s)")


if __name__ == '__main__':
    main()

