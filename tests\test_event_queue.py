import os
import sys
import tempfile
from pathlib import Path
import asyncio

REPO_ROOT = Path(__file__).resolve().parents[1]
sys.path.insert(0, str(REPO_ROOT))

from _internal.src.event_queue import get_event_queue


def test_event_queue_publish(tmp_path):
    out_dir = tmp_path / 'events'
    eq = get_event_queue({'EventQueue': {'enabled': True, 'dir': str(out_dir)}})

    async def _run():
        await eq.publish('test.event', {'a': 1})
        await eq.publish('test.event', {'a': 2})

    asyncio.run(_run())

    f = out_dir / 'events.jsonl'
    assert f.exists()
    lines = f.read_text(encoding='utf-8').splitlines()
    assert len(lines) == 2

