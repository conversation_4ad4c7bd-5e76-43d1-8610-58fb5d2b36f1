from __future__ import annotations
import gzip
import re
import urllib.parse
import urllib.request
from typing import Optional

GUID_ID_PATTERNS = [
    re.compile(r"[?&]id=([a-f0-9-]{36})", re.IGNORECASE),  # UUID format: ae5bc131-16be-4abb-855d-fdb7dee1739f
    re.compile(r"[?&]id=(\d+)", re.IGNORECASE),             # Legacy numeric format
    re.compile(r"/details/([a-f0-9-]{36})", re.IGNORECASE), # UUID in details URL
    re.compile(r"/details/(\d+)", re.IGNORECASE),           # Legacy numeric in details URL
    re.compile(r"/nzb/([a-f0-9-]{36})", re.IGNORECASE),     # UUID in nzb URL
    re.compile(r"/nzb/(\d+)", re.IGNORECASE),               # Legacy numeric in nzb URL
]

def extract_newznab_id(guid: str) -> Optional[str]:
    for pat in GUID_ID_PATTERNS:
        m = pat.search(guid)
        if m:
            return m.group(1)
    return None

def fetch_nzb(base_url: str, api_key: str, nzb_id: str, uid: Optional[str] = None, timeout: float = 5.0) -> bytes:
    # Ensure base_url ends with /api
    if not base_url.endswith('/api'):
        base_url = base_url.rstrip('/') + '/api'
    
    # Build parameters for NZBFinder API
    params = {
        't': 'get', 
        'id': nzb_id, 
        'apikey': api_key
    }
    
    # Add UID if provided (required for some indexers like NZBFinder)
    if uid:
        params['uid'] = uid
    
    params_str = urllib.parse.urlencode(params)
    url = f"{base_url}?{params_str}"
    req = urllib.request.Request(url, headers={'User-Agent': 'PreflightAnalyzer/0.1', 'Accept-Encoding': 'gzip'})
    with urllib.request.urlopen(req, timeout=timeout) as resp:  # nosec
        raw = resp.read()
        if resp.headers.get('Content-Encoding') == 'gzip' or raw[:2] == b'\x1f\x8b':
            try:
                raw = gzip.decompress(raw)
            except OSError:
                pass
        return raw
