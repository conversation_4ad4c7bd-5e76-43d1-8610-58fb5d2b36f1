"""
Dynamic Season-by-Season TV Show Progression Manager

This module provides state-driven, sequential season downloading for TV shows.
Instead of downloading all seasons simultaneously, it:
1. Monitors only the current season in Sonarr
2. Waits for completion (all episodes have hasFile=True)
3. Automatically advances to the next season
4. Blocks season packs throughout the process

Key Features:
- Opt-in via config/sequential_series.txt 
- Crash-resistant atomic state files
- Season pack detection and removal
- Dynamic Sonarr monitoring updates
"""

import json
import re
import tempfile
import aiohttp
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Set


STATE_ROOT = Path("_internal/state/season_progression")

def _slug(s: str) -> str:
    """Create a filesystem-safe slug from a string."""
    return re.sub(r'[^a-z0-9]+', '_', s.lower()).strip('_')


class TVSeasonProgressionManager:
    """
    Manages dynamic season-by-season progression for TV shows.
    
    State file schema per series:
    {
        "series_id": int,
        "title": str,
        "year": int,
        "total_seasons": int,
        "episodes_per_season": {"1": 6, "2": 6, ...},
        "current_season": int,
        "seasons_completed": [1, 2, ...],
        "series_complete": bool,
        "initialized_at": "ISO timestamp",
        "last_advanced_at": "ISO timestamp"
    }
    """
    
    def __init__(self, logger):
        self.logger = logger
        STATE_ROOT.mkdir(parents=True, exist_ok=True)

    def series_key(self, title: str, year: int, series_id: int) -> str:
        """Generate unique key for series state file."""
        return f"{_slug(title)}_{year}_{series_id}"

    def _path(self, key: str) -> Path:
        """Get path to state file for given key."""
        return STATE_ROOT / f"{key}.json"

    def load(self, key: str) -> Dict:
        """Load state for series (returns empty dict if not found)."""
        p = self._path(key)
        if p.exists():
            try:
                return json.loads(p.read_text(encoding="utf-8"))
            except Exception as e:
                self.logger.warning(f"Failed to load state for {key}: {e}")
                return {}
        return {}

    def save(self, key: str, data: Dict) -> None:
        """Atomically save state for series."""
        p = self._path(key)
        tmp = p.with_suffix(".tmp")
        tmp.write_text(json.dumps(data, indent=2), encoding="utf-8")
        tmp.replace(p)  # Atomic rename

    async def init_if_needed(self, series_obj: Dict, episodes: List[Dict], opt_in_titles: Set[str]) -> Optional[str]:
        """
        Initialize progression state if series is opted-in and not already initialized.
        
        Args:
            series_obj: Sonarr series object
            episodes: List of all episodes for this series
            opt_in_titles: Set of titles from config/sequential_series.txt
            
        Returns:
            Series key if initialized/found, None if not applicable
        """
        title = series_obj.get("title")
        year = series_obj.get("year")
        sid = series_obj.get("id")
        
        if not title or not year:
            return None
            
        # Check if this series is opted-in for sequential progression
        series_display = f"{title} ({year})"
        if opt_in_titles and series_display not in opt_in_titles:
            return None
            
        key = self.series_key(title, year, sid)
        state = self.load(key)
        
        # Already initialized
        if state.get("initialized_at"):
            return key
            
        # Build episode counts per season (skip season 0/specials)
        episodes_per_season = {}
        for ep in episodes:
            sn = ep.get("seasonNumber")
            if sn == 0:  # Skip specials
                continue
            episodes_per_season.setdefault(str(sn), 0)
            episodes_per_season[str(sn)] += 1
            
        total_seasons = len(episodes_per_season)
        if total_seasons == 0:
            self.logger.warning(f"No regular seasons found for {series_display}")
            return None
            
        # Initialize state
        state = {
            "series_id": sid,
            "title": title,
            "year": year,
            "total_seasons": total_seasons,
            "episodes_per_season": episodes_per_season,
            "current_season": 1,
            "seasons_completed": [],
            "series_complete": False,
            "initialized_at": datetime.utcnow().isoformat() + "Z",
            "last_advanced_at": None
        }
        
        self.save(key, state)
        self.logger.info(f"[SEQUENTIAL] Initialized progression for {series_display} -> Season 1 ({episodes_per_season.get('1', 0)} episodes)")
        return key

    async def is_current_season_complete(self, key: str, sonarr_url: str, api_key: str) -> bool:
        """
        Check if current season should be considered complete using intelligent logic:
        1. Standard: All episodes downloaded (hasFile=True)
        2. Intelligent: Episodes failed due to missing articles or other permanent failures
        """
        state = self.load(key)
        if not state or state.get("series_complete"):
            return False
            
        cur = state["current_season"]
        needed = state["episodes_per_season"].get(str(cur), 0)
        if needed == 0:
            return False
            
        # Query Sonarr for episode status and history
        async with aiohttp.ClientSession() as session:
            url = f"{sonarr_url}/api/v3/episode?seriesId={state['series_id']}"
            headers = {"X-Api-Key": api_key}
            
            async with session.get(url, headers=headers) as resp:
                if resp.status != 200:
                    self.logger.warning(f"Failed to load episodes for completion check: {resp.status}")
                    return False
                eps = await resp.json()
                
            # Analyze current season episodes
            season_episodes = [e for e in eps if e.get("seasonNumber") == cur]
            downloaded = sum(1 for e in season_episodes if e.get("hasFile"))
            
            # Standard completion check
            if downloaded >= needed:
                self.logger.info(f"[SEQUENTIAL] Season {cur} complete - standard ({downloaded}/{needed}) for {state['title']}")
                return True
            
            # Intelligent completion check for failed episodes
            missing_articles_failures = 0
            permanent_failures = 0
            
            for ep in season_episodes:
                if not ep.get("hasFile") and ep.get("monitored"):
                    # Check episode history for failures
                    ep_id = ep.get("id")
                    hist_url = f"{sonarr_url}/api/v3/history?seriesId={state['series_id']}&episodeId={ep_id}"
                    
                    async with session.get(hist_url, headers=headers) as hist_resp:
                        if hist_resp.status == 200:
                            history = await hist_resp.json()
                            records = history.get("records", [])
                            
                            # Look for recent failures
                            for record in records:
                                if record.get("eventType") == "downloadFailed":
                                    failure_reason = record.get("data", {}).get("message", "").lower()
                                    
                                    # Detect missing articles or permanent failures
                                    if any(keyword in failure_reason for keyword in [
                                        "missing article", "article missing", "no articles", 
                                        "not found", "failed to find", "no releases",
                                        "aborted, cannot be completed"
                                    ]):
                                        missing_articles_failures += 1
                                        break
                                    elif any(keyword in failure_reason for keyword in [
                                        "permanently failed", "blacklisted", "too old"
                                    ]):
                                        permanent_failures += 1
                                        break
            
            # Intelligent completion logic
            total_accounted = downloaded + missing_articles_failures + permanent_failures
            
            if total_accounted >= needed:
                self.logger.info(f"[SEQUENTIAL] Season {cur} complete - intelligent ({downloaded} downloaded, {missing_articles_failures} missing articles, {permanent_failures} permanent failures) for {state['title']}")
                return True
            elif downloaded > 0 and (downloaded / needed) >= 0.8 and missing_articles_failures > 0:
                self.logger.info(f"[SEQUENTIAL] Season {cur} complete - high success rate ({downloaded}/{needed} = {downloaded/needed*100:.1f}%) with missing articles failures for {state['title']}")
                return True
            else:
                self.logger.debug(f"[SEQUENTIAL] Season {cur} progress: {downloaded}/{needed} downloaded, {missing_articles_failures} missing articles, {permanent_failures} permanent failures for {state['title']}")
                return False

    async def enforce_monitoring(self, key: str, sonarr_url: str, api_key: str) -> None:
        """
        Ensure only the current season is monitored in Sonarr.
        """
        state = self.load(key)
        if not state or state.get("series_complete"):
            return
            
        cur = state["current_season"]
        headers = {"X-Api-Key": api_key}
        
        async with aiohttp.ClientSession() as session:
            # Get current series object
            async with session.get(f"{sonarr_url}/api/v3/series/{state['series_id']}", headers=headers) as r:
                if r.status != 200:
                    self.logger.warning(f"Failed to get series for monitoring update: {r.status}")
                    return
                series_obj = await r.json()
                
            # Update season monitoring
            changed = False
            for s in series_obj.get("seasons", []):
                sn = s.get("seasonNumber")
                if sn == 0:  # Skip specials
                    continue
                    
                desired = (sn == cur)
                if s.get("monitored") != desired:
                    s["monitored"] = desired
                    changed = True
                    
            # Apply changes if needed
            if changed:
                async with session.put(f"{sonarr_url}/api/v3/series/{state['series_id']}", 
                                     headers=headers, json=series_obj) as pr:
                    if pr.status == 202:
                        self.logger.info(f"[SEQUENTIAL] Monitoring set: only Season {cur} active for {state['title']}")
                    else:
                        self.logger.warning(f"Failed to update monitoring (HTTP {pr.status}) for {state['title']}")
                        
            # CRITICAL: Force individual episode searches instead of season packs
            await self._force_episode_searches(session, sonarr_url, headers, state['series_id'], cur)

    async def _force_episode_searches(self, session, sonarr_url: str, headers: dict, series_id: int, season_num: int) -> None:
        """
        Force Sonarr to search for individual episodes only, NEVER season packs.
        """
        try:
            # Get episodes for current season
            async with session.get(f"{sonarr_url}/api/v3/episode?seriesId={series_id}", headers=headers) as r:
                if r.status != 200:
                    return
                episodes = await r.json()
                
            # Find episodes in current season that need files
            target_episodes = [
                ep for ep in episodes 
                if ep.get("seasonNumber") == season_num and not ep.get("hasFile", False) and ep.get("monitored", False)
            ]
            
            if target_episodes:
                self.logger.info(f"[EPISODE-SEARCH] Found {len(target_episodes)} episodes needing download in Season {season_num}")
                
                # Trigger individual episode searches (max 3 at a time to avoid overload)
                for i, ep in enumerate(target_episodes[:3]):
                    episode_id = ep.get("id")
                    episode_num = ep.get("episodeNumber")
                    ep_title = ep.get("title", f"Episode {episode_num}")
                    
                    if episode_id:
                        search_data = {"episodeIds": [episode_id]}
                        async with session.post(f"{sonarr_url}/api/v3/command", 
                                              headers=headers, 
                                              json={"name": "EpisodeSearch", **search_data}) as sr:
                            if sr.status in [200, 201]:
                                self.logger.info(f"[INDIVIDUAL-EPISODE] Searching for S{season_num:02d}E{episode_num:02d} - {ep_title}")
                            else:
                                self.logger.warning(f"[EPISODE-SEARCH-FAILED] Could not trigger search for S{season_num:02d}E{episode_num:02d}: HTTP {sr.status}")
                                
                if len(target_episodes) > 3:
                    remaining = len(target_episodes) - 3
                    self.logger.info(f"[EPISODE-SEARCH] {remaining} more episodes will be searched in next cycle")
            else:
                self.logger.debug(f"[EPISODE-SEARCH] No episodes need downloading in Season {season_num}")
                
        except Exception as e:
            self.logger.warning(f"Failed to force episode searches: {e}")

    async def advance(self, key: str, sonarr_url: str, api_key: str) -> None:
        """
        Advance to the next season after current season completion.
        """
        state = self.load(key)
        if not state or state.get("series_complete"):
            return
            
        cur = state["current_season"]
        state["seasons_completed"].append(cur)
        next_season = cur + 1
        
        if next_season > state["total_seasons"]:
            # Series complete
            state["series_complete"] = True
            self.logger.info(f"[SEQUENTIAL] Series complete: {state['title']} ({state['year']})")
        else:
            # Advance to next season
            state["current_season"] = next_season
            next_episode_count = state["episodes_per_season"].get(str(next_season), 0)
            self.logger.info(f"[SEQUENTIAL] Advancing {state['title']} to Season {next_season} ({next_episode_count} episodes)")
            
        state["last_advanced_at"] = datetime.utcnow().isoformat() + "Z"
        self.save(key, state)
        
        # Update monitoring if not complete
        if not state.get("series_complete"):
            await self.enforce_monitoring(key, sonarr_url, api_key)

    def list_series(self) -> List[str]:
        """Get list of all series keys being managed."""
        return [p.stem for p in STATE_ROOT.glob("*.json")]

    def get_status_summary(self) -> List[Dict]:
        """Get status summary for all managed series."""
        summary = []
        for key in self.list_series():
            state = self.load(key)
            if state:
                summary.append({
                    "title": f"{state.get('title')} ({state.get('year')})",
                    "current_season": state.get("current_season"),
                    "total_seasons": state.get("total_seasons"),
                    "seasons_completed": state.get("seasons_completed", []),
                    "series_complete": state.get("series_complete", False)
                })
        return summary
