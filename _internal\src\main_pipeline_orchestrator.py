#!/usr/bin/env python3
"""
PlexMovieAutomator Main Pipeline Orchestrator
==============================================

This is a simplified orchestrator for SABnzbd post-processing integration.
It runs individual pipeline stages based on command line arguments.

Usage:
    python main_pipeline_orchestrator.py --run-mode once --stages 02_download_and_organize
"""

import sys
import argparse
import logging
from pathlib import Path

# Add project root to path
PROJECT_ROOT = Path(__file__).parent.parent.parent
sys.path.insert(0, str(PROJECT_ROOT))

def setup_logging():
    """Setup logging for the orchestrator."""
    log_dir = PROJECT_ROOT / "_internal" / "logs"
    log_dir.mkdir(exist_ok=True)
    
    log_file = log_dir / "orchestrator.log"
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

def run_stage_02():
    """Run the download and organize stage for both movies and TV shows."""
    logger = logging.getLogger(__name__)
    
    try:
        # Import and run the download and organize stage
        logger.info("Starting Stage 02: Download and Organize (Movies + TV Shows)")

        # Import the stage function
        sys.path.insert(0, str(PROJECT_ROOT))
        sys.path.insert(0, str(PROJECT_ROOT / "_internal"))
        from utils.common_helpers import load_settings
        
        # Load settings from the correct path
        settings_path = PROJECT_ROOT / "_internal" / "config" / "settings.ini"
        settings_dict = load_settings(str(settings_path))
        if not settings_dict:
            logger.error("Failed to load settings")
            return False
        
        # Import and run the stage
        import asyncio
        from importlib import import_module
        
        # Import the download and organize module
        download_module = import_module("02_download_and_organize")
        
        # Run BOTH movie and TV show monitoring
        logger.info("Processing Movies (Radarr)...")
        movie_success = asyncio.run(
            download_module.monitor_radarr_downloads(
                settings_dict=settings_dict,
                logger_instance=logger,
                mcp_manager=None
            )
        )
        
        logger.info("Processing TV Shows (Sonarr)...")
        tv_success = asyncio.run(
            download_module.monitor_sonarr_downloads(
                settings_dict=settings_dict,
                logger_instance=logger,
                mcp_manager=None
            )
        )
        
        overall_success = movie_success and tv_success
        
        if overall_success:
            logger.info("Stage 02 completed successfully (Movies + TV Shows)")
            return True
        else:
            logger.error(f"Stage 02 had issues - Movies: {movie_success}, TV: {tv_success}")
            return False
            
    except Exception as e:
        logger.error(f"Error running Stage 02: {e}")
        return False

def main():
    """Main orchestrator function."""
    logger = setup_logging()
    
    parser = argparse.ArgumentParser(description="PlexMovieAutomator Pipeline Orchestrator")
    parser.add_argument("--run-mode", choices=["once", "continuous"], default="once",
                       help="Run mode: once or continuous")
    parser.add_argument("--stages", nargs="+", default=["02_download_and_organize"],
                       help="Stages to run")
    
    args = parser.parse_args()
    
    logger.info(f"Orchestrator started - Mode: {args.run_mode}, Stages: {args.stages}")
    
    success = True
    
    for stage in args.stages:
        if stage == "02_download_and_organize":
            logger.info("Running Stage 02: Download and Organize")
            stage_success = run_stage_02()
            if not stage_success:
                success = False
                logger.error(f"Stage {stage} failed")
        else:
            logger.warning(f"Unknown stage: {stage}")
            success = False
    
    if success:
        logger.info("All stages completed successfully")
        return 0
    else:
        logger.error("One or more stages failed")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
