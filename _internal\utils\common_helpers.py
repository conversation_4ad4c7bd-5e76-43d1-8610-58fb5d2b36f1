#!/usr/bin/env python3
"""
PlexMovieAutomator/src/utils/common_helpers.py

Common utility functions used across the PlexMovieAutomator pipeline.
- Configuration loading (settings.ini)
- State management (movies_to_process.json)
- Logging setup
- External command execution
- File/Folder operations
- Video/Movie specific utilities
- Text/String utilities
- Unique ID generation
"""

import os
import sys
import shutil
import subprocess
import logging
import logging.handlers
import json
import uuid
import configparser
from pathlib import Path
from datetime import datetime, timezone
import re
import threading # For state file lock

# --- Configuration Management (settings.ini) ---
_config_object = None
_settings_file_path_loaded = None

def load_settings(settings_file_path: str = "_internal/config/settings.ini", project_root_str: str = None) -> dict:
    """
    Loads settings from the specified INI file.
    Resolves paths relative to the project root.
    """
    global _config_object, _settings_file_path_loaded

    if project_root_str:
        project_root = Path(project_root_str).resolve()
    else:
        # Assume this file is in PlexMovieAutomator/src/utils/
        project_root = Path(__file__).resolve().parent.parent.parent

    absolute_settings_path = (project_root / settings_file_path).resolve()
    _settings_file_path_loaded = absolute_settings_path # Store for reference in get_path_setting

    if not absolute_settings_path.is_file():
        # Attempt to log, but logger might not be set up yet if this is the first call.
        # So, print to stderr and exit for this critical failure.
        error_msg = f"CRITICAL: Settings file not found at {absolute_settings_path}"
        print(error_msg, file=sys.stderr)
        # Optionally, raise FileNotFoundError(error_msg) or sys.exit(1)
        # For now, let's allow it to proceed and return None, other functions will check _config_object
        return None


    parser = configparser.ConfigParser(interpolation=configparser.ExtendedInterpolation())
    # Provide [DEFAULT] section with project_root for interpolation, e.g. %(project_root)s/logs
    parser.read_dict({'DEFAULT': {'project_root': str(project_root)}})
    
    try:
        parser.read(absolute_settings_path)
        settings_dict = {}
        for section in parser.sections():
            settings_dict[section] = {}
            for key, value in parser.items(section):
                # Attempt to resolve paths if they seem relative and contain known base dir placeholders
                # A more robust path resolution should happen in get_path_setting
                settings_dict[section][key] = value
        _config_object = settings_dict # Store globally for easy access by get_setting
        
        # Basic logging before full logger setup
        print(f"INFO: Successfully loaded settings from: {absolute_settings_path}")
        return _config_object
    except configparser.Error as e:
        error_msg = f"CRITICAL: Error parsing settings file '{absolute_settings_path}': {e}"
        print(error_msg, file=sys.stderr)
        sys.exit(f"Error: Could not parse settings file: {e}") # Critical error
    except Exception as e:
        error_msg = f"CRITICAL: Unexpected error loading settings '{absolute_settings_path}': {e}"
        print(error_msg, file=sys.stderr)
        sys.exit(f"Error: Unexpected error loading settings: {e}") # Critical error

def get_setting(section: str, key: str, default=None, expected_type=str, settings_dict=None, required=False) -> any:
    """
    Retrieves a specific setting value from the loaded configuration.
    Handles type conversion (str, int, bool, list) and required settings.
    """
    config_to_use = settings_dict if settings_dict is not None else _config_object
    if config_to_use is None:
        # This case should ideally be caught by load_settings failing or orchestrator ensuring settings are loaded.
        if required:
            error_msg = f"Setting [{section}].{key} is required, but settings are not loaded."
            logging.critical(error_msg)
            raise ValueError(error_msg)
        return default

    value_str = config_to_use.get(section, {}).get(key)

    if value_str is None:
        if required:
            error_msg = f"Required setting [{section}].{key} not found in configuration."
            logging.critical(error_msg)
            raise ValueError(error_msg)
        if default is not None:
            logging.debug(f"Setting [{section}].{key} not found, using default: {default}")
            return default
        logging.warning(f"Setting [{section}].{key} not found and no default provided. Returning None.")
        return None

    try:
        if expected_type is bool:
            if isinstance(value_str, bool): return value_str
            return value_str.lower() in ['true', 'yes', '1', 'on']
        elif expected_type is int:
            if isinstance(value_str, int): return value_str
            return int(value_str)
        elif expected_type is float:
            if isinstance(value_str, float): return value_str
            return float(value_str)
        elif expected_type is list: # Assumes comma-separated string for lists
            if isinstance(value_str, list): return value_str
            return [item.strip() for item in value_str.split(',') if item.strip()]
        elif expected_type is str:
            return str(value_str)
        else: # If no specific type conversion, return as string or original if not str
            return value_str
    except ValueError as e:
        logging.warning(f"Could not convert setting [{section}].{key}='{value_str}' to {expected_type.__name__}: {e}. Returning default: {default}")
        if required:
            error_msg = f"Required setting [{section}].{key} has invalid format for type {expected_type.__name__}."
            logging.critical(error_msg)
            raise ValueError(error_msg)
        return default

def get_path_setting(section: str, key: str, settings_dict=None, must_exist=False, is_dir=False, create_if_not_exist_dir=False, make_absolute=True) -> Path | None:
    """
    Retrieves a path setting, resolves it to an absolute path relative to the project root,
    and optionally checks for existence or creates it if it's a directory.
    """
    path_str = get_setting(section, key, settings_dict=settings_dict, expected_type=str)
    if not path_str:
        if must_exist:
            logging.error(f"Required path setting [{section}].{key} not found.")
        return None

    # Determine project root - if settings file is in _internal/config/, go up two levels
    if _settings_file_path_loaded:
        if "_internal" in str(_settings_file_path_loaded):
            # Settings file is in _internal/config/, so project root is two levels up
            project_root = Path(_settings_file_path_loaded).parent.parent.parent
        else:
            # Legacy path structure
            project_root = Path(_settings_file_path_loaded).parent.parent
    else:
        project_root = Path.cwd()
    
    path = Path(path_str)
    if not path.is_absolute():
        path = (project_root / path).resolve()
    else:
        path = path.resolve() # Ensure it's a clean absolute path

    if must_exist and not path.exists():
        logging.error(f"Path for [{section}].{key} does not exist: {path}")
        return None
    
    if is_dir:
        if path.exists() and not path.is_dir():
            logging.error(f"Path for [{section}].{key} exists but is not a directory: {path}")
            return None
        if create_if_not_exist_dir and not path.exists():
            try:
                path.mkdir(parents=True, exist_ok=True)
                logging.info(f"Created directory for [{section}].{key}: {path}")
            except Exception as e:
                logging.error(f"Failed to create directory for [{section}].{key} at {path}: {e}")
                return None
    return path

# --- State File Management (config/movies_to_process.json) ---
_movies_state_file_path_global_state = None
_movies_state_lock_state = threading.Lock()

def _get_resolved_state_file_path(state_file_path_setting_key="movies_to_process_json_path", settings_dict=None) -> Path | None:
    global _movies_state_file_path_global_state
    if _movies_state_file_path_global_state: # If already resolved and stored
        return _movies_state_file_path_global_state

    path_obj = get_path_setting("General", state_file_path_setting_key, settings_dict=settings_dict)
    if path_obj:
        _movies_state_file_path_global_state = path_obj
        return path_obj
    
    # Fallback if not in settings, use default relative to project root
    if _settings_file_path_loaded:
        project_root = _settings_file_path_loaded.parent.parent
        default_path = (project_root / "config" / "movies_to_process.json").resolve()
        _movies_state_file_path_global_state = default_path
        logging.warning(f"State file path not in settings, defaulting to {default_path}")
        return default_path
        
    logging.error("Could not determine state file path.")
    return None


def read_state_file(settings_dict) -> list[dict]:
    """Reads the JSON state file. Returns an empty list if file not found or error."""
    state_file_p = _get_resolved_state_file_path(settings_dict=settings_dict)
    if not state_file_p:
        return []

    with _movies_state_lock_state:
        if state_file_p.exists():
            try:
                with open(state_file_p, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                if isinstance(data, list):
                    logging.info(f"Loaded {len(data)} movie entries from {state_file_p}")
                    return data
                else:
                    logging.warning(f"State file {state_file_p} does not contain a list. Initializing with empty list.")
                    return []
            except json.JSONDecodeError as e:
                logging.error(f"Error decoding JSON from state file {state_file_p}: {e}. Initializing with empty list.")
                return []
            except Exception as e:
                logging.error(f"Unexpected error loading state file {state_file_p}: {e}. Initializing with empty list.")
                return []
        else:
            logging.info(f"State file {state_file_p} not found. Initializing with empty list.")
            return []

def write_state_file(movies_data_list: list, settings_dict):
    """Writes the movie data list to the JSON state file atomically."""
    state_file_p = _get_resolved_state_file_path(settings_dict=settings_dict)
    if not state_file_p:
        logging.error("Cannot save state: State file path not determined.")
        return False

    temp_file_p = state_file_p.with_suffix(state_file_p.suffix + '.tmp')
    backup_file_p = state_file_p.with_suffix(state_file_p.suffix + '.bak')

    with _movies_state_lock_state:
        try:
            ensure_dir_exists(str(state_file_p.parent))
            with open(temp_file_p, 'w', encoding='utf-8') as f:
                json.dump(movies_data_list, f, indent=2, ensure_ascii=False)
            
            if state_file_p.exists(): # Create backup before replacing
                shutil.copy2(state_file_p, backup_file_p)
            
            shutil.move(str(temp_file_p), str(state_file_p))
            logging.info(f"Saved {len(movies_data_list)} movie entries to {state_file_p}")
            return True
        except Exception as e:
            logging.error(f"Error saving state file to {state_file_p}: {e}")
            if temp_file_p.exists():
                try: temp_file_p.unlink()
                except OSError: pass
            return False

# Functions to manipulate the movies_data_list (these would be called by individual scripts)
def find_movie_in_state(identifier_value: str, movies_data_list: list, identifier_key: str = "unique_id") -> dict | None:
    for movie in movies_data_list:
        if movie.get(identifier_key) == identifier_value:
            return movie
    return None

def update_movie_in_state(identifier_value: str, updates_dict: dict, movies_data_list: list, identifier_key: str = "unique_id") -> bool:
    movie = find_movie_in_state(identifier_value, movies_data_list, identifier_key)
    if movie:
        movie.update(updates_dict)
        movie["last_updated_timestamp"] = datetime.now(timezone.utc).isoformat()
        # Add to history if status is changing and part of updates_dict
        if "status" in updates_dict and updates_dict["status"] != movie.get("previous_status_for_history"): # Avoid duplicate history for same status
            history_entry = {
                "timestamp": movie["last_updated_timestamp"],
                "status": updates_dict["status"],
                "message": updates_dict.get("status_message", "Status updated.")
            }
            movie.setdefault("history", []).append(history_entry)
            movie["previous_status_for_history"] = updates_dict["status"]
        return True
    return False

def add_movie_to_state(movie_dict: dict, movies_data_list: list) -> tuple[list, str | None]:
    if "unique_id" not in movie_dict:
        movie_dict["unique_id"] = generate_unique_id()
    if "request_timestamp" not in movie_dict:
        movie_dict["request_timestamp"] = datetime.now(timezone.utc).isoformat()
    movie_dict["last_updated_timestamp"] = datetime.now(timezone.utc).isoformat()
    
    # Ensure basic structure for history
    if "history" not in movie_dict:
        movie_dict["history"] = []
    
    # Add initial history entry if status is present
    if "status" in movie_dict:
         movie_dict["history"].append({
            "timestamp": movie_dict["last_updated_timestamp"],
            "status": movie_dict["status"],
            "message": movie_dict.get("status_message", "Movie added to processing.")
         })
         movie_dict["previous_status_for_history"] = movie_dict["status"]

    movies_data_list.append(movie_dict)
    return movies_data_list, movie_dict["unique_id"]


# --- Logging Setup ---
_logger_initialized = False
def setup_logging(settings_dict: dict, log_name_override: str = None) -> logging.Logger:
    """
    Configures logging. Can be called multiple times, but will only configure root once.
    Allows individual scripts to get a named logger.
    """
    global _logger_initialized
    
    file_log_path_str = get_path_setting("Logging", "log_file_path", settings_dict=settings_dict, make_absolute=True)
    file_level_str = get_setting("Logging", "file_log_level", default="INFO", settings_dict=settings_dict).upper()
    console_level_str = get_setting("Logging", "console_log_level", default="INFO", settings_dict=settings_dict).upper()
    max_bytes = get_setting("Logging", "log_rotation_max_bytes", default=10485760, expected_type=int, settings_dict=settings_dict) # 10MB
    backup_count = get_setting("Logging", "log_rotation_backup_count", default=5, expected_type=int, settings_dict=settings_dict)

    if not _logger_initialized:
        _logger_initialized = True # Configure root logger only once
        
        log_format = "%(asctime)s [%(levelname)-8s] [%(name)s] [%(module)s.%(funcName)s:%(lineno)d] - %(message)s"
        date_format = "%Y-%m-%d %H:%M:%S"
        
        root_logger = logging.getLogger() # Get the root logger
        # Determine the lowest level needed for the root logger based on handlers
        file_level_numeric = getattr(logging, file_level_str, logging.INFO)
        console_level_numeric = getattr(logging, console_level_str, logging.INFO)
        root_logger.setLevel(min(file_level_numeric, console_level_numeric))

        # Remove default handlers if any to avoid duplication
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)

        # File Handler
        if file_log_path_str:
            ensure_dir_exists(str(Path(file_log_path_str).parent))
            file_handler = logging.handlers.RotatingFileHandler(
                file_log_path_str, maxBytes=max_bytes, backupCount=backup_count, encoding='utf-8'
            )
            file_handler.setFormatter(logging.Formatter(log_format, datefmt=date_format))
            file_handler.setLevel(file_level_numeric)
            root_logger.addHandler(file_handler)

        # Console Handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(logging.Formatter(log_format, datefmt=date_format))
        console_handler.setLevel(console_level_numeric)
        root_logger.addHandler(console_handler)
        
        logging.info(f"Root logging initialized. File: '{file_log_path_str}' ({file_level_str}), Console: ({console_level_str})")

    # Return a named logger for the calling module
    module_name = log_name_override if log_name_override else __name__
    return logging.getLogger(module_name)


# --- External Command Execution ---
def run_command(command_list: list, working_dir: str = None, log_output: bool = True, dry_run: bool = False, logger_instance: logging.Logger = None):
    """
    Executes an external command and logs its output.
    Returns a tuple (return_code, stdout_str, stderr_str).
    """
    current_logger = logger_instance if logger_instance else logging.getLogger(__name__) # Fallback if not passed

    command_str_for_log = " ".join(f'"{c}"' if " " in str(c) else str(c) for c in command_list)
    current_logger.debug(f"Preparing to execute: {command_str_for_log}" + (f" in CWD: {working_dir}" if working_dir else ""))

    if dry_run:
        current_logger.info(f"DRY RUN: Would execute: {command_str_for_log}")
        return 0, "Dry run stdout", "Dry run stderr"

    try:
        process = subprocess.Popen(
            command_list,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            encoding='utf-8',
            errors='replace',
            cwd=working_dir,
            creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0 # Hide console window on Windows
        )
        stdout, stderr = process.communicate(timeout=3600) # Add a long timeout (e.g., 1 hour for mkvmerge)
        return_code = process.returncode

        log_prefix = f"Cmd: '{Path(command_list[0]).name} {' '.join(command_list[1:])[:50]}...'"
        if stdout and log_output: current_logger.debug(f"{log_prefix} STDOUT:\n{stdout.strip()}")
        
        if stderr:
            if return_code != 0: current_logger.error(f"{log_prefix} STDERR:\n{stderr.strip()}")
            elif log_output: current_logger.debug(f"{log_prefix} STDERR (warnings):\n{stderr.strip()}")
        
        if return_code == 0: current_logger.debug(f"Command successful: {command_str_for_log}")
        else: current_logger.error(f"Command failed with exit code {return_code}: {command_str_for_log}")
        
        return return_code, stdout, stderr

    except subprocess.TimeoutExpired:
        process.kill()
        stdout, stderr = process.communicate()
        current_logger.error(f"Command timed out after 1 hour: {command_str_for_log}")
        return -3, stdout, "TimeoutExpired" # Special return code for timeout
    except FileNotFoundError:
        current_logger.error(f"Executable not found: {command_list[0]}. Ensure it's installed and in PATH, or path correctly in settings.ini.")
        return -1, "", "FileNotFoundError"
    except Exception as e:
        current_logger.exception(f"Exception running command '{command_list[0]}': {e}")
        return -2, "", str(e)

# --- File/Folder Utilities ---
def ensure_dir_exists(dir_path_str: str | Path, logger_instance: logging.Logger = None) -> bool:
    """Ensures that a directory exists, creating it if necessary."""
    current_logger = logger_instance if logger_instance else logging.getLogger(__name__)
    dir_path = Path(dir_path_str)
    if not dir_path.exists():
        try:
            dir_path.mkdir(parents=True, exist_ok=True)
            current_logger.info(f"Created directory: {dir_path}")
            return True
        except Exception as e:
            current_logger.error(f"Failed to create directory {dir_path}: {e}")
            return False
    elif not dir_path.is_dir():
        current_logger.error(f"Path exists but is not a directory: {dir_path}")
        return False
    return True

def safe_move_file(src_path_str: str | Path, dest_path_str: str | Path, create_dest_dir: bool = True, logger_instance: logging.Logger = None) -> bool:
    """Moves a file, optionally creating the destination directory. Logs the action."""
    current_logger = logger_instance if logger_instance else logging.getLogger(__name__)
    src_path = Path(src_path_str)
    dest_path = Path(dest_path_str)

    if not src_path.is_file():
        current_logger.error(f"Source for move is not a file or does not exist: {src_path}")
        return False
    
    if create_dest_dir:
        if not ensure_dir_exists(dest_path.parent, logger_instance=current_logger):
            return False
            
    try:
        shutil.move(str(src_path), str(dest_path))
        current_logger.info(f"Moved: '{src_path}' to '{dest_path}'")
        return True
    except Exception as e:
        current_logger.error(f"Failed to move '{src_path}' to '{dest_path}': {e}")
        return False

def safe_copy_file(src_path_str: str | Path, dest_path_str: str | Path, create_dest_dir: bool = True, logger_instance: logging.Logger = None) -> bool:
    """Copies a file (with metadata), optionally creating the destination directory. Logs."""
    current_logger = logger_instance if logger_instance else logging.getLogger(__name__)
    src_path = Path(src_path_str)
    dest_path = Path(dest_path_str)

    if not src_path.is_file():
        current_logger.error(f"Source for copy is not a file or does not exist: {src_path}")
        return False
    
    if create_dest_dir:
        if not ensure_dir_exists(dest_path.parent, logger_instance=current_logger):
            return False
            
    try:
        shutil.copy2(str(src_path), str(dest_path)) # copy2 preserves metadata
        current_logger.info(f"Copied: '{src_path}' to '{dest_path}'")
        return True
    except Exception as e:
        current_logger.error(f"Failed to copy '{src_path}' to '{dest_path}': {e}")
        return False

def safe_delete_file(file_path_str: str | Path, logger_instance: logging.Logger = None) -> bool:
    current_logger = logger_instance if logger_instance else logging.getLogger(__name__)
    file_path = Path(file_path_str)
    try:
        if file_path.is_file():
            file_path.unlink()
            current_logger.info(f"Deleted file: {file_path}")
        elif not file_path.exists():
            current_logger.debug(f"File to delete does not exist: {file_path}")
        else:
            current_logger.warning(f"Path is not a file, cannot delete: {file_path}")
            return False
        return True
    except Exception as e:
        current_logger.error(f"Failed to delete file {file_path}: {e}")
        return False

def safe_delete_folder(folder_path_str: str | Path, ignore_errors: bool = False, logger_instance: logging.Logger = None) -> bool:
    current_logger = logger_instance if logger_instance else logging.getLogger(__name__)
    folder_path = Path(folder_path_str)
    try:
        if folder_path.is_dir():
            shutil.rmtree(str(folder_path), ignore_errors=ignore_errors)
            current_logger.info(f"Deleted folder: {folder_path} (ignore_errors={ignore_errors})")
        elif not folder_path.exists():
            current_logger.debug(f"Folder to delete does not exist: {folder_path}")
        else:
            current_logger.warning(f"Path is not a directory, cannot delete: {folder_path}")
            return False
        return True
    except Exception as e:
        current_logger.error(f"Failed to delete folder {folder_path}: {e}")
        return False

# --- Video/Movie Specific Utilities ---
def get_video_resolution(file_path_str: str | Path, ffprobe_exe_path: str, logger_instance: logging.Logger = None):
    current_logger = logger_instance if logger_instance else logging.getLogger(__name__)
    file_path = Path(file_path_str)
    if not file_path.is_file():
        current_logger.error(f"Video file not found for resolution check: {file_path}")
        return None, None

    command = [
        ffprobe_exe_path, "-v", "error", "-select_streams", "v:0",
        "-show_entries", "stream=width,height", "-of", "csv=s=x:p=0", str(file_path)
    ]
    rc, stdout, stderr = run_command(command, log_output=False, logger_instance=current_logger)

    if rc == 0 and stdout:
        try:
            # Handle potential extra separators like trailing 'x'
            clean_output = stdout.strip().rstrip('x')
            output_parts = [part for part in clean_output.split('x') if part.strip()]
            if len(output_parts) >= 2:
                width_str, height_str = output_parts[0], output_parts[1]
                current_logger.info(f"Detected resolution {width_str}x{height_str} for {file_path.name}")
                return int(width_str), int(height_str)
            else:
                current_logger.error(f"Could not parse ffprobe resolution output '{stdout.strip()}' for {file_path.name}: insufficient parts")
                return None, None
        except ValueError as ve:
            current_logger.error(f"Could not parse ffprobe resolution output '{stdout.strip()}' for {file_path.name}: {ve}")
            return None, None
    else:
        current_logger.error(f"ffprobe failed for {file_path.name}. RC: {rc}. Stderr: {stderr.strip()}")
        return None, None

def find_largest_movie_file(folder_path_str: str | Path, movie_extensions_list: list, logger_instance: logging.Logger = None) -> str | None:
    current_logger = logger_instance if logger_instance else logging.getLogger(__name__)
    folder_path = Path(folder_path_str)
    if not folder_path.is_dir():
        current_logger.error(f"Cannot find largest movie file: Directory not found at {folder_path}")
        return None

    largest_file_path = None
    largest_size = -1
    
    # Ensure extensions start with a dot and are lowercase for matching
    normalized_extensions = [ext.lower() if ext.startswith('.') else f".{ext.lower()}" for ext in movie_extensions_list]

    for item in folder_path.rglob('*'):
        if item.is_file() and item.suffix.lower() in normalized_extensions:
            try:
                current_size = item.stat().st_size
                if current_size > largest_size:
                    largest_size = current_size
                    largest_file_path = item
            except FileNotFoundError:
                current_logger.warning(f"File vanished during size check: {item}")
                continue
    
    if largest_file_path:
        current_logger.info(f"Largest movie file in {folder_path}: {largest_file_path.name} ({largest_size} bytes)")
        return str(largest_file_path)
    else:
        current_logger.warning(f"No movie files matching extensions {normalized_extensions} found in {folder_path}")
        return None

def generate_plex_movie_name(title: str, year: int | str, id_value: str | int = None, id_type: str = "tmdb") -> str:
    """Constructs a Plex-compliant name: "Movie Title (Year) {id_type-id_value}"."""
    sanitized_title = re.sub(r'[<>:"/\\|?*]', '', title).strip()
    year_str = str(year) if year else "UnknownYear"
    
    name_parts = [sanitized_title, f"({year_str})"]
    if id_value:
        clean_id_type = re.sub(r'\W+', '', id_type).lower()
        name_parts.append(f"{{{clean_id_type}-{id_value}}}")
    return " ".join(name_parts)

# --- Text/String Utilities ---
def normalize_text_for_matching(text: str | None) -> str:
    if not text: return ""
    text = re.sub(r'[^\w\s-]', '', text) # Keep alphanumeric, whitespace, underscore, hyphen
    text = text.lower()
    return re.sub(r'\s+', ' ', text).strip()

def extract_year_from_title(title_string: str | None) -> tuple[str, int | None]:
    if not title_string: return "", None
    match = re.search(r'\s*\((\b(?:19|20)\d{2}\b)\)\s*$', title_string) # Match YYYY year
    if match:
        year = int(match.group(1))
        title_without_year = title_string[:match.start()].strip()
        return title_without_year, year
    return title_string.strip(), None

# --- Unique ID Generation ---
def generate_unique_id(prefix="movie_") -> str:
    return f"{prefix}{uuid.uuid4()}"

# --- Threading Lock for state file (example of how it could be used externally)
# This lock is defined in this module but would be imported and used by any function
# in other modules that directly calls write_state_file, IF those modules might run concurrently.
# For a single orchestrator calling stage scripts sequentially, the lock in write_state_file itself is sufficient.
STATE_FILE_IO_LOCK = threading.Lock()


# --- SQLite Database Configuration Helpers ---
def get_sqlite_database_path(settings_dict: dict = None) -> str:
    """
    Get the SQLite database path from settings.

    Args:
        settings_dict: The settings dictionary (if None, will load from default path)

    Returns:
        Path to the SQLite database file
    """
    return get_setting("General", "sqlite_database_path", settings_dict=settings_dict,
                      default="_internal/data/pipeline_state.db")


def get_database_config(settings_dict: dict = None) -> dict:
    """
    Get database configuration settings.

    Args:
        settings_dict: The settings dictionary (if None, will load from default path)

    Returns:
        Dictionary containing database configuration
    """
    if settings_dict is None:
        settings_dict = load_settings()

    return {
        'auto_sync_filesystem': get_setting("Database", "auto_sync_filesystem", settings_dict=settings_dict, default=True, expected_type=bool),
        'auto_heal_discrepancies': get_setting("Database", "auto_heal_discrepancies", settings_dict=settings_dict, default=True, expected_type=bool),
        'backup_enabled': get_setting("Database", "backup_enabled", settings_dict=settings_dict, default=True, expected_type=bool),
        'backup_retention_days': get_setting("Database", "backup_retention_days", settings_dict=settings_dict, default=30, expected_type=int),
        'backup_directory': get_setting("Database", "backup_directory", settings_dict=settings_dict, default="_internal/backups"),
        'health_check_enabled': get_setting("Database", "health_check_enabled", settings_dict=settings_dict, default=True, expected_type=bool),
        'health_alert_threshold': get_setting("Database", "health_alert_threshold", settings_dict=settings_dict, default=80, expected_type=int)
    }


def should_use_sqlite(settings_dict: dict = None) -> bool:
    """
    Determine if SQLite should be used instead of JSON files.

    Args:
        settings_dict: The settings dictionary (if None, will load from default path)

    Returns:
        True if SQLite should be used, False for legacy JSON mode
    """
    # Check if SQLite database path is configured
    sqlite_path = get_sqlite_database_path(settings_dict)
    return bool(sqlite_path and sqlite_path != "")


# --- Legacy State Management Compatibility ---
def load_movies_state(settings_dict: dict = None) -> list:
    """
    Load movies state using filesystem-first approach with metadata enrichment.

    This function replaces the old SQLite/JSON approach with pure filesystem scanning,
    enriched with metadata from the metadata-only database.

    Args:
        settings_dict: The settings dictionary (if None, will load from default path)

    Returns:
        List of movie dictionaries with current state from filesystem
    """
    try:
        from .filesystem_first_state_manager import FilesystemFirstStateManager, MetadataOnlyDatabase
        from pathlib import Path

        workspace_root = Path.cwd()
        filesystem_manager = FilesystemFirstStateManager(workspace_root)
        metadata_db = MetadataOnlyDatabase(workspace_root)

        # Discover all movies by stage using filesystem scanning
        discovered_movies = filesystem_manager.discover_movies_by_stage()
        
        # Convert to legacy format for backward compatibility
        legacy_movies = []
        
        for stage, movies in discovered_movies.items():
            for movie in movies:
                movie_id = filesystem_manager.generate_movie_identifier(movie)
                
                # Get metadata from database if available
                metadata_record = metadata_db.get_movie_metadata(movie_id)
                
                # Create legacy-compatible movie record
                legacy_movie = {
                    'unique_id': movie_id,
                    'title': movie.get('cleaned_title', 'Unknown'),
                    'cleaned_title': movie.get('cleaned_title', 'Unknown'),
                    'year': movie.get('year'),
                    'status': stage,  # Current stage from filesystem
                    'created_at': movie.get('discovered_at'),
                    'last_updated_timestamp': movie.get('discovered_at'),
                    
                    # Filesystem-based paths
                    'paths': {
                        'movie_directory': movie.get('movie_directory'),
                        'download_folder': movie.get('download_folder'),
                        'final_directory': movie.get('final_directory'),
                        'main_movie_file': movie.get('main_movie_file'),
                        'processed_content': movie.get('processed_content')
                    },
                    
                    # Metadata from database if available
                    'tmdb_id': metadata_record.get('tmdb_id') if metadata_record else None,
                    'imdb_id': metadata_record.get('imdb_id') if metadata_record else None,
                    'metadata': metadata_record.get('metadata', {}) if metadata_record else {},
                    'metadata_details': metadata_record.get('metadata', {}) if metadata_record else {},
                    
                    # Processing info from markers
                    'error_message': None,
                    'processing_metrics': {},
                    'extracted_subtitle_info': []
                }
                
                # Add error information if in error stage
                if stage == 'error':
                    movie_dir = Path(movie.get('movie_directory', ''))
                    if movie_dir.exists():
                        error_data = filesystem_manager.get_stage_marker_data(movie_dir, 'error')
                        if error_data:
                            legacy_movie['error_message'] = error_data.get('error_message', 'Unknown error')
                
                legacy_movies.append(legacy_movie)
        
        metadata_db.close()
        return legacy_movies

    except Exception as e:
        logging.error(f"Failed to load from filesystem-first approach, falling back to JSON: {e}")
        # Fall back to legacy JSON approach as last resort
        return read_state_file(settings_dict)