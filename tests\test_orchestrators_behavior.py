import asyncio
import types

# ----- Movie orchestrator tests -----

async def _run_select_root(monkeypatch, settings, radarr_roots):
    from _internal.src import intake_movie_orchestrator as imo

    class StubRadarr:
        def __init__(self, url, key):
            self.url = url
            self.key = key
        async def get_rootfolders(self, session):
            return radarr_roots

    monkeypatch.setattr(imo, "RadarrClient", StubRadarr)

    # Use forward slashes in settings; function will normalize
    result = await imo.select_root_folder(
        session=None,
        radarr_url="http://localhost:7878",
        api_key="k",
        settings=settings,
        logger=types.SimpleNamespace(info=lambda *a, **k: None, warning=lambda *a, **k: None)
    )
    return result


def test_select_root_existing_match(tmp_path, monkeypatch):
    settings = {"Paths": {"plex_movies_directory": str(tmp_path)}}
    radarr_roots = [{"path": str(tmp_path)}]

    async def run():
        return await _run_select_root(monkeypatch, settings, radarr_roots)

    root = asyncio.run(run())
    assert root.replace("/", "\\").lower() == str(tmp_path).replace("/", "\\").lower()


def test_select_root_fallback_to_first(monkeypatch, tmp_path):
    # desired path not in radarr list -> choose first
    settings = {"Paths": {"plex_movies_directory": str(tmp_path / "nonexistent")}}
    radarr_roots = [{"path": str(tmp_path / "registered1")}, {"path": str(tmp_path / "registered2")}]

    async def run():
        return await _run_select_root(monkeypatch, settings, radarr_roots)

    root = asyncio.run(run())
    assert root.replace("/", "\\").endswith("registered1")


def test_add_with_profiles(monkeypatch):
    from _internal.src import intake_movie_orchestrator as imo

    added = []
    class StubRadarr:
        def __init__(self, url, key): pass
        async def add_movie(self, session, payload):
            added.append(payload.get("qualityProfileId"))
            return {"id": len(added), "title": payload.get("title")}

    monkeypatch.setattr(imo, "RadarrClient", StubRadarr)

    async def run():
        return await imo.add_with_profiles(
            session=None,
            radarr_url="http://localhost:7878",
            api_key="k",
            best_match={"title": "T", "year": 2020, "tmdbId": 1, "images": []},
            quality_profile_ids=[3,4],
            root_folder_path="X:/Movies",
            search_term="T 2020",
            logger=types.SimpleNamespace(info=lambda *a, **k: None, warning=lambda *a, **k: None)
        )

    res = asyncio.run(run())
    assert len(res) == 2
    assert added == [3,4]


# ----- TV orchestrator tests -----

async def _run_cfg_multi(monkeypatch):
    from _internal.src import intake_tv_orchestrator as ito

    calls = {"set": [], "cmd": 0}

    class StubSonarr:
        def __init__(self, url, key): pass
        async def get_episodes_by_series(self, session, sid):
            return [
                {"id": 11, "seasonNumber": 1, "episodeNumber": 1},
                {"id": 12, "seasonNumber": 1, "episodeNumber": 2},
                {"id": 21, "seasonNumber": 2, "episodeNumber": 1},
            ]
        async def update_series(self, session, series_obj):
            return True
        async def set_episodes_monitor_state(self, session, ids, monitored):
            calls["set"].append((tuple(sorted(ids)), monitored))
            return True
        async def issue_command(self, session, payload):
            calls["cmd"] += 1
            return {"name": payload.get("name"), "id": calls["cmd"]}

    monkeypatch.setattr(ito, "SonarrClient", StubSonarr)

    async def run():
        return await ito.configure_multiple_episodes(
            session=None,
            sonarr_url="http://localhost:8989",
            api_key="k",
            series_id=1,
            episodes_list=[{"season":1, "episode":2}, {"season":2, "episode":1}],
            logger=types.SimpleNamespace(info=lambda *a, **k: None, warning=lambda *a, **k: None),
            settings=None,
            disable_immediate_search=False,
        )

    ok = asyncio.run(run())
    assert ok is True
    # Expect season-level unmonitor then target monitor
    assert any(monitored is False for _, monitored in calls["set"])
    assert any(monitored is True for _, monitored in calls["set"])
    assert calls["cmd"] == 2


async def _run_cfg_season(monkeypatch):
    from _internal.src import intake_tv_orchestrator as ito

    class StubSonarr:
        def __init__(self, url, key): pass
        async def get_series_by_id(self, session, sid):
            return {"id": sid, "seasons": [{"seasonNumber": 1}, {"seasonNumber": 2}]}
        async def update_series(self, session, series_obj):
            return True
        async def get_episodes_by_series(self, session, sid):
            return [
                {"id": 11, "seasonNumber": 2, "episodeNumber": 1},
                {"id": 12, "seasonNumber": 2, "episodeNumber": 2},
            ]
        async def issue_command(self, session, payload):
            return {"id": 1, "name": payload.get("name")}

    monkeypatch.setattr(ito, "SonarrClient", StubSonarr)

    async def run():
        return await ito.configure_specific_season(
            session=None,
            sonarr_url="http://localhost:8989",
            api_key="k",
            series_id=1,
            season_num=2,
            logger=types.SimpleNamespace(info=lambda *a, **k: None, warning=lambda *a, **k: None),
            disable_immediate_search=False,
        )

    ok = asyncio.run(run())
    assert ok is True

