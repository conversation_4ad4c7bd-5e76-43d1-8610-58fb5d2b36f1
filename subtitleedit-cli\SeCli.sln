﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.3.32819.101
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "seconv", "src\se-cli\seconv.csproj", "{7AD23395-BCF1-417D-A57B-B11A9A61DA25}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "SolutionItems", "SolutionItems", "{C34E175A-40EC-41DC-BEE6-2F934BF6C8FF}"
	ProjectSection(SolutionItems) = preProject
		docker\Dockerfile = docker\Dockerfile
		LICENSE = LICENSE
		README.md = README.md
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{7AD23395-BCF1-417D-A57B-B11A9A61DA25}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7AD23395-BCF1-417D-A57B-B11A9A61DA25}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7AD23395-BCF1-417D-A57B-B11A9A61DA25}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7AD23395-BCF1-417D-A57B-B11A9A61DA25}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {58ADC2A1-F3BC-4568-8747-B88B60E9C80E}
	EndGlobalSection
EndGlobal
