#!/usr/bin/env python3
"""
PlexMovieAutomator/03_mkv_processor.py

- Processes organized MKV files based on "mkv_processing_pending" status.
- Adapts the core logic from the user's original, detailed mkv_processor.py script.
- Cleans metadata, selects best video/audio tracks, sets properties.
- Creates a clean video/audio-only MKV.
- Extracts all selected subtitles for further handling.
- Updates movie status in the central state file.

Auto-activates virtual environment if not already active.
"""

import sys
import os
from pathlib import Path

# Auto-activate virtual environment
def ensure_venv():
    """Ensure we're running in the virtual environment"""
    # Get the current script directory
    root_dir = Path(__file__).parent
    venv_python = root_dir / "_internal" / "venv" / "Scripts" / "python.exe"

    # Check if we're already in venv or if venv doesn't exist
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        # Already in virtual environment
        return

    if venv_python.exists():
        # Re-run this script with venv python
        import subprocess
        print(f"🔄 Activating virtual environment: {venv_python}")
        result = subprocess.run([str(venv_python)] + sys.argv, cwd=str(root_dir))
        sys.exit(result.returncode)
    else:
        print("⚠️ Virtual environment not found, running with system Python")

# Activate venv before any other imports
ensure_venv()

# Setup paths for clean imports
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent / "_internal"))

import logging
import shutil
import subprocess
import time
from datetime import datetime, timezone
from typing import Optional, Dict, List, Any, Tuple
import time
import json
import asyncio
from pathlib import Path
import sys
import subprocess
import shutil
import time
import logging
from datetime import datetime, timezone
from typing import Optional, Dict, List, Any, Tuple

# --- Utility Imports ---
try:
    from _internal.utils.common_helpers import (
        get_path_setting,
        get_setting,
    )
    from _internal.utils.filesystem_first_state_manager import FilesystemFirstStateManager, MetadataOnlyDatabase
except ImportError:
    from utils.common_helpers import (
        get_path_setting,
        get_setting,
    )
    # Import SQLite state manager for fallback
    sys.path.insert(0, str(Path(__file__).parent / "_internal"))
    from _internal.utils.filesystem_first_state_manager import FilesystemFirstStateManager, MetadataOnlyDatabase

# Import MKV utility functions
try:
    from src.utils.mkv_utils import (
        get_mkv_info,
        clean_mkv_title_and_tags,
        clean_attachments_iterative,
        set_track_properties_by_uid,
        select_audio_track,
        get_audio_track_name,
        select_subtitle_tracks,
        create_clean_video_audio_mkv,
        extract_subtitle_tracks_individually,
        parse_master_list
    )
except ImportError:
    from _internal.utils.mkv_utils import (
        get_mkv_info,
        clean_mkv_title_and_tags,
        clean_attachments_iterative,
        set_track_properties_by_uid,
        select_audio_track,
        get_audio_track_name,
        select_subtitle_tracks,
        create_clean_video_audio_mkv,
        extract_subtitle_tracks_individually,
        parse_master_list
    )

# --- Robust State Management Imports ---
try:
    from _internal.utils.filesystem_first_state_manager import create_filesystem_first_manager
    from _internal.utils.idempotent_helpers import create_idempotent_operations
except ImportError:
    try:
        from _internal.utils.filesystem_first_state_manager import create_filesystem_first_manager
        from utils.idempotent_helpers import create_idempotent_operations
    except ImportError:
        # Fallback - will use legacy system
        create_robust_state_manager = None
        create_idempotent_operations = None

# --- Global Logger ---
logger = None

def process_single_mkv_simple(movie: dict, organized_path: Path, settings: dict, show_language_map: dict) -> tuple[bool, str | None]:
    """
    Simplified MKV processing based on working version.
    
    Core Requirements:
    1. Extract ALL audio tracks with proper extensions
    2. Create clean V/A MKV and extract subtitles
    3. Move all processed files to 3_mkv_cleaned_subtitles_extracted 
    4. Keep original MKV file in source
    
    Returns:
        bool: Success/failure
    """
    try:
        logger.info(f"🎬 Processing: {movie.get('cleaned_title', 'Unknown')}")
        
        # Check if files are already in final destination first
        next_stage_base = Path("workspace") / "3_mkv_cleaned_subtitles_extracted"
        src_lower = str(organized_path).lower()
        if "4k" in src_lower or "2160p" in src_lower:
            resolution = "4k"
        elif "1080p" in src_lower:
            resolution = "1080p"
        elif "720p" in src_lower:
            resolution = "720p"
        else:
            resolution = "sd_or_unknown"
        # Destination must be movies/<resolution>/<Movie (Year)>
        next_stage_dir = next_stage_base / "movies" / resolution / organized_path.parent.name
        
        # Check if processing is already complete
        if next_stage_dir.exists():
            va_file_exists = any(f.name.endswith('.processed.mkv') for f in next_stage_dir.iterdir() if f.is_file())
            audio_dir_exists = (next_stage_dir / "_Processed_Audio").exists()
            
            if va_file_exists and audio_dir_exists:
                logger.info(f"✅ Files already in destination: {next_stage_dir}")
                # Determine next status based on existing subtitle files
                next_status = "final_mux_pending"
                dest_subs_dir = next_stage_dir / "_Processed_Subtitles"
                if dest_subs_dir.exists():
                    for f in dest_subs_dir.iterdir():
                        if f.suffix.lower() in [".sup", ".sub", ".idx"]:
                            next_status = "subtitle_ocr_pending"
                            break
                return True, next_status
        
        # Setup directories first (before backup, since backup removes the folder)
        parent_folder = organized_path.parent
        temp_dir = parent_folder / "_Temp_Processing"
        temp_dir.mkdir(exist_ok=True)
        processing_file = temp_dir / f"{organized_path.stem}_TEMP{organized_path.suffix}"

        # Create output directories
        output_va_dir = parent_folder / "_Processed_VideoAudio"
        output_subs_dir = parent_folder / "_Processed_Subtitles"
        output_audio_dir = parent_folder / "_Processed_Audio"
        
        output_va_dir.mkdir(exist_ok=True)
        output_subs_dir.mkdir(exist_ok=True)
        output_audio_dir.mkdir(exist_ok=True)

        # Copy to temp for processing
        shutil.copy2(organized_path, processing_file)
        logger.info(f"📄 Created temp file: {processing_file.name}")

        # If source is not MKV, convert temp file to MKV format for proper extraction
        if not organized_path.suffix.lower() == '.mkv':
            mkv_temp_file = temp_dir / f"{organized_path.stem}_TEMP.mkv"
            mkvmerge_path = "C:/Program Files/MKVToolNix/mkvmerge.exe"
            
            # Convert to MKV format preserving all tracks
            convert_cmd = [
                mkvmerge_path, 
                "-o", str(mkv_temp_file),
                str(processing_file)
            ]
            
            logger.info(f"🔄 Converting {organized_path.suffix} to MKV format for extraction...")
            result = subprocess.run(convert_cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                # Replace temp file with converted MKV version
                processing_file.unlink()  # Remove original format temp file
                processing_file = mkv_temp_file
                logger.info(f"✅ Converted temp file to MKV: {processing_file.name}")
            else:
                logger.warning(f"⚠️ Failed to convert to MKV, using original format: {result.stderr}")

        # Get track info
        mkv_info = get_mkv_info(processing_file, settings)
        if not mkv_info:
            raise ValueError("Failed to get MKV info")

        # Clean file (like working version)
        clean_mkv_title_and_tags(processing_file, settings)
        clean_attachments_iterative(processing_file, settings)
        
        # Re-inspect after cleaning
        mkv_info = get_mkv_info(processing_file, settings)
        if not mkv_info:
            raise ValueError("Failed to re-inspect after cleaning")

        # Select tracks (simplified)
        video_tracks = [t for t in mkv_info["tracks"] if t.get("type") == "video"]
        if not video_tracks:
            raise ValueError("No video tracks found")
        selected_video = video_tracks[0]

        # Select audio track
        selected_audio = select_audio_track(mkv_info, preferred_lang="eng", original_lang_from_master=show_language_map.get(movie.get("cleaned_title")))
        if not selected_audio:
            raise ValueError("No audio track found")

        # Set track properties
        set_track_properties_by_uid(processing_file, selected_video["properties"].get("uid"), language="und", name="", settings=settings)
        audio_name = get_audio_track_name(selected_audio)
        set_track_properties_by_uid(processing_file, selected_audio["properties"].get("uid"),
                                   language=selected_audio["properties"].get("language", "und"), name=audio_name, settings=settings)

        # Select and extract subtitles
        subtitle_tracks_to_extract = select_subtitle_tracks(mkv_info, preferred_lang="eng", processing_file_path=processing_file, settings=settings)
        
        # Create clean V/A MKV
        output_va_mkv_path = output_va_dir / f"{organized_path.stem}.processed.mkv"
        if not create_clean_video_audio_mkv(processing_file, output_va_mkv_path, selected_video.get("id"), selected_audio.get("id"), settings):
            raise ValueError("Failed to create clean V/A MKV")
        logger.info(f"✅ Created clean V/A: {output_va_mkv_path.name}")

        # Extract subtitles
        has_image_subtitles = False
        if subtitle_tracks_to_extract:
            success, extracted_files_map = extract_subtitle_tracks_individually(processing_file, organized_path.stem, output_subs_dir, subtitle_tracks_to_extract, settings)
            if success:
                logger.info(f"✅ Extracted {len(extracted_files_map)} subtitle tracks")
                
                # Check for image-based subtitles
                image_codecs = {"S_HDMV/PGS", "S_VOBSUB", "S_DVD/PGS", "S_DVD/VOBSUB"}
                for track in subtitle_tracks_to_extract:
                    codec = track["properties"].get("codec_id", "").upper()
                    if any(img_codec in codec for img_codec in image_codecs):
                        has_image_subtitles = True
                        break

        # EXTRACT ALL AUDIO TRACKS (CORE REQUIREMENT FROM WORKING VERSION)
        logger.info(f"🎵 Extracting ALL audio tracks for manual review...")
        
        # Simple codec mapping (from working version)
        codec_extensions = {
            "A_TRUEHD": "thd",
            "A_DTS": "dts",
            "A_AC3": "ac3", 
            "A_EAC3": "eac3",
            "A_AAC": "aac",
            "A_FLAC": "flac",
            "A_OPUS": "opus",
            "A_VORBIS": "ogg",
            "TrueHD": "thd",
            "DTS": "dts",
            "AC-3": "ac3",
            "E-AC-3": "eac3"
        }

        # Direct audio extraction (like working version)
        audio_tracks = [t for t in mkv_info["tracks"] if t.get("type") == "audio"]
        mkvextract_path = "C:/Program Files/MKVToolNix/mkvextract.exe"
        
        for track in audio_tracks:
            try:
                track_id = track.get("id")
                props = track.get("properties", {})
                codec = props.get("codec_id", "unknown")
                language = props.get("language", "und")
                track_name = props.get("track_name", f"Track_{track_id}")
                
                # Get extension  
                ext = codec_extensions.get(codec, "mka")
                
                # Create filename (like working version)
                safe_name = track_name.replace(' ', '_').replace('/', '_')[:30]
                output_file = output_audio_dir / f"{safe_name}_{language}_{codec}.{ext}"
                
                # Direct mkvextract command - use temp processing file instead of original
                cmd = [mkvextract_path, str(processing_file), 'tracks', f"{track_id}:{output_file}"]
                
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
                
                if result.returncode == 0:
                    logger.info(f"✅ Extracted audio: {output_file.name}")
                else:
                    logger.error(f"❌ Failed audio track {track_id}: {result.stderr}")
                    
            except Exception as e:
                logger.error(f"❌ Exception extracting audio track {track_id}: {e}")

        # Verify audio extraction
        audio_files = list(output_audio_dir.glob("*"))
        logger.info(f"📁 Audio extraction complete: {len(audio_files)} files created")

    # MOVE ALL FILES TO NEXT STAGE (CORE REQUIREMENT)
        next_stage_dir.mkdir(parents=True, exist_ok=True)
        logger.info(f"📁 Moving files to: {next_stage_dir}")

        # Move V/A file
        dest_va_file = next_stage_dir / output_va_mkv_path.name
        if output_va_mkv_path.exists():
            shutil.move(str(output_va_mkv_path), str(dest_va_file))
            logger.info(f"✅ Moved V/A: {dest_va_file.name}")

        # Move subtitles
        dest_subs_dir = next_stage_dir / "_Processed_Subtitles"
        if output_subs_dir.exists() and any(output_subs_dir.iterdir()):
            dest_subs_dir.mkdir(exist_ok=True)
            for sub_file in output_subs_dir.iterdir():
                if sub_file.is_file():
                    shutil.move(str(sub_file), str(dest_subs_dir / sub_file.name))
            logger.info(f"✅ Moved subtitles to: {dest_subs_dir}")

        # Move audio files (CORE REQUIREMENT)
        dest_audio_dir = next_stage_dir / "_Processed_Audio"  
        if output_audio_dir.exists() and any(output_audio_dir.iterdir()):
            dest_audio_dir.mkdir(exist_ok=True)
            for audio_file in output_audio_dir.iterdir():
                if audio_file.is_file():
                    shutil.move(str(audio_file), str(dest_audio_dir / audio_file.name))
            logger.info(f"✅ Moved audio files to: {dest_audio_dir}")

        # FILESYSTEM-FIRST: Handle marker files properly (KEEP original movie file in source)
        source_movie_dir = organized_path.parent
        
        # Clear the .organized marker from source directory
        organized_marker = source_movie_dir / '.organized'
        if organized_marker.exists():
            organized_marker.unlink()
            logger.info(f"🗑️ Removed .organized marker from source")
        
        # Set .mkv_complete marker in destination directory ONLY
        mkv_complete_marker = next_stage_dir / '.mkv_complete'
        marker_data = {
            'timestamp': datetime.now().isoformat(),
            'stage': 'mkv_complete',
            'processed_files': {
                'video_audio': dest_va_file.name if dest_va_file.exists() else None,
                'subtitles_dir': '_Processed_Subtitles' if dest_subs_dir.exists() else None,
                'audio_dir': '_Processed_Audio' if dest_audio_dir.exists() else None
            },
            'original_movie_location': str(organized_path)  # Reference to original location
        }
        
        try:
            with open(mkv_complete_marker, 'w') as f:
                import json
                json.dump(marker_data, f, indent=2)
            logger.info(f"✅ Created .mkv_complete marker in destination")
        except Exception as e:
            logger.warning(f"⚠️ Failed to create .mkv_complete marker: {e}")

        # Clean up temp processing file 
        if processing_file.exists():
            processing_file.unlink(missing_ok=True)
            logger.info(f"🗑️ Cleaned up temp file: {processing_file.name}")

        # Clean up empty directories (improved logic)
        cleanup_dirs = [output_va_dir, output_subs_dir, output_audio_dir, temp_dir]
        for cleanup_dir in cleanup_dirs:
            if cleanup_dir.exists():
                try:
                    # Check if directory is empty
                    if not any(cleanup_dir.iterdir()):
                        cleanup_dir.rmdir()
                        logger.info(f"🗑️ Removed empty directory: {cleanup_dir.name}")
                    else:
                        logger.warning(f"⚠️ Directory not empty, keeping: {cleanup_dir.name}")
                except Exception as e:
                    logger.warning(f"⚠️ Could not remove directory {cleanup_dir.name}: {e}")

        # Try to remove source directory if it's empty (after moving everything)
        try:
            if source_movie_dir.exists() and not any(source_movie_dir.iterdir()):
                source_movie_dir.rmdir()
                logger.info(f"🗑️ Removed empty source directory: {source_movie_dir.name}")
        except Exception as e:
            logger.warning(f"⚠️ Could not remove source directory {source_movie_dir.name}: {e}")

        # Determine next status for database update
        next_status = "final_mux_pending"  # Default
        if has_image_subtitles:
            next_status = "subtitle_ocr_pending"
        
        # Clean up after successful completion
        try:
            from _internal.utils.filesystem_first_state_manager import FilesystemFirstStateManager
            fs_manager = FilesystemFirstStateManager(Path.cwd())
            cleanup_success = fs_manager.cleanup_stage_after_completion(source_movie_dir, "mkv_processing")
            if cleanup_success:
                logger.info(f"✅ Cleanup completed after MKV processing")
        except Exception as e:
            logger.warning(f"⚠️ Cleanup failed but processing continues: {e}")

        # CREATE BACKUP OF ORIGINAL FILES AFTER SUCCESSFUL PROCESSING
        logger.info(f"📁 Creating backup of original files after successful processing...")
        try:
            # Determine resolution for backup folder from the movie directory path
            movie_dir_path = Path(movie.get("movie_directory", ""))
            resolution_from_path = None
            for part in movie_dir_path.parts:
                if part in ["4k", "2160p"]:
                    resolution_from_path = "4k"
                    break
                elif part in ["1080p", "720p", "sd_or_unknown"]:
                    resolution_from_path = part
                    break
            
            # Default to 1080p if no resolution detected
            backup_resolution = resolution_from_path or "1080p"
            
            # Only attempt backup if source directory still exists
            if source_movie_dir.exists():
                backup_success = fs_manager.move_original_to_backup(source_movie_dir, backup_resolution)
                if backup_success:
                    logger.info(f"✅ Original movie folder backed up and removed successfully")
                else:
                    logger.warning(f"⚠️ Failed to backup original movie folder")
            else:
                logger.info(f"📂 Source directory already removed during cleanup - backup not needed")
        except Exception as e:
            logger.warning(f"⚠️ Error backing up original folder: {e}")

        logger.info(f"✅ Processing complete - original files preserved in backup")
        logger.info(f"🎯 All processed files moved to: {next_stage_dir}")

        return True, next_status  # Return both success and next status

    except Exception as e:
        logger.error(f"❌ Processing failed for {movie.get('cleaned_title')}: {e}")
        
        # Cleanup temp file and directory on error
        try:
            if 'processing_file' in locals() and processing_file and processing_file.exists():
                processing_file.unlink(missing_ok=True)
                logger.info(f"🗑️ Cleaned up temp file after error: {processing_file.name}")
            
            # Also cleanup temp directory if empty
            if 'temp_dir' in locals() and temp_dir and temp_dir.exists():
                if not any(temp_dir.iterdir()):
                    temp_dir.rmdir()
                    logger.info(f"🗑️ Removed empty temp directory after error: {temp_dir.name}")
        except Exception as cleanup_error:
            logger.warning(f"⚠️ Cleanup error: {cleanup_error}")
        
    return False, None


def process_single_episode_mkv_simple(episode: dict, organized_path: Path, settings: dict, show_language_map: dict) -> tuple[bool, str | None]:
    """
    Process one TV episode:
    - Output video: SxxEyy.processed.mkv
    - Output subs/audio: SxxEyy[ _t<trackId>].ext
    - Destination: workspace/3_mkv_cleaned_subtitles_extracted/tv_shows/<res>/<Series (Year)>/Season XX
    - After all episodes under the series are processed (no MKVs left), delete the whole series folder from 2_downloaded_and_organized.
    """
    logger = logging.getLogger(__name__)
    logger.info(f"🎬 Starting TV episode MKV processing: {organized_path.name}")

    try:
        series_title = episode.get("series_title", "Unknown")
        season_number = int(episode.get("season_number", 1))
        episode_number = int(episode.get("episode_number", 1))
        episode_title = episode.get("episode_title", "")

        logger.info(
            f"📺 Processing episode: {series_title} S{season_number:02d}E{episode_number:02d} - {episode_title}"
        )

        # Source paths
        source_episode_dir = organized_path.parent
        series_dir = source_episode_dir.parent
        series_name = series_dir.name

        fs_manager = FilesystemFirstStateManager(Path.cwd())
        next_stage_base = Path("workspace") / "3_mkv_cleaned_subtitles_extracted"

        # Determine resolution from parent of series_dir or path fallback
        res_candidate = series_dir.parent.name if series_dir.parent else ""
        res_lower = res_candidate.lower()
        if "4k" in res_lower or "2160" in res_lower:
            resolution = "4k"
        elif "1080" in res_lower:
            resolution = "1080p"
        elif "720" in res_lower:
            resolution = "720p"
        else:
            pstr = str(organized_path).lower()
            if "4k" in pstr or "2160" in pstr:
                resolution = "4k"
            elif "1080" in pstr:
                resolution = "1080p"
            elif "720" in pstr:
                resolution = "720p"
            else:
                resolution = "sd_or_unknown"

        next_stage_dir = next_stage_base / "tv_shows" / resolution / series_name / f"Season {season_number:02d}"

        # Working dirs
        temp_dir = source_episode_dir / "_temp_mkv_processing"
        output_va_dir = temp_dir / "_VA_MKV"
        output_subs_dir = temp_dir / "_Processed_Subtitles"
        output_audio_dir = temp_dir / "_Processed_Audio"
        for d in (temp_dir, output_va_dir, output_subs_dir, output_audio_dir):
            d.mkdir(parents=True, exist_ok=True)

        # Copy for processing
        processing_file = temp_dir / organized_path.name
        shutil.copy2(organized_path, processing_file)

        # If source is not MKV, convert temp file to MKV format for proper extraction
        if not organized_path.suffix.lower() == '.mkv':
            mkv_temp_file = temp_dir / f"{organized_path.stem}_TEMP.mkv"
            mkvmerge_path = "C:/Program Files/MKVToolNix/mkvmerge.exe"
            
            # Convert to MKV format preserving all tracks
            convert_cmd = [
                mkvmerge_path, 
                "-o", str(mkv_temp_file),
                str(processing_file)
            ]
            
            logger.info(f"🔄 Converting episode {organized_path.suffix} to MKV format for extraction...")
            result = subprocess.run(convert_cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                # Replace temp file with converted MKV version
                processing_file.unlink()  # Remove original format temp file
                processing_file = mkv_temp_file
                logger.info(f"✅ Converted episode temp file to MKV: {processing_file.name}")
            else:
                logger.warning(f"⚠️ Failed to convert episode to MKV, using original format: {result.stderr}")

        mkv_info = get_mkv_info(processing_file, settings)
        if not mkv_info:
            raise ValueError("Failed to analyze episode MKV structure")

        video_tracks = [t for t in mkv_info.get("tracks", []) if t.get("type") == "video"]
        if not video_tracks:
            raise ValueError("No video track found in episode")
        selected_video = video_tracks[0]

        original_lang_hint = show_language_map.get(series_title)
        selected_audio = select_audio_track(
            mkv_info, preferred_lang="eng", original_lang_from_master=original_lang_hint
        )
        if not selected_audio:
            raise ValueError("No suitable audio track found in episode")

        set_track_properties_by_uid(
            processing_file,
            selected_video.get("properties", {}).get("uid"),
            language="und",
            name="",
            settings=settings,
        )
        audio_name = get_audio_track_name(selected_audio)
        set_track_properties_by_uid(
            processing_file,
            selected_audio.get("properties", {}).get("uid"),
            language=selected_audio.get("properties", {}).get("language", "und"),
            name=audio_name,
            settings=settings,
        )

        subtitle_tracks_to_extract = select_subtitle_tracks(
            mkv_info, preferred_lang="eng", processing_file_path=processing_file, settings=settings
        )

        # Output names
        base_stem = organized_path.stem  # e.g., S02E02
        episode_filename = f"{base_stem}.processed.mkv"
        output_va_mkv_path = output_va_dir / episode_filename
        if not create_clean_video_audio_mkv(
            processing_file, output_va_mkv_path, selected_video.get("id"), selected_audio.get("id"), settings
        ):
            raise ValueError("Failed to create clean episode V/A MKV")

        # Extract subs and rename to SxxEyy[ _t<trackId>].ext
        has_image_subtitles = False
        if subtitle_tracks_to_extract:
            success, extracted_files_map = extract_subtitle_tracks_individually(
                processing_file, organized_path.stem, output_subs_dir, subtitle_tracks_to_extract, settings
            )
            if success:
                try:
                    multiple = len(subtitle_tracks_to_extract) > 1
                    if isinstance(extracted_files_map, dict) and extracted_files_map:
                        for tr in subtitle_tracks_to_extract:
                            tid = tr.get("id")
                            old_path = extracted_files_map.get(tid)
                            if not old_path:
                                continue
                            old_p = Path(old_path)
                            new_suffix = old_p.suffix
                            new_name = f"{base_stem}{('_t'+str(tid)) if multiple else ''}{new_suffix}"
                            new_path = output_subs_dir / new_name
                            if old_p.exists():
                                try:
                                    old_p.rename(new_path)
                                except Exception:
                                    shutil.copy2(str(old_p), str(new_path))
                                    old_p.unlink(missing_ok=True)
                    else:
                        files = list(output_subs_dir.glob(f"{organized_path.stem}*"))
                        for idx, f in enumerate(files, start=1):
                            target = output_subs_dir / f"{base_stem}{('_t'+str(idx)) if len(files)>1 else ''}{f.suffix}"
                            try:
                                f.rename(target)
                            except Exception:
                                shutil.copy2(str(f), str(target))
                                f.unlink(missing_ok=True)
                except Exception:
                    pass
                image_codecs = {"S_HDMV/PGS", "S_VOBSUB", "S_DVD/PGS", "S_DVD/VOBSUB"}
                for tr in subtitle_tracks_to_extract:
                    codec = tr.get("properties", {}).get("codec_id", "").upper()
                    if any(img in codec for img in image_codecs):
                        has_image_subtitles = True
                        break

        # Extract ALL audio tracks to SxxEyy[ _t<trackId>].ext
        codec_extensions = {
            "A_TRUEHD": "thd",
            "A_DTS": "dts",
            "A_AC3": "ac3",
            "A_EAC3": "eac3",
            "A_AAC": "aac",
            "A_FLAC": "flac",
            "A_OPUS": "opus",
            "A_VORBIS": "ogg",
            "TrueHD": "thd",
            "DTS": "dts",
            "AC-3": "ac3",
            "E-AC-3": "eac3",
        }
        audio_tracks = [t for t in mkv_info.get("tracks", []) if t.get("type") == "audio"]
        mkvextract_path = "C:/Program Files/MKVToolNix/mkvextract.exe"
        for tr in audio_tracks:
            try:
                track_id = tr.get("id")
                props = tr.get("properties", {})
                codec = props.get("codec_id", "unknown")
                ext = codec_extensions.get(codec, "mka")
                multiple_audio = len(audio_tracks) > 1
                base_name = f"{base_stem}{('_t'+str(track_id)) if multiple_audio else ''}.{ext}"
                output_file = output_audio_dir / base_name
                cmd = [mkvextract_path, str(processing_file), "tracks", f"{track_id}:{output_file}"]
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
                if result.returncode == 0:
                    logger.info(f"✅ Extracted episode audio: {output_file.name}")
                else:
                    logger.error(f"❌ Failed episode audio track {track_id}: {result.stderr}")
            except Exception as ex:
                logger.error(f"❌ Exception extracting episode audio track {track_id}: {ex}")

        # Move results to destination
        next_stage_dir.mkdir(parents=True, exist_ok=True)
        dest_va_file = next_stage_dir / output_va_mkv_path.name
        if output_va_mkv_path.exists():
            shutil.move(str(output_va_mkv_path), str(dest_va_file))
        dest_subs_dir = next_stage_dir / "_Processed_Subtitles"
        if output_subs_dir.exists() and any(output_subs_dir.iterdir()):
            dest_subs_dir.mkdir(exist_ok=True)
            for sub_file in output_subs_dir.iterdir():
                if sub_file.is_file():
                    shutil.move(str(sub_file), str(dest_subs_dir / sub_file.name))
        dest_audio_dir = next_stage_dir / "_Processed_Audio"
        if output_audio_dir.exists() and any(output_audio_dir.iterdir()):
            dest_audio_dir.mkdir(exist_ok=True)
            for af in output_audio_dir.iterdir():
                if af.is_file():
                    shutil.move(str(af), str(dest_audio_dir / af.name))

        # Cleanup working dirs
        for d in (output_va_dir, output_subs_dir, output_audio_dir, temp_dir):
            try:
                if d.exists() and not any(d.iterdir()):
                    d.rmdir()
            except Exception:
                pass

        next_status = "subtitle_ocr_pending" if has_image_subtitles else "final_mux_pending"

        # Backup episode originals and delete series folder when finished
        try:
            backup_base = fs_manager.stage_directories.get(
                "temp_backup", Path("workspace") / "temp_original_backup"
            )
            backup_dest_dir = backup_base / "tv_shows" / resolution / series_name / f"Season {season_number:02d}"
            backup_dest_dir.mkdir(parents=True, exist_ok=True)

            # Count series MKVs before
            try:
                series_mkvs_before = len(list(series_dir.rglob("*.mkv")))
            except Exception:
                series_mkvs_before = 0

            # Copy episode file and sidecars by stem, then delete originals
            sidecar_exts = {".mkv", ".mp4", ".m4v", ".avi", ".srt", ".ass", ".sub", ".idx", ".nfo", ".txt"}
            files_backed_up = 0
            for item in list(source_episode_dir.iterdir()):
                if item.is_file() and item.stem == base_stem and item.suffix.lower() in sidecar_exts:
                    shutil.copy2(str(item), str(backup_dest_dir / item.name))
                    try:
                        item.unlink()
                    except Exception as del_ex:
                        logger.warning(f"⚠️ Could not delete original file {item.name}: {del_ex}")
                    files_backed_up += 1
            logger.info(f"✅ Backed up {files_backed_up} original episode files to: {backup_dest_dir}")

            # Remove temp processing dir if present
            try:
                work_tmp = source_episode_dir / "_temp_mkv_processing"
                if work_tmp.exists():
                    shutil.rmtree(work_tmp, ignore_errors=True)
            except Exception:
                pass

            # Series-level cleanup: delete entire series when no MKVs remain
            try:
                series_mkvs_after = len(list(series_dir.rglob("*.mkv")))
                remaining = max(series_mkvs_after, 0)
                if series_mkvs_before:
                    logger.info(f"📊 Series progress after this episode: {remaining} remaining of {series_mkvs_before}")
                else:
                    logger.info(f"📊 Series progress after this episode: {remaining} remaining")
                if remaining == 0:
                    shutil.rmtree(series_dir, ignore_errors=True)
                    logger.info(f"🗑️ Deleted entire series folder from downloaded_and_organized: {series_dir}")
            except Exception as prune_ex:
                logger.warning(f"⚠️ Series pruning check warning: {prune_ex}")
        except Exception as ex:
            logger.warning(f"⚠️ Error backing up original episode files: {ex}")

        # Destination marker
        try:
            fs_manager.set_stage_marker(
                next_stage_dir,
                "mkv_complete",
                {
                    "completion_time": datetime.now(timezone.utc).isoformat(),
                    "next_status": next_status,
                    "episode_info": {
                        "series_title": series_title,
                        "season_number": season_number,
                        "episode_number": episode_number,
                        "episode_title": episode_title,
                    },
                },
            )
        except Exception:
            pass

        logger.info(
            f"✅ Completed episode MKV processing for {series_title} S{season_number:02d}E{episode_number:02d}; files moved to {next_stage_dir}"
        )
        return True, next_status

    except Exception as e:
        logger.error(f"❌ Error in episode MKV processing for {organized_path}: {e}", exc_info=True)
        try:
            if 'processing_file' in locals() and processing_file and processing_file.exists():
                processing_file.unlink(missing_ok=True)
            if 'temp_dir' in locals() and temp_dir and temp_dir.exists() and not any(temp_dir.iterdir()):
                temp_dir.rmdir()
        except Exception:
            pass
        return False, None

# --- Main Stage Function (called by Orchestrator) ---

async def run_mkv_processor_stage(movies_data_list: list, settings: dict, main_logger: logging.Logger, mcp_manager=None) -> bool:
    """
    MCP-Enhanced Stage 03: Intelligent MKV processing with track optimization and quality learning.

    This stage operates purely with SQLite database - no legacy format conversion.
    Returns boolean success/failure instead of movies list.

    MCP Enhancements:
    - Sequential task breakdown for complex MKV operations
    - Memory-based learning for optimal track selection
    - Intelligent error handling with automatic retry strategies
    - Performance tracking and bottleneck detection
    - Quality metrics collection and analysis
    - SQLite-based state management for reliability

    Args:
        movies_data_list: Legacy parameter (ignored - stage works with SQLite directly)
        settings: Pipeline settings
        main_logger: Logger instance
        mcp_manager: MCP manager instance

    Returns:
        bool: True if stage completed successfully, False otherwise
    """
    global logger
    logger = main_logger

    logger.info("===== Starting MCP-Enhanced Stage 03: MKV Processor with SQLite =====")
    logger.info(f"🔍 DEBUG: Received {len(movies_data_list)} movies for processing")

    # Initialize SQLite state manager
    workspace_root = Path.cwd()
    sqlite_manager = FilesystemFirstStateManager(workspace_root)

    # Sync database with filesystem at start of stage
    logger.info("Synchronizing database with filesystem...")
    sync_results = sqlite_manager.discover_movies_by_stage()
    total_movies = sum(len(movies) for movies in sync_results.values())
    logger.info(f"Found {total_movies} movies across {len(sync_results)} stages")

    # Initialize MCP services for this stage
    sequential_service = mcp_manager.services.get('sequential_thinking') if mcp_manager else None
    memory_service = mcp_manager.services.get('memory_manager') if mcp_manager else None
    github_service = mcp_manager.services.get('github_integration') if mcp_manager else None

    # Create sequential thinking task for the entire MKV processing batch
    mkv_batch_task_id = None
    if sequential_service:
        mkv_batch_task_id = await sequential_service.create_task(
            task_type="mkv_processing_batch",
            movie_id="batch_mkv_processor",
            pipeline_stage="03_mkv_processor",
            custom_steps=[
                "Load MKV processing preferences from memory",
                "Identify movies for processing",
                "Process each MKV with intelligent track selection",
                "Collect quality metrics and processing statistics",
                "Update memory with processing results",
                "Handle errors with intelligent retry logic"
            ]
        )
        if mkv_batch_task_id:
            await sequential_service.start_task(mkv_batch_task_id)
            logger.info(f"Created MCP sequential task: {mkv_batch_task_id}")

    try:
        # --- 1. Load MKV Processing Preferences from Memory ---
        processing_preferences = {}
        track_selection_patterns = {}

        if memory_service:
            processing_preferences = await memory_service.retrieve_memory(
                category="mkv_processing_preferences",
                key="user_defaults"
            ) or {
                "preferred_audio_language": "eng",
                "preferred_subtitle_language": "eng",
                "extract_all_subtitles": True,
                "clean_attachments": True,
                "clean_tags": True
            }

            track_selection_patterns = await memory_service.retrieve_memory(
                category="track_selection_patterns",
                key="historical_data"
            ) or {
                "audio_language_preferences": ["eng", "jpn", "kor"],
                "audio_codec_preferences": ["TrueHD Atmos", "DTS-HD MA", "AC-3"],
                "subtitle_language_preferences": ["eng"],
                "common_audio_patterns": {}
            }

            logger.info(f"Loaded MKV processing preferences and track selection patterns from memory")

        # Get movies from filesystem discovery that need MKV processing
        logger.info("🔍 Discovering movies ready for MKV processing using filesystem-first approach...")
        
        # Use filesystem discovery instead of passed movie data
        sync_results = sqlite_manager.discover_movies_by_stage()
        
        # Find movies that are ready for MKV processing
        # Check both 'organized' and 'mkv_processing_pending' stages
        movies_to_process_sqlite = []
        movies_to_process_sqlite.extend(sync_results.get('organized', []))
        movies_to_process_sqlite.extend(sync_results.get('mkv_processing_pending', []))
        
        logger.info(f"Found {len(movies_to_process_sqlite)} movies ready for MKV processing")

        # FILESYSTEM-FIRST FILTERING: Only process movies that have the right markers
        movies_to_process = []
        for movie_data in movies_to_process_sqlite:
            movie_id = movie_data.get("unique_id")
            title = movie_data.get("cleaned_title", "Unknown")
            year = movie_data.get("year", "Unknown")
            movie_dir = Path(movie_data.get("movie_directory", ""))
            
            # Check filesystem markers instead of database status
            needs_processing = False
            reason = ""
            
            if not movie_dir.exists():
                logger.warning(f"⚠️ Movie directory not found: {movie_dir}")
                continue
            
            # Check for organized marker (ready for MKV processing)
            if (movie_dir / '.organized').exists():
                # Make sure we're not already processing or complete
                if (movie_dir / '.mkv_processing').exists():
                    logger.info(f"🔄 {title} ({year}): MKV processing already in progress")
                    continue
                elif (movie_dir / '.mkv_complete').exists():
                    logger.info(f"✅ {title} ({year}): MKV processing already complete")
                    continue
                else:
                    needs_processing = True
                    reason = "Ready for MKV processing (has .organized marker)"
            
            # Also check for movies without markers but with MKV files (fallback)
            elif not any((movie_dir / marker).exists() for marker in ['.organized', '.mkv_processing', '.mkv_complete']):
                # Check if there are MKV files to process
                mkv_files = list(movie_dir.glob("*.mkv"))
                if mkv_files and not any('processed' in f.name.lower() for f in mkv_files):
                    needs_processing = True
                    reason = "Has unprocessed MKV files (no markers present)"
            
            if needs_processing:
                movies_to_process.append(movie_data)
                logger.info(f"📁 {title} ({year}): {reason}")
            else:
                logger.debug(f"⏭️ Skipping {title} ({year}): not ready for MKV processing")

        if not movies_to_process:
            logger.info("No movies currently need MKV processing.")
            logger.info("All movies are either completed or have valid processed files.")

            # Complete MCP task if no work to do
            if sequential_service and mkv_batch_task_id:
                await sequential_service.complete_task(mkv_batch_task_id)

            # Cleanup (filesystem manager doesn't need explicit closing)

            return True

        logger.info(f"Found {len(movies_to_process)} movie(s) for MKV processing with filesystem-first approach.")

        # Helper function to set filesystem markers instead of database status
        def set_processing_marker(movie_dir: Path, marker_name: str, data: dict = None):
            """Set a filesystem marker file for processing stage"""
            try:
                sqlite_manager.set_stage_marker(movie_dir, marker_name, data)
                logger.debug(f"Set {marker_name} marker for {movie_dir.name}")
            except Exception as e:
                logger.error(f"Error setting {marker_name} marker: {e}")

        def clear_processing_marker(movie_dir: Path, marker_name: str):
            """Clear a filesystem marker file"""
            try:
                sqlite_manager.clear_stage_marker(movie_dir, marker_name)
                logger.debug(f"Cleared {marker_name} marker for {movie_dir.name}")
            except Exception as e:
                logger.error(f"Error clearing {marker_name} marker: {e}")

        # Load the master language list once
        master_list_path = get_path_setting("Paths", "movies_master_list_path", settings_dict=settings)
        if master_list_path is None:
            # Fallback if settings not found
            master_list_path = Path("config/movies_master_list.txt")
            logger.warning(f"Using fallback master list path: {master_list_path}")
        
        show_language_map = parse_master_list(master_list_path) or {}

        # MCP Enhancement: Track batch processing statistics
        batch_stats = {
            "start_time": datetime.now(timezone.utc).isoformat(),
            "total_movies": len(movies_to_process),
            "successful_processing": 0,
            "failed_processing": 0,
            "processing_times": [],
            "track_selection_stats": {
                "audio_tracks_selected": [],
                "subtitle_tracks_extracted": [],
                "video_quality_detected": []
            }
        }

        for movie_data in movies_to_process:
            movie_start_time = time.time()
            # Handle missing unique_id by generating one from title and year
            movie_id = movie_data.get("unique_id") or f"{movie_data.get('cleaned_title', 'Unknown')}_{movie_data.get('year', '0000')}"
            title_log_str = f"'{movie_data.get('cleaned_title')}' ({movie_id})"
            movie_dir = Path(movie_data.get("movie_directory", ""))

            # MCP Enhancement: Create individual movie processing task
            movie_task_id = None
            if sequential_service:
                movie_task_id = await sequential_service.create_task(
                    task_type="mkv_processing_single",
                    movie_id=movie_id,
                    pipeline_stage="03_mkv_processor",
                    custom_steps=[
                        "Validate input file",
                        "Analyze track structure",
                        "Select optimal tracks",
                        "Clean metadata and attachments",
                        "Create clean video/audio MKV",
                        "Extract subtitle tracks",
                        "Update processing statistics"
                    ]
                )
                if movie_task_id:
                    await sequential_service.start_task(movie_task_id)

            # Set .mkv_processing marker (filesystem-first approach)
            set_processing_marker(movie_dir, "mkv_processing", {
                "start_time": datetime.now(timezone.utc).isoformat(),
                "movie_id": movie_id
            })

            # Discover video files using filesystem scanning instead of stored paths
            from _internal.src.fs_helpers import find_video_files
            video_files = find_video_files(movie_dir)
            # Filter out already processed files and only include files directly in the movie directory
            unprocessed_video_files = [f for f in video_files 
                                     if f.parent == movie_dir and 'processed' not in f.name.lower()]
            
            if not unprocessed_video_files:
                logger.error(f"Cannot process {title_log_str}: No unprocessed video files found in {movie_dir}")
                organized_mkv_path = None
            else:
                # Use the first unprocessed video file found (will be processed into MKV format)
                organized_mkv_path = unprocessed_video_files[0]
                logger.info(f"Found video file to process: {organized_mkv_path.name}")

            if not organized_mkv_path:
                logger.error(f"Cannot process {title_log_str}: 'organized_mkv_path' is missing.")

                # MCP Enhancement: Track validation errors
                if memory_service:
                    await memory_service.store_memory(
                        category="mkv_processing_errors",
                        key=f"validation_error_{movie_id}",
                        value={
                            "movie_id": movie_id,
                            "title": movie_data.get("cleaned_title"),
                            "error": "No unprocessed video files found",
                            "timestamp": datetime.now(timezone.utc).isoformat()
                        },
                        tags=["error", "validation", "stage03", "mkv"]
                    )

                set_processing_marker(movie_dir, "error", {
                    "error_message": "No unprocessed video files found in movie directory."
                })

                if sequential_service and movie_task_id:
                    await sequential_service.fail_task(movie_task_id, "No unprocessed video files found")

                batch_stats["failed_processing"] += 1
                continue

            if not organized_mkv_path.is_file():
                logger.error(f"Cannot process {title_log_str}: File not found at '{organized_mkv_path}'.")

                # MCP Enhancement: Track file not found errors
                if memory_service:
                    await memory_service.store_memory(
                        category="mkv_processing_errors",
                        key=f"file_not_found_{movie_id}",
                        value={
                            "movie_id": movie_id,
                            "title": movie_data.get("cleaned_title"),
                            "error": f"File not found at {organized_mkv_path}",
                            "timestamp": datetime.now(timezone.utc).isoformat()
                        },
                        tags=["error", "file_not_found", "stage03", "mkv"]
                    )

                set_processing_marker(movie_dir, "error", {
                    "error_message": f"File not found at specified path: {organized_mkv_path}"
                })

                if sequential_service and movie_task_id:
                    await sequential_service.fail_task(movie_task_id, f"File not found: {organized_mkv_path}")

                batch_stats["failed_processing"] += 1
                continue

            # --- Call the SIMPLIFIED processing helper (WORKING VERSION APPROACH) ---
            success, next_status = process_single_mkv_simple(movie_data, organized_mkv_path, settings, show_language_map)

            # Track processing time
            processing_time = time.time() - movie_start_time
            batch_stats["processing_times"].append(processing_time)

            if success:
                logger.info(f"Successfully finished MKV processing for {title_log_str} in {processing_time:.2f}s.")
                batch_stats["successful_processing"] += 1

                # Clear the processing marker (simple function handles completion markers)
                clear_processing_marker(movie_dir, "mkv_processing")
                
                # NOTE: process_single_mkv_simple() already creates the .mkv_complete marker 
                # in the correct destination directory, so no need to create it again here

                # Complete individual movie task
                if sequential_service and movie_task_id:
                    await sequential_service.complete_task(movie_task_id)
            else:
                logger.error(f"MKV processing failed for {title_log_str}. Check logs for details.")
                batch_stats["failed_processing"] += 1

                # Clear the processing marker and set error marker
                clear_processing_marker(movie_dir, "mkv_processing")
                set_processing_marker(movie_dir, "error", {
                    "error_message": "MKV processing failed"
                })

                # Fail individual movie task
                if sequential_service and movie_task_id:
                    await sequential_service.fail_task(movie_task_id, "MKV processing failed")

        # MCP Enhancement: Update batch processing statistics
        batch_stats["end_time"] = datetime.now(timezone.utc).isoformat()
        batch_stats["total_processing_time"] = sum(batch_stats["processing_times"])
        batch_stats["average_processing_time"] = sum(batch_stats["processing_times"]) / len(batch_stats["processing_times"]) if batch_stats["processing_times"] else 0
        batch_stats["success_rate"] = (batch_stats["successful_processing"] / batch_stats["total_movies"] * 100) if batch_stats["total_movies"] > 0 else 0

        if memory_service:
            await memory_service.store_memory(
                category="mkv_processing_batch_stats",
                key=f"batch_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                value=batch_stats,
                tags=["stats", "batch", "stage03", "mkv"]
            )

            logger.info(f"MKV Processing Statistics: {batch_stats['successful_processing']}/{batch_stats['total_movies']} successful ({batch_stats['success_rate']:.1f}%)")

        # Complete MCP batch task
        if sequential_service and mkv_batch_task_id:
            await sequential_service.complete_task(mkv_batch_task_id)
            logger.info(f"Completed MCP sequential task: {mkv_batch_task_id}")

        logger.info("===== Finished MCP-Enhanced Stage 03: MKV Processor =====")

        logger.info("===== Finished MCP-Enhanced Stage 03: MKV Processor =====")

        # Get final count for logging based on filesystem state
        completed_movies = []
        stage_dirs = sqlite_manager.stage_directories
        
        for stage_dir in stage_dirs.values():
            if stage_dir.exists():
                for movie_dir in stage_dir.iterdir():
                    if movie_dir.is_dir() and (movie_dir / ".mkv_complete").exists():
                        completed_movies.append(movie_dir.name)
        
        logger.info(f"Stage completed successfully - {len(completed_movies)} movies processed")

        # Cleanup (filesystem manager doesn't need explicit closing)

        return True

    except Exception as e:
        logger.error(f"Critical error in MCP-enhanced Stage 03: {e}")

        # MCP Enhancement: Handle stage-level errors
        if github_service:
            await github_service.create_issue(
                title=f"Stage 03 Critical Failure: {str(e)[:50]}...",
                body=f"**Stage 03 Critical Failure:**\n\n```\n{str(e)}\n```\n\n**Stage:** 03_mkv_processor\n**Timestamp:** {datetime.now(timezone.utc).isoformat()}\n\n**Impact:** MKV processing stage failed",
                labels=["critical", "stage-03", "pipeline-failure"]
            )

        if sequential_service and mkv_batch_task_id:
            await sequential_service.fail_task(mkv_batch_task_id, str(e))

        # Cleanup on error (filesystem manager doesn't need explicit closing)

        return False


async def run_tv_mkv_processor_stage(tv_episodes_data_list: list, settings: dict, main_logger: logging.Logger, mcp_manager=None) -> bool:
    """
    MCP-Enhanced Stage 03: Intelligent TV episode MKV processing with season/episode organization.

    TV Show equivalent of run_mkv_processor_stage() with sophisticated episode handling:
    - Episode-based discovery and processing
    - Season/series directory organization
    - Sonarr integration for TV show episodes
    - Same MCP enhancements as movie processing
    - Episode-specific track optimization and quality learning

    Args:
        tv_episodes_data_list: Legacy parameter (ignored - stage works with filesystem discovery)
        settings: Pipeline settings
        main_logger: Logger instance
        mcp_manager: MCP manager instance

    Returns:
        bool: True if stage completed successfully, False otherwise
    """
    logger = main_logger
    logger.info("===== Starting MCP-Enhanced Stage 03: TV Episode MKV Processor =====")

    # Initialize services and IDs up front (must exist for except path)
    sequential_service = None
    memory_service = None
    github_service = None
    episode_batch_task_id = None

    try:
        # Local imports
        from _internal.utils.filesystem_first_state_manager import FilesystemFirstStateManager

    # Filesystem-first manager for TV shows
        fs_manager = FilesystemFirstStateManager(Path.cwd())

        # Initialize optional MCP services if available
        if mcp_manager is not None:
            if hasattr(mcp_manager, "get_service"):
                try:
                    sequential_service = mcp_manager.get_service("sequential_thinking")
                    memory_service = mcp_manager.get_service("memory_manager")
                    github_service = mcp_manager.get_service("github_integration")
                except Exception:
                    pass
            elif hasattr(mcp_manager, "services"):
                try:
                    sequential_service = mcp_manager.services.get("sequential_thinking")
                    memory_service = mcp_manager.services.get("memory_manager")
                    github_service = mcp_manager.services.get("github_integration")
                except Exception:
                    pass

        # Create sequential task for episode batch processing
        if sequential_service:
            episode_batch_task_id = await sequential_service.create_task(
                task_type="tv_mkv_processing_batch",
                pipeline_stage="03_mkv_processor",
                custom_steps=[
                    "Initialize TV episode discovery",
                    "Load processing preferences from memory",
                    "Discover episodes ready for MKV processing",
                    "Process episodes with track optimization",
                    "Update episode processing statistics",
                    "Complete batch processing",
                ],
            )
            if episode_batch_task_id:
                await sequential_service.start_task(episode_batch_task_id)

        # Load TV episode processing preferences and patterns
        processing_preferences: dict = {}
        track_selection_patterns: dict = {}

        if memory_service:
            processing_preferences = await memory_service.retrieve_memory(
                category="tv_episode_processing_preferences", key="user_defaults"
            ) or {
                "preferred_audio_language": "eng",
                "preferred_subtitle_language": "eng",
                "extract_all_subtitles": True,
                "clean_attachments": True,
                "clean_tags": True,
                "season_organization": True,
            }

            track_selection_patterns = await memory_service.retrieve_memory(
                category="tv_track_selection_patterns", key="historical_data"
            ) or {
                "audio_language_preferences": ["eng", "jpn", "kor"],
                "audio_codec_preferences": ["TrueHD Atmos", "DTS-HD MA", "AC-3"],
                "subtitle_language_preferences": ["eng"],
                "common_episode_patterns": {},
                "series_specific_patterns": {},
            }

            logger.info("Loaded TV episode MKV processing preferences from memory")

        # Discover TV episodes (filesystem-first)
        logger.info("🔍 Discovering TV episodes ready for MKV processing using filesystem-first approach...")
        sync_results = fs_manager.discover_tv_episodes_by_stage()

        # Find episodes ready for MKV processing
        episodes_to_process_sqlite: list = []
        episodes_to_process_sqlite.extend(sync_results.get("organized", []))
        episodes_to_process_sqlite.extend(sync_results.get("mkv_processing_pending", []))

        logger.info(f"Found {len(episodes_to_process_sqlite)} TV episodes ready for MKV processing")

        # Filter by filesystem markers per-episode
        episodes_to_process: list = []
        for episode_data in episodes_to_process_sqlite:
            series_title = episode_data.get("series_title", "Unknown")
            season_num = episode_data.get("season_number", 1)
            episode_num = episode_data.get("episode_number", 1)
            episode_dir = Path(episode_data.get("episode_directory", ""))

            needs_processing = False
            reason = ""

            if not episode_dir.exists():
                logger.warning(f"⚠️ Episode directory not found: {episode_dir}")
                continue

            if (episode_dir / ".organized").exists():
                if (episode_dir / ".mkv_processing").exists():
                    logger.info(
                        f"🔄 {series_title} S{season_num:02d}E{episode_num:02d}: MKV processing in progress"
                    )
                    continue
                if (episode_dir / ".mkv_complete").exists():
                    logger.info(
                        f"✅ {series_title} S{season_num:02d}E{episode_num:02d}: MKV processing complete"
                    )
                    continue
                needs_processing = True
                reason = "Ready for episode MKV processing (has .organized marker)"
            elif not any(
                (episode_dir / marker).exists()
                for marker in [".organized", ".mkv_processing", ".mkv_complete"]
            ):
                mkv_files = list(episode_dir.glob("*.mkv"))
                if mkv_files and not any("processed" in f.name.lower() for f in mkv_files):
                    needs_processing = True
                    reason = "Has unprocessed episode MKV files (no markers present)"

            if needs_processing:
                episodes_to_process.append(episode_data)
                logger.info(f"📺 {series_title} S{season_num:02d}E{episode_num:02d}: {reason}")
            else:
                logger.debug(
                    f"⏭️ Skipping {series_title} S{season_num:02d}E{episode_num:02d}: not ready for MKV processing"
                )

        if not episodes_to_process:
            logger.info("No TV episodes currently need MKV processing.")
            logger.info("All episodes are either completed or have valid processed files.")
            if sequential_service and episode_batch_task_id:
                await sequential_service.complete_task(episode_batch_task_id)
            return True

        logger.info(
            f"Found {len(episodes_to_process)} TV episode(s) for MKV processing with filesystem-first approach."
        )

        # Helpers for filesystem markers
        def set_episode_processing_marker(episode_dir: Path, marker_name: str, data: dict | None = None) -> None:
            try:
                fs_manager.set_stage_marker(episode_dir, marker_name, data)
                logger.debug(f"Set {marker_name} marker for episode {episode_dir.name}")
            except Exception as _e:
                logger.error(f"Error setting episode {marker_name} marker: {_e}")

        def clear_episode_processing_marker(episode_dir: Path, marker_name: str) -> None:
            try:
                fs_manager.clear_stage_marker(episode_dir, marker_name)
                logger.debug(f"Cleared {marker_name} marker for episode {episode_dir.name}")
            except Exception as _e:
                logger.error(f"Error clearing episode {marker_name} marker: {_e}")

        # Load language map (reuse movies master list for now)
        master_list_path = get_path_setting("Paths", "movies_master_list_path", settings_dict=settings)
        if master_list_path is None:
            master_list_path = Path("config/movies_master_list.txt")
            logger.warning(f"Using fallback master list path: {master_list_path}")
        show_language_map = parse_master_list(master_list_path) or {}

        # Batch stats
        batch_stats = {
            "start_time": datetime.now(timezone.utc).isoformat(),
            "total_episodes": len(episodes_to_process),
            "successful_processing": 0,
            "failed_processing": 0,
            "processing_times": [],
            "track_selection_stats": {
                "audio_tracks_selected": [],
                "subtitle_tracks_extracted": [],
                "video_quality_detected": [],
            },
            "series_stats": {},
        }

        for episode_data in episodes_to_process:
            episode_start_time = time.time()

            series_title = episode_data.get("series_title", "Unknown")
            season_number = episode_data.get("season_number", 1)
            episode_number = episode_data.get("episode_number", 1)
            episode_id = episode_data.get("unique_id") or f"{series_title}_S{season_number:02d}E{episode_number:02d}"
            episode_log_str = f"'{series_title}' S{season_number:02d}E{episode_number:02d} ({episode_id})"
            episode_dir = Path(episode_data.get("episode_directory", ""))

            episode_task_id = None
            if sequential_service:
                episode_task_id = await sequential_service.create_task(
                    task_type="tv_mkv_processing_single",
                    episode_id=episode_id,
                    pipeline_stage="03_mkv_processor",
                    custom_steps=[
                        "Validate episode input file",
                        "Analyze episode track structure",
                        "Select optimal tracks for episode",
                        "Clean episode metadata and attachments",
                        "Create clean episode video/audio MKV",
                        "Extract episode subtitle tracks",
                        "Update episode processing statistics",
                    ],
                )
                if episode_task_id:
                    await sequential_service.start_task(episode_task_id)

            # Mark processing
            set_episode_processing_marker(
                episode_dir,
                "mkv_processing",
                {
                    "start_time": datetime.now(timezone.utc).isoformat(),
                    "episode_id": episode_id,
                    "series_title": series_title,
                    "season_number": season_number,
                    "episode_number": episode_number,
                },
            )

            # Find source video file (any supported format)
            from _internal.src.fs_helpers import find_video_files
            video_files = find_video_files(episode_dir)
            # Filter to only files directly in the episode directory and exclude processed files
            unprocessed_video_files = [f for f in video_files 
                                     if f.parent == episode_dir and "processed" not in f.name.lower()]
            if not unprocessed_video_files:
                logger.error(
                    f"Cannot process {episode_log_str}: No unprocessed video files found in {episode_dir}"
                )
                organized_mkv_path: Path | None = None
            else:
                organized_mkv_path = unprocessed_video_files[0]
                logger.info(f"Found episode video file to process: {organized_mkv_path.name}")

            if not organized_mkv_path:
                logger.error(f"Cannot process {episode_log_str}: 'organized_mkv_path' is missing.")
                if memory_service:
                    await memory_service.store_memory(
                        category="tv_mkv_processing_errors",
                        key=f"validation_error_{episode_id}",
                        value={
                            "episode_id": episode_id,
                            "series_title": series_title,
                            "season_number": season_number,
                            "episode_number": episode_number,
                            "error": "No unprocessed episode video files found",
                            "timestamp": datetime.now(timezone.utc).isoformat(),
                        },
                        tags=["error", "validation", "stage03", "tv", "mkv"],
                    )
                set_episode_processing_marker(
                    episode_dir, "error", {"error_message": "No unprocessed episode video files found in directory."}
                )
                if sequential_service and episode_task_id:
                    await sequential_service.fail_task(
                        episode_task_id, "No unprocessed episode video files found"
                    )
                batch_stats["failed_processing"] += 1
                continue

            if not organized_mkv_path.is_file():
                logger.error(
                    f"Cannot process {episode_log_str}: File not found at '{organized_mkv_path}'."
                )
                if memory_service:
                    await memory_service.store_memory(
                        category="tv_mkv_processing_errors",
                        key=f"file_not_found_{episode_id}",
                        value={
                            "episode_id": episode_id,
                            "series_title": series_title,
                            "error": f"Episode file not found at {organized_mkv_path}",
                            "timestamp": datetime.now(timezone.utc).isoformat(),
                        },
                        tags=["error", "file_not_found", "stage03", "tv", "mkv"],
                    )
                set_episode_processing_marker(
                    episode_dir,
                    "error",
                    {"error_message": f"Episode file not found at specified path: {organized_mkv_path}"},
                )
                if sequential_service and episode_task_id:
                    await sequential_service.fail_task(
                        episode_task_id, f"Episode file not found: {organized_mkv_path}"
                    )
                batch_stats["failed_processing"] += 1
                continue

            # Process the episode
            success, next_status = process_single_episode_mkv_simple(
                episode_data, organized_mkv_path, settings, show_language_map
            )

            processing_time = time.time() - episode_start_time
            batch_stats["processing_times"].append(processing_time)

            if series_title not in batch_stats["series_stats"]:
                batch_stats["series_stats"][series_title] = {
                    "episodes_processed": 0,
                    "episodes_successful": 0,
                    "episodes_failed": 0,
                    "average_processing_time": 0,
                }
            batch_stats["series_stats"][series_title]["episodes_processed"] += 1

            if success:
                logger.info(
                    f"Successfully finished episode MKV processing for {episode_log_str} in {processing_time:.2f}s."
                )
                batch_stats["successful_processing"] += 1
                batch_stats["series_stats"][series_title]["episodes_successful"] += 1
                clear_episode_processing_marker(episode_dir, "mkv_processing")
                if sequential_service and episode_task_id:
                    await sequential_service.complete_task(episode_task_id)
            else:
                logger.error(
                    f"Episode MKV processing failed for {episode_log_str}. Check logs for details."
                )
                batch_stats["failed_processing"] += 1
                batch_stats["series_stats"][series_title]["episodes_failed"] += 1
                clear_episode_processing_marker(episode_dir, "mkv_processing")
                set_episode_processing_marker(
                    episode_dir, "error", {"error_message": "Episode MKV processing failed"}
                )
                if sequential_service and episode_task_id:
                    await sequential_service.fail_task(episode_task_id, "Episode MKV processing failed")

        # Finalize stats
        batch_stats["end_time"] = datetime.now(timezone.utc).isoformat()
        batch_stats["total_processing_time"] = sum(batch_stats["processing_times"])  # type: ignore[arg-type]
        batch_stats["average_processing_time"] = (
            sum(batch_stats["processing_times"]) / len(batch_stats["processing_times"]) if batch_stats["processing_times"] else 0
        )
        batch_stats["success_rate"] = (
            (batch_stats["successful_processing"] / batch_stats["total_episodes"] * 100)
            if batch_stats["total_episodes"] > 0
            else 0
        )

        if memory_service:
            await memory_service.store_memory(
                category="tv_mkv_processing_batch_stats",
                key=f"batch_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                value=batch_stats,
                tags=["stats", "batch", "stage03", "tv", "mkv"],
            )
            logger.info(
                f"TV Episode MKV Processing Statistics: {batch_stats['successful_processing']}/{batch_stats['total_episodes']} successful ({batch_stats['success_rate']:.1f}%)"
            )
            for series_name, series_stats in batch_stats["series_stats"].items():
                logger.info(
                    f"  {series_name}: {series_stats['episodes_successful']}/{series_stats['episodes_processed']} episodes successful"
                )

        if sequential_service and episode_batch_task_id:
            await sequential_service.complete_task(episode_batch_task_id)
            logger.info(f"Completed MCP sequential task: {episode_batch_task_id}")

        logger.info("===== Finished MCP-Enhanced Stage 03: TV Episode MKV Processor =====")

        # Log number of completed episodes (coarse)
        completed_episodes: list[str] = []
        stage_dirs = fs_manager.stage_directories
        for stage_dir in stage_dirs.values():
            if stage_dir.exists():
                for series_dir in stage_dir.iterdir():
                    if series_dir.is_dir():
                        for season_dir in series_dir.iterdir():
                            if season_dir.is_dir() and (season_dir / ".mkv_complete").exists():
                                completed_episodes.append(f"{series_dir.name}/{season_dir.name}")
        logger.info(f"Stage completed successfully - {len(completed_episodes)} TV episodes processed")

        return True

    except Exception as e:
        logger.error(f"Critical error in MCP-enhanced TV Episode Stage 03: {e}")
        try:
            if github_service:
                await github_service.create_issue(
                    title=f"TV Episode Stage 03 Critical Failure: {str(e)[:50]}...",
                    body=("**TV Episode Stage 03 Critical Failure:**\\n\\n" +
                         "```\\n" + str(e) + "\\n```\\n\\n" +
                         f"**Stage:** 03_mkv_processor (TV Episodes)\\n**Timestamp:** {datetime.now(timezone.utc).isoformat()}\\n\\n" +
                         "**Impact:** TV episode MKV processing stage failed"),
                    labels=["critical", "stage-03", "tv-episodes", "pipeline-failure"],
                )
            if sequential_service and episode_batch_task_id:
                await sequential_service.fail_task(episode_batch_task_id, str(e))
        except Exception:
            pass
        return False


async def _process_single_mkv_enhanced(movie: dict, organized_path: Path, settings: dict, show_language_map: dict,
                                      processing_preferences: dict, track_selection_patterns: dict,
                                      sqlite_manager, memory_service=None, sequential_service=None, movie_task_id=None) -> tuple[bool, dict]:
    """
    MCP-Enhanced single MKV processing with intelligent track selection and quality learning.

    Returns:
        tuple[bool, dict]: (success, processing_metrics)
    """
    movie_id = movie["unique_id"]
    processing_metrics = {
        "selected_audio": None,
        "extracted_subtitles": [],
        "video_quality": None,
        "processing_time": 0,
        "track_analysis": {}
    }

    start_time = time.time()

    try:
        # MANDATORY STAGE COMPLETION: Always ensure processed files end up in the correct next stage folder
        # Script 3's job is to move processed files from 2_downloaded_and_organized to 3_mkv_cleaned_subtitles_extracted
        
        parent_folder = organized_path.parent
        existing_va_dir = parent_folder / "_Processed_VideoAudio"
        existing_subs_dir = parent_folder / "_Processed_Subtitles"
        existing_audio_dir = parent_folder / "_Processed_Audio"
        
        # Check if processed files already exist in source location
        va_files_exist = existing_va_dir.exists() and any(f.name.endswith('.processed.mkv') for f in existing_va_dir.iterdir() if f.is_file())
        sub_files_exist = existing_subs_dir.exists() and any(existing_subs_dir.iterdir())
        audio_files_exist = existing_audio_dir.exists() and any(existing_audio_dir.iterdir())
        
        # CRITICAL FIX: If audio files are missing, force full reprocessing (user requirement)
        if va_files_exist and not audio_files_exist:
            logger.warning(f"⚠️ Audio files missing for {movie.get('cleaned_title')} - forcing full reprocessing")
            # Delete existing processed files to force complete reprocessing
            if existing_va_dir.exists():
                shutil.rmtree(existing_va_dir)
            if existing_subs_dir.exists():
                shutil.rmtree(existing_subs_dir)
            va_files_exist = False
            sub_files_exist = False
            audio_files_exist = False
        
        # Check if files are already in the final destination (3_mkv_cleaned_subtitles_extracted)
        next_stage_base = Path("workspace") / "3_mkv_cleaned_subtitles_extracted"
        resolution = "1080p"  # Default
        if "4K" in str(organized_path.parent):
            resolution = "4K"
        elif "1080p" in str(organized_path.parent):
            resolution = "1080p"
        else:
            resolution = "SD_or_unknown"
        
        next_stage_dir = next_stage_base / resolution / organized_path.parent.name
        # ...existing code that checks va/audio in destination...
        # When already done, return a tuple
        # return True, next_status

        # 1. Setup paths and create a temporary working file (normal processing path)
        parent_folder = organized_path.parent
        temp_dir = parent_folder / "_Temp_Processing"
        temp_dir.mkdir(exist_ok=True)
        processing_file = temp_dir / f"{organized_path.stem}_TEMP{organized_path.suffix}"

        # Create ALL processing directories at the start (user requirement)

        output_va_dir = parent_folder / "_Processed_VideoAudio"
        output_subs_dir = parent_folder / "_Processed_Subtitles"
        output_audio_dir = parent_folder / "_Processed_Audio"  # FIXED: Create at start
        
        output_va_dir.mkdir(exist_ok=True)
        output_subs_dir.mkdir(exist_ok=True)
        output_audio_dir.mkdir(exist_ok=True)  # FIXED: Create immediately
        
        logger.info(f"✅ Created processing directories:")
        logger.info(f"  📁 Video/Audio: {output_va_dir.name}")
        logger.info(f"  📁 Subtitles: {output_subs_dir.name}")
        logger.info(f"  📁 Audio: {output_audio_dir.name}")

        # Copy original to temp for processing
        shutil.copy2(organized_path, processing_file)
        logger.info(f"Created temporary processing file: {processing_file}")

        # 2. Run the enhanced processing chain with MCP intelligence

        # Initial inspection with enhanced analysis
        mkv_info = get_mkv_info(processing_file, settings)
        if not mkv_info:
            raise ValueError("Failed to retrieve initial track info.")

        # MCP Enhancement: Analyze track structure for learning
        track_analysis = {
            "video_tracks": len([t for t in mkv_info["tracks"] if t.get("type") == "video"]),
            "audio_tracks": len([t for t in mkv_info["tracks"] if t.get("type") == "audio"]),
            "subtitle_tracks": len([t for t in mkv_info["tracks"] if t.get("type") == "subtitles"]),
            "total_tracks": len(mkv_info["tracks"]),
            "file_size_mb": round(organized_path.stat().st_size / (1024 * 1024), 2)
        }
        processing_metrics["track_analysis"] = track_analysis

        # Store track analysis for learning
        if memory_service:
            await memory_service.store_memory(
                category="mkv_track_analysis",
                key=f"analysis_{movie_id}",
                value={
                    "movie_id": movie_id,
                    "title": movie.get("cleaned_title"),
                    "track_analysis": track_analysis,
                    "timestamp": datetime.now(timezone.utc).isoformat()
                },
                tags=["analysis", "tracks", "stage03", movie.get("cleaned_title", "").lower().replace(" ", "_")]
            )

        # Cleaning with progress tracking
        if sequential_service and movie_task_id:
            await sequential_service.update_task_progress(movie_task_id, 20, "Cleaning metadata and attachments")

        clean_mkv_title_and_tags(processing_file, settings)
        clean_attachments_iterative(processing_file, settings)

        # Re-inspect after cleaning to get stable track properties
        mkv_info = get_mkv_info(processing_file, settings)
        if not mkv_info:
            raise ValueError("Failed to re-inspect after cleaning.")

        # Enhanced Video Selection with quality detection
        if sequential_service and movie_task_id:
            await sequential_service.update_task_progress(movie_task_id, 40, "Selecting optimal video track")

        video_tracks = [t for t in mkv_info["tracks"] if t.get("type") == "video"]
        if not video_tracks:
            raise ValueError("No video tracks found.")

        selected_video = video_tracks[0]  # Usually only one video track

        # MCP Enhancement: Detect video quality metrics
        video_properties = selected_video.get("properties", {})
        video_quality = {
            "width": video_properties.get("pixel_dimensions", "").split("x")[0] if "x" in video_properties.get("pixel_dimensions", "") else None,
            "height": video_properties.get("pixel_dimensions", "").split("x")[1] if "x" in video_properties.get("pixel_dimensions", "") else None,
            "codec": video_properties.get("codec_id", ""),
            "fps": video_properties.get("default_duration", "")
        }
        processing_metrics["video_quality"] = video_quality

        set_track_properties_by_uid(processing_file, selected_video["properties"].get("uid"), language="und", name="", settings=settings)

        # Enhanced Audio Selection with intelligent preferences
        if sequential_service and movie_task_id:
            await sequential_service.update_task_progress(movie_task_id, 60, "Selecting optimal audio track")

        # Use learned preferences for audio selection
        preferred_audio_lang = processing_preferences.get("preferred_audio_language", "eng")
        original_lang_hint = show_language_map.get(movie.get("cleaned_title"))

        selected_audio = select_audio_track(mkv_info, preferred_lang=preferred_audio_lang, original_lang_from_master=original_lang_hint)
        if not selected_audio:
            raise ValueError("No suitable audio track found.")

        # MCP Enhancement: Learn from audio selection
        audio_selection_data = {
            "selected_language": selected_audio["properties"].get("language", "und"),
            "selected_codec": selected_audio["properties"].get("codec_id", ""),
            "track_name": selected_audio["properties"].get("track_name", ""),
            "channels": selected_audio["properties"].get("audio_channels", ""),
            "movie_title": movie.get("cleaned_title")
        }
        processing_metrics["selected_audio"] = audio_selection_data

        if memory_service:
            await memory_service.store_memory(
                category="audio_selection_patterns",
                key=f"selection_{movie_id}",
                value=audio_selection_data,
                tags=["audio", "selection", "stage03", preferred_audio_lang]
            )

        audio_name = get_audio_track_name(selected_audio)
        set_track_properties_by_uid(processing_file, selected_audio["properties"].get("uid"),
                                   language=selected_audio["properties"].get("language", "und"),
                                   name=audio_name, settings=settings)

        # Re-inspect again before subtitle operations
        mkv_info = get_mkv_info(processing_file, settings) or mkv_info

        # Enhanced Subtitle Selection with learning
        if sequential_service and movie_task_id:
            await sequential_service.update_task_progress(movie_task_id, 80, "Selecting and extracting subtitles")

        preferred_subtitle_lang = processing_preferences.get("preferred_subtitle_language", "eng")
        subtitle_tracks_to_extract = select_subtitle_tracks(mkv_info, preferred_lang=preferred_subtitle_lang,
                                                           processing_file_path=processing_file, settings=settings)

        # Create Clean V/A MKV
        output_va_mkv_path = output_va_dir / f"{organized_path.stem}.processed.mkv"
        if not create_clean_video_audio_mkv(processing_file, output_va_mkv_path, selected_video.get("id"), selected_audio.get("id"), settings):
            raise ValueError("Failed to create clean video/audio MKV.")

        # Extract Subtitles with enhanced tracking
        extracted_sub_files = []
        subtitle_extraction_data = []

        if subtitle_tracks_to_extract:
            success, extracted_files_map = extract_subtitle_tracks_individually(processing_file, organized_path.stem, output_subs_dir, subtitle_tracks_to_extract, settings)
            if not success:
                logger.warning("Some subtitle extractions failed.")
            extracted_sub_files = list(extracted_files_map.values())

            # MCP Enhancement: Track subtitle extraction patterns
            for track in subtitle_tracks_to_extract:
                subtitle_data = {
                    "language": track["properties"].get("language", "und"),
                    "codec": track["properties"].get("codec_id", ""),
                    "track_name": track["properties"].get("track_name", ""),
                    "is_image_based": track["properties"].get("codec_id", "").upper() in {"S_HDMV/PGS", "S_VOBSUB"}
                }
                subtitle_extraction_data.append(subtitle_data)

            processing_metrics["extracted_subtitles"] = subtitle_extraction_data

            if memory_service:
                await memory_service.store_memory(
                    category="subtitle_extraction_patterns",
                    key=f"extraction_{movie_id}",
                    value={
                        "movie_id": movie_id,
                        "title": movie.get("cleaned_title"),
                        "extracted_subtitles": subtitle_extraction_data,
                        "extraction_count": len(subtitle_extraction_data)
                    },
                    tags=["subtitle", "extraction", "stage03", preferred_subtitle_lang]
                )

        # ENHANCED: Determine next status based on subtitle types and user preferences
        next_status = "final_mux_pending"  # Default if no OCR needed
        
        # Comprehensive image-based codec detection (not just PGS and VobSub)
        image_based_codecs = {
            "S_HDMV/PGS", "S_VOBSUB", "S_DVD/PGS", "S_DVD/VOBSUB", 
            "S_HDMV", "S_PGS", "SUP", "VOBSUB", "DVD"
        }
        
        extracted_subtitle_info = []
        has_image_subtitles = False

        for track in subtitle_tracks_to_extract:
            codec = track["properties"].get("codec_id", "").upper()
            track_name = track["properties"].get("track_name", "").lower()
            
            # Check if this codec is image-based (needs OCR)
            is_image_based = any(img_codec in codec for img_codec in image_based_codecs)
            
            # Additional detection for image formats
            if any(keyword in track_name for keyword in ['pgs', 'sup', 'vobsub', 'dvd']):
                is_image_based = True
            
            extracted_subtitle_info.append({
                "codec": codec, 
                "is_image": is_image_based,
                "track_name": track_name
            })
            
            if is_image_based:
                has_image_subtitles = True
                print(f"  📸 Image-based subtitle detected: {codec} - will need OCR processing")

        # Set next status based on subtitle content
        if has_image_subtitles:
            next_status = "subtitle_ocr_pending"
            print(f"  ➡️ Next stage: Subtitle OCR (image-based subtitles found)")
        else:
            next_status = "final_mux_pending"  
            print(f"  ➡️ Next stage: Final Mux (only text-based subtitles)")

        # Update movie state with enhanced information in SQLite database
        processing_time = time.time() - start_time
        processing_metrics["processing_time"] = processing_time

        sqlite_manager.set_stage_marker(
            unique_id=movie_id,
            new_status=next_status,
            additional_data={
                "paths": {
                    **movie.get("paths", {}),
                    "processed_va_mkv_path": str(output_va_mkv_path),
                    "extracted_subtitles_dir": str(output_subs_dir)
                },
                "extracted_subtitle_info": extracted_subtitle_info,
                "processing_metrics": processing_metrics,
                "last_updated_timestamp": datetime.now(timezone.utc).isoformat()
            }
        )

        # Extract audio tracks for manual review (SIMPLIFIED APPROACH - BASED ON WORKING VERSION)
        # Note: output_audio_dir already created at the beginning

        try:
            # Get all audio tracks from mkv_info
            audio_tracks = [track for track in mkv_info["tracks"] if track.get("type") == "audio"]
            logger.info(f"🎵 Extracting {len(audio_tracks)} audio tracks for manual review...")

            # Simple codec mapping (like working test script)
            codec_extensions = {
                "A_TRUEHD": "thd",
                "A_DTS": "dts", 
                "A_AC3": "ac3",
                "A_EAC3": "eac3",
                "A_AAC": "aac",
                "TrueHD": "thd",
                "DTS": "dts",
                "AC-3": "ac3",
                "E-AC-3": "eac3"
            }

            # Direct extraction with simple logic
            mkvextract_path = "C:/Program Files/MKVToolNix/mkvextract.exe"
            
            for track in audio_tracks:
                track_id = track.get("id")
                props = track.get("properties", {})
                codec = props.get("codec_id", "unknown")
                language = props.get("language", "und")
                
                # Get extension
                ext = codec_extensions.get(codec, "mka")
                
                # Simple filename
                output_file = output_audio_dir / f"track_{track_id}_{language}_{codec}.{ext}"
                
                # Extract with direct command
                cmd = [mkvextract_path, str(organized_path), 'tracks', f"{track_id}:{output_file}"]
                
                logger.info(f"Extracting track {track_id}: {' '.join(cmd)}")
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
                
                if result.returncode == 0:
                    logger.info(f"✅ Extracted: {output_file.name}")
                else:
                    logger.error(f"❌ Failed track {track_id}: {result.stderr}")

            # Verify extraction results
            extracted_files = list(output_audio_dir.glob("*"))
            logger.info(f"📁 Audio extraction complete: {len(extracted_files)} files created")

        except Exception as e:
            logger.warning(f"Audio extraction failed: {e}")
            # Don't fail the entire process

        # MOVE PROCESSED FILES TO NEXT STAGE FOLDER
        # Create the destination folder in the next pipeline stage
        next_stage_base = Path("workspace") / "3_mkv_cleaned_subtitles_extracted"
        
        # Determine resolution folder
        resolution = "1080p"  # Default, could be enhanced to detect from video properties
        if processing_metrics.get("video_quality", {}).get("width"):
            width = int(processing_metrics["video_quality"]["width"])
            if width >= 3840:
                resolution = "4K"
            elif width >= 1920:
                resolution = "1080p"
            else:
                resolution = "SD_or_unknown"
        
        next_stage_dir = next_stage_base / resolution / organized_path.parent.name
        next_stage_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"📁 Moving processed files to next stage: {next_stage_dir}")
        
        # Move the processed video/audio file
        dest_va_file = next_stage_dir / output_va_mkv_path.name
        if output_va_mkv_path.exists():
            shutil.move(str(output_va_mkv_path), str(dest_va_file))
            logger.info(f"✅ Moved processed V/A file: {dest_va_file.name}")
        
        # Move extracted subtitles 
        dest_subs_dir = next_stage_dir / "_Processed_Subtitles"
        if output_subs_dir.exists() and any(output_subs_dir.iterdir()):
            dest_subs_dir.mkdir(exist_ok=True)
            for sub_file in output_subs_dir.iterdir():
                if sub_file.is_file():
                    dest_sub_file = dest_subs_dir / sub_file.name
                    shutil.move(str(sub_file), str(dest_sub_file))
            logger.info(f"✅ Moved extracted subtitles to: {dest_subs_dir}")
            
        # Move extracted audio files for manual review
        dest_audio_dir = next_stage_dir / "_Processed_Audio"
        if output_audio_dir.exists() and any(output_audio_dir.iterdir()):
            dest_audio_dir.mkdir(exist_ok=True)
            for audio_file in output_audio_dir.iterdir():
                if audio_file.is_file():
                    dest_audio_file = dest_audio_dir / audio_file.name
                    shutil.move(str(audio_file), str(dest_audio_file))
            logger.info(f"✅ Moved extracted audio files to: {dest_audio_dir}")
        
        # Clean up empty processing directories in original location
        for temp_dir in [output_va_dir, output_subs_dir, output_audio_dir, temp_dir]:
            if temp_dir.exists() and not any(temp_dir.iterdir()):
                temp_dir.rmdir()
        
        # Update paths in database to reflect new locations
        sqlite_manager.set_stage_marker(
            unique_id=movie_id,
            new_status=next_status,
            additional_data={
                "paths": {
                    **movie.get("paths", {}),
                    "processed_va_mkv_path": str(dest_va_file),
                    "extracted_subtitles_dir": str(dest_subs_dir),
                    "extracted_audio_dir": str(dest_audio_dir),
                    "next_stage_directory": str(next_stage_dir)
                },
                "extracted_subtitle_info": extracted_subtitle_info,
                "processing_metrics": processing_metrics,
                "last_updated_timestamp": datetime.now(timezone.utc).isoformat()
            }
        )
        
        logger.info(f"🎯 Files successfully moved to next stage: {next_stage_dir}")

        # KEEP ORIGINAL FILE - Do NOT delete until after quality check (Stage 7)
        # User preference: Keep original file until final quality check is complete
        logger.info(f"✅ MKV processing complete - keeping original file until quality check")
        logger.info(f"📁 Original file location: {organized_path}")
        logger.info(f"🎯 Processed files ready for next stage")

        # Cleanup temp file only
        processing_file.unlink(missing_ok=True)

        # Final progress update
        if sequential_service and movie_task_id:
            await sequential_service.update_task_progress(movie_task_id, 100, "MKV processing completed successfully")

        logger.info(f"MKV processing completed successfully for {movie.get('cleaned_title')} in {processing_time:.2f}s")
        return True, processing_metrics

    except Exception as e:
        logger.error(f"Error processing MKV for {movie.get('cleaned_title')}: {e}")

        # MCP Enhancement: Store processing errors for learning
        if memory_service:
            await memory_service.store_memory(
                category="mkv_processing_errors",
                key=f"processing_error_{movie_id}_{int(time.time())}",
                value={
                    "movie_id": movie_id,
                    "title": movie.get("cleaned_title"),
                    "error": str(e),
                    "processing_time": time.time() - start_time,
                    "timestamp": datetime.now(timezone.utc).isoformat()
                },
                tags=["error", "processing", "stage03", "mkv"]
            )

        # Cleanup temp file on error
        if processing_file.exists():
            processing_file.unlink(missing_ok=True)

        sqlite_manager.set_stage_marker(
            unique_id=movie_id,
            new_status="error_mkv_processing",
            error_message=f"MKV processing failed: {str(e)}"
        )

        return False, processing_metrics


# Legacy _process_single_mkv function removed - replaced with _process_single_mkv_enhanced
# which uses proper SQLite operations instead of legacy update_movie_in_state calls


# --- Standalone Execution Support ---
async def robust_mkv_processor():
    """
    New robust MKV processor using file-based discovery.
    No manual JSON editing required!
    """
    # Setup logging for robust execution
    import logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    robust_logger = logging.getLogger("robust_pipeline_03")

    robust_logger.info("🚀 Starting Robust MKV Processor (No Manual JSON Editing!)")

    # Initialize robust state management
    if create_robust_state_manager is None:
        robust_logger.warning("Robust state management not available, falling back to legacy system")
        return await standalone_mkv_processor()

    state_manager = create_filesystem_first_manager()
    idempotent_ops = create_idempotent_operations(state_manager)

    # Discover movies by scanning filesystem
    robust_logger.info("🔍 Discovering movies by scanning filesystem...")
    movies_by_stage = state_manager.discover_movies_by_stage()

    # Process movies ready for MKV processing
    mkv_pending = movies_by_stage.get('mkv_processing_pending', [])
    mkv_interrupted = movies_by_stage.get('mkv_processing_interrupted', [])

    all_movies_to_process = mkv_pending + mkv_interrupted
    robust_logger.info(f"Found {len(mkv_pending)} movies ready for MKV processing")
    robust_logger.info(f"Found {len(mkv_interrupted)} interrupted MKV processing tasks")

    if not all_movies_to_process:
        robust_logger.info("No movies found ready for MKV processing")
        return

    # Process each movie
    for movie_info in all_movies_to_process:
        movie_name = f"{movie_info.get('title', 'Unknown')} ({movie_info.get('year', 'Unknown')})"
        robust_logger.info(f"🎬 Processing: {movie_name}")

        try:
            # Use idempotent MKV processing (safe to run multiple times)
            success, updated_info = idempotent_ops.safe_mkv_process(movie_info)

            if success:
                robust_logger.info(f"✅ Successfully processed MKV: {movie_name}")
            else:
                robust_logger.error(f"❌ Failed to process MKV: {movie_name}")

        except Exception as e:
            robust_logger.error(f"Error processing {movie_name}: {e}")

    robust_logger.info("🎯 Robust MKV processing complete!")

async def standalone_mkv_processor():
    """Legacy standalone MKV processor for fallback."""
    # This would contain the existing standalone logic
    import logging
    fallback_logger = logging.getLogger("fallback_pipeline_03")
    fallback_logger.info("Running legacy MKV processor...")
    # TODO: Implement legacy fallback if needed

if __name__ == "__main__":
    """
    Standalone execution for Pipeline 3 - MKV Processor
    Preserves all MCP capabilities while allowing manual testing
    """
    import json
    from pathlib import Path
    import argparse
    
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='MKV Processor Script - Pipeline 03')
    parser.add_argument('--movies-only', action='store_true',
                       help='Process only movies (command-line mode)')
    parser.add_argument('--tv-only', action='store_true',
                       help='Process only TV shows (command-line mode)')
    parser.add_argument('--all', action='store_true',
                       help='Process both movies and TV shows (command-line mode)')
    
    args = parser.parse_args()
    
    print("🎬 MKV Processor - Pipeline 03")
    print("   Default: Interactive mode (use --movies-only, --tv-only, or --all for command-line mode)")
    
    # Import settings loader with fallback
    try:
        from _internal.utils.common_helpers import load_settings
    except ImportError:
        try:
            from utils.common_helpers import load_settings
        except ImportError:
            import sys
            sys.path.insert(0, str(Path(__file__).parent))
            from _internal.utils.common_helpers import load_settings
    
    async def standalone_mkv_processor():
        # Setup logging for standalone execution
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        standalone_logger = logging.getLogger("standalone_pipeline_03")
        
        filesystem_manager = None
        metadata_db = None
        try:
            standalone_logger.info("===== Starting Standalone Pipeline 03 Execution =====")

            # Initialize filesystem-first state manager
            workspace_root = Path.cwd()
            filesystem_manager = FilesystemFirstStateManager(workspace_root)
            metadata_db = MetadataOnlyDatabase(workspace_root)
            standalone_logger.info("Filesystem-first state manager initialized")

            # TEMPORARILY DISABLED: Sync database with filesystem 
            # (The sync is incorrectly marking organized movies as errors)
            # sync_results = sqlite_manager.discover_movies_by_stage()
            # total_movies = sum(len(movies) for movies in sync_results.values())
            # standalone_logger.info(f"Found {total_movies} movies")
            standalone_logger.info("Skipping filesystem sync to preserve correct statuses")

            # Load settings
            settings_dict = load_settings("_internal/config/settings.ini")
            standalone_logger.info("Settings loaded successfully")

            # Get movies from filesystem discovery
            movies_by_stage = filesystem_manager.discover_movies_by_stage()
            
            # Get movies that are ready for MKV processing
            # Include both explicit mkv_processing_pending and organized stages
            movies_ready_for_mkv = []
            movies_ready_for_mkv.extend(movies_by_stage.get('mkv_processing_pending', []))
            movies_ready_for_mkv.extend(movies_by_stage.get('organized', []))
            standalone_logger.info(f"Found {len(movies_ready_for_mkv)} movies ready for MKV processing (including organized)")

            # Convert to legacy format for compatibility
            movies_data = []
            for movie in movies_ready_for_mkv:
                # Extract basic info (adjust for filesystem-first structure)
                movie_dir = movie.get('movie_directory', '')
                main_movie_file = movie.get('main_movie_file', '')
                
                # Extract title and year from directory name if available
                # Format: "Title (Year)" 
                import re
                title = "Unknown"
                year = None
                if movie_dir:
                    dir_name = Path(movie_dir).name
                    match = re.search(r'^(.+?) \((\d{4})\)$', dir_name)
                    if match:
                        title = match.group(1)
                        year = int(match.group(2))
                
                legacy_movie = {
                    'unique_id': movie.get('unique_id', movie_dir),  # Use directory as fallback ID
                    'title': title,
                    'cleaned_title': title,
                    'year': year,
                    'tmdb_id': movie.get('tmdb_id'),
                    'status': movie.get('current_stage', 'mkv_processing_pending'),  # Use filesystem stage
                    'created_at': movie.get('discovered_at'),
                    'last_updated_timestamp': movie.get('discovered_at'),
                    'paths': {
                        'organized_mkv_path': main_movie_file,
                        'movie_directory': movie_dir
                    },
                    'metadata': {},
                    'metadata_details': {},
                    'error_message': movie.get('error_message'),
                    'processing_metrics': {},
                    'extracted_subtitle_info': []
                }
                movies_data.append(legacy_movie)
            
            # Filter movies that need MKV processing using ROBUST APPROACH
            # Include movies that might need reprocessing or continuation
            mkv_states = [
                "organized",                  # NEW: Movies organized by Script 2, ready for MKV processing
                "download_completed", 
                "mkv_processing_pending",
                "mkv_processing_active",      # Resume interrupted processing
                "error_mkv_processing",       # Retry failed processing  
                "subtitle_ocr_pending",       # Allow reprocessing if files missing
                "final_mux_pending"           # Allow reprocessing if files missing
            ]
            
            # Get all movies with relevant statuses
            all_candidates = [m for m in movies_data if m.get("status") in mkv_states]
            
            standalone_logger.info(f"🔍 DEBUG: Found {len(all_candidates)} candidates from {len(movies_data)} total movies")
            standalone_logger.info(f"🔍 DEBUG: Looking for statuses: {mkv_states}")
            
            # SMART FILTERING: Only process movies that actually need processing
            movies_to_process = []
            for movie in all_candidates:
                status = movie.get("status")
                title = movie.get("cleaned_title", "Unknown")
                year = movie.get("year", "Unknown")
                movie_id = movie.get("unique_id")
                
                # DEBUG: Log each movie being evaluated
                standalone_logger.info(f"🔍 DEBUG: Evaluating {title} ({year}) with status '{status}'")
                
                # Check if processing is actually needed
                needs_processing = False
                reason = ""
                
                if status in ["organized", "download_completed", "mkv_processing_pending", "mkv_processing_active"]:
                    needs_processing = True
                    reason = "Standard processing needed"
                    standalone_logger.info(f"🎯 DEBUG: {title} matches standard processing criteria")
                
                elif status == "error_mkv_processing":
                    needs_processing = True
                    reason = "Retry failed processing"
                    
                elif status in ["subtitle_ocr_pending", "final_mux_pending"]:
                    # For these statuses, do more complex checking
                    # SMART DETECTION: Check if processed files exist in source folder or next stage
                    paths = movie.get("paths", {})
                    organized_path = paths.get("organized_mkv_path")
                    processed_va_path = paths.get("processed_va_mkv_path")
                    next_stage_dir = paths.get("next_stage_directory")
                    
                    # First check if files exist in source folder (ready to move)
                    if organized_path:
                        source_parent = Path(organized_path).parent
                        source_va_dir = source_parent / "_Processed_VideoAudio"
                        source_subs_dir = source_parent / "_Processed_Subtitles"
                        source_audio_dir = source_parent / "_Processed_Audio"
                        
                        # Check if processed files exist in source folder
                        va_files_exist = source_va_dir.exists() and any(f.name.endswith('.processed.mkv') for f in source_va_dir.iterdir() if f.is_file())
                        sub_files_exist = source_subs_dir.exists() and any(source_subs_dir.iterdir())
                        
                        if va_files_exist or sub_files_exist:
                            needs_processing = True
                            reason = "Existing processed files in source folder"
                            standalone_logger.info(f"🚀 {title} ({year}): Found existing processed files, will move them")
                        else:
                            # Then check if files are missing from next stage (need reprocessing)
                            if processed_va_path and next_stage_dir:
                                next_stage_path = Path(next_stage_dir)
                                processed_va_file = next_stage_path / Path(processed_va_path).name
                                
                                if not processed_va_file.exists():
                                    needs_processing = True
                                    reason = f"Files missing from next stage - reprocessing needed"
                                    standalone_logger.info(f"🔄 {title} ({year}): Processed files missing, will reprocess")
                                else:
                                    standalone_logger.info(f"✅ {title} ({year}): Already processed, files exist in next stage")
                            else:
                                # No path info, assume needs processing
                                needs_processing = True
                                reason = "Missing path information - reprocessing needed"
                    else:
                        # No organized path, assume needs processing
                        needs_processing = True
                        reason = "Missing organized path - reprocessing needed"
                
                if needs_processing:
                    movies_to_process.append(movie)
                    standalone_logger.info(f"📋 {title} ({year}): {reason}")
                else:
                    standalone_logger.info(f"❌ {title} ({year}): status '{status}', no processing needed")
            
            if not movies_to_process:
                standalone_logger.info("No movies need MKV processing")
                standalone_logger.info("All movies are either completed or have valid processed files.")
                standalone_logger.info("Current movie statuses:")
                for movie in movies_data:
                    title = movie.get("cleaned_title", "Unknown")
                    year = movie.get("year", "")
                    status = movie.get("status", "unknown")
                    standalone_logger.info(f"  - {title} ({year}): {status}")
                # Do not return; continue to check TV episodes
            
            standalone_logger.info(f"Processing {len(movies_to_process)} movies for MKV processing:")
            for movie in movies_to_process:
                title = movie.get("cleaned_title", "Unknown")
                year = movie.get("year", "Unknown")
                status = movie.get("status", "unknown")
                standalone_logger.info(f"  - {title} ({year}): {status}")
            
            # DEBUG: Log the movies_data being passed to the processing function
            standalone_logger.info(f"🔍 DEBUG: About to call run_mkv_processor_stage with {len(movies_data)} movies:")
            
            # DUAL CONTENT PROCESSING: Handle both movies and TV shows
            # Always invoke movie stage; it performs its own filesystem-first discovery
            standalone_logger.info("Invoking movie MKV processor stage; it will perform its own discovery.")
            await run_mkv_processor_stage(movies_data, settings_dict, standalone_logger, mcp_manager=None)

            # SOPHISTICATED TV SHOW SUPPORT: Process TV episodes with same sophistication
            standalone_logger.info("🔍 Discovering TV episodes ready for MKV processing...")
            
            # Get TV episodes from filesystem discovery
            tv_episodes_by_stage = filesystem_manager.discover_tv_episodes_by_stage()
            episodes_ready_for_mkv = tv_episodes_by_stage.get('mkv_processing_pending', [])
            standalone_logger.info(f"Found {len(episodes_ready_for_mkv)} TV episodes ready for MKV processing")

            # Convert TV episodes to processing format
            episodes_data = []
            for episode in episodes_ready_for_mkv:
                episode_dir = episode.get('episode_directory', '')
                main_episode_file = episode.get('main_episode_file', '')
                
                # Extract series info from directory structure
                # Format: "Series Name/Season XX/Episode files"
                series_title = "Unknown"
                season_number = 1
                episode_number = 1
                
                if episode_dir:
                    episode_path = Path(episode_dir)
                    series_title = episode_path.parent.parent.name if episode_path.parent.parent else "Unknown"
                    season_dir = episode_path.parent.name if episode_path.parent else ""
                    
                    # Extract season number from "Season XX" format
                    import re
                    season_match = re.search(r'Season (\d+)', season_dir)
                    if season_match:
                        season_number = int(season_match.group(1))
                    
                    # Extract episode number from filename
                    episode_match = re.search(r'S(\d+)E(\d+)', episode_path.name)
                    if episode_match:
                        season_number = int(episode_match.group(1))
                        episode_number = int(episode_match.group(2))
                
                legacy_episode = {
                    'unique_id': episode.get('unique_id', episode_dir),
                    'series_title': series_title,
                    'season_number': season_number,
                    'episode_number': episode_number,
                    'episode_title': episode.get('episode_title', ''),
                    'status': episode.get('current_stage', 'mkv_processing_pending'),
                    'created_at': episode.get('discovered_at'),
                    'last_updated_timestamp': episode.get('discovered_at'),
                    'paths': {
                        'organized_mkv_path': main_episode_file,
                        'episode_directory': episode_dir
                    },
                    'metadata': {},
                    'error_message': episode.get('error_message'),
                    'processing_metrics': {}
                }
                episodes_data.append(legacy_episode)

            # Filter episodes that need MKV processing
            tv_episodes_to_process = []
            for episode in episodes_data:
                status = episode.get("status")
                series_title = episode.get("series_title", "Unknown")
                season_num = episode.get("season_number", 1)
                episode_num = episode.get("episode_number", 1)
                episode_id = episode.get("unique_id")
                
                # Same smart filtering logic as movies but for episodes
                needs_processing = False
                reason = ""
                
                if status in ["organized", "download_completed", "mkv_processing_pending", "mkv_processing_active"]:
                    needs_processing = True
                    reason = "Standard episode processing needed"
                
                elif status == "error_mkv_processing":
                    needs_processing = True
                    reason = "Retry failed episode processing"
                    
                elif status in ["subtitle_ocr_pending", "final_mux_pending"]:
                    # Check if processed episode files exist
                    paths = episode.get("paths", {})
                    organized_path = paths.get("organized_mkv_path")
                    
                    if organized_path:
                        source_parent = Path(organized_path).parent
                        source_va_dir = source_parent / "_Processed_VideoAudio"
                        source_subs_dir = source_parent / "_Processed_Subtitles"
                        source_audio_dir = source_parent / "_Processed_Audio"
                        
                        # Check if processed episode files exist in source folder
                        va_files_exist = source_va_dir.exists() and any(f.name.endswith('.processed.mkv') for f in source_va_dir.iterdir() if f.is_file())
                        sub_files_exist = source_subs_dir.exists() and any(source_subs_dir.iterdir())
                        
                        if va_files_exist or sub_files_exist:
                            needs_processing = True
                            reason = "Episode files already processed, just need to move to next stage"
                        else:
                            needs_processing = True
                            reason = "Episode files missing - reprocessing needed"
                    else:
                        needs_processing = True
                        reason = "Missing episode organized path - reprocessing needed"
                
                if needs_processing:
                    tv_episodes_to_process.append(episode)
                    standalone_logger.info(f"📺 {series_title} S{season_num:02d}E{episode_num:02d}: {reason}")

            # Process TV episodes if any found
            if tv_episodes_to_process:
                standalone_logger.info(f"Processing {len(tv_episodes_to_process)} TV episodes for MKV processing:")
                for episode in tv_episodes_to_process:
                    series_title = episode.get("series_title", "Unknown")
                    season_num = episode.get("season_number", 1)
                    episode_num = episode.get("episode_number", 1)
                    status = episode.get("status", "unknown")
                    standalone_logger.info(f"  📺 {series_title} S{season_num:02d}E{episode_num:02d}: {status}")
                
                # Process TV episodes using sophisticated TV show logic
                updated_episodes = await run_tv_mkv_processor_stage(episodes_data, settings_dict, standalone_logger, mcp_manager=None)
            else:
                standalone_logger.info("No TV episodes need MKV processing")

            # Sync pipeline state (filesystem is automatically persistent)
            movies_by_stage = filesystem_manager.discover_movies_by_stage()
            tv_episodes_by_stage = filesystem_manager.discover_tv_episodes_by_stage()
            total_content = sum(len(movies) for movies in movies_by_stage.values()) + sum(len(episodes) for episodes in tv_episodes_by_stage.values())
            standalone_logger.info(f"Pipeline state synchronized - found {total_content} total content items")

            # Show results from filesystem discovery for both movies and TV shows
            completed_movies = movies_by_stage.get("mkv_processing_completed", [])
            completed_episodes = tv_episodes_by_stage.get("mkv_processing_completed", [])
            
            if completed_movies:
                standalone_logger.info(f"🎉 {len(completed_movies)} movies completed MKV processing:")
                for movie in completed_movies:
                    title = movie.get("title", "Unknown")
                    year = movie.get("year", "")
                    standalone_logger.info(f"  ✅ {title} ({year})")
            
            if completed_episodes:
                standalone_logger.info(f"📺 {len(completed_episodes)} TV episodes completed MKV processing:")
                for episode in completed_episodes:
                    series_title = episode.get("series_title", "Unknown")
                    season_num = episode.get("season_number", 1)
                    episode_num = episode.get("episode_number", 1)
                    standalone_logger.info(f"  ✅ {series_title} S{season_num:02d}E{episode_num:02d}")
            
            if not completed_movies and not completed_episodes:
                standalone_logger.info("No content completed MKV processing in this run")

            standalone_logger.info("===== Finished Standalone Pipeline 03 Execution =====")

        except Exception as e:
            standalone_logger.error(f"Error in standalone execution: {e}", exc_info=True)
        finally:
            # Clean up database connections
            if metadata_db:
                metadata_db.close()
    
    # Interactive media selection functions
    def display_interactive_menu():
        """
        Display the main interactive menu for content type selection.

        Returns:
            str: Selected content type ('movies', 'tv_shows', 'both', 'quit')
        """
        print(f"\n{'='*60}")
        print(f"🎬📺 PlexMovieAutomator - Interactive Content Selection")
        print(f"{'='*60}")
        print(f"\nWhat type of content would you like to process?")
        print(f"  1. Movies only")
        print(f"  2. TV Shows only")
        print(f"  3. Both Movies and TV Shows")
        print(f"  4. Quit")

        while True:
            try:
                choice = input(f"\nEnter your choice [1-4]: ").strip()

                if choice == '1':
                    return 'movies'
                elif choice == '2':
                    return 'tv_shows'
                elif choice == '3':
                    return 'both'
                elif choice == '4':
                    return 'quit'
                else:
                    print(f"Please enter a number between 1 and 4")

            except KeyboardInterrupt:
                print(f"\n👋 Exiting...")
                return 'quit'

    async def main_mkv_processor():
        # Setup logging for execution
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        main_logger = logging.getLogger("pipeline_03")
        
        filesystem_manager = None
        metadata_db = None
        try:
            main_logger.info("===== Starting Pipeline 03 Execution =====")

            # Determine content type based on arguments or interactive selection
            if args.movies_only or args.tv_only or args.all:
                # Command line mode
                if args.movies_only:
                    content_type_choice = 'movies'
                elif args.tv_only:
                    content_type_choice = 'tv_shows'
                else:  # args.all
                    content_type_choice = 'both'
                main_logger.info(f"Command-line mode: Processing {content_type_choice}")
            else:
                # Interactive mode (default)
                content_type_choice = display_interactive_menu()
                
                if content_type_choice == 'quit':
                    main_logger.info("👋 User chose to quit")
                    return

            # Initialize filesystem-first state manager
            workspace_root = Path.cwd()
            filesystem_manager = FilesystemFirstStateManager(workspace_root)
            metadata_db = MetadataOnlyDatabase(workspace_root)
            main_logger.info("Filesystem-first state manager initialized")

            # Load settings
            settings_dict = load_settings("_internal/config/settings.ini")
            main_logger.info("Settings loaded successfully")

            # Get content from filesystem discovery based on user choice
            movies_by_stage = filesystem_manager.discover_movies_by_stage()
            tv_episodes_by_stage = filesystem_manager.discover_tv_episodes_by_stage()
            
            movies_to_process = []
            tv_episodes_to_process = []

            # Filter content based on user selection
            if content_type_choice in ['movies', 'both']:
                # Get movies that are ready for MKV processing
                mkv_pending_movies = movies_by_stage.get("mkv_processing_pending", [])
                
                for movie in mkv_pending_movies:
                    title = movie.get("title", "Unknown")
                    year = movie.get("year", "")
                    movie_directory = movie.get("movie_directory")
                    
                    if movie_directory and Path(movie_directory).exists():
                        movies_to_process.append(movie)
                        main_logger.info(f"🎬 {title} ({year}): Ready for MKV processing")

            if content_type_choice in ['tv_shows', 'both']:
                # Get TV episodes that are ready for MKV processing
                mkv_pending_episodes = tv_episodes_by_stage.get("mkv_processing_pending", [])
                
                for episode in mkv_pending_episodes:
                    series_title = episode.get("series_title", "Unknown")
                    season_num = episode.get("season_number", 1)
                    episode_num = episode.get("episode_number", 1)
                    episode_directory = episode.get("episode_directory")
                    
                    if episode_directory and Path(episode_directory).exists():
                        tv_episodes_to_process.append(episode)
                        main_logger.info(f"📺 {series_title} S{season_num:02d}E{episode_num:02d}: Ready for MKV processing")

            # Process content based on selection
            if movies_to_process:
                main_logger.info(f"Processing {len(movies_to_process)} movies for MKV processing:")
                # Process movies using sophisticated movie logic
                updated_movies = await run_mkv_processor_stage(movies_to_process, settings_dict, main_logger, mcp_manager=None)
            
            if tv_episodes_to_process:
                main_logger.info(f"Processing {len(tv_episodes_to_process)} TV episodes for MKV processing:")
                # Process TV episodes using sophisticated TV show logic
                updated_episodes = await run_tv_mkv_processor_stage(tv_episodes_to_process, settings_dict, main_logger, mcp_manager=None)

            # Show results
            if content_type_choice == 'movies':
                main_logger.info("✅ Movie MKV processing completed")
            elif content_type_choice == 'tv_shows':
                main_logger.info("✅ TV Show MKV processing completed")
            else:
                main_logger.info("✅ Movie and TV Show MKV processing completed")

            main_logger.info("===== Finished Pipeline 03 Execution =====")

        except Exception as e:
            main_logger.error(f"Error in execution: {e}", exc_info=True)
        finally:
            # Clean up database connections
            if metadata_db:
                metadata_db.close()
    
    # Run main MKV processing with user choice or command-line args
    asyncio.run(main_mkv_processor())