#!/usr/bin/env python3
"""
PlexMovieAutomator/_internal/utils/tv_year_resolver.py

Multi-Source TV Series Year Resolution

This utility provides robust year lookup for TV series using multiple authoritative sources:
1. Sonarr series metadata (highest priority - authoritative)
2. TMDb TV series API (authoritative) 
3. TVDB API (authoritative, if configured)
4. User request file (lowest priority - fallback for "please find this show")

Guarantees every TV series folder includes the correct (Year) format.
"""

import re
import logging
import aiohttp
from typing import Optional, Dict, Any, List
from pathlib import Path

logger = logging.getLogger(__name__)

class TVYearResolver:
    """
    Resolves TV series years using multiple authoritative sources with fallback chain.
    """
    
    def __init__(self, settings_dict: Dict[str, Any]):
        self.settings_dict = settings_dict
        # Look for TMDb API key in multiple locations and formats
        self.tmdb_api_key = (
            settings_dict.get("tmdb_api_key") or 
            settings_dict.get("TMDb", {}).get("api_key") or
            settings_dict.get("TMDb", {}).get("tmdb_api_key") or
            settings_dict.get("APIKeys", {}).get("tmdb_api_key")
        )
        # Look for TVDB API key in multiple locations and formats
        self.tvdb_api_key = (
            settings_dict.get("tvdb_api_key") or 
            settings_dict.get("TVDB", {}).get("api_key") or
            settings_dict.get("TVDB", {}).get("tvdb_api_key") or
            settings_dict.get("APIKeys", {}).get("tvdb_api_key")
        )
        self.sonarr_url = settings_dict.get("Sonarr", {}).get("url", "http://localhost:8989")
        self.sonarr_api_key = settings_dict.get("Sonarr", {}).get("api_key")
        
        # Cache for API lookups to avoid repeated calls
        self._tmdb_cache: Dict[str, Dict[str, Any]] = {}
        self._sonarr_cache: Dict[str, Dict[str, Any]] = {}
        
        logger.info(f"TVYearResolver initialized:")
        logger.info(f"  TMDb API: {'✓' if self.tmdb_api_key else '✗'} ({self.tmdb_api_key[:8] + '...' if self.tmdb_api_key else 'None'})")
        logger.info(f"  Sonarr API: {'✓' if self.sonarr_api_key else '✗'} ({self.sonarr_api_key[:8] + '...' if self.sonarr_api_key else 'None'})")
        logger.info(f"  TVDB API: {'✓' if self.tvdb_api_key else '✗'} ({self.tvdb_api_key[:8] + '...' if self.tvdb_api_key else 'None'})")
    
    def load_user_requests_map(self) -> Dict[str, Dict[str, Any]]:
        """
        Load user TV requests with their specified years.
        Returns: Dict[normalized_title, {title, year, season_episode}]
        """
        import os
        
        # Get the project root directory (where the script files are located)
        current_dir = Path(__file__).parent.parent.parent  # Go up from _internal/utils/ to project root
        
        candidates = [
            current_dir / "new_tv_requests.txt",
            current_dir / "example_tv_requests.txt", 
            current_dir / "_internal" / "config" / "tv_requests.txt",
            current_dir / "config" / "tv_requests.txt",
            Path("new_tv_requests.txt"),  # Fallback to relative path
            Path("example_tv_requests.txt"),  # Fallback to relative path
        ]
        
        req_map: Dict[str, Dict[str, Any]] = {}
        req_path = next((p for p in candidates if p.exists()), None)
        
        if not req_path:
            logger.warning("No TV requests file found - year resolution will use API sources only")
            logger.warning(f"Searched paths: {[str(p) for p in candidates]}")
            return req_map
            
        logger.info(f"✅ Found TV requests file: {req_path}")
        logger.info(f"🔍 Loading user TV requests from: {req_path}")
            
        try:
            logger.info(f"Loading user TV requests from: {req_path}")
            lines = req_path.read_text(encoding="utf-8", errors="ignore").splitlines()
            
            for line_num, raw_line in enumerate(lines, 1):
                line = raw_line.strip()
                if not line or line.startswith("#"):
                    continue
                
                title, year, season_episode = None, None, None
                
                # Pattern 1: "Title (Year) SxxEyy" 
                m = re.match(r"^(.*?)\s*\((\d{4})\)\s*(S\d+E\d+.*)?$", line)
                if m:
                    title = m.group(1).strip()
                    year = int(m.group(2))
                    season_episode = m.group(3)
                else:
                    # Pattern 2: "Title, Year, SxxEyy"
                    parts = [p.strip() for p in line.split(",")]
                    if len(parts) >= 2 and re.fullmatch(r"\d{4}", parts[1]):
                        title = parts[0]
                        year = int(parts[1])
                        season_episode = parts[2] if len(parts) > 2 else None
                    else:
                        # Pattern 3: Just title - will need API lookup
                        title = line
                
                if title:
                    normalized_key = self._normalize_title(title)
                    req_map[normalized_key] = {
                        "title": title,
                        "year": year,
                        "season_episode": season_episode,
                        "source": f"user_request_line_{line_num}"
                    }
                    logger.debug(f"📝 Loaded request: '{title}' ({year}) → normalized: '{normalized_key}'")
        
        except Exception as e:
            logger.error(f"Error loading TV requests from {req_path}: {e}")
        
        logger.info(f"Loaded {len(req_map)} TV series requests from user file")
        return req_map
    
    def _normalize_title(self, title: str) -> str:
        """Normalize title for consistent matching across sources."""
        if not title:
            return ""
        
        # Convert to lowercase and clean
        normalized = str(title).lower().strip()
        
        # Remove common suffixes that cause mismatches
        suffixes_to_remove = [
            " us", " uk", " 2019", " 2020", " 2021", " 2022", " 2023", " 2024", " 2025",
            " (us)", " (uk)", " (2019)", " (2020)", " (2021)", " (2022)", " (2023)", " (2024)", " (2025)"
        ]
        
        for suffix in suffixes_to_remove:
            if normalized.endswith(suffix):
                normalized = normalized[:-len(suffix)].strip()
        
        # Remove all non-alphanumeric characters
        return re.sub(r"[^a-z0-9]+", "", normalized)
    
    def _fuzzy_match_title(self, series_title: str, user_requests: Dict[str, Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """
        Try fuzzy matching for title variations that might not match exactly.
        """
        normalized_input = self._normalize_title(series_title)
        
        # Try exact match first
        if normalized_input in user_requests:
            return user_requests[normalized_input]
        
        # Try fuzzy matching - check if input title is contained in any request or vice versa
        for req_normalized, req_data in user_requests.items():
            # Check if one title contains the other (handles "office" vs "theoffice" cases)
            if len(normalized_input) >= 4 and len(req_normalized) >= 4:
                if normalized_input in req_normalized or req_normalized in normalized_input:
                    logger.info(f"🔄 Fuzzy match: '{series_title}' matched '{req_data['title']}' via substring")
                    return req_data
                    
                # Check for common variations (the office vs office, kim possible vs kimpossible)
                variations = [
                    (normalized_input.replace("the", ""), req_normalized.replace("the", "")),
                    (normalized_input.replace(" ", ""), req_normalized.replace(" ", "")),
                ]
                
                for var1, var2 in variations:
                    if var1 and var2 and var1 == var2:
                        logger.info(f"🔄 Fuzzy match: '{series_title}' matched '{req_data['title']}' via normalization")
                        return req_data
        
        return None
    
    async def resolve_series_year(self, series_title: str, season: Optional[int] = None, 
                                episode: Optional[int] = None) -> Dict[str, Any]:
        """
        Resolve series year using authoritative APIs first, user requests as fallback.
        
        Priority Order:
        1. Sonarr API (highest priority - authoritative)
        2. TMDb API (authoritative)
        3. TVDB API (authoritative)
        4. User request file (lowest priority - just "please find this")
        
        Args:
            series_title: Series title to resolve
            season: Optional season number for context
            episode: Optional episode number for context
            
        Returns:
            Dict with resolved_title, resolved_year, source, confidence
        """
        result = {
            "original_title": series_title,
            "resolved_title": series_title,
            "resolved_year": None,
            "source": "none",
            "confidence": 0.0,
            "season": season,
            "episode": episode
        }
        
        normalized_title = self._normalize_title(series_title)
        logger.info(f"🔍 Resolving year for: '{series_title}' (normalized: '{normalized_title}')")
        
        # Source 1: Sonarr series metadata (HIGHEST PRIORITY - authoritative)
        if self.sonarr_api_key:
            sonarr_result = await self._lookup_sonarr_year(series_title)
            if sonarr_result and sonarr_result.get("year"):
                result.update({
                    "resolved_title": sonarr_result.get("title", series_title),
                    "resolved_year": sonarr_result["year"],
                    "source": "sonarr_api",
                    "confidence": 1.0
                })
                logger.info(f"✅ Found in Sonarr (authoritative): {result['resolved_title']} ({result['resolved_year']})")
                return result
            # Sonarr was tried but didn't find anything
            logger.debug(f"Sonarr search completed but no results for '{series_title}'")
        
        # Source 2: TMDb TV API (authoritative)
        if self.tmdb_api_key:
            tmdb_result = await self._lookup_tmdb_year(series_title)
            if tmdb_result and tmdb_result.get("first_air_date"):
                tmdb_year = int(tmdb_result["first_air_date"][:4])
                result.update({
                    "resolved_title": tmdb_result.get("name", series_title),
                    "resolved_year": tmdb_year,
                    "source": "tmdb_api",
                    "confidence": 0.9
                })
                logger.info(f"✅ Found in TMDb (authoritative): {result['resolved_title']} ({result['resolved_year']})")
                return result
            logger.debug(f"TMDb search completed but no results for '{series_title}'")
        
        # Source 3: TVDB API (authoritative)
        if self.tvdb_api_key:
            tvdb_result = await self._lookup_tvdb_year(series_title)
            if tvdb_result and tvdb_result.get("year"):
                result.update({
                    "resolved_title": tvdb_result.get("name", series_title),
                    "resolved_year": tvdb_result["year"],
                    "source": "tvdb_api", 
                    "confidence": 0.8
                })
                logger.info(f"✅ Found in TVDB (authoritative): {result['resolved_title']} ({result['resolved_year']})")
                return result
            logger.debug(f"TVDB search completed but no results for '{series_title}'")
        
        # Source 4: User requests file (LOWEST PRIORITY - just "please find this")
        user_requests = self.load_user_requests_map()
        
        # Try exact match in user requests
        if normalized_title in user_requests:
            req = user_requests[normalized_title]
            result.update({
                "resolved_title": req["title"],
                "resolved_year": req["year"],
                "source": "user_request_file",
                "confidence": 0.7
            })
            logger.info(f"✅ Found in user requests (fallback): {req['title']} ({req['year']})")
            return result
        
        # Try fuzzy matching for user requests
        fuzzy_match = self._fuzzy_match_title(series_title, user_requests)
        if fuzzy_match:
            result.update({
                "resolved_title": fuzzy_match["title"],
                "resolved_year": fuzzy_match["year"],
                "source": "user_request_file_fuzzy",
                "confidence": 0.6
            })
            logger.info(f"✅ Fuzzy matched in user requests (fallback): {fuzzy_match['title']} ({fuzzy_match['year']})")
            return result
        
        # Last resort: Keep original title, no year
        sources_tried = []
        sources_tried.append(f"sonarr={'✓' if self.sonarr_api_key else '✗'}")
        sources_tried.append(f"tmdb={'✓' if self.tmdb_api_key else '✗'}")
        sources_tried.append(f"tvdb={'✓' if hasattr(self, 'tvdb_api_key') and self.tvdb_api_key else '✗'}")
        sources_tried.append(f"user_requests={'✓' if user_requests else '✗'}")
        
        logger.warning(f"❌ Could not resolve year for: '{series_title}' - no authoritative sources available")
        logger.warning(f"   📋 Sources checked: {', '.join(sources_tried)}")
        
        return result
    
    async def _lookup_sonarr_year(self, series_title: str) -> Optional[Dict[str, Any]]:
        """Lookup series year from Sonarr API."""
        if not self.sonarr_api_key:
            return None
            
        cache_key = f"sonarr_{self._normalize_title(series_title)}"
        if cache_key in self._sonarr_cache:
            return self._sonarr_cache[cache_key]
        
        try:
            headers = {'X-Api-Key': self.sonarr_api_key}
            timeout = aiohttp.ClientTimeout(total=10)
            
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(f"{self.sonarr_url}/api/v3/series", headers=headers) as response:
                    if response.status == 200:
                        series_list = await response.json()
                        
                        normalized_target = self._normalize_title(series_title)
                        best_match = None
                        best_score = 0
                        
                        for series in series_list:
                            series_name = series.get("title", "")
                            normalized_series = self._normalize_title(series_name)
                            
                            # Exact match
                            if normalized_series == normalized_target:
                                best_match = series
                                break
                            
                            # Fuzzy match
                            if normalized_target in normalized_series or normalized_series in normalized_target:
                                score = len(set(normalized_target) & set(normalized_series)) / max(len(normalized_target), len(normalized_series))
                                if score > best_score and score > 0.7:
                                    best_match = series
                                    best_score = score
                        
                        self._sonarr_cache[cache_key] = best_match
                        return best_match
                    else:
                        logger.warning(f"Sonarr API error: HTTP {response.status}")
        except Exception as e:
            logger.debug(f"Sonarr lookup failed for '{series_title}': {e}")
        
        self._sonarr_cache[cache_key] = None
        return None
    
    async def _lookup_tmdb_year(self, series_title: str) -> Optional[Dict[str, Any]]:
        """Lookup series year from TMDb TV API."""
        if not self.tmdb_api_key:
            return None
            
        cache_key = f"tmdb_{self._normalize_title(series_title)}"
        if cache_key in self._tmdb_cache:
            return self._tmdb_cache[cache_key]
        
        try:
            timeout = aiohttp.ClientTimeout(total=10)
            
            async with aiohttp.ClientSession(timeout=timeout) as session:
                # Search for TV series
                search_url = "https://api.themoviedb.org/3/search/tv"
                params = {
                    "api_key": self.tmdb_api_key,
                    "query": series_title,
                    "language": "en-US"
                }
                
                async with session.get(search_url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        results = data.get("results", [])
                        
                        if results:
                            # Return the first/best match
                            best_match = results[0]
                            self._tmdb_cache[cache_key] = best_match
                            return best_match
                    else:
                        logger.warning(f"TMDb API error: HTTP {response.status}")
        except Exception as e:
            logger.debug(f"TMDb lookup failed for '{series_title}': {e}")
        
        self._tmdb_cache[cache_key] = None
        return None
    
    async def _lookup_tvdb_year(self, series_title: str) -> Optional[Dict[str, Any]]:
        """
        Look up series year from TVDB API.
        Returns dictionary with series info if found.
        """
        if not hasattr(self, 'tvdb_api_key') or not self.tvdb_api_key:
            logger.debug(f"TVDB API key not configured, skipping lookup for '{series_title}'")
            return None
            
        try:
            # TVDB API v4 endpoint for series search
            search_url = "https://api4.thetvdb.com/v4/search"
            headers = {
                "Authorization": f"Bearer {self.tvdb_api_key}",
                "Content-Type": "application/json"
            }
            
            params = {
                "query": series_title,
                "type": "series",
                "limit": 5
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(search_url, headers=headers, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        if data.get("data") and len(data["data"]) > 0:
                            # Get the first (best) match
                            series = data["data"][0]
                            first_aired = series.get("first_air_time")
                            
                            if first_aired:
                                # Parse year from date string (format: YYYY-MM-DD)
                                year = int(first_aired.split("-")[0])
                                result = {
                                    "name": series.get("name", series_title),
                                    "year": year,
                                    "first_air_date": first_aired
                                }
                                logger.info(f"TVDB found '{series_title}' with year {year}")
                                return result
                                
                        logger.debug(f"TVDB: No series found matching '{series_title}'")
                        return None
                    else:
                        logger.warning(f"TVDB API error: HTTP {response.status}")
                        return None
                        
        except Exception as e:
            logger.error(f"TVDB lookup error for '{series_title}': {e}")
            return None

# Test function
async def test_tv_year_resolver():
    """Test the TV year resolver with sample titles."""
    import sys
    from pathlib import Path
    
    # Add parent directory to path for imports
    sys.path.insert(0, str(Path(__file__).parent.parent))
    
    try:
        from utils.common_helpers import load_settings
        # Load settings with correct path
        settings = load_settings("_internal/config/settings.ini")
    except ImportError:
        # Fallback: load settings directly if common_helpers not available
        import configparser
        settings_path = Path(__file__).resolve().parent.parent / "config" / "settings.ini"
        if settings_path.exists():
            config = configparser.ConfigParser(interpolation=configparser.ExtendedInterpolation())
            config.read(settings_path)
            settings = {}
            for section in config.sections():
                settings[section] = dict(config.items(section))
        else:
            settings = None

    # Test cases
    test_titles = [
        "The Office",
        "Courage the Cowardly Dog", 
        "Kim Possible",
        "Breaking Bad",
        "Game of Thrones"
    ]
    
    # Use real settings or fallback
    if settings is None:
        print("WARNING: Settings file not found, using test configuration")
        settings = {
            "tmdb_api_key": "efd5018b7283ecb6fa1c0c525783bc08",
            "TMDb": {"api_key": "efd5018b7283ecb6fa1c0c525783bc08"},
            "Sonarr": {
                "url": "http://localhost:8989",
                "api_key": "745e39af03d3443c989632c27a0fcd47"
            },
            "General": {
                "new_tv_requests_file": "new_tv_requests.txt"
            }
        }
    else:
        print("✅ Successfully loaded real settings file!")
    
    resolver = TVYearResolver(settings)
    
    print("🧪 Testing TV Year Resolver")
    print("=" * 50)
    
    for title in test_titles:
        print(f"\n🔍 Testing: {title}")
        result = await resolver.resolve_series_year(title)
        
        status = "✅" if result["resolved_year"] else "❌"
        print(f"{status} Result: {result['resolved_title']} ({result['resolved_year']}) "
              f"[{result['source']}, confidence: {result['confidence']:.1f}]")

if __name__ == "__main__":
    import asyncio
    asyncio.run(test_tv_year_resolver())
