from __future__ import annotations
import json
import urllib.parse
import urllib.request
from typing import Any, Dict, List


def _api_get(base_url: str, api_key: str, path: str, params: Dict[str, Any] | None = None, timeout: float = 15.0):
    q = urllib.parse.urlencode(params or {})
    url = f"{base_url.rstrip('/')}{path}{'?' + q if q else ''}"
    req = urllib.request.Request(url, headers={'X-Api-Key': api_key, 'User-Agent': 'PreflightAnalyzer/0.1'})
    with urllib.request.urlopen(req, timeout=timeout) as resp:  # nosec
        return json.loads(resp.read().decode('utf-8', errors='replace'))


def _api_post(base_url: str, api_key: str, path: str, payload: Dict[str, Any], timeout: float = 15.0):
    url = f"{base_url.rstrip('/')}{path}"
    data = json.dumps(payload).encode('utf-8')
    req = urllib.request.Request(url, data=data, headers={'X-Api-Key': api_key, 'Content-Type': 'application/json'})
    with urllib.request.urlopen(req, timeout=timeout) as resp:  # nosec
        return json.loads(resp.read().decode('utf-8', errors='replace'))


def fetch_releases_for_episode(base_url: str, api_key: str, episode_id: int) -> List[Dict[str, Any]]:
    return _api_get(base_url, api_key, f"/api/v3/release", params={'episodeId': episode_id})


def grab_release(base_url: str, api_key: str, guid: str):
    return _api_post(base_url, api_key, "/api/v3/release", payload={'guid': guid})


def manual_search_episode(base_url: str, api_key: str, series_id: int, episode_ids: List[int]):
    return _api_post(base_url, api_key, "/api/v3/command", payload={'name': 'manualSearch', 'seriesId': series_id, 'episodeIds': episode_ids})
