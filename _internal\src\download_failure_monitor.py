#!/usr/bin/env python3
"""
Download Failure Monitor and Fallback Trigger

This service monitors download progress through the telemetry system and
automatically triggers intelligent fallbacks when downloads fail.

Integration Points:
- Monitors telemetry for download failures
- Tracks preflight decisions and user selections
- Triggers intelligent fallback system when needed
- Updates telemetry tracking for new downloads
"""

import asyncio
import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional
from datetime import datetime, timedelta

from .intelligent_fallback_system import get_fallback_system
from ..utils.telemetry_integration import TelemetryIntegrator


class DownloadFailureMonitor:
    """
    Monitors downloads and triggers intelligent fallbacks on failure.
    """
    
    def __init__(self, settings_dict: dict, logger: logging.Logger):
        self.settings = settings_dict
        self.logger = logger
        self.fallback_system = get_fallback_system(settings_dict, logger)
        
        # Track user selections to maintain preference hierarchy
        self.user_selections: Dict[int, Dict[str, Any]] = {}
        
        # Track active downloads for failure detection
        self.active_downloads: Dict[str, Dict[str, Any]] = {}
        
        self.monitoring = False
        
    def record_user_selection(self, radarr_id: int, selected_candidate: Dict[str, Any], candidate_index: int):
        """
        Record when a user manually selects a specific candidate.
        
        Args:
            radarr_id: Radarr movie ID
            selected_candidate: The candidate data user selected
            candidate_index: Index in the preflight candidates list
        """
        self.user_selections[radarr_id] = {
            "candidate": selected_candidate,
            "index": candidate_index,
            "timestamp": datetime.now(),
            "guid": selected_candidate.get("guid")
        }
        
        self.logger.info(f"📝 Recorded user selection for movie {radarr_id}: candidate #{candidate_index + 1}")
        self.logger.info(f"   🎬 {selected_candidate.get('title')}")
    
    def record_download_start(self, radarr_id: int, candidate: Dict[str, Any], is_fallback: bool = False):
        """
        Record when a download starts.
        
        Args:
            radarr_id: Radarr movie ID
            candidate: Candidate being downloaded
            is_fallback: True if this is a fallback download
        """
        download_id = f"radarr_{radarr_id}"
        
        self.active_downloads[download_id] = {
            "radarr_id": radarr_id,
            "candidate": candidate,
            "started": datetime.now(),
            "is_fallback": is_fallback,
            "guid": candidate.get("guid"),
            "status": "downloading"
        }
        
        self.logger.info(f"📥 Tracking download: {candidate.get('title')} (Movie ID: {radarr_id})")
    
    async def start_monitoring(self, check_interval: int = 30):
        """
        Start monitoring downloads for failures.
        
        Args:
            check_interval: How often to check for failures (seconds)
        """
        if self.monitoring:
            self.logger.warning("Monitor is already running")
            return
            
        self.monitoring = True
        self.logger.info(f"🔍 Starting download failure monitoring (check interval: {check_interval}s)")
        
        try:
            while self.monitoring:
                await self._check_for_failures()
                await asyncio.sleep(check_interval)
                
        except asyncio.CancelledError:
            self.logger.info("🛑 Download monitoring cancelled")
        except Exception as e:
            self.logger.error(f"Error in download monitoring: {e}")
        finally:
            self.monitoring = False
    
    def stop_monitoring(self):
        """Stop the monitoring loop."""
        self.monitoring = False
        self.logger.info("🛑 Stopping download failure monitoring")
    
    async def _check_for_failures(self):
        """Check all active downloads for failures and trigger fallbacks if needed."""
        try:
            # Use telemetry to check download status
            async with TelemetryIntegrator(self.settings, self.logger, verbose_mode=False) as telemetry:
                
                current_downloads = []
                failed_downloads = []
                
                # Check each active download
                for download_id, download_info in list(self.active_downloads.items()):
                    radarr_id = download_info["radarr_id"]
                    
                    # Check if download has been running too long (possible stall)
                    elapsed = datetime.now() - download_info["started"]
                    max_download_time = timedelta(hours=6)  # Configurable timeout
                    
                    if elapsed > max_download_time:
                        self.logger.warning(f"⏰ Download timeout for movie {radarr_id} (running {elapsed})")
                        failed_downloads.append((download_id, download_info, "timeout"))
                        continue
                    
                    # TODO: Integrate with actual telemetry status checking
                    # For now, we'll rely on external signals or manual failure reporting
                    
                # Process any detected failures
                for download_id, download_info, failure_reason in failed_downloads:
                    await self._handle_detected_failure(download_id, download_info, failure_reason)
                    
        except Exception as e:
            self.logger.error(f"Error checking for failures: {e}")
    
    async def report_download_failure(self, radarr_id: int, failed_guid: str, failure_reason: str = "unknown"):
        """
        Manually report a download failure (called from external systems).
        
        Args:
            radarr_id: Radarr movie ID that failed
            failed_guid: GUID of the failed download
            failure_reason: Reason for failure
        """
        self.logger.error(f"💥 Download failure reported for movie {radarr_id}: {failure_reason}")
        self.logger.error(f"   📄 Failed GUID: {failed_guid}")
        
        # Find the download info
        download_id = f"radarr_{radarr_id}"
        download_info = self.active_downloads.get(download_id)
        
        if not download_info:
            # Create minimal download info for fallback processing
            download_info = {
                "radarr_id": radarr_id,
                "guid": failed_guid,
                "started": datetime.now(),
                "is_fallback": False,
                "status": "failed"
            }
        
        await self._handle_detected_failure(download_id, download_info, failure_reason)
    
    async def _handle_detected_failure(self, download_id: str, download_info: Dict[str, Any], failure_reason: str):
        """
        Handle a detected download failure by triggering intelligent fallback.
        
        Args:
            download_id: Internal download tracking ID
            download_info: Download information
            failure_reason: Reason for failure
        """
        try:
            radarr_id = download_info["radarr_id"]
            failed_guid = download_info.get("guid", "")
            
            self.logger.error(f"🔄 Processing download failure for movie {radarr_id}")
            self.logger.error(f"   💥 Reason: {failure_reason}")
            
            # Remove from active downloads
            if download_id in self.active_downloads:
                del self.active_downloads[download_id]
            
            # Check if user made a manual selection
            user_selection_index = None
            if radarr_id in self.user_selections:
                user_selection = self.user_selections[radarr_id]
                if user_selection["guid"] == failed_guid:
                    user_selection_index = user_selection["index"]
                    self.logger.info(f"📍 Failed download was user selection #{user_selection_index + 1}")
            
            # Trigger intelligent fallback
            success = await self.fallback_system.handle_download_failure(
                radarr_id=radarr_id,
                failed_guid=failed_guid,
                user_selection_index=user_selection_index
            )
            
            if success:
                self.logger.info(f"✅ Intelligent fallback triggered successfully for movie {radarr_id}")
                
                # Record the new fallback download
                # (The candidate info will be updated by the fallback system)
                
            else:
                self.logger.error(f"❌ Intelligent fallback failed for movie {radarr_id}")
                
                # Could implement additional strategies here:
                # - Notify user
                # - Mark movie as problematic
                # - Try different quality profile
                
        except Exception as e:
            self.logger.error(f"Error handling download failure: {e}")
    
    def report_download_success(self, radarr_id: int, completed_guid: str):
        """
        Report successful download completion.
        
        Args:
            radarr_id: Radarr movie ID that completed
            completed_guid: GUID of the completed download
        """
        download_id = f"radarr_{radarr_id}"
        
        if download_id in self.active_downloads:
            del self.active_downloads[download_id]
            
        # Clear any user selection tracking
        if radarr_id in self.user_selections:
            del self.user_selections[radarr_id]
            
        # Clear fallback system tracking
        self.fallback_system.clear_active_fallback(radarr_id)
        
        self.logger.info(f"✅ Download completed successfully for movie {radarr_id}")
        self.logger.info(f"   📄 GUID: {completed_guid}")
    
    def get_monitoring_status(self) -> Dict[str, Any]:
        """Get current monitoring status and statistics."""
        return {
            "monitoring": self.monitoring,
            "active_downloads": len(self.active_downloads),
            "user_selections": len(self.user_selections),
            "downloads": [
                {
                    "radarr_id": info["radarr_id"],
                    "title": info.get("candidate", {}).get("title", "Unknown"),
                    "started": info["started"].isoformat(),
                    "is_fallback": info.get("is_fallback", False),
                    "elapsed_minutes": int((datetime.now() - info["started"]).total_seconds() / 60)
                }
                for info in self.active_downloads.values()
            ]
        }


# Global instance for easy access
_failure_monitor: Optional[DownloadFailureMonitor] = None

def get_failure_monitor(settings_dict: dict, logger: logging.Logger) -> DownloadFailureMonitor:
    """Get or create the global failure monitor instance."""
    global _failure_monitor
    if _failure_monitor is None:
        _failure_monitor = DownloadFailureMonitor(settings_dict, logger)
    return _failure_monitor
