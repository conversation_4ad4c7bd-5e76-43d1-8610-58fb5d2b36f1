from pathlib import Path
import json

from _internal.src.telemetry_dashboard import write_csv, write_sqlite


def test_telemetry_sinks_write(tmp_path):
    summary = {
        'by_type': {'file.organized': 3, 'download.completed': 1},
        'by_series': {str((10, 'Chernobyl')): 2},
        'by_movie': {str((1, 'Inception')): 1},
    }
    csv_path = tmp_path / 'out.csv'
    db_path = tmp_path / 'out.db'

    write_csv(summary, csv_path)
    assert csv_path.exists() and csv_path.read_text(encoding='utf-8').startswith('metric,key,value')

    write_sqlite(summary, db_path)
    assert db_path.exists() and db_path.stat().st_size > 0

