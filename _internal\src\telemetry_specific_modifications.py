"""
SPECIFIC MODIFICATIONS FOR YOUR EXISTING TELEMETRY SYSTEM
========================================================

This file shows the exact modifications needed for your current telemetry system
to integrate the intelligent fallback functionality.

Based on your actual telemetry structure at _internal/utils/real_time_telemetry.py
"""

print("🔧 SPECIFIC MODIFICATIONS FOR RealTimeTelemetry CLASS")
print("=" * 60)
print()

print("📍 LOCATION: _internal/utils/real_time_telemetry.py")
print()

print("1️⃣ ADD TO __init__ METHOD (around line 75)")
print("-" * 50)

init_modification = '''
# IN RealTimeTelemetry.__init__() method, ADD these lines:

def __init__(self, config: dict, logger: Optional[logging.Logger] = None):
    # ... existing initialization code ...
    
    # ADD THESE NEW ATTRIBUTES:
    self.previous_status = {}  # Track previous job status for failure detection
    self.movie_failures = {}   # Track failure history per radarr_id
    self.fallback_system = None  # Will be initialized when needed
    
    # Keep track of original candidates for fallback
    self.movie_candidates = {}  # {radarr_id: {candidate_info, user_selection, etc}}
'''

print(init_modification)
print()

print("2️⃣ MODIFY THE MONITORING LOOP (around line 900-950)")
print("-" * 50)

monitoring_modification = '''
# FIND the _monitoring_loop method (around line 900) and MODIFY it:

async def _monitoring_loop(self):
    """Enhanced monitoring loop with failure detection."""
    
    self.logger.info("Starting telemetry monitoring loop")
    
    while self._running:
        try:
            # Your existing status update logic
            await self._update_statuses()
            
            # NEW: Add failure detection and fallback triggering
            await self._check_for_failures_and_trigger_fallbacks()
            
            # Your existing dashboard update
            await self._update_dashboard()
            
            await asyncio.sleep(self.interval)
            
        except asyncio.CancelledError:
            self.logger.info("Monitoring loop cancelled")
            break
        except Exception as e:
            self.logger.error(f"Error in monitoring loop: {e}")
            await asyncio.sleep(self.interval)
'''

print(monitoring_modification)
print()

print("3️⃣ ADD NEW METHODS TO RealTimeTelemetry CLASS")
print("-" * 50)

new_methods = '''
# ADD THESE NEW METHODS to the RealTimeTelemetry class:

def store_movie_candidate_info(self, radarr_id: int, candidate_details: dict, 
                              user_selection_index: Optional[int] = None,
                              original_system_recommendation_index: Optional[int] = None):
    """Store candidate information for potential fallback."""
    
    self.movie_candidates[radarr_id] = {
        "candidate_details": candidate_details,
        "user_selection_index": user_selection_index,
        "original_system_recommendation_index": original_system_recommendation_index,
        "stored_at": datetime.now()
    }
    
    self.logger.info(f"📄 Stored candidate info for Radarr ID {radarr_id}")

async def _check_for_failures_and_trigger_fallbacks(self):
    """NEW METHOD: Check for download failures and trigger intelligent fallbacks."""
    
    # Initialize fallback system if needed
    if not self.fallback_system:
        from .intelligent_fallback_system import IntelligentFallbackSystem
        self.fallback_system = IntelligentFallbackSystem(self.config, self.logger)
    
    # Check each active job for failure patterns
    for job_id, job in list(self.active_jobs.items()):
        radarr_id = job.radarr_id
        
        if not radarr_id:
            continue
        
        # Get previous status for comparison
        prev_job = self.previous_status.get(job_id)
        
        if prev_job:
            # Pattern 1: Job was downloading with progress, now failed/disappeared
            if (prev_job.progress > 0.1 and prev_job.progress < 1.0 and 
                job.status in ['failed', 'error', 'cancelled']):
                
                await self._handle_detected_failure(
                    job, f"failed_at_{prev_job.progress*100:.1f}%", radarr_id
                )
            
            # Pattern 2: Job status changed to failed
            elif (job.status in ['failed', 'error'] and 
                  prev_job.status not in ['failed', 'error']):
                
                await self._handle_detected_failure(
                    job, "status_changed_to_failed", radarr_id
                )
        
        # Pattern 3: Job disappeared from queue (common failure pattern)
        elif job_id in self.previous_status and job_id not in self.active_jobs:
            prev_job = self.previous_status[job_id]
            if prev_job.progress > 0.1 and prev_job.progress < 1.0:
                await self._handle_detected_failure(
                    prev_job, "job_disappeared_from_queue", prev_job.radarr_id
                )
    
    # Update previous status for next check
    self.previous_status = {job_id: job for job_id, job in self.active_jobs.items()}

async def _handle_detected_failure(self, failed_job: DownloadJob, failure_reason: str, radarr_id: int):
    """NEW METHOD: Handle detected failure with intelligent fallback."""
    
    movie_title = failed_job.title
    progress = getattr(failed_job, 'progress', 0) * 100
    
    self.logger.error(f"💥 DOWNLOAD FAILURE DETECTED: {movie_title}")
    self.logger.error(f"   📊 Progress when failed: {progress:.1f}%") 
    self.logger.error(f"   🚨 Failure reason: {failure_reason}")
    self.logger.error(f"   🎬 Radarr ID: {radarr_id}")
    
    # Record failure
    if radarr_id not in self.movie_failures:
        self.movie_failures[radarr_id] = []
    
    failure_record = {
        "failed_at": datetime.now(),
        "failure_reason": failure_reason,
        "progress_when_failed": progress,
        "candidate_guid": getattr(failed_job, 'download_client_id', 'unknown'),
        "job_id": failed_job.job_id
    }
    
    self.movie_failures[radarr_id].append(failure_record)
    
    # Get stored candidate info for fallback
    candidate_info = self.movie_candidates.get(radarr_id)
    
    if candidate_info and self.fallback_system:
        try:
            self.logger.info(f"🔄 Triggering intelligent fallback for {movie_title}")
            
            # Trigger fallback using stored candidate information
            async with aiohttp.ClientSession() as session:
                success = await self.fallback_system.handle_download_failure(
                    movie_title=movie_title,
                    failed_guid=failure_record["candidate_guid"],
                    radarr_id=radarr_id,
                    session=session,
                    user_selection_index=candidate_info.get("user_selection_index"),
                    original_system_recommendation_index=candidate_info.get("original_system_recommendation_index")
                )
                
                if success:
                    self.logger.info(f"✅ Intelligent fallback triggered successfully for {movie_title}")
                    
                    # Update dashboard to show fallback triggered
                    self._write_dashboard_message(
                        f"🔄 FALLBACK TRIGGERED: {movie_title} (Attempt #{len(self.movie_failures[radarr_id])})"
                    )
                else:
                    self.logger.error(f"❌ Intelligent fallback failed for {movie_title}")
                    
        except Exception as e:
            self.logger.error(f"Error triggering fallback for {movie_title}: {e}")
    else:
        self.logger.warning(f"⚠️  No candidate info stored for {movie_title} - cannot trigger fallback")

def _write_dashboard_message(self, message: str):
    """Write a message to the dashboard log file."""
    
    if hasattr(self, '_telemetry_log_file') and self._telemetry_log_file:
        timestamp = datetime.now().strftime("%H:%M:%S")
        dashboard_line = f"[{timestamp}] {message}"
        
        try:
            self._telemetry_log_file.write(dashboard_line + "\\n")
            self._telemetry_log_file.flush()
        except Exception as e:
            self.logger.error(f"Error writing to dashboard: {e}")

def get_failure_history(self, radarr_id: int) -> List[dict]:
    """Get failure history for a specific movie."""
    return self.movie_failures.get(radarr_id, [])

def get_attempt_count(self, radarr_id: int) -> int:
    """Get the number of attempts made for a specific movie."""
    return len(self.movie_failures.get(radarr_id, [])) + 1  # +1 for current attempt
'''

print(new_methods)
print()

print("4️⃣ MODIFY YOUR INTAKE SCRIPT (01_intake_and_nzb_search.py)")
print("-" * 50)

intake_modification = '''
# IN track_movie_success_with_telemetry() function (around line 6244), ADD:

async def track_movie_success_with_telemetry(result, metadata, telemetry_integrator, logger):
    """
    Enhanced movie tracking with fallback support.
    """
    if result.get('success'):
        # ... existing code ...
        
        if result.get('reason') == 'successfully_added':
            # ... existing telemetry tracking code ...
            
            if telemetry_integrator and radarr_movie_id:
                # Existing telemetry tracking
                job_id = telemetry_integrator.track_movie_download(
                    title=title,
                    radarr_id=radarr_movie_id,
                    quality=quality_profile
                )
                
                # NEW: Store candidate details for potential fallback
                candidate_details = metadata.get('selected_candidate', {})
                user_selection_index = metadata.get('user_selection_index')
                original_recommendation_index = metadata.get('original_recommendation_index')
                
                if telemetry_integrator.telemetry and candidate_details:
                    telemetry_integrator.telemetry.store_movie_candidate_info(
                        radarr_id=radarr_movie_id,
                        candidate_details=candidate_details,
                        user_selection_index=user_selection_index,
                        original_system_recommendation_index=original_recommendation_index
                    )
                
                print(f"📊 Movie queued for download: {title}")
                print(f"   🔬 Real-time tracking: {job_id[:8]}...")
                print(f"   🛡️ Fallback protection: Enabled")
                
                logger.info(f"Telemetry job started: {job_id} for movie {radarr_movie_id}")
                logger.info(f"Candidate info stored for fallback protection")
'''

print(intake_modification)
print()

print("5️⃣ ENHANCED DASHBOARD DISPLAY")
print("-" * 50)

dashboard_modification = '''
# MODIFY the dashboard display method to show attempt history:

def _format_telemetry_output(self, jobs: List[DownloadJob]) -> str:
    """Enhanced dashboard formatting with attempt tracking."""
    
    timestamp = datetime.now().strftime("%H:%M:%S")
    
    if not jobs:
        return f"[{timestamp}] No active downloads\\n"
    
    lines = [f"[{timestamp}] ENHANCED DOWNLOAD DASHBOARD"]
    lines.append("=" * 60)
    
    for job in jobs:
        title = job.title[:40]
        progress = job.progress * 100
        
        # Check if this is a fallback attempt
        attempt_count = self.get_attempt_count(job.radarr_id) if job.radarr_id else 1
        
        if attempt_count > 1:
            lines.append(f"🔄 {title} (Attempt #{attempt_count})")
            lines.append(f"   Progress: {progress:5.1f}% | Status: {job.status}")
            
            # Show previous failures
            if job.radarr_id:
                failures = self.get_failure_history(job.radarr_id)
                for i, failure in enumerate(failures[-2:], 1):  # Show last 2 failures
                    reason = failure['failure_reason'][:30]
                    failed_progress = failure['progress_when_failed']
                    lines.append(f"   Previous #{i}: Failed at {failed_progress:.1f}% ({reason})")
        else:
            lines.append(f"📥 {title}")
            lines.append(f"   Progress: {progress:5.1f}% | Status: {job.status}")
    
    lines.append("=" * 60)
    return "\\n".join(lines) + "\\n"
'''

print(dashboard_modification)
print()

print("6️⃣ TESTING THE INTEGRATION")
print("-" * 50)

test_code = '''
# ADD THIS TEST METHOD to verify the integration works:

async def test_failure_detection_integration():
    """Test the failure detection and fallback integration."""
    
    # This method can be added to RealTimeTelemetry for testing
    print("🧪 Testing failure detection integration...")
    
    # Simulate a failure scenario
    test_job = DownloadJob(
        job_id="test_job_123",
        title="Test Movie (2023)",
        radarr_id=999,
        progress=0.738,  # 73.8% like your actual failure
        status="failed"
    )
    
    # Store test candidate info
    self.store_movie_candidate_info(
        radarr_id=999,
        candidate_details={
            "title": "Test.Movie.2023.1080p.BluRay.x264-GROUP",
            "guid": "test_guid_456",
            "size": 5000000000  # 5GB
        },
        user_selection_index=27  # Like your LEGi0N example
    )
    
    # Trigger failure detection
    await self._handle_detected_failure(test_job, "simulated_failure", 999)
    
    print("✅ Failure detection test completed")
    print("📊 Check logs for fallback trigger confirmation")
'''

print(test_code)
print()

print("🚀 SUMMARY OF CHANGES:")
print("-" * 30)
print("✅ Added failure detection to monitoring loop")
print("✅ Added candidate info storage for fallback")
print("✅ Added automatic fallback triggering")
print("✅ Enhanced dashboard with attempt tracking")
print("✅ Integrated with your existing intake script")
print()

print("📋 IMPLEMENTATION ORDER:")
print("1. Add new attributes to __init__ method")
print("2. Add new methods to RealTimeTelemetry class")
print("3. Modify monitoring loop to include failure detection")
print("4. Update intake script to store candidate info")
print("5. Test with a known failure scenario")
print()

print("🎯 EXPECTED RESULT:")
print("When a download fails at 73.8% (like your Top Gun example):")
print("- System automatically detects the failure")
print("- Triggers intelligent fallback to correct candidate (#27 LEGi0N)")
print("- Prevents Radarr from downloading wrong candidate (#24 WiLDCAT)")
print("- Shows attempt progression in dashboard")
print("- Logs all actions for debugging")

if __name__ == "__main__":
    print("\n✅ Specific modification guide complete!")
    print("🔧 These changes integrate seamlessly with your existing telemetry system.")
