#!/usr/bin/env python3
"""
PlexMovieAutomator/07_poster_handler.py

Advanced Multi-Source Poster Handler - Download & Analyze Posters from Multiple Sources (Script 7)

This script implements the comprehensive poster acquisition system described in the PDFs:
- Scans folder 5 (awaiting_poster) for movies ready for poster selection
- Uses TMDB as foundational source to get movie IDs (TMDb ID, IMDb ID)
- Downloads posters from multiple sources: TMDB, Fanart.tv, ThePosterDB
- Uses AI vision models to analyze and filter poster quality
- Provides interactive selection interface for best poster
- Places selected poster.jpg in movie folder
- Moves movie + poster to folder 6 (final_plex_ready) → ready for quality check

Architecture:
- Centralized Identity: TMDB API for movie ID resolution
- Multi-source Collection: Query all sources using canonical IDs
- AI-powered Analysis: Vision models for quality assessment
- Interactive Selection: User chooses from analyzed options

Flow: Folder 5 → Multi-source Download → AI Analysis → User Selection → Folder 6
"""

import sys
import os
import logging
import shutil
import requests
import json
import time
import re
import base64
import io
import numpy as np
from pathlib import Path
from datetime import datetime
from typing import Dict, <PERSON>, Optional, Tuple
from dataclasses import dataclass
from urllib.parse import urljoin, urlparse
import base64

# Fix Windows console encoding issues
if sys.platform == "win32":
    import codecs
    sys.stdout = codecs.getwriter("utf-8")(sys.stdout.detach())
    sys.stderr = codecs.getwriter("utf-8")(sys.stderr.detach())

# Add the _internal directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "_internal"))

try:
    from utils.common_helpers import load_settings
except ImportError:
    print("Error: Could not import required modules.")
    print("Please ensure you're running this from the PlexMovieAutomator root directory.")
    sys.exit(1)

# Configure logging with UTF-8 encoding
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('_internal/logs/poster_handler.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


@dataclass
class MovieIdentifiers:
    """Container for movie identification data."""
    title: str
    year: Optional[int]
    tmdb_id: Optional[str]
    imdb_id: Optional[str]


@dataclass
class TVShowIdentifiers:
    """Container for TV show identification data."""
    series_title: str
    season_number: Optional[int]
    episode_number: Optional[int]
    episode_title: Optional[str]
    year: Optional[int]
    tvdb_id: Optional[str]
    tmdb_id: Optional[str]
    imdb_id: Optional[str]


@dataclass
class PosterInfo:
    """Container for poster metadata."""
    url: str
    source: str
    width: Optional[int]
    height: Optional[int]
    aspect_ratio: Optional[float]
    vote_average: Optional[float]
    language: Optional[str]
    file_path: Optional[str]
    description: Optional[str]


class UnifiedAPIClient:
    """
    Unified client for accessing multiple poster sources.
    Implements the centralized identity architecture from the PDFs.
    """

    def __init__(self, settings: dict):
        self.settings = settings
        self.tmdb_api_key = settings.get('APIKeys', {}).get('tmdb_api_key')
        self.fanart_api_key = settings.get('APIKeys', {}).get('fanart_api_key')
        self.fanart_client_key = settings.get('APIKeys', {}).get('fanart_client_key')
        self.theposterdb_username = settings.get('APIKeys', {}).get('theposterdb_username')
        self.theposterdb_password = settings.get('APIKeys', {}).get('theposterdb_password')

        # Session for maintaining cookies (needed for ThePosterDB)
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })

    def find_movie_id(self, title: str, year: Optional[int] = None) -> Optional[MovieIdentifiers]:
        """
        Find movie IDs using TMDB as the foundational source.
        Returns TMDb ID and IMDb ID for use with other sources.
        """
        try:
            if not self.tmdb_api_key:
                logger.warning("⚠️ No TMDB API key found in settings")
                return None

            # Search for movie on TMDB
            search_url = "https://api.themoviedb.org/3/search/movie"
            params = {
                'api_key': self.tmdb_api_key,
                'query': title
            }
            if year:
                params['year'] = year

            logger.info(f"🔍 Searching TMDB for: {title} ({year})")
            response = self.session.get(search_url, params=params, timeout=10)
            response.raise_for_status()
            search_data = response.json()

            if not search_data.get('results'):
                logger.warning(f"⚠️ No TMDB results found for: {title}")
                return None

            # Get the first result
            movie_data = search_data['results'][0]
            tmdb_id = str(movie_data['id'])

            # Get detailed info including IMDb ID
            details_url = f"https://api.themoviedb.org/3/movie/{tmdb_id}"
            details_params = {'api_key': self.tmdb_api_key}

            details_response = self.session.get(details_url, params=details_params, timeout=10)
            details_response.raise_for_status()
            details_data = details_response.json()

            imdb_id = details_data.get('imdb_id')

            identifiers = MovieIdentifiers(
                title=title,
                year=year,
                tmdb_id=tmdb_id,
                imdb_id=imdb_id
            )

            logger.info(f"✅ Found movie IDs - TMDb: {tmdb_id}, IMDb: {imdb_id}")
            return identifiers

        except Exception as e:
            logger.error(f"❌ Failed to find movie ID for {title}: {e}")
            return None

    def get_posters_from_tmdb(self, movie_id: str) -> List[PosterInfo]:
        """
        Get all posters from TMDB for a movie.
        TMDB can have dozens or hundreds of posters per movie.
        """
        try:
            if not self.tmdb_api_key or not movie_id:
                return []

            # Get movie images from TMDB
            images_url = f"https://api.themoviedb.org/3/movie/{movie_id}/images"
            params = {
                'api_key': self.tmdb_api_key,
                'include_image_language': 'en,null'  # English and no language
            }

            logger.info(f"📥 Fetching TMDB posters for movie ID: {movie_id}")
            response = self.session.get(images_url, params=params, timeout=10)
            response.raise_for_status()
            images_data = response.json()

            posters = []
            poster_list = images_data.get('posters', [])

            logger.info(f"📊 Found {len(poster_list)} posters on TMDB")

            for poster_data in poster_list:
                file_path = poster_data.get('file_path')
                if not file_path:
                    continue

                # Construct full URL (use 'original' for highest quality)
                poster_url = f"https://image.tmdb.org/t/p/original{file_path}"

                poster_info = PosterInfo(
                    url=poster_url,
                    source="TMDB",
                    width=poster_data.get('width'),
                    height=poster_data.get('height'),
                    aspect_ratio=poster_data.get('aspect_ratio'),
                    vote_average=poster_data.get('vote_average'),
                    language=poster_data.get('iso_639_1'),
                    file_path=file_path,
                    description=f"TMDB poster ({poster_data.get('width')}x{poster_data.get('height')})"
                )
                posters.append(poster_info)

            logger.info(f"✅ Collected {len(posters)} TMDB posters")
            return posters

        except Exception as e:
            logger.error(f"❌ Failed to get TMDB posters: {e}")
            return []

    def get_posters_from_fanart(self, movie_id: str) -> List[PosterInfo]:
        """
        Get posters from Fanart.tv using TMDb ID.
        Fanart.tv provides high-quality community artwork.
        """
        try:
            if not self.fanart_api_key or not movie_id:
                return []

            # Query Fanart.tv API
            fanart_url = f"http://webservice.fanart.tv/v3/movies/{movie_id}"
            params = {
                'api_key': self.fanart_api_key
            }
            if self.fanart_client_key:
                params['client_key'] = self.fanart_client_key

            logger.info(f"📥 Fetching Fanart.tv posters for movie ID: {movie_id}")
            response = self.session.get(fanart_url, params=params, timeout=10)
            response.raise_for_status()
            fanart_data = response.json()

            posters = []

            # Fanart.tv has different poster types
            poster_types = ['movieposter', 'moviebanner']

            for poster_type in poster_types:
                poster_list = fanart_data.get(poster_type, [])

                for poster_data in poster_list:
                    poster_url = poster_data.get('url')
                    if not poster_url:
                        continue

                    poster_info = PosterInfo(
                        url=poster_url,
                        source="Fanart.tv",
                        width=None,  # Fanart.tv doesn't provide dimensions
                        height=None,
                        aspect_ratio=None,
                        vote_average=poster_data.get('likes', 0),  # Use likes as quality metric
                        language=poster_data.get('lang'),
                        file_path=None,
                        description=f"Fanart.tv {poster_type} (likes: {poster_data.get('likes', 0)})"
                    )
                    posters.append(poster_info)

            logger.info(f"✅ Collected {len(posters)} Fanart.tv posters")
            return posters

        except Exception as e:
            logger.error(f"❌ Failed to get Fanart.tv posters: {e}")
            return []

    def get_posters_from_theposterdb(self, movie_id: str) -> List[PosterInfo]:
        """
        Get posters from ThePosterDB using web scraping.
        ThePosterDB has no public API, so we need to scrape HTML.
        This is brittle and may break if the site changes.
        """
        try:
            if not self.theposterdb_username or not self.theposterdb_password or not movie_id:
                logger.warning("⚠️ ThePosterDB credentials not configured, skipping")
                return []

            # First, login to ThePosterDB
            if not self._login_to_theposterdb():
                return []

            # Search for movie using TMDb ID
            search_url = "https://theposterdb.com/search"
            search_params = {'term': movie_id}

            logger.info(f"📥 Searching ThePosterDB for movie ID: {movie_id}")
            response = self.session.get(search_url, params=search_params, timeout=15)
            response.raise_for_status()

            # Parse HTML to find movie page
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(response.text, 'html.parser')

            # Look for movie links in search results
            movie_links = soup.find_all('a', href=re.compile(r'/movie/\d+'))

            if not movie_links:
                logger.warning(f"⚠️ No ThePosterDB results found for movie ID: {movie_id}")
                return []

            # Get the first movie result
            movie_url = urljoin("https://theposterdb.com", movie_links[0]['href'])

            # Fetch movie page
            logger.info(f"📥 Fetching ThePosterDB movie page: {movie_url}")
            movie_response = self.session.get(movie_url, timeout=15)
            movie_response.raise_for_status()

            # Parse movie page for poster links
            movie_soup = BeautifulSoup(movie_response.text, 'html.parser')

            posters = []

            # Look for poster download links
            poster_links = movie_soup.find_all('a', href=re.compile(r'/api/assets/\d+'))

            for link in poster_links:
                poster_url = urljoin("https://theposterdb.com", link['href'])

                # Try to get poster metadata from surrounding elements
                poster_container = link.find_parent('div', class_=re.compile(r'poster|image'))
                description = "ThePosterDB poster"

                if poster_container:
                    # Look for title or description
                    title_elem = poster_container.find(['h3', 'h4', 'span'], class_=re.compile(r'title|name'))
                    if title_elem:
                        description = f"ThePosterDB: {title_elem.get_text(strip=True)}"

                poster_info = PosterInfo(
                    url=poster_url,
                    source="ThePosterDB",
                    width=None,  # ThePosterDB doesn't provide dimensions in HTML
                    height=None,
                    aspect_ratio=None,
                    vote_average=None,
                    language=None,
                    file_path=None,
                    description=description
                )
                posters.append(poster_info)

            logger.info(f"✅ Collected {len(posters)} ThePosterDB posters")
            return posters

        except ImportError:
            logger.warning("⚠️ BeautifulSoup not available, skipping ThePosterDB")
            return []
        except Exception as e:
            logger.error(f"❌ Failed to get ThePosterDB posters: {e}")
            return []

    def _login_to_theposterdb(self) -> bool:
        """
        Login to ThePosterDB to access poster downloads.
        Returns True if login successful, False otherwise.
        """
        try:
            # Get login page to extract CSRF token
            login_page_url = "https://theposterdb.com/login"
            login_page = self.session.get(login_page_url, timeout=10)
            login_page.raise_for_status()

            from bs4 import BeautifulSoup
            soup = BeautifulSoup(login_page.text, 'html.parser')

            # Find CSRF token
            csrf_token = None
            csrf_input = soup.find('input', {'name': '_token'})
            if csrf_input:
                csrf_token = csrf_input.get('value')

            # Prepare login data
            login_data = {
                'email': self.theposterdb_username,
                'password': self.theposterdb_password,
            }

            if csrf_token:
                login_data['_token'] = csrf_token

            # Submit login
            login_url = "https://theposterdb.com/login"
            login_response = self.session.post(login_url, data=login_data, timeout=10)

            # Check if login was successful (usually redirects or changes page content)
            if login_response.status_code == 200 and 'dashboard' in login_response.url.lower():
                logger.info("✅ Successfully logged into ThePosterDB")
                return True
            else:
                logger.warning("⚠️ ThePosterDB login may have failed")
                return False

        except ImportError:
            logger.warning("⚠️ BeautifulSoup not available for ThePosterDB login")
            return False
        except Exception as e:
            logger.error(f"❌ Failed to login to ThePosterDB: {e}")
            return False

    def collect_all_posters(self, identifiers: MovieIdentifiers) -> List[PosterInfo]:
        """
        Collect posters from all available sources.
        Implements the multi-source architecture from the PDFs.
        """
        all_posters = []

        logger.info(f"🎨 Collecting posters from all sources for: {identifiers.title}")

        # 1. TMDB - Foundational source
        if identifiers.tmdb_id:
            tmdb_posters = self.get_posters_from_tmdb(identifiers.tmdb_id)
            all_posters.extend(tmdb_posters)

        # 2. Fanart.tv - Community artwork
        if identifiers.tmdb_id:
            fanart_posters = self.get_posters_from_fanart(identifiers.tmdb_id)
            all_posters.extend(fanart_posters)

        # 3. ThePosterDB - Dedicated poster repository
        if identifiers.tmdb_id:
            theposterdb_posters = self.get_posters_from_theposterdb(identifiers.tmdb_id)
            all_posters.extend(theposterdb_posters)

        logger.info(f"📊 Total posters collected: {len(all_posters)}")
        logger.info(f"   TMDB: {len([p for p in all_posters if p.source == 'TMDB'])}")
        logger.info(f"   Fanart.tv: {len([p for p in all_posters if p.source == 'Fanart.tv'])}")
        logger.info(f"   ThePosterDB: {len([p for p in all_posters if p.source == 'ThePosterDB'])}")

        return all_posters


class TVShowAPIClient:
    """
    Unified client for accessing TV show poster sources.
    
    TV Show equivalent of UnifiedAPIClient with episode-specific handling:
    - TVDB as foundational TV show source (gets TVDB ID, series info)
    - TMDB for additional TV show posters (supports TV shows) 
    - Fanart.tv for TV show community artwork
    - Episode-specific poster discovery and management
    - Season poster support and series-wide poster handling
    """

    def __init__(self, settings: dict):
        self.settings = settings
        self.tvdb_api_key = settings.get('APIKeys', {}).get('tvdb_api_key')  # TVDB API key
        self.tvdb_pin = settings.get('APIKeys', {}).get('tvdb_pin')  # TVDB user PIN
        self.tmdb_api_key = settings.get('APIKeys', {}).get('tmdb_api_key')  # TMDB also supports TV
        self.fanart_api_key = settings.get('APIKeys', {}).get('fanart_api_key')  # Fanart.tv supports TV
        self.fanart_client_key = settings.get('APIKeys', {}).get('fanart_client_key')

        # Session for API calls
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # TVDB authentication token
        self.tvdb_token = None

    def authenticate_tvdb(self) -> bool:
        """
        Authenticate with TVDB API to get access token.
        TVDB requires authentication for all API calls.
        """
        try:
            if not self.tvdb_api_key:
                logger.warning("⚠️ No TVDB API key found in settings")
                return False

            # TVDB v4 API authentication
            auth_url = "https://api4.thetvdb.com/v4/login"
            auth_data = {
                "apikey": self.tvdb_api_key
            }
            
            if self.tvdb_pin:
                auth_data["pin"] = self.tvdb_pin

            logger.info("🔐 Authenticating with TVDB API...")
            response = self.session.post(auth_url, json=auth_data, timeout=10)
            response.raise_for_status()
            
            auth_result = response.json()
            if auth_result.get('status') == 'success':
                self.tvdb_token = auth_result['data']['token']
                self.session.headers['Authorization'] = f'Bearer {self.tvdb_token}'
                logger.info("✅ TVDB authentication successful")
                return True
            else:
                logger.error("❌ TVDB authentication failed")
                return False

        except Exception as e:
            logger.error(f"❌ Failed to authenticate with TVDB: {e}")
            return False

    def find_tv_show_id(self, series_title: str, year: Optional[int] = None) -> Optional[TVShowIdentifiers]:
        """
        Find TV show IDs using TVDB as the foundational source.
        Returns TVDB ID, TMDB ID, and IMDb ID for use with other sources.
        """
        try:
            # First authenticate with TVDB if not already done
            if not self.tvdb_token and not self.authenticate_tvdb():
                logger.warning("⚠️ TVDB authentication failed, trying TMDB as fallback")
                return self._find_tv_show_id_tmdb_fallback(series_title, year)

            # Search for TV show on TVDB
            search_url = "https://api4.thetvdb.com/v4/search"
            params = {
                'query': series_title,
                'type': 'series',
                'limit': 10
            }
            if year:
                params['year'] = year

            logger.info(f"🔍 Searching TVDB for TV show: {series_title} ({year})")
            response = self.session.get(search_url, params=params, timeout=10)
            response.raise_for_status()
            search_data = response.json()

            if not search_data.get('data') or not search_data['data']:
                logger.warning(f"⚠️ No TVDB results found for: {series_title}")
                return self._find_tv_show_id_tmdb_fallback(series_title, year)

            # Get the first result
            series_data = search_data['data'][0]
            tvdb_id = str(series_data['tvdb_id'])

            # Get detailed series info for additional IDs
            details_url = f"https://api4.thetvdb.com/v4/series/{tvdb_id}/extended"
            details_response = self.session.get(details_url, timeout=10)
            details_response.raise_for_status()
            details_data = details_response.json()

            series_details = details_data.get('data', {})
            
            # Extract additional IDs
            tmdb_id = None
            imdb_id = None
            
            remote_ids = series_details.get('remoteIds', [])
            for remote_id in remote_ids:
                if remote_id.get('sourceName') == 'TheMovieDB.com':
                    tmdb_id = remote_id.get('id')
                elif remote_id.get('sourceName') == 'IMDB':
                    imdb_id = remote_id.get('id')

            identifiers = TVShowIdentifiers(
                series_title=series_title,
                season_number=None,
                episode_number=None,
                episode_title=None,
                year=year,
                tvdb_id=tvdb_id,
                tmdb_id=tmdb_id,
                imdb_id=imdb_id
            )

            logger.info(f"✅ Found TV show IDs - TVDB: {tvdb_id}, TMDB: {tmdb_id}, IMDb: {imdb_id}")
            return identifiers

        except Exception as e:
            logger.error(f"❌ Failed to find TV show ID for {series_title}: {e}")
            return self._find_tv_show_id_tmdb_fallback(series_title, year)

    def _find_tv_show_id_tmdb_fallback(self, series_title: str, year: Optional[int] = None) -> Optional[TVShowIdentifiers]:
        """
        Fallback method to find TV show IDs using TMDB when TVDB fails.
        """
        try:
            if not self.tmdb_api_key:
                logger.warning("⚠️ No TMDB API key found for TV show fallback")
                return None

            # Search for TV show on TMDB
            search_url = "https://api.themoviedb.org/3/search/tv"
            params = {
                'api_key': self.tmdb_api_key,
                'query': series_title
            }
            if year:
                params['first_air_date_year'] = year

            logger.info(f"🔍 Searching TMDB (fallback) for TV show: {series_title} ({year})")
            response = self.session.get(search_url, params=params, timeout=10)
            response.raise_for_status()
            search_data = response.json()

            if not search_data.get('results'):
                logger.warning(f"⚠️ No TMDB TV results found for: {series_title}")
                return None

            # Get the first result
            tv_data = search_data['results'][0]
            tmdb_id = str(tv_data['id'])

            # Get detailed info including external IDs
            details_url = f"https://api.themoviedb.org/3/tv/{tmdb_id}/external_ids"
            details_params = {'api_key': self.tmdb_api_key}

            details_response = self.session.get(details_url, params=details_params, timeout=10)
            details_response.raise_for_status()
            details_data = details_response.json()

            tvdb_id = details_data.get('tvdb_id')
            imdb_id = details_data.get('imdb_id')

            identifiers = TVShowIdentifiers(
                series_title=series_title,
                season_number=None,
                episode_number=None,
                episode_title=None,
                year=year,
                tvdb_id=str(tvdb_id) if tvdb_id else None,
                tmdb_id=tmdb_id,
                imdb_id=imdb_id
            )

            logger.info(f"✅ Found TV show IDs (TMDB fallback) - TVDB: {tvdb_id}, TMDB: {tmdb_id}, IMDb: {imdb_id}")
            return identifiers

        except Exception as e:
            logger.error(f"❌ Failed to find TV show ID (TMDB fallback) for {series_title}: {e}")
            return None

    def get_posters_from_tvdb(self, tvdb_id: str, season_number: Optional[int] = None) -> List[PosterInfo]:
        """
        Get posters from TVDB for a TV show or specific season.
        TVDB has series posters, season posters, and episode thumbnails.
        """
        try:
            if not self.tvdb_token or not tvdb_id:
                return []

            posters = []

            if season_number is not None:
                # Get season-specific posters
                season_url = f"https://api4.thetvdb.com/v4/series/{tvdb_id}/seasons/default"
                season_response = self.session.get(season_url, timeout=10)
                season_response.raise_for_status()
                season_data = season_response.json()

                # Find the specific season
                seasons = season_data.get('data', {}).get('seasons', [])
                target_season = None
                for season in seasons:
                    if season.get('number') == season_number:
                        target_season = season
                        break

                if target_season:
                    season_id = target_season.get('id')
                    artwork_url = f"https://api4.thetvdb.com/v4/seasons/{season_id}/extended"
                    artwork_response = self.session.get(artwork_url, timeout=10)
                    artwork_response.raise_for_status()
                    artwork_data = artwork_response.json()

                    season_artwork = artwork_data.get('data', {}).get('artwork', [])
                    for artwork in season_artwork:
                        if artwork.get('type') == 'season':
                            poster_url = artwork.get('image')
                            if poster_url:
                                poster_info = PosterInfo(
                                    url=poster_url,
                                    source="TVDB",
                                    width=artwork.get('width'),
                                    height=artwork.get('height'),
                                    aspect_ratio=None,
                                    vote_average=None,
                                    language=artwork.get('language'),
                                    file_path=None,
                                    description=f"TVDB Season {season_number} poster"
                                )
                                posters.append(poster_info)

            else:
                # Get series-wide posters
                artwork_url = f"https://api4.thetvdb.com/v4/series/{tvdb_id}/artworks"
                params = {'type': 'poster'}
                
                logger.info(f"📥 Fetching TVDB posters for series ID: {tvdb_id}")
                response = self.session.get(artwork_url, params=params, timeout=10)
                response.raise_for_status()
                artwork_data = response.json()

                artwork_list = artwork_data.get('data', [])
                logger.info(f"📊 Found {len(artwork_list)} posters on TVDB")

                for artwork in artwork_list:
                    poster_url = artwork.get('image')
                    if not poster_url:
                        continue

                    poster_info = PosterInfo(
                        url=poster_url,
                        source="TVDB",
                        width=artwork.get('width'),
                        height=artwork.get('height'),
                        aspect_ratio=None,
                        vote_average=None,
                        language=artwork.get('language'),
                        file_path=None,
                        description=f"TVDB series poster ({artwork.get('width')}x{artwork.get('height')})"
                    )
                    posters.append(poster_info)

            logger.info(f"✅ Collected {len(posters)} TVDB posters")
            return posters

        except Exception as e:
            logger.error(f"❌ Failed to get TVDB posters: {e}")
            return []

    def get_posters_from_tmdb_tv(self, tmdb_id: str, season_number: Optional[int] = None) -> List[PosterInfo]:
        """
        Get TV show posters from TMDB (which also supports TV shows).
        Can get series posters or season-specific posters.
        """
        try:
            if not self.tmdb_api_key or not tmdb_id:
                return []

            posters = []

            if season_number is not None:
                # Get season-specific posters from TMDB
                season_images_url = f"https://api.themoviedb.org/3/tv/{tmdb_id}/season/{season_number}/images"
                params = {
                    'api_key': self.tmdb_api_key,
                    'include_image_language': 'en,null'
                }

                logger.info(f"📥 Fetching TMDB TV season {season_number} posters for series ID: {tmdb_id}")
                response = self.session.get(season_images_url, params=params, timeout=10)
                response.raise_for_status()
                images_data = response.json()

                poster_list = images_data.get('posters', [])
                logger.info(f"📊 Found {len(poster_list)} season posters on TMDB")

                for poster_data in poster_list:
                    file_path = poster_data.get('file_path')
                    if not file_path:
                        continue

                    poster_url = f"https://image.tmdb.org/t/p/original{file_path}"

                    poster_info = PosterInfo(
                        url=poster_url,
                        source="TMDB",
                        width=poster_data.get('width'),
                        height=poster_data.get('height'),
                        aspect_ratio=poster_data.get('aspect_ratio'),
                        vote_average=poster_data.get('vote_average'),
                        language=poster_data.get('iso_639_1'),
                        file_path=file_path,
                        description=f"TMDB TV Season {season_number} poster ({poster_data.get('width')}x{poster_data.get('height')})"
                    )
                    posters.append(poster_info)

            else:
                # Get series-wide posters from TMDB
                images_url = f"https://api.themoviedb.org/3/tv/{tmdb_id}/images"
                params = {
                    'api_key': self.tmdb_api_key,
                    'include_image_language': 'en,null'
                }

                logger.info(f"📥 Fetching TMDB TV series posters for series ID: {tmdb_id}")
                response = self.session.get(images_url, params=params, timeout=10)
                response.raise_for_status()
                images_data = response.json()

                poster_list = images_data.get('posters', [])
                logger.info(f"📊 Found {len(poster_list)} series posters on TMDB")

                for poster_data in poster_list:
                    file_path = poster_data.get('file_path')
                    if not file_path:
                        continue

                    poster_url = f"https://image.tmdb.org/t/p/original{file_path}"

                    poster_info = PosterInfo(
                        url=poster_url,
                        source="TMDB",
                        width=poster_data.get('width'),
                        height=poster_data.get('height'),
                        aspect_ratio=poster_data.get('aspect_ratio'),
                        vote_average=poster_data.get('vote_average'),
                        language=poster_data.get('iso_639_1'),
                        file_path=file_path,
                        description=f"TMDB TV series poster ({poster_data.get('width')}x{poster_data.get('height')})"
                    )
                    posters.append(poster_info)

            logger.info(f"✅ Collected {len(posters)} TMDB TV posters")
            return posters

        except Exception as e:
            logger.error(f"❌ Failed to get TMDB TV posters: {e}")
            return []

    def get_posters_from_fanart_tv(self, tvdb_id: str) -> List[PosterInfo]:
        """
        Get TV show posters from Fanart.tv using TVDB ID.
        Fanart.tv provides high-quality community TV show artwork.
        """
        try:
            if not self.fanart_api_key or not tvdb_id:
                return []

            # Query Fanart.tv API for TV shows
            fanart_url = f"http://webservice.fanart.tv/v3/tv/{tvdb_id}"
            params = {
                'api_key': self.fanart_api_key
            }
            if self.fanart_client_key:
                params['client_key'] = self.fanart_client_key

            logger.info(f"📥 Fetching Fanart.tv TV posters for TVDB ID: {tvdb_id}")
            response = self.session.get(fanart_url, params=params, timeout=10)
            response.raise_for_status()
            fanart_data = response.json()

            posters = []

            # Fanart.tv has different TV show poster types
            tv_poster_types = ['tvposter', 'seasonposter', 'tvbanner']

            for poster_type in tv_poster_types:
                poster_list = fanart_data.get(poster_type, [])

                for poster_data in poster_list:
                    poster_url = poster_data.get('url')
                    if not poster_url:
                        continue

                    poster_info = PosterInfo(
                        url=poster_url,
                        source="Fanart.tv",
                        width=None,  # Fanart.tv doesn't provide dimensions
                        height=None,
                        aspect_ratio=None,
                        vote_average=poster_data.get('likes', 0),  # Use likes as quality metric
                        language=poster_data.get('lang'),
                        file_path=None,
                        description=f"Fanart.tv TV {poster_type} (likes: {poster_data.get('likes', 0)})"
                    )
                    posters.append(poster_info)

            logger.info(f"✅ Collected {len(posters)} Fanart.tv TV posters")
            return posters

        except Exception as e:
            logger.error(f"❌ Failed to get Fanart.tv TV posters: {e}")
            return []

    def collect_all_tv_posters(self, identifiers: TVShowIdentifiers, season_number: Optional[int] = None) -> List[PosterInfo]:
        """
        Collect TV show posters from all available sources.
        
        TV Show equivalent of collect_all_posters() with episode-specific handling:
        - TVDB as foundational TV show source
        - TMDB for additional TV show posters
        - Fanart.tv for TV show community artwork
        - Season-specific poster support
        """
        all_posters = []

        series_name = f"{identifiers.series_title}"
        if season_number:
            series_name += f" Season {season_number}"

        logger.info(f"🎨 Collecting TV show posters from all sources for: {series_name}")

        # 1. TVDB - Foundational TV show source
        if identifiers.tvdb_id:
            tvdb_posters = self.get_posters_from_tvdb(identifiers.tvdb_id, season_number)
            all_posters.extend(tvdb_posters)

        # 2. TMDB - Additional TV show posters
        if identifiers.tmdb_id:
            tmdb_tv_posters = self.get_posters_from_tmdb_tv(identifiers.tmdb_id, season_number)
            all_posters.extend(tmdb_tv_posters)

        # 3. Fanart.tv - Community TV show artwork
        if identifiers.tvdb_id:
            fanart_tv_posters = self.get_posters_from_fanart_tv(identifiers.tvdb_id)
            all_posters.extend(fanart_tv_posters)

        logger.info(f"📊 Total TV show posters collected: {len(all_posters)}")
        logger.info(f"   TVDB: {len([p for p in all_posters if p.source == 'TVDB'])}")
        logger.info(f"   TMDB: {len([p for p in all_posters if p.source == 'TMDB'])}")
        logger.info(f"   Fanart.tv: {len([p for p in all_posters if p.source == 'Fanart.tv'])}")

        return all_posters


def download_poster_image(poster_info: PosterInfo, save_path: Path) -> bool:
    """
    Download a poster image from URL to local file, or copy from local file if it's a personal image.

    Args:
        poster_info: PosterInfo object with URL and metadata
        save_path: Path where to save the image

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Check if this is a local file (personal image)
        if poster_info.url.startswith("file://"):
            # Handle local personal image
            local_path = Path(poster_info.url.replace("file://", ""))
            if not local_path.exists():
                logger.error(f"❌ Personal image file not found: {local_path}")
                return False
            
            logger.info(f"� Copying personal image: {local_path.name}")
            
            # Ensure directory exists
            save_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Copy the local file
            import shutil
            shutil.copy2(local_path, save_path)
            
            file_size_mb = save_path.stat().st_size / (1024 * 1024)
            logger.info(f"✅ Copied personal image: {save_path.name} ({file_size_mb:.1f} MB)")
            return True
        
        else:
            # Handle remote URL download
            logger.info(f"�📥 Downloading poster from {poster_info.source}: {poster_info.url}")

            response = requests.get(poster_info.url, timeout=30, stream=True)
            response.raise_for_status()

            # Ensure directory exists
            save_path.parent.mkdir(parents=True, exist_ok=True)

            # Save image
            with open(save_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)

            file_size_mb = save_path.stat().st_size / (1024 * 1024)
            logger.info(f"✅ Downloaded poster: {save_path.name} ({file_size_mb:.1f} MB)")
            return True

    except Exception as e:
        logger.error(f"❌ Failed to download/copy poster: {e}")
        return False


def calculate_text_coverage_from_regions(text_regions: list, img_width: int, img_height: int) -> float:
    """
    Calculate text coverage percentage from bounding box regions.

    Args:
        text_regions: List of [x_min, y_min, x_max, y_max] normalized coordinates (0-1)
        img_width: Image width in pixels
        img_height: Image height in pixels

    Returns:
        float: Text coverage percentage (0-100)
    """
    if not text_regions:
        return 0.0

    total_image_area = img_width * img_height
    total_text_area = 0

    for region in text_regions:
        if len(region) == 4:
            x_min, y_min, x_max, y_max = region
            # Convert normalized coordinates to pixels
            x_min_px = max(0, min(img_width, x_min * img_width))
            y_min_px = max(0, min(img_height, y_min * img_height))
            x_max_px = max(0, min(img_width, x_max * img_width))
            y_max_px = max(0, min(img_height, y_max * img_height))

            # Calculate area of this text region
            region_area = max(0, (x_max_px - x_min_px) * (y_max_px - y_min_px))
            total_text_area += region_area

    # Calculate percentage
    coverage_percent = (total_text_area / total_image_area) * 100.0
    return min(100.0, coverage_percent)  # Cap at 100%


def calculate_ocr_text_coverage(image_path: Path) -> float:
    """
    Calculate text coverage using OCR (Tesseract) bounding boxes.

    Args:
        image_path: Path to the image file

    Returns:
        float: Text coverage percentage (0-100) based on OCR detection
    """
    try:
        import pytesseract
        from PIL import Image
        
        # Configure Tesseract executable path for Windows
        pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
        
        # Load image
        with Image.open(image_path) as img:
            if img.mode != 'RGB':
                img = img.convert('RGB')
            
            width, height = img.size
            total_area = width * height
            
            # Get bounding box data from Tesseract
            data = pytesseract.image_to_data(img, output_type=pytesseract.Output.DICT)
            
            total_text_area = 0
            for i in range(len(data['text'])):
                # Only count confident text detections
                confidence = int(data['conf'][i])
                if confidence > 30 and data['text'][i].strip():  # Filter out low confidence
                    x = data['left'][i]
                    y = data['top'][i]
                    w = data['width'][i]
                    h = data['height'][i]
                    
                    # Calculate area of this text region
                    region_area = w * h
                    total_text_area += region_area
            
            # Calculate percentage
            coverage_percent = (total_text_area / total_area) * 100.0
            return min(100.0, coverage_percent)  # Cap at 100%
            
    except ImportError:
        logger.info("🔍 Tesseract/pytesseract not available for OCR coverage calculation")
        return 0.0  # Return 0 so edge detection can take over
    except Exception as e:
        logger.info(f"🔍 OCR coverage calculation failed: {e}")
        return 0.0  # Return 0 so edge detection can take over


def coverage_from_boxes(boxes: List[List[float]], w: int, h: int) -> float:
    """
    Calculate text coverage percentage from normalized bounding boxes.
    
    Args:
        boxes: List of [x_min, y_min, x_max, y_max] normalized coordinates
        w: Image width (for logging only, boxes are already normalized)
        h: Image height (for logging only, boxes are already normalized)
        
    Returns:
        float: Coverage percentage (0-100)
    """
    if not boxes:
        return 0.0
    
    total_area = 0.0
    for box in boxes:
        if len(box) == 4:
            x1, y1, x2, y2 = box
            # Ensure coordinates are in valid range
            x1 = max(0.0, min(1.0, x1))
            y1 = max(0.0, min(1.0, y1))
            x2 = max(0.0, min(1.0, x2))
            y2 = max(0.0, min(1.0, y2))
            
            # Calculate area (already normalized, so this gives percentage directly)
            area = max(0.0, (x2 - x1) * (y2 - y1))
            total_area += area
    
    return min(100.0, total_area * 100.0)  # Convert to percentage and cap at 100%


def calculate_edge_density_coverage(img_array, width: int, height: int) -> float:
    """
    Calculate text coverage using MUCH better edge detection that actually varies per image.

    Args:
        img_array: numpy array of image data
        width: Image width
        height: Image height

    Returns:
        float: Text coverage percentage (0-100) based on edge density
    """
    import numpy as np

    # Convert to grayscale
    gray = np.dot(img_array[...,:3], [0.2989, 0.5870, 0.1140])

    try:
        from scipy import ndimage
        
        # Use multiple edge detection approaches and combine them
        # 1. Sobel edge detection
        sobel_x = ndimage.sobel(gray, axis=1)
        sobel_y = ndimage.sobel(gray, axis=0)
        sobel_magnitude = np.sqrt(sobel_x**2 + sobel_y**2)
        
        # 2. Laplacian edge detection (good for text)
        laplacian = ndimage.laplace(gray)
        laplacian_magnitude = np.abs(laplacian)
        
        # 3. Gradient magnitude
        grad_x, grad_y = np.gradient(gray)
        gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2)
        
        # Use adaptive thresholding instead of fixed percentiles
        # Calculate statistics for each method
        sobel_mean = np.mean(sobel_magnitude)
        sobel_std = np.std(sobel_magnitude)
        sobel_threshold = sobel_mean + 2 * sobel_std  # 2 standard deviations above mean
        
        laplacian_mean = np.mean(laplacian_magnitude)
        laplacian_std = np.std(laplacian_magnitude)
        laplacian_threshold = laplacian_mean + 1.5 * laplacian_std
        
        gradient_mean = np.mean(gradient_magnitude)
        gradient_std = np.std(gradient_magnitude)
        gradient_threshold = gradient_mean + 2 * gradient_std
        
        # Count strong edges using adaptive thresholds
        sobel_edges = sobel_magnitude > sobel_threshold
        laplacian_edges = laplacian_magnitude > laplacian_threshold
        gradient_edges = gradient_magnitude > gradient_threshold
        
        # Combine edge detections (text usually shows up in all methods)
        combined_edges = sobel_edges | laplacian_edges | gradient_edges
        
        # Calculate edge density
        total_pixels = width * height
        edge_pixels = np.sum(combined_edges)
        edge_density = (edge_pixels / total_pixels) * 100
        
        logger.info(f"🔍 Advanced edge detection: sobel_thresh={sobel_threshold:.1f}, laplace_thresh={laplacian_threshold:.1f}, grad_thresh={gradient_threshold:.1f}")
        logger.info(f"🔍 Edge pixels: sobel={np.sum(sobel_edges)}, laplace={np.sum(laplacian_edges)}, grad={np.sum(gradient_edges)}, combined={edge_pixels}")
        logger.info(f"🔍 Edge density: {edge_density:.3f}%")

    except ImportError:
        # Much better fallback without scipy
        # Use multiple simple gradient approaches
        
        # Horizontal and vertical gradients
        grad_x = np.abs(np.diff(gray, axis=1))
        grad_y = np.abs(np.diff(gray, axis=0))
        
        # Diagonal gradients
        grad_diag1 = np.abs(np.diff(gray[:-1, :], axis=1) - np.diff(gray[1:, :], axis=1))  # Diagonal difference
        grad_diag2 = np.abs(np.diff(gray[:, :-1], axis=0) - np.diff(gray[:, 1:], axis=0))  # Other diagonal
        
        # Use adaptive thresholding for fallback too
        x_threshold = np.mean(grad_x) + 1.5 * np.std(grad_x)
        y_threshold = np.mean(grad_y) + 1.5 * np.std(grad_y)
        
        strong_x_edges = np.sum(grad_x > x_threshold)
        strong_y_edges = np.sum(grad_y > y_threshold)
        
        # Estimate edge density from gradients
        total_possible_edges = (width - 1) * height + width * (height - 1)
        edge_density = ((strong_x_edges + strong_y_edges) / total_possible_edges) * 100
        
        logger.info(f"🔍 Fallback edge detection: x_thresh={x_threshold:.1f}, y_thresh={y_threshold:.1f}")
        logger.info(f"🔍 Strong edges: x={strong_x_edges}, y={strong_y_edges}, density={edge_density:.3f}%")

    # Much more realistic coverage mapping based on actual edge density ranges
    if edge_density < 0.1:
        coverage = 0  # Truly textless (almost no edges)
        logger.info(f"🔍 Coverage mapping: {edge_density:.3f}% → 0% (textless)")
    elif edge_density < 0.5:
        coverage = edge_density * 20  # Very minimal text (0-10%)
        logger.info(f"🔍 Coverage mapping: {edge_density:.3f}% → {coverage:.1f}% (minimal)")
    elif edge_density < 2.0:
        coverage = 10 + (edge_density - 0.5) * 10  # Light text (10-25%)
        logger.info(f"🔍 Coverage mapping: {edge_density:.3f}% → {coverage:.1f}% (light)")
    elif edge_density < 5.0:
        coverage = 25 + (edge_density - 2.0) * 8.33  # Moderate text (25-50%)
        logger.info(f"🔍 Coverage mapping: {edge_density:.3f}% → {coverage:.1f}% (moderate)")
    else:
        coverage = min(80, 50 + (edge_density - 5.0) * 6)  # Heavy text (50-80%)
        logger.info(f"🔍 Coverage mapping: {edge_density:.3f}% → {coverage:.1f}% (heavy)")

    return coverage


def calculate_image_quality_metrics(img_array, width: int, height: int) -> dict:
    """
    Calculate actual image quality metrics from pixel data.

    Args:
        img_array: numpy array of image data
        width: Image width
        height: Image height

    Returns:
        dict: Quality metrics including sharpness, color richness, resolution score
    """
    import numpy as np

    # Calculate sharpness (variance of Laplacian)
    gray = np.dot(img_array[...,:3], [0.2989, 0.5870, 0.1140])
    laplacian_var = np.var(gray)
    sharpness_score = min(laplacian_var / 1000, 1.0)  # Normalize

    # Calculate color richness
    color_std = np.std(img_array, axis=(0,1))
    color_richness = np.mean(color_std) / 255.0

    # Resolution quality score
    total_pixels = width * height
    resolution_score = min(total_pixels / (2000 * 3000), 1.0)  # Normalize to 2000x3000

    # Aspect ratio score (posters should be roughly 2:3)
    aspect_ratio = width / height
    aspect_score = 1.0 if 0.6 <= aspect_ratio <= 0.8 else 0.5

    # Artistic quality (combination of factors)
    artistic_score = (sharpness_score * 0.3 +
                     color_richness * 0.3 +
                     aspect_score * 0.2 +
                     resolution_score * 0.2)

    # Overall quality score
    overall_score = (artistic_score * 0.4 +
                    sharpness_score * 0.3 +
                    resolution_score * 0.3)

    return {
        'sharpness_score': round(sharpness_score, 3),
        'color_richness': round(color_richness, 3),
        'resolution_score': round(resolution_score, 3),
        'aspect_ratio': round(aspect_ratio, 3),
        'artistic_score': round(artistic_score, 3),
        'overall_score': round(overall_score, 3)
    }


def analyze_poster_with_advanced_ollama(image_path: Path) -> Dict:
    """
    Fixed advanced AI poster analysis with proper error handling.
    Uses simplified but effective analysis for reliable results.

    Args:
        image_path: Path to poster image

    Returns:
        dict: Comprehensive AI analysis results with categorization data
    """
    try:
        import requests
        import base64
        from PIL import Image

        # Load and optimize image
        with Image.open(image_path) as img:
            if img.mode != 'RGB':
                img = img.convert('RGB')

            original_width, original_height = img.size

            # Calculate baseline quality metrics from actual image data
            import numpy as np
            img_array = np.array(img)
            baseline_metrics = calculate_image_quality_metrics(img_array, original_width, original_height)

            # Resize for faster processing
            if original_width > 512:
                new_width = 512
                new_height = int((original_height * new_width) / original_width)
                img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)

            # Convert to base64
            import io
            img_buffer = io.BytesIO()
            img.save(img_buffer, format='JPEG', quality=85)
            img_base64 = base64.b64encode(img_buffer.getvalue()).decode('utf-8')

        # Pure bounding-box prompt (no quality scores or examples)
        analysis_prompt = """
You are a poster‑text detector. Examine the image and return *only* valid JSON:

{ "text_regions": [ [x_min, y_min, x_max, y_max], … ] }

where each coordinate is normalized between 0.0 and 1.0.  
If there is no text, return { "text_regions": [] }.  
Do not include any other keys, labels, or commentary.
"""

        logger.debug("🤖 Starting Ollama poster analysis...")
        response = requests.post(
            'http://localhost:11434/api/generate',
            json={
                'model': 'qwen2.5vl:latest',
                'prompt': analysis_prompt,
                'images': [img_base64],
                'stream': False
            },
            timeout=15
        )

        if response.status_code == 200:
            result = response.json()
            response_text = result.get('response', '{}')

            # Robust JSON parsing with regex
            try:
                import json
                import re

                # Use regex to extract JSON blob, handling extra text/commentary
                m = re.search(r'(\{.*\})', response_text, re.DOTALL)
                if m:
                    analysis = json.loads(m.group(1))
                    logger.debug(f"🤖 Ollama raw analysis: {analysis}")
                else:
                    analysis = {}
                    logger.warning("⚠️ No valid JSON found in Ollama response")
            except json.JSONDecodeError as e:
                logger.warning(f"⚠️ Failed to parse Ollama JSON: {e}")
                analysis = {}

            # ALWAYS compute all three coverage methods, then take the maximum
            text_regions = analysis.get('text_regions', [])

            # 1. Calculate coverage from model's bounding boxes
            model_coverage = coverage_from_boxes(text_regions, original_width, original_height)

            # 2. ALWAYS calculate coverage from edge detection
            edge_coverage = calculate_edge_density_coverage(
                img_array, original_width, original_height
            )

            # 3. ALWAYS calculate coverage from OCR
            ocr_coverage = calculate_ocr_text_coverage(image_path)

            # 4. Take the maximum of all three approaches
            final_text_coverage = max(model_coverage, edge_coverage, ocr_coverage)

            # Debug logging
            logger.info(f"📊 Model coverage from {len(text_regions)} regions: {model_coverage:.1f}%")
            if text_regions:
                for i, region in enumerate(text_regions):
                    logger.info(f"    Region {i+1}: {region}")
            logger.info(f"📊 Edge detection coverage: {edge_coverage:.1f}%")
            logger.info(f"📊 OCR detection coverage: {ocr_coverage:.1f}%")
            logger.info(f"📊 Final coverage (max of all three): {final_text_coverage:.1f}%")

            # Use baseline quality metrics (never trust model quality scores either)
            final_quality = baseline_metrics['overall_score']

            # Create comprehensive analysis with ONLY calculated values
            comprehensive_analysis = {
                # Core metrics - use ONLY calculated values
                'artistic_quality': baseline_metrics['artistic_score'],
                'professional_quality': baseline_metrics['overall_score'],
                'overall_quality': final_quality,
                'quality_score': final_quality,

                # Text analysis - use ONLY calculated values
                'has_text': final_text_coverage > 0,
                'is_textless': final_text_coverage == 0,
                'text_coverage_percent': round(final_text_coverage, 1),
                'has_excessive_text': final_text_coverage > 50,
                'text_quality_score': 0.8,
                'text_readability': 'GOOD',
                'text_language': 'EN',

                # Style analysis
                'visual_style': analysis.get('visual_style', 'PHOTOGRAPHIC'),
                'mood_tone': analysis.get('mood_tone', 'DRAMATIC'),
                'composition': 'CENTERED',
                'dominant_colors': [],

                # Categorization flags - use ONLY calculated values
                'category_textless': final_text_coverage == 0,
                'category_excessive_text': final_text_coverage > 50,
                'category_high_quality': final_quality >= 0.8,
                'category_style': 'photographic',  # Default since we can't trust model
                'category_mood': 'dramatic',       # Default since we can't trust model

                # Metadata
                'ai_analysis': True,
                'ollama_analysis': True,
                'advanced_analysis': True,
                'original_dimensions': f"{original_width}x{original_height}",
                'file_size_mb': round(image_path.stat().st_size / (1024*1024), 2),

                # Legacy compatibility
                'language': 'en',
                'visual_appeal': analysis.get('artistic_quality', 0.7)
            }

            logger.info(f"🤖 Advanced Ollama analysis complete:")
            logger.info(f"   Quality: {comprehensive_analysis['overall_quality']:.3f}")
            logger.info(f"   Style: {comprehensive_analysis['visual_style']}")
            logger.info(f"   Text Coverage: {comprehensive_analysis['text_coverage_percent']}%")
            logger.info(f"   Textless: {comprehensive_analysis['is_textless']}")

            return comprehensive_analysis

        else:
            logger.warning(f"⚠️ Ollama analysis failed: HTTP {response.status_code}")
            return analyze_poster_with_ai(image_path)

    except requests.exceptions.Timeout:
        logger.warning(f"⚠️ Ollama analysis timed out, falling back to basic analysis")
        return analyze_poster_with_ai(image_path)
    except Exception as e:
        logger.warning(f"⚠️ Ollama analysis failed: {e}, falling back to basic analysis")
        return analyze_poster_with_ai(image_path)

def analyze_poster_with_ai(image_path: Path) -> Dict:
    """
    Analyze poster using AI vision model for quality assessment.
    Implements advanced poster analysis as described in the PDFs.

    Args:
        image_path: Path to poster image

    Returns:
        dict: Analysis results (quality score, text detection, etc.)
    """
    try:
        from PIL import Image
        import numpy as np

        # Load and analyze image
        with Image.open(image_path) as img:
            # Convert to RGB if needed
            if img.mode != 'RGB':
                img = img.convert('RGB')

            width, height = img.size
            aspect_ratio = width / height

            # Basic image quality metrics
            img_array = np.array(img)

            # Calculate sharpness (variance of Laplacian)
            gray = np.dot(img_array[...,:3], [0.2989, 0.5870, 0.1140])
            laplacian_var = np.var(gray)
            sharpness_score = min(laplacian_var / 1000, 1.0)  # Normalize

            # Calculate color richness
            color_std = np.std(img_array, axis=(0,1))
            color_richness = np.mean(color_std) / 255.0

            # Resolution quality score
            total_pixels = width * height
            resolution_score = min(total_pixels / (2000 * 3000), 1.0)  # Normalize to 2000x3000

            # Use the improved edge detection method
            edge_coverage = calculate_edge_density_coverage(img_array, width, height)
            
            # Also try OCR-based coverage
            ocr_coverage = calculate_ocr_text_coverage(image_path)
            
            # Take the maximum of both methods
            text_coverage = max(edge_coverage, ocr_coverage)
            has_text = text_coverage > 0

            # Language detection (placeholder - would use OCR in full implementation)
            language = 'en'  # Default to English

            # Use the helper function for consistent quality calculation
            quality_metrics = calculate_image_quality_metrics(img_array, width, height)

            analysis = {
                'quality_score': quality_metrics['overall_score'],
                'has_text': has_text,
                'is_textless': text_coverage == 0,
                'text_coverage_percent': round(text_coverage, 1),
                'language': language,
                'artistic_quality': quality_metrics['artistic_score'],
                'professional_quality': quality_metrics['overall_score'],
                'overall_quality': quality_metrics['overall_score'],
                'artistic_score': quality_metrics['artistic_score'],
                'resolution_score': quality_metrics['resolution_score'],
                'sharpness_score': quality_metrics['sharpness_score'],
                'color_richness': quality_metrics['color_richness'],
                'aspect_ratio': quality_metrics['aspect_ratio'],
                'dimensions': f"{width}x{height}",
                'overall_score': quality_metrics['overall_score'],
                'has_excessive_text': text_coverage > 50,
                'category_textless': text_coverage == 0,
                'category_excessive_text': text_coverage > 50,
                'category_high_quality': quality_metrics['overall_score'] >= 0.8
            }

            logger.debug(f"🤖 AI analysis for {image_path.name}: {analysis}")
            return analysis

    except ImportError:
        logger.warning("⚠️ PIL/numpy not available for AI analysis, using basic metrics")
        # Fallback to basic analysis - still try to get some real metrics
        try:
            from PIL import Image
            with Image.open(image_path) as img:
                width, height = img.size
                file_size_mb = image_path.stat().st_size / (1024*1024)
                # Basic quality estimate based on resolution and file size
                total_pixels = width * height
                resolution_score = min(total_pixels / (2000 * 3000), 1.0)
                quality_estimate = min(0.9, max(0.3, resolution_score * 0.8 + (file_size_mb / 10) * 0.2))

                return {
                    'quality_score': round(quality_estimate, 3),
                    'has_text': True,  # Conservative assumption
                    'is_textless': False,
                    'text_coverage_percent': 25.0,  # Conservative middle estimate
                    'language': 'en',
                    'artistic_score': round(quality_estimate * 0.9, 3),
                    'resolution_score': round(resolution_score, 3),
                    'overall_score': round(quality_estimate, 3),
                    'overall_quality': round(quality_estimate, 3),
                    'dimensions': f"{width}x{height}"
                }
        except:
            # Last resort fallback
            return {
                'quality_score': 0.5,
                'has_text': True,
                'is_textless': False,
                'text_coverage_percent': 30.0,
                'language': 'en',
                'overall_score': 0.5,
                'overall_quality': 0.5
            }
    except Exception as e:
        logger.error(f"❌ Failed to analyze poster with AI: {e}")
        # Try to get at least basic image info
        try:
            from PIL import Image
            with Image.open(image_path) as img:
                width, height = img.size
                return {
                    'quality_score': 0.4,
                    'has_text': True,
                    'is_textless': False,
                    'text_coverage_percent': 20.0,
                    'language': 'unknown',
                    'overall_score': 0.4,
                    'overall_quality': 0.4,
                    'dimensions': f"{width}x{height}"
                }
        except:
            return {
                'quality_score': 0.3,
                'has_text': True,
                'is_textless': False,
                'text_coverage_percent': 25.0,
                'language': 'unknown',
                'overall_score': 0.3,
                'overall_quality': 0.3
            }


def discover_movies_awaiting_poster():
    """
    Discover movies awaiting poster in folder 5.
    Supports both new movies/tv_shows structure and legacy structure.

    Returns:
        List of movie directories awaiting poster
    """
    logger.info("🔍 Discovering movies awaiting poster...")

    awaiting_movies = []
    workspace_path = Path("workspace")
    awaiting_poster_path = workspace_path / "5_awaiting_poster"

    if not awaiting_poster_path.exists():
        logger.warning(f"Awaiting poster directory not found: {awaiting_poster_path}")
        return awaiting_movies

    # Check for new structure (movies/tv_shows subdirectories)
    movies_dir = awaiting_poster_path / "movies"
    tv_shows_dir = awaiting_poster_path / "tv_shows"
    
    content_dirs_to_scan = []
    
    if movies_dir.exists() or tv_shows_dir.exists():
        # New structure: scan movies/ and tv_shows/ subdirectories
        logger.info("📁 Using new movies/tv_shows structure")
        if movies_dir.exists():
            content_dirs_to_scan.append(("movies", movies_dir))
        if tv_shows_dir.exists():
            content_dirs_to_scan.append(("tv_shows", tv_shows_dir))
    else:
        # Legacy structure: scan directly for resolution folders
        logger.info("📁 Using legacy structure")
        content_dirs_to_scan.append(("legacy", awaiting_poster_path))
    
    for content_type, content_dir in content_dirs_to_scan:
        logger.info(f"🔍 Scanning {content_type} content...")
        
        # Check resolution directories within content directory
        for item in content_dir.iterdir():
            if item.is_dir():
                # Check if this is a resolution folder
                if item.name.lower() in ['1080p', '4k', '720p', '480p', '2160p']:
                    resolution_dir = item
                    logger.info(f"  📁 Scanning {resolution_dir.name} in {content_type}...")
                    
                    for movie_dir in resolution_dir.iterdir():
                        if movie_dir.is_dir():
                            # Check if we have a movie file
                            movie_files = list(movie_dir.glob("*.mkv"))
                            if movie_files:
                                logger.info(f"    ✅ Ready: {movie_dir.name} ({content_type})")
                                awaiting_movies.append(movie_dir)
                            else:
                                logger.warning(f"    ⚠️ No movie file found: {movie_dir.name}")
                elif content_type == "legacy":
                    # In legacy mode, this might be a movie directory directly
                    movie_dir = item
                    # Check if we have a movie file
                    movie_files = list(movie_dir.glob("*.mkv"))
                    if movie_files:
                        logger.info(f"  ✅ Ready: {movie_dir.name} (legacy)")
                        awaiting_movies.append(movie_dir)
                    else:
                        logger.warning(f"  ⚠️ No movie file found: {movie_dir.name}")

    logger.info(f"📊 Found {len(awaiting_movies)} movies awaiting poster")
    return awaiting_movies


def discover_tv_shows_awaiting_poster():
    """
    Discover TV shows awaiting poster in folder 5.
    Supports both new movies/tv_shows structure and legacy structure.

    Returns:
        List of TV show directories awaiting poster
    """
    logger.info("📺 Discovering TV shows awaiting poster...")

    awaiting_tv_shows = []
    workspace_path = Path("workspace")
    awaiting_poster_path = workspace_path / "5_awaiting_poster"

    if not awaiting_poster_path.exists():
        logger.warning(f"Awaiting poster directory not found: {awaiting_poster_path}")
        return awaiting_tv_shows

    # Check for new structure (movies/tv_shows subdirectories)
    tv_shows_dir = awaiting_poster_path / "tv_shows"
    
    content_dirs_to_scan = []
    
    if tv_shows_dir.exists():
        # New structure: scan tv_shows/ subdirectory
        logger.info("📁 Using new movies/tv_shows structure")
        content_dirs_to_scan.append(("tv_shows", tv_shows_dir))
    else:
        # Legacy structure: scan directly for resolution folders, but look for TV show patterns
        logger.info("📁 Using legacy structure for TV shows")
        content_dirs_to_scan.append(("legacy", awaiting_poster_path))
    
    for content_type, content_dir in content_dirs_to_scan:
        logger.info(f"📺 Scanning {content_type} content...")
        
        # Check resolution directories within content directory
        for item in content_dir.iterdir():
            if item.is_dir():
                # Check if this is a resolution folder
                if item.name.lower() in ['1080p', '4k', '720p', '480p', '2160p']:
                    resolution_dir = item
                    logger.info(f"  📁 Scanning {resolution_dir.name} in {content_type}...")
                    
                    for show_dir in resolution_dir.iterdir():
                        if show_dir.is_dir():
                            # Check if we have TV show structure (seasons/episodes)
                            has_episodes = False
                            
                            # Look for episode files (*.mkv)
                            episode_files = list(show_dir.glob("**/*.mkv"))
                            if episode_files:
                                has_episodes = True
                            
                            # Also check for season directories
                            season_dirs = [d for d in show_dir.iterdir() if d.is_dir() and 
                                         any(pattern in d.name.lower() for pattern in ['season', 's0', 's1', 's2', 's3', 's4', 's5', 's6', 's7', 's8', 's9'])]
                            if season_dirs:
                                has_episodes = True
                            
                            if has_episodes:
                                logger.info(f"    ✅ Ready: {show_dir.name} ({content_type})")
                                awaiting_tv_shows.append(show_dir)
                            else:
                                logger.warning(f"    ⚠️ No episodes found: {show_dir.name}")
                                
                elif content_type == "legacy":
                    # In legacy mode, this might be a TV show directory directly
                    show_dir = item
                    # Check if we have TV show structure
                    has_episodes = False
                    
                    # Look for episode files (*.mkv)
                    episode_files = list(show_dir.glob("**/*.mkv"))
                    if episode_files:
                        has_episodes = True
                    
                    # Also check for season directories
                    season_dirs = [d for d in show_dir.iterdir() if d.is_dir() and 
                                 any(pattern in d.name.lower() for pattern in ['season', 's0', 's1', 's2', 's3', 's4', 's5', 's6', 's7', 's8', 's9'])]
                    if season_dirs:
                        has_episodes = True
                    
                    if has_episodes:
                        logger.info(f"  ✅ Ready: {show_dir.name} (legacy)")
                        awaiting_tv_shows.append(show_dir)
                    else:
                        logger.warning(f"  ⚠️ No episodes found: {show_dir.name}")

    logger.info(f"📊 Found {len(awaiting_tv_shows)} TV shows awaiting poster")
    return awaiting_tv_shows


def parse_movie_name(movie_name: str) -> Tuple[str, Optional[int]]:
    """
    Parse movie name to extract title and year.
    Expected format: "Movie Title (Year)"

    Args:
        movie_name: Movie directory name

    Returns:
        Tuple of (title, year)
    """
    try:
        # Match pattern: "Title (Year)"
        match = re.match(r'^(.+?)\s*\((\d{4})\)$', movie_name)
        if match:
            title = match.group(1).strip()
            year = int(match.group(2))
            return title, year
        else:
            # No year found, use whole name as title
            return movie_name, None
    except Exception as e:
        logger.warning(f"⚠️ Failed to parse movie name '{movie_name}': {e}")
        return movie_name, None


def parse_tv_show_name(show_name: str) -> Tuple[str, Optional[int]]:
    """
    Parse TV show name to extract title and year.
    Expected formats: 
    - "Show Title (Year)"
    - "Show Title"
    - "Show Title - Season X"

    Args:
        show_name: TV show directory name

    Returns:
        Tuple of (title, year)
    """
    try:
        # First try to match "Title (Year)" pattern
        match = re.match(r'^(.+?)\s*\((\d{4})\)$', show_name)
        if match:
            title = match.group(1).strip()
            year = int(match.group(2))
            return title, year
        
        # Try to match "Title - Season X" pattern and extract just the title
        match = re.match(r'^(.+?)\s*-\s*Season\s+\d+', show_name, re.IGNORECASE)
        if match:
            title = match.group(1).strip()
            return title, None
        
        # Otherwise use whole name as title
        return show_name, None
        
    except Exception as e:
        logger.warning(f"⚠️ Failed to parse TV show name '{show_name}': {e}")
        return show_name, None


def create_permanent_poster_archive(movie_name: str, poster: PosterInfo, analysis: Dict, poster_data: bytes, is_selected: bool = False) -> Path:
    """
    Save poster to permanent archive with AI-driven categorization.
    Creates organized folder structure for manual review and override.

    Args:
        movie_name: Name of the movie
        poster: Poster information
        analysis: AI analysis results
        poster_data: Raw poster image data

    Returns:
        Path to saved poster file
    """
    try:
        # Create proper folder structure: temp_original_backup/Posters/MovieName/
        posters_base = Path("workspace") / "temp_original_backup" / "Posters"
        archive_base = posters_base / movie_name
        archive_base.mkdir(parents=True, exist_ok=True)

        # Generate unique filename with source and quality info
        quality_score = analysis.get('overall_quality', 0.0)
        source = poster.source.lower()
        base_filename = f"{source}_{quality_score:.3f}_{poster.width}x{poster.height}.jpg"

        # If this is the selected poster, also create a "poster.jpg" copy
        if is_selected:
            selected_filename = "poster.jpg"
            selected_path = archive_base / selected_filename
            selected_path.write_bytes(poster_data)
            logger.info(f"🎯 Saved selected poster as: {selected_filename}")

        # Save main poster file with detailed name
        main_poster_path = archive_base / base_filename
        main_poster_path.write_bytes(poster_data)

        # Create categorized copies based on AI analysis (avoid duplicates)
        categories = []

        # Primary text category (mutually exclusive)
        if analysis.get('is_textless', False):
            categories.append('textless')
        elif analysis.get('has_excessive_text', False):
            categories.append('excessive_text')
        else:
            categories.append('has_text')

        # Quality category (mutually exclusive)
        if analysis.get('category_high_quality', False):
            categories.append('high_quality')
        else:
            categories.append('standard_quality')

        # Style category
        style = analysis.get('visual_style', 'PHOTOGRAPHIC').lower()
        if style and style != 'unknown':
            categories.append(f"style_{style}")

        # Mood category
        mood = analysis.get('mood_tone', 'DRAMATIC').lower()
        if mood and mood != 'unknown':
            categories.append(f"mood_{mood}")

        # Source category
        categories.append(f"source_{source}")

        # Create organized category folders with hard links to save space
        for category in categories:
            category_dir = archive_base / category
            category_dir.mkdir(exist_ok=True)
            category_file = category_dir / base_filename

            # Create hard link to save space (or copy if hard link fails)
            try:
                if not category_file.exists():
                    category_file.hardlink_to(main_poster_path)
            except (OSError, NotImplementedError):
                # Fallback to copy if hard links not supported
                if not category_file.exists():
                    shutil.copy2(main_poster_path, category_file)

        # Create analysis metadata file
        metadata_file = archive_base / f"{base_filename}.analysis.json"
        metadata = {
            'poster_info': {
                'source': poster.source,
                'url': poster.url,
                'width': poster.width,
                'height': poster.height,
                'vote_average': poster.vote_average,
                'language': poster.language
            },
            'ai_analysis': analysis,
            'categories': categories,
            'is_selected': is_selected,
            'timestamp': __import__('datetime').datetime.now().isoformat()
        }

        import json

        # Custom JSON encoder to handle non-serializable objects
        def json_serializer(obj):
            if isinstance(obj, (bool, int, float, str, type(None))):
                return obj
            elif isinstance(obj, list):
                return [json_serializer(item) for item in obj]
            elif isinstance(obj, dict):
                return {key: json_serializer(value) for key, value in obj.items()}
            else:
                return str(obj)  # Convert non-serializable objects to string

        serializable_metadata = json_serializer(metadata)
        metadata_file.write_text(json.dumps(serializable_metadata, indent=2), encoding='utf-8')

        logger.debug(f"📁 Archived poster: {base_filename}")
        logger.debug(f"   Categories: {', '.join(categories)}")

        return main_poster_path

    except Exception as e:
        logger.warning(f"⚠️ Failed to archive poster: {e}")
        # Return a temporary path as fallback
        temp_path = Path("temp_poster_fallback.jpg")
        temp_path.write_bytes(poster_data)
        return temp_path


def add_plex_automator_marker(movie_dir: Path) -> bool:
    """
    Add .plex_automator marker file to identify automated movies.
    This helps distinguish movies processed through the automator from manually curated ones.

    Args:
        movie_dir: Path to movie directory

    Returns:
        True if successful, False otherwise
    """
    try:
        marker_file = movie_dir / ".plex_automator"

        # Create marker with timestamp and info
        marker_content = f"""# Plex Automator Marker
# This movie was processed through the PlexMovieAutomator system
# Created: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
# Movie: {movie_dir.name}
#
# This marker helps distinguish automated movies from manually curated ones.
# You can use this to identify movies that may need additional poster work.
"""

        marker_file.write_text(marker_content, encoding='utf-8')
        logger.debug(f"🏷️ Added Plex Automator marker to {movie_dir.name}")
        return True

    except Exception as e:
        logger.warning(f"⚠️ Failed to add marker to {movie_dir.name}: {e}")
        return False


def mark_selected_poster_in_archive(movie_name: str, selected_poster: PosterInfo, poster_data: bytes, analysis: Dict):
    """
    Mark the selected poster in the archive and create a poster.jpg copy.

    Args:
        movie_name: Name of the movie
        selected_poster: The poster that was selected
        poster_data: Raw poster image data
        analysis: AI analysis results
    """
    try:
        # Create the selected poster copy in the archive
        create_permanent_poster_archive(movie_name, selected_poster, analysis, poster_data, is_selected=True)
        logger.info(f"🎯 Marked selected poster in archive for: {movie_name}")
    except Exception as e:
        logger.warning(f"⚠️ Failed to mark selected poster in archive: {e}")


def check_for_personal_images(movie_name: str) -> List[Path]:
    """
    Check for personal images that were previously collected for this movie.
    Looks in the movie's poster folder for categorized personal images.
    
    Args:
        movie_name: Name of the movie
        
    Returns:
        List of personal image files found
    """
    # Look in the movie's poster folder structure
    movie_poster_dir = Path("workspace") / "temp_original_backup" / "Posters" / movie_name
    
    if not movie_poster_dir.exists():
        return []
    
    image_extensions = {'.jpg', '.jpeg', '.png', '.webp', '.bmp', '.tiff'}
    personal_images = []
    
    # Look for images with "personal_" prefix in category folders and root
    for item in movie_poster_dir.rglob("*"):
        if (item.is_file() and 
            item.suffix.lower() in image_extensions and 
            "personal_" in item.name.lower()):
            personal_images.append(item)
    
    logger.info(f"🖼️  Found {len(personal_images)} personal images for {movie_name}")
    return personal_images


def integrate_personal_images(movie_name: str, posters: List[PosterInfo]) -> List[PosterInfo]:
    """
    Integrate personal images into the poster selection process.
    
    Args:
        movie_name: Name of the movie
        posters: Current list of posters from API sources
        
    Returns:
        Enhanced list including personal images
    """
    personal_images = check_for_personal_images(movie_name)
    
    if not personal_images:
        return posters
    
    logger.info(f"🔗 Integrating {len(personal_images)} personal images for {movie_name}")
    
    for image_path in personal_images:
        try:
            # Create PosterInfo for personal image
            personal_poster = PosterInfo(
                url=f"file://{image_path.absolute()}",
                source="Personal Collection",
                width=None,  # Could be determined if needed
                height=None,
                aspect_ratio=None,
                vote_average=None,
                language=None,
                file_path=str(image_path),
                description=f"Personal image: {image_path.name}"
            )
            posters.append(personal_poster)
            logger.info(f"  ✅ Added personal image: {image_path.name}")
            
        except Exception as e:
            logger.warning(f"  ⚠️  Failed to add personal image {image_path.name}: {e}")
    
    return posters


def rank_posters_by_quality(posters: List[PosterInfo], settings: dict, movie_name: str = "Unknown Movie") -> List[Tuple[PosterInfo, Dict]]:
    """
    Advanced poster ranking system with permanent archiving and AI categorization.
    Ranks posters by comprehensive AI analysis, user preferences, and source quality.

    Args:
        posters: List of poster info objects
        settings: User preferences and configuration
        movie_name: Name of the movie for archiving purposes

    Returns:
        List of (poster, analysis) tuples sorted by quality
    """
    logger.info(f"🎯 Ranking {len(posters)} posters by quality for: {movie_name}")

    # Get user preferences
    poster_prefs = settings.get('PosterPreferences', {})

    # Parse boolean settings (INI files read as strings)
    prefer_textless_raw = poster_prefs.get('prefer_textless', False)
    if isinstance(prefer_textless_raw, str):
        prefer_textless = prefer_textless_raw.lower() in ('true', '1', 'yes', 'on')
    else:
        prefer_textless = bool(prefer_textless_raw)

    min_resolution = poster_prefs.get('min_resolution', '1000x1500')

    # Parse preferred_sources (could be string from INI file)
    preferred_sources_raw = poster_prefs.get('preferred_sources', 'TMDB,Fanart.tv,ThePosterDB')
    if isinstance(preferred_sources_raw, str):
        preferred_sources = [s.strip() for s in preferred_sources_raw.split(',')]
    else:
        preferred_sources = preferred_sources_raw

    ranked_posters = []

    for poster in posters:
        try:
            # Create temporary file for analysis in proper temp location (inside temp_original_backup)
            temp_dir = Path("workspace") / "temp_original_backup" / "temp_analysis"
            temp_dir.mkdir(parents=True, exist_ok=True)
            temp_path = temp_dir / f"poster_analysis_{poster.source}_{hash(poster.url) % 10000}.jpg"

            # Download poster for analysis
            if download_poster_image(poster, temp_path):
                # Get poster data for permanent archiving
                poster_data = temp_path.read_bytes()

                # Check if advanced Ollama analysis should be used
                poster_prefs = settings.get('PosterPreferences', {})
                use_ollama = poster_prefs.get('use_ollama_models', 'true').lower() == 'true'

                if use_ollama:
                    # Use advanced Ollama analysis with comprehensive evaluation
                    analysis = analyze_poster_with_advanced_ollama(temp_path)
                    if analysis.get('advanced_analysis'):
                        logger.debug(f"🤖 Used advanced Ollama analysis for {poster.source} poster")
                    else:
                        logger.debug(f"⚠️ Advanced analysis failed, using fallback for {poster.source}")
                        analysis = analyze_poster_with_ai(temp_path)
                else:
                    # Use basic AI analysis
                    analysis = analyze_poster_with_ai(temp_path)
                    logger.debug(f"🔧 Used basic AI analysis for {poster.source} poster")

                # Save to permanent archive with AI categorization (not selected yet)
                archive_path = create_permanent_poster_archive(movie_name, poster, analysis, poster_data, is_selected=False)

                # Calculate preference-based adjustments
                preference_bonus = 0.0

                # Source preference bonus
                if poster.source in preferred_sources:
                    source_index = preferred_sources.index(poster.source)
                    preference_bonus += (len(preferred_sources) - source_index) * 0.1

                # Advanced text preference scoring
                if prefer_textless:
                    if analysis.get('is_textless', False):
                        preference_bonus += 0.3  # Strong bonus for truly textless
                    elif analysis.get('text_coverage_percent', 50) < 15:
                        preference_bonus += 0.15  # Moderate bonus for minimal text
                    elif analysis.get('has_excessive_text', False):
                        preference_bonus -= 0.2  # Penalty for excessive text
                else:
                    # Prefer readable text over excessive text
                    if analysis.get('text_readability') == 'EXCELLENT':
                        preference_bonus += 0.15
                    elif analysis.get('has_excessive_text', False):
                        preference_bonus -= 0.1

                # Resolution preference
                try:
                    if poster.width and poster.height:
                        min_w, min_h = map(int, min_resolution.split('x'))
                        width = int(poster.width) if isinstance(poster.width, str) else poster.width
                        height = int(poster.height) if isinstance(poster.height, str) else poster.height
                        if width >= min_w and height >= min_h:
                            preference_bonus += 0.15
                except (ValueError, TypeError):
                    pass

                # Community rating bonus (from TMDB votes or Fanart likes)
                try:
                    if poster.vote_average:
                        vote_avg = float(poster.vote_average) if isinstance(poster.vote_average, str) else poster.vote_average
                        if poster.source == 'TMDB':
                            # TMDB uses 0-10 scale
                            rating_bonus = (vote_avg / 10.0) * 0.2
                        elif poster.source == 'Fanart.tv':
                            # Fanart uses likes count
                            rating_bonus = min(vote_avg / 100.0, 0.2)
                        else:
                            rating_bonus = 0.1
                        preference_bonus += rating_bonus
                except (ValueError, TypeError):
                    pass

                # Calculate final score
                final_score = analysis.get('overall_score', 0.5) + preference_bonus
                analysis['final_score'] = round(min(final_score, 1.0), 3)
                analysis['preference_bonus'] = round(preference_bonus, 3)

                ranked_posters.append((poster, analysis))

                # Clean up temp analysis file
                if temp_path.exists():
                    temp_path.unlink()

            else:
                # If download failed, use basic scoring
                basic_analysis = {
                    'overall_score': 0.5,
                    'final_score': 0.5,
                    'quality_score': 0.5,
                    'preference_bonus': 0.0
                }
                ranked_posters.append((poster, basic_analysis))

        except Exception as e:
            logger.warning(f"⚠️ Failed to analyze poster from {poster.source}: {e}")
            # Add with low score if analysis fails
            basic_analysis = {
                'overall_score': 0.3,
                'final_score': 0.3,
                'quality_score': 0.3,
                'preference_bonus': 0.0
            }
            ranked_posters.append((poster, basic_analysis))

    # Sort by final score (highest first)
    ranked_posters.sort(key=lambda x: x[1]['final_score'], reverse=True)

    logger.info(f"✅ Ranked posters by quality:")
    for i, (poster, analysis) in enumerate(ranked_posters[:5], 1):
        logger.info(f"  {i}. {poster.source} - Score: {analysis['final_score']} "
                   f"(Quality: {analysis.get('quality_score', 'N/A')}, "
                   f"Bonus: {analysis['preference_bonus']})")

    return ranked_posters


def interactive_poster_selection(posters: List[PosterInfo], movie_dir: Path) -> Optional[PosterInfo]:
    """
    Interactive poster selection interface.
    Downloads sample posters and lets user choose the best one.

    Args:
        posters: List of available posters
        movie_dir: Movie directory for temporary downloads

    Returns:
        Selected PosterInfo or None if cancelled
    """
    if not posters:
        logger.warning("⚠️ No posters available for selection")
        return None

    # Limit to top 10 posters to avoid overwhelming the user
    top_posters = posters[:10]

    # Create temporary directory for poster previews
    temp_dir = movie_dir / "temp_posters"
    temp_dir.mkdir(exist_ok=True)

    try:
        logger.info(f"📥 Downloading {len(top_posters)} poster previews...")

        # Download preview versions of posters
        downloaded_posters = []
        for i, poster in enumerate(top_posters, 1):
            preview_path = temp_dir / f"poster_{i:02d}_{poster.source.lower()}.jpg"

            if download_poster_image(poster, preview_path):
                downloaded_posters.append((i, poster, preview_path))
            else:
                logger.warning(f"⚠️ Failed to download preview {i}")

        if not downloaded_posters:
            logger.error("❌ No poster previews could be downloaded")
            return None

        # Display poster options
        print(f"\n🎨 Poster Selection for: {movie_dir.name}")
        print("=" * 60)

        for i, poster, preview_path in downloaded_posters:
            file_size_mb = preview_path.stat().st_size / (1024 * 1024)
            print(f"{i:2d}. {poster.source:<12} - {poster.description}")
            print(f"    URL: {poster.url[:80]}...")
            print(f"    Size: {file_size_mb:.1f} MB")
            if poster.vote_average:
                print(f"    Rating: {poster.vote_average}")
            print()

        # Get user selection
        while True:
            try:
                choice = input(f"Select poster (1-{len(downloaded_posters)}, 's' to skip, 'q' to quit): ").strip().lower()

                if choice == 'q':
                    logger.info("👋 Exiting poster selection")
                    return None
                elif choice == 's':
                    logger.info("⏭️ Skipping poster selection")
                    return None
                else:
                    choice_num = int(choice)
                    if 1 <= choice_num <= len(downloaded_posters):
                        selected = downloaded_posters[choice_num - 1]
                        logger.info(f"✅ Selected poster {choice_num} from {selected[1].source}")
                        return selected[1]
                    else:
                        print(f"Please enter a number between 1 and {len(downloaded_posters)}")
            except ValueError:
                print("Please enter a valid number, 's' to skip, or 'q' to quit")

    finally:
        # Clean up temporary files
        try:
            for file in temp_dir.glob("*"):
                file.unlink()
            temp_dir.rmdir()
            logger.debug("🗑️ Cleaned up temporary poster files")
        except Exception as e:
            logger.warning(f"⚠️ Failed to clean up temp files: {e}")

    return None


def filter_posters_by_criteria(posters: List[PosterInfo], settings: dict) -> List[PosterInfo]:
    """
    Filter posters based on user criteria from PDF requirements.

    Args:
        posters: List of poster info objects
        settings: User preferences and filtering criteria

    Returns:
        Filtered list of posters
    """
    poster_prefs = settings.get('PosterPreferences', {})

    # Filtering criteria
    min_resolution = poster_prefs.get('min_resolution', '500x750')
    max_file_size_mb = poster_prefs.get('max_file_size_mb', 10)

    # Parse allowed_languages (could be string from INI file)
    allowed_languages_raw = poster_prefs.get('allowed_languages', 'en,null')
    if isinstance(allowed_languages_raw, str):
        allowed_languages = [s.strip() for s in allowed_languages_raw.split(',')]
        allowed_languages.append(None)  # Add None for no language
    else:
        allowed_languages = allowed_languages_raw

    # Parse blocked_sources (could be string from INI file)
    blocked_sources_raw = poster_prefs.get('blocked_sources', '')
    if isinstance(blocked_sources_raw, str) and blocked_sources_raw:
        blocked_sources = [s.strip() for s in blocked_sources_raw.split(',')]
    else:
        blocked_sources = blocked_sources_raw if blocked_sources_raw else []

    # Parse boolean settings (INI files read as strings)
    require_aspect_ratio_raw = poster_prefs.get('require_poster_aspect_ratio', True)
    if isinstance(require_aspect_ratio_raw, str):
        require_aspect_ratio = require_aspect_ratio_raw.lower() in ('true', '1', 'yes', 'on')
    else:
        require_aspect_ratio = bool(require_aspect_ratio_raw)

    filtered_posters = []
    min_w, min_h = map(int, min_resolution.split('x'))

    for poster in posters:
        # Source filtering
        if poster.source in blocked_sources:
            logger.debug(f"🚫 Filtered out {poster.source} poster (blocked source)")
            continue

        # Resolution filtering
        try:
            if poster.width and poster.height:
                width = int(poster.width) if isinstance(poster.width, str) else poster.width
                height = int(poster.height) if isinstance(poster.height, str) else poster.height
                if width < min_w or height < min_h:
                    logger.debug(f"🚫 Filtered out {width}x{height} poster (too small)")
                    continue
        except (ValueError, TypeError):
            # Skip resolution filtering if dimensions are invalid
            pass

        # Aspect ratio filtering (posters should be roughly 2:3)
        try:
            if require_aspect_ratio and poster.width and poster.height:
                width = int(poster.width) if isinstance(poster.width, str) else poster.width
                height = int(poster.height) if isinstance(poster.height, str) else poster.height
                aspect_ratio = width / height
                if not (0.5 <= aspect_ratio <= 0.8):  # Allow some flexibility
                    logger.debug(f"🚫 Filtered out poster with aspect ratio {aspect_ratio:.2f}")
                    continue
        except (ValueError, TypeError, ZeroDivisionError):
            # Skip aspect ratio filtering if dimensions are invalid
            pass

        # Language filtering
        if poster.language and poster.language not in allowed_languages:
            logger.debug(f"🚫 Filtered out {poster.language} poster (language not allowed)")
            continue

        filtered_posters.append(poster)

    logger.info(f"📊 Filtered {len(posters)} → {len(filtered_posters)} posters")
    return filtered_posters


def auto_select_best_poster(posters: List[PosterInfo], settings: dict, movie_name: str = "Unknown Movie") -> Optional[PosterInfo]:
    """
    Automatic poster selection using AI analysis and user preferences.
    Implements batch processing mode from PDF requirements.

    Args:
        posters: List of available posters
        settings: User preferences and configuration

    Returns:
        Best poster based on AI analysis, or None if none suitable
    """
    if not posters:
        return None

    logger.info(f"🤖 Auto-selecting best poster from {len(posters)} options...")

    # Filter posters by criteria
    filtered_posters = filter_posters_by_criteria(posters, settings)
    if not filtered_posters:
        logger.warning("⚠️ No posters passed filtering criteria")
        return None

    # Rank posters by quality (save to proper movie folder)
    ranked_posters = rank_posters_by_quality(filtered_posters, settings, movie_name)
    if not ranked_posters:
        logger.warning("⚠️ No posters could be ranked")
        return None

    # Select the highest-ranked poster
    best_poster, analysis = ranked_posters[0]

    logger.info(f"🎯 Auto-selected poster from {best_poster.source}")
    logger.info(f"   Quality Score: {analysis.get('quality_score', 'N/A')}")
    logger.info(f"   Final Score: {analysis.get('final_score', 'N/A')}")
    logger.info(f"   Dimensions: {analysis.get('dimensions', 'Unknown')}")

    return best_poster


def batch_process_all_movies(settings: dict) -> Dict:
    """
    Advanced batch processing mode implementing PDF requirements.
    Processes all movies in awaiting_poster folder automatically.

    Args:
        settings: Configuration settings

    Returns:
        dict: Processing results and statistics
    """
    logger.info("🚀 Starting advanced batch poster processing...")

    awaiting_movies = discover_movies_awaiting_poster()
    if not awaiting_movies:
        logger.info("✅ No movies awaiting poster processing")
        return {'processed': 0, 'successful': 0, 'failed': 0}

    results = {
        'processed': 0,
        'successful': 0,
        'failed': 0,
        'movies': []
    }

    poster_prefs = settings.get('PosterPreferences', {})
    auto_threshold = float(poster_prefs.get('auto_select_threshold', 0.8))

    for movie_dir in awaiting_movies:
        try:
            movie_name = movie_dir.name
            logger.info(f"\n🎬 Batch processing: {movie_name}")

            # Parse movie name
            title, year = parse_movie_name(movie_name)

            # Initialize API client
            api_client = UnifiedAPIClient(settings)

            # Find movie IDs
            identifiers = api_client.find_movie_id(title, year)
            if not identifiers:
                logger.error(f"❌ Could not find movie IDs for: {title}")
                results['failed'] += 1
                results['movies'].append({'name': movie_name, 'status': 'failed', 'reason': 'no_ids'})
                continue

            # Collect posters
            all_posters = api_client.collect_all_posters(identifiers)
            
            # Integrate personal images if available
            all_posters = integrate_personal_images(movie_name, all_posters)
            
            if not all_posters:
                logger.error(f"❌ No posters found for: {title}")
                results['failed'] += 1
                results['movies'].append({'name': movie_name, 'status': 'failed', 'reason': 'no_posters'})
                continue

            # Filter and rank posters
            filtered_posters = filter_posters_by_criteria(all_posters, settings)
            if not filtered_posters:
                filtered_posters = all_posters[:5]  # Use top 5 if filtering removes all

            ranked_posters = rank_posters_by_quality(filtered_posters, settings, movie_name)
            if not ranked_posters:
                logger.error(f"❌ Could not rank posters for: {title}")
                results['failed'] += 1
                results['movies'].append({'name': movie_name, 'status': 'failed', 'reason': 'ranking_failed'})
                continue

            # Auto-select best poster if it meets threshold
            best_poster, best_analysis = ranked_posters[0]
            final_score = best_analysis.get('final_score', 0.0)

            if final_score >= auto_threshold:
                # Download selected poster
                poster_file = movie_dir / "poster.jpg"
                if download_poster_image(best_poster, poster_file):
                    # Move to final folder
                    if move_to_final_plex_ready(movie_dir):
                        logger.info(f"✅ Batch processed: {movie_name} (Score: {final_score:.3f})")
                        results['successful'] += 1
                        results['movies'].append({
                            'name': movie_name,
                            'status': 'success',
                            'score': final_score,
                            'source': best_poster.source
                        })
                    else:
                        results['failed'] += 1
                        results['movies'].append({'name': movie_name, 'status': 'failed', 'reason': 'move_failed'})
                else:
                    results['failed'] += 1
                    results['movies'].append({'name': movie_name, 'status': 'failed', 'reason': 'download_failed'})
            else:
                logger.warning(f"⚠️ Best poster score {final_score:.3f} below threshold {auto_threshold}")
                results['failed'] += 1
                results['movies'].append({
                    'name': movie_name,
                    'status': 'failed',
                    'reason': 'below_threshold',
                    'score': final_score
                })

            results['processed'] += 1

        except Exception as e:
            logger.error(f"❌ Batch processing error for {movie_dir.name}: {e}")
            results['failed'] += 1
            results['movies'].append({'name': movie_dir.name, 'status': 'failed', 'reason': str(e)})

    # Summary
    logger.info(f"\n{'='*60}")
    logger.info(f"📊 Batch Processing Complete:")
    logger.info(f"   📁 Total processed: {results['processed']}")
    logger.info(f"   ✅ Successful: {results['successful']}")
    logger.info(f"   ❌ Failed: {results['failed']}")
    logger.info(f"   📈 Success rate: {(results['successful']/results['processed']*100):.1f}%" if results['processed'] > 0 else "   📈 Success rate: 0%")

    return results


def cleanup_temp_analysis_files():
    """Clean up temporary analysis files after processing."""
    try:
        temp_dir = Path("workspace") / "temp_original_backup" / "temp_analysis"
        if temp_dir.exists():
            import shutil
            shutil.rmtree(temp_dir)
            logger.debug("🗑️ Cleaned up temp analysis directory")
    except Exception as e:
        logger.warning(f"⚠️ Failed to cleanup temp analysis files: {e}")


def cleanup_temp_ranking_folder():
    """Clean up temporary ranking folder after processing."""
    try:
        temp_ranking_dir = Path("workspace") / "temp_original_backup" / "Posters" / "temp_ranking"
        if temp_ranking_dir.exists():
            import shutil
            shutil.rmtree(temp_ranking_dir)
            logger.debug("🗑️ Cleaned up temp ranking directory")
    except Exception as e:
        logger.warning(f"⚠️ Failed to cleanup temp ranking folder: {e}")


def download_multi_source_poster(movie_name: str, movie_dir: Path, settings: dict) -> bool:
    """
    Download poster using multi-source approach from PDFs.

    This implements the comprehensive poster acquisition system:
    1. Use TMDB to get movie IDs (foundational source)
    2. Collect posters from TMDB, Fanart.tv, ThePosterDB
    3. Present interactive selection to user
    4. Download selected poster

    Args:
        movie_name: Name of the movie (e.g., "The Matrix (1999)")
        movie_dir: Path to movie directory
        settings: Settings dictionary

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Parse movie name
        title, year = parse_movie_name(movie_name)
        logger.info(f"🎬 Multi-source poster search for: {title} ({year})")

        # Initialize unified API client
        api_client = UnifiedAPIClient(settings)

        # Step 1: Find movie IDs using TMDB (foundational source)
        identifiers = api_client.find_movie_id(title, year)
        if not identifiers:
            logger.error(f"❌ Could not find movie IDs for: {title}")
            return False

        # Step 2: Collect posters from all sources
        all_posters = api_client.collect_all_posters(identifiers)
        
        # Integrate personal images if available
        all_posters = integrate_personal_images(movie_name, all_posters)
        
        if not all_posters:
            logger.error(f"❌ No posters found from any source for: {title}")
            return False

        # Step 3: Advanced poster processing with filtering and ranking
        poster_prefs = settings.get('PosterPreferences', {})
        processing_mode = poster_prefs.get('processing_mode', 'interactive')

        # Parse numeric settings (INI files read as strings)
        max_posters_raw = poster_prefs.get('max_posters_to_analyze', 20)
        try:
            max_posters = int(max_posters_raw) if isinstance(max_posters_raw, str) else max_posters_raw
        except (ValueError, TypeError):
            max_posters = 20

        # Limit posters for performance
        if len(all_posters) > max_posters:
            logger.info(f"📊 Limiting analysis to top {max_posters} posters")
            all_posters = all_posters[:max_posters]

        # Filter posters by user criteria
        filtered_posters = filter_posters_by_criteria(all_posters, settings)
        if not filtered_posters:
            logger.warning(f"⚠️ No posters passed filtering criteria, using unfiltered list")
            filtered_posters = all_posters

        # Choose selection method based on user preference
        if processing_mode == 'auto':
            logger.info("🤖 Using automatic poster selection")
            selected_poster = auto_select_best_poster(filtered_posters, settings, movie_name)
        else:
            logger.info("👤 Using interactive poster selection")
            # Rank posters for better presentation
            ranked_posters = rank_posters_by_quality(filtered_posters, settings, movie_name)
            # Extract just the poster objects for interactive selection
            ranked_poster_list = [poster for poster, analysis in ranked_posters]
            selected_poster = interactive_poster_selection(ranked_poster_list, movie_dir)

        if not selected_poster:
            logger.warning(f"⚠️ No poster selected for: {title}")
            return False

        # Step 4: Download selected poster
        poster_file = movie_dir / "poster.jpg"
        if download_poster_image(selected_poster, poster_file):
            logger.info(f"✅ Successfully downloaded poster from {selected_poster.source}")

            # Mark the selected poster in the permanent archive
            try:
                poster_data = poster_file.read_bytes()
                # We need to get the analysis for the selected poster - let's do a quick analysis
                temp_dir = Path("workspace") / "temp_original_backup" / "temp_analysis"
                temp_dir.mkdir(parents=True, exist_ok=True)
                temp_analysis_path = temp_dir / "selected_poster_analysis.jpg"
                temp_analysis_path.write_bytes(poster_data)

                # Quick analysis of selected poster
                poster_prefs = settings.get('PosterPreferences', {})
                use_ollama = poster_prefs.get('use_ollama_models', 'true').lower() == 'true'

                if use_ollama:
                    selected_analysis = analyze_poster_with_advanced_ollama(temp_analysis_path)
                else:
                    selected_analysis = analyze_poster_with_ai(temp_analysis_path)

                # Mark as selected in archive (use full movie name with year)
                mark_selected_poster_in_archive(movie_name, selected_poster, poster_data, selected_analysis)

                # Clean up temp analysis file
                if temp_analysis_path.exists():
                    temp_analysis_path.unlink()

            except Exception as e:
                logger.warning(f"⚠️ Failed to mark selected poster in archive: {e}")

            # Add Plex Automator marker for tracking
            add_plex_automator_marker(movie_dir)

            return True
        else:
            logger.error(f"❌ Failed to download selected poster")
            return False

    except Exception as e:
        logger.error(f"❌ Failed multi-source poster download for {movie_name}: {e}")
        return False


def download_poster_fallback(movie_name: str, movie_dir: Path, settings: dict) -> bool:
    """
    Fallback poster download using simple TMDB approach.
    Used when multi-source approach fails or is disabled.

    Args:
        movie_name: Name of the movie (e.g., "The Matrix (1999)")
        movie_dir: Path to movie directory
        settings: Settings dictionary

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Parse movie name
        title, year = parse_movie_name(movie_name)
        logger.info(f"🎬 Fallback poster search for: {title} ({year})")

        # Get TMDB API key from settings
        tmdb_api_key = settings.get('APIKeys', {}).get('tmdb_api_key')
        if not tmdb_api_key:
            logger.warning("⚠️ No TMDB API key found in settings")
            return False

        # Search for movie on TMDB
        search_url = "https://api.themoviedb.org/3/search/movie"
        search_params = {
            'api_key': tmdb_api_key,
            'query': title
        }
        if year:
            search_params['year'] = year

        response = requests.get(search_url, params=search_params, timeout=10)
        response.raise_for_status()
        search_data = response.json()

        if not search_data.get('results'):
            logger.warning(f"⚠️ No TMDB results found for: {title}")
            return False

        # Get the first result
        movie_data = search_data['results'][0]
        poster_path = movie_data.get('poster_path')

        if not poster_path:
            logger.warning(f"⚠️ No poster found for: {title}")
            return False
        
        # Download poster
        poster_size = settings.get('PosterAndQCPrep', {}).get('poster_size', 'w500')
        poster_url = f"https://image.tmdb.org/t/p/{poster_size}{poster_path}"
        
        logger.info(f"📥 Downloading poster from: {poster_url}")
        
        poster_response = requests.get(poster_url, timeout=30)
        poster_response.raise_for_status()
        
        # Save poster to movie directory
        poster_file = movie_dir / "poster.jpg"
        with open(poster_file, 'wb') as f:
            f.write(poster_response.content)
        
        logger.info(f"✅ Downloaded poster: {poster_file.name}")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to download poster for {movie_name}: {e}")
        return False


def move_to_final_plex_ready(movie_dir: Path) -> bool:
    """
    Move movie + poster to folder 6 (final_plex_ready).
    Only moves movie.mkv and poster.jpg - clean final output.
    
    Args:
        movie_dir: Path to movie directory in folder 5 (awaiting_poster)
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        movie_name = movie_dir.name
        
        # Determine source structure and resolution
        if movie_dir.parent.parent.name in ['movies', 'tv_shows']:
            # New structure: workspace/5_awaiting_poster/movies|tv_shows/resolution/movie_name
            content_type = movie_dir.parent.parent.name
            resolution = movie_dir.parent.name
            logger.debug(f"📁 Using new structure: {content_type}/{resolution}")
        else:
            # Legacy structure: workspace/5_awaiting_poster/resolution/movie_name
            content_type = "movies"  # Default to movies for legacy
            resolution = movie_dir.parent.name
            logger.debug(f"📁 Using legacy structure, defaulting to movies")
        
        # Create folder 6 destination - maintain content type structure
        folder6_base = Path("workspace") / "6_final_plex_ready"
        if content_type in ['movies', 'tv_shows']:
            folder6_dest = folder6_base / content_type / resolution / movie_name
        else:
            # Fallback to legacy structure
            folder6_dest = folder6_base / resolution / movie_name
        folder6_dest.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"📁 Moving to folder 6: {content_type}/{resolution}/{movie_name}")
        
        # Move only the movie file and poster
        files_moved = 0
        
        # Move movie file
        movie_files = list(movie_dir.glob("*.mkv"))
        if movie_files:
            movie_file = movie_files[0]
            dest_movie = folder6_dest / movie_file.name
            shutil.move(str(movie_file), str(dest_movie))
            logger.info(f"📁 Moved movie: {movie_file.name} → folder 6")
            files_moved += 1
        
        # Move poster file
        poster_file = movie_dir / "poster.jpg"
        if poster_file.exists():
            dest_poster = folder6_dest / "poster.jpg"
            shutil.move(str(poster_file), str(dest_poster))
            logger.info(f"📁 Moved poster: poster.jpg → folder 6")
            files_moved += 1
        
        # Remove the now-empty folder 5 directory
        try:
            # Remove any remaining files first
            for remaining_file in movie_dir.iterdir():
                if remaining_file.is_file():
                    remaining_file.unlink()
                    logger.debug(f"🗑️ Removed leftover file: {remaining_file.name}")
            
            movie_dir.rmdir()
            logger.info(f"🗑️ Removed empty folder 5 directory: {movie_name}")
        except OSError:
            # Directory not empty - log what's left
            remaining_files = list(movie_dir.iterdir())
            logger.warning(f"⚠️ Folder 5 directory not empty: {[f.name for f in remaining_files]}")
        
        logger.info(f"✅ Successfully finalized movie: {movie_name} ({files_moved} files)")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to move to final plex ready: {e}")
        return False


def process_posters():
    """
    Process all movies and TV shows awaiting poster.
    """
    # Load settings
    try:
        settings = load_settings("_internal/config/settings.ini")
        logger.info("📋 Settings loaded")
    except Exception as e:
        logger.warning(f"⚠️ Failed to load settings: {e}, using defaults")
        settings = {}
    
    # Process movies
    awaiting_movies = discover_movies_awaiting_poster()
    if awaiting_movies:
        logger.info(f"🎬 Processing {len(awaiting_movies)} movies awaiting poster...")
        process_movies_awaiting_poster(awaiting_movies, settings)
    else:
        logger.info("🎬 No movies awaiting poster processing")
    
    # Process TV shows
    awaiting_tv_shows = discover_tv_shows_awaiting_poster()
    if awaiting_tv_shows:
        logger.info(f"📺 Processing {len(awaiting_tv_shows)} TV shows awaiting poster...")
        process_tv_shows_awaiting_poster(awaiting_tv_shows, settings)
    else:
        logger.info("📺 No TV shows awaiting poster processing")


def process_movies_awaiting_poster(awaiting_movies: List[Path], settings: dict):
    """
    Process movies awaiting poster with multi-source collection and AI analysis.
    
    Args:
        awaiting_movies: List of movie directories awaiting poster
        settings: Settings dictionary
    """
    for movie_dir in awaiting_movies:
        movie_name = movie_dir.name
        logger.info(f"🎬 Processing movie: {movie_name}")
        
        try:
            # Use multi-source poster collection
            poster_prefs = settings.get('PosterPreferences', {})
            use_interactive = poster_prefs.get('use_interactive_selection', 'false').lower() == 'true'
            
            if use_interactive:
                success = download_multi_source_poster_interactive(movie_name, movie_dir, settings)
            else:
                success = download_multi_source_poster(movie_name, movie_dir, settings)
            
            if success:
                # Move to final folder
                move_success = move_to_final_plex_ready(movie_dir)
                if move_success:
                    logger.info(f"✅ Completed movie: {movie_name}")
                else:
                    logger.warning(f"⚠️ Poster downloaded but failed to move to final folder: {movie_name}")
            else:
                logger.warning(f"⚠️ Failed to download poster for movie: {movie_name}")
                
        except Exception as e:
            logger.error(f"❌ Failed to process movie {movie_name}: {e}")


def process_tv_shows_awaiting_poster(awaiting_tv_shows: List[Path], settings: dict):
    """
    Process TV shows awaiting poster with multi-source collection and AI analysis.
    
    Args:
        awaiting_tv_shows: List of TV show directories awaiting poster
        settings: Settings dictionary
    """
    for show_dir in awaiting_tv_shows:
        show_name = show_dir.name
        logger.info(f"📺 Processing TV show: {show_name}")
        
        try:
            # Use multi-source TV show poster collection
            poster_prefs = settings.get('PosterPreferences', {})
            use_interactive = poster_prefs.get('use_interactive_selection', 'false').lower() == 'true'
            
            if use_interactive:
                success = download_multi_source_tv_poster_interactive(show_name, show_dir, settings)
            else:
                success = download_multi_source_tv_poster(show_name, show_dir, settings)
            
            if success:
                # Move to final folder
                move_success = move_tv_show_to_final_plex_ready(show_dir)
                if move_success:
                    logger.info(f"✅ Completed TV show: {show_name}")
                else:
                    logger.warning(f"⚠️ Poster downloaded but failed to move to final folder: {show_name}")
            else:
                logger.warning(f"⚠️ Failed to download poster for TV show: {show_name}")
                
        except Exception as e:
            logger.error(f"❌ Failed to process TV show {show_name}: {e}")


def download_multi_source_tv_poster(show_name: str, show_dir: Path, settings: dict) -> bool:
    """
    Download TV show poster using multi-source approach with TVDB, TMDB, and Fanart.tv.
    Uses AI-powered quality assessment to select the best poster.

    Args:
        show_name: Name of the TV show (e.g., "Breaking Bad (2008)")
        show_dir: Path to TV show directory
        settings: Settings dictionary

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Parse TV show name
        title, year = parse_tv_show_name(show_name)
        logger.info(f"📺 Multi-source TV poster search for: {title} ({year})")

        # Initialize TV show API client
        tv_api = TVShowAPIClient(settings)

        # Get TV show posters from all sources
        all_posters = []

        # TVDB (primary source for TV shows)
        logger.info("🔍 Searching TVDB for TV show posters...")
        tvdb_posters = tv_api.get_tvdb_posters(title, year)
        all_posters.extend(tvdb_posters)
        logger.info(f"  ✅ Found {len(tvdb_posters)} TVDB posters")

        # TMDB TV shows
        logger.info("🔍 Searching TMDB for TV show posters...")
        tmdb_tv_posters = tv_api.get_tmdb_tv_posters(title, year)
        all_posters.extend(tmdb_tv_posters)
        logger.info(f"  ✅ Found {len(tmdb_tv_posters)} TMDB TV posters")

        # Fanart.tv TV shows
        logger.info("🔍 Searching Fanart.tv for TV show artwork...")
        fanart_tv_posters = tv_api.get_fanart_tv_posters(title, year)
        all_posters.extend(fanart_tv_posters)
        logger.info(f"  ✅ Found {len(fanart_tv_posters)} Fanart.tv TV posters")

        if not all_posters:
            logger.warning(f"⚠️ No TV show posters found from any source for: {title}")
            return False

        # Integrate personal images if any exist
        all_posters = integrate_personal_images(show_name, all_posters)

        # Filter posters based on criteria
        filtered_posters = filter_posters_by_criteria(all_posters, settings)
        logger.info(f"📊 Filtered to {len(filtered_posters)} suitable TV show posters")

        if not filtered_posters:
            logger.warning(f"⚠️ No TV show posters passed filtering criteria")
            return False

        # Rank posters by quality using AI analysis
        ranked_posters = rank_posters_by_quality(filtered_posters, settings, show_name)

        if not ranked_posters:
            logger.warning(f"⚠️ No TV show posters survived quality ranking")
            return False

        # Select best poster (top ranked)
        best_poster, best_analysis = ranked_posters[0]
        logger.info(f"🎯 Selected best TV show poster: {best_poster.source} "
                   f"(Score: {best_analysis['final_score']:.3f})")

        # Download the selected poster
        poster_file = show_dir / "poster.jpg"
        if download_poster_image(best_poster, poster_file):
            logger.info(f"✅ Downloaded TV show poster: {poster_file.name}")

            # Mark as selected in archive and save poster data
            try:
                poster_data = poster_file.read_bytes()
                mark_selected_poster_in_archive(show_name, best_poster, poster_data, best_analysis)
            except Exception as e:
                logger.warning(f"⚠️ Failed to mark selected TV show poster in archive: {e}")

            # Add Plex Automator marker for tracking
            add_plex_automator_marker(show_dir)

            return True
        else:
            logger.error(f"❌ Failed to download selected TV show poster")
            return False

    except Exception as e:
        logger.error(f"❌ Failed multi-source TV poster download for {show_name}: {e}")
        return False


def download_multi_source_tv_poster_interactive(show_name: str, show_dir: Path, settings: dict) -> bool:
    """
    Download TV show poster using multi-source approach with interactive selection.
    User can review and select from top-ranked posters.

    Args:
        show_name: Name of the TV show (e.g., "Breaking Bad (2008)")
        show_dir: Path to TV show directory
        settings: Settings dictionary

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Parse TV show name
        title, year = parse_tv_show_name(show_name)
        logger.info(f"📺 Interactive TV poster search for: {title} ({year})")

        # Initialize TV show API client
        tv_api = TVShowAPIClient(settings)

        # Get TV show posters from all sources
        all_posters = []

        # TVDB (primary source for TV shows)
        logger.info("🔍 Searching TVDB for TV show posters...")
        tvdb_posters = tv_api.get_tvdb_posters(title, year)
        all_posters.extend(tvdb_posters)
        logger.info(f"  ✅ Found {len(tvdb_posters)} TVDB posters")

        # TMDB TV shows
        logger.info("🔍 Searching TMDB for TV show posters...")
        tmdb_tv_posters = tv_api.get_tmdb_tv_posters(title, year)
        all_posters.extend(tmdb_tv_posters)
        logger.info(f"  ✅ Found {len(tmdb_tv_posters)} TMDB TV posters")

        # Fanart.tv TV shows
        logger.info("🔍 Searching Fanart.tv for TV show artwork...")
        fanart_tv_posters = tv_api.get_fanart_tv_posters(title, year)
        all_posters.extend(fanart_tv_posters)
        logger.info(f"  ✅ Found {len(fanart_tv_posters)} Fanart.tv TV posters")

        if not all_posters:
            logger.warning(f"⚠️ No TV show posters found from any source for: {title}")
            return False

        # Integrate personal images if any exist
        all_posters = integrate_personal_images(show_name, all_posters)

        # Filter posters based on criteria
        filtered_posters = filter_posters_by_criteria(all_posters, settings)
        logger.info(f"📊 Filtered to {len(filtered_posters)} suitable TV show posters")

        if not filtered_posters:
            logger.warning(f"⚠️ No TV show posters passed filtering criteria")
            return False

        # Rank posters by quality using AI analysis
        ranked_posters = rank_posters_by_quality(filtered_posters, settings, show_name)

        if not ranked_posters:
            logger.warning(f"⚠️ No TV show posters survived quality ranking")
            return False

        # Interactive selection from top posters
        poster_list = [poster for poster, _ in ranked_posters]
        selected_poster = interactive_poster_selection(poster_list, show_dir)

        if not selected_poster:
            logger.info("ℹ️ No TV show poster selected by user")
            return False

        # Find the analysis for the selected poster
        selected_analysis = None
        for poster, analysis in ranked_posters:
            if poster.url == selected_poster.url:
                selected_analysis = analysis
                break

        if not selected_analysis:
            selected_analysis = {'final_score': 0.8, 'overall_score': 0.8}

        logger.info(f"🎯 User selected TV show poster: {selected_poster.source} "
                   f"(Score: {selected_analysis['final_score']:.3f})")

        # Download the selected poster
        poster_file = show_dir / "poster.jpg"
        if download_poster_image(selected_poster, poster_file):
            logger.info(f"✅ Downloaded TV show poster: {poster_file.name}")

            # Mark as selected in archive and save poster data
            try:
                poster_data = poster_file.read_bytes()
                mark_selected_poster_in_archive(show_name, selected_poster, poster_data, selected_analysis)
            except Exception as e:
                logger.warning(f"⚠️ Failed to mark selected TV show poster in archive: {e}")

            # Add Plex Automator marker for tracking
            add_plex_automator_marker(show_dir)

            return True
        else:
            logger.error(f"❌ Failed to download selected TV show poster")
            return False

    except Exception as e:
        logger.error(f"❌ Failed interactive TV poster download for {show_name}: {e}")
        return False


def move_tv_show_to_final_plex_ready(show_dir: Path) -> bool:
    """
    Move TV show + poster to folder 6 (final_plex_ready).
    Moves all episode files and the poster - maintains TV show structure.
    
    Args:
        show_dir: Path to TV show directory in folder 5 (awaiting_poster)
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        show_name = show_dir.name
        
        # Determine source structure and resolution
        if show_dir.parent.parent.name in ['movies', 'tv_shows']:
            # New structure: workspace/5_awaiting_poster/tv_shows/resolution/show_name
            content_type = show_dir.parent.parent.name
            resolution = show_dir.parent.name
            logger.debug(f"📁 Using new structure: {content_type}/{resolution}")
        else:
            # Legacy structure: workspace/5_awaiting_poster/resolution/show_name
            content_type = "tv_shows"  # Default to tv_shows
            resolution = show_dir.parent.name
            logger.debug(f"📁 Using legacy structure, defaulting to tv_shows")
        
        # Create folder 6 destination - maintain content type structure
        folder6_base = Path("workspace") / "6_final_plex_ready"
        if content_type in ['movies', 'tv_shows']:
            folder6_dest = folder6_base / content_type / resolution / show_name
        else:
            # Fallback to tv_shows structure
            folder6_dest = folder6_base / "tv_shows" / resolution / show_name
        folder6_dest.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"📁 Moving TV show to folder 6: {content_type}/{resolution}/{show_name}")
        
        # Move all content including seasons/episodes and poster
        files_moved = 0
        
        # Move poster file first
        poster_file = show_dir / "poster.jpg"
        if poster_file.exists():
            dest_poster = folder6_dest / "poster.jpg"
            shutil.move(str(poster_file), str(dest_poster))
            logger.info(f"📁 Moved poster: poster.jpg → folder 6")
            files_moved += 1
        
        # Move all other content (episodes, seasons, etc.)
        for item in show_dir.iterdir():
            if item != poster_file:  # Skip poster since we already moved it
                dest_item = folder6_dest / item.name
                if item.is_dir():
                    shutil.move(str(item), str(dest_item))
                    logger.info(f"📁 Moved directory: {item.name} → folder 6")
                    files_moved += 1
                elif item.is_file():
                    shutil.move(str(item), str(dest_item))
                    logger.info(f"📁 Moved file: {item.name} → folder 6")
                    files_moved += 1
        
        # Remove the now-empty folder 5 directory
        try:
            show_dir.rmdir()
            logger.info(f"🗑️ Removed empty folder 5 directory: {show_name}")
        except OSError:
            # Directory not empty - log what's left
            remaining_files = list(show_dir.iterdir())
            logger.warning(f"⚠️ Folder 5 directory not empty: {[f.name for f in remaining_files]}")
        
        logger.info(f"✅ Successfully finalized TV show: {show_name} ({files_moved} items)")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to move TV show to final plex ready: {e}")
        return False
    
    if not awaiting_movies:
        logger.info("✅ No movies awaiting poster")
        return
    
    logger.info(f"\n🎨 Starting Poster Processing")
    logger.info(f"📊 Found {len(awaiting_movies)} movies to process")
    logger.info("=" * 50)
    
    successful = 0
    failed = 0
    
    for movie_dir in awaiting_movies:
        try:
            movie_name = movie_dir.name
            logger.info(f"\n🎬 Processing: {movie_name}")
            
            # Try multi-source poster download first
            poster_success = download_multi_source_poster(movie_name, movie_dir, settings)

            # If multi-source fails, try fallback
            if not poster_success:
                logger.info(f"🔄 Multi-source failed, trying fallback for: {movie_name}")
                poster_success = download_poster_fallback(movie_name, movie_dir, settings)
            
            if not poster_success:
                logger.warning(f"⚠️ Continuing without poster for: {movie_name}")
            
            # Move to final plex ready (with or without poster)
            if move_to_final_plex_ready(movie_dir):
                logger.info(f"✅ Successfully finalized: {movie_name}")
                successful += 1
            else:
                logger.error(f"❌ Failed to finalize: {movie_name}")
                failed += 1
                
        except Exception as e:
            logger.error(f"❌ Error processing {movie_dir.name}: {e}")
            failed += 1
    
    # Summary
    logger.info(f"\n{'='*50}")
    logger.info(f"📊 Poster Processing Summary:")
    logger.info(f"   ✅ Successful: {successful}")
    logger.info(f"   ❌ Failed: {failed}")
    logger.info(f"   📁 Total processed: {successful + failed}")
    
    if successful > 0:
        logger.info(f"\n🎉 {successful} movies are now ready for Plex!")
        logger.info(f"📁 Check workspace/7_final_plex_ready/ for finalized movies")

    # Clean up temporary files
    cleanup_temp_analysis_files()
    cleanup_temp_ranking_folder()


def display_interactive_menu():
    """
    Display the main interactive menu for content type selection.

    Returns:
        str: Selected content type ('movies', 'tv_shows', 'both', 'quit')
    """
    print(f"\n{'='*60}")
    print(f"🎬📺 PlexMovieAutomator - Interactive Content Selection")
    print(f"{'='*60}")
    print(f"\nWhat type of content would you like to process?")
    print(f"  1. Movies only")
    print(f"  2. TV Shows only")
    print(f"  3. Both Movies and TV Shows")
    print(f"  4. Quit")

    while True:
        try:
            choice = input(f"\nEnter your choice [1-4]: ").strip()

            if choice == '1':
                return 'movies'
            elif choice == '2':
                return 'tv_shows'
            elif choice == '3':
                return 'both'
            elif choice == '4':
                return 'quit'
            else:
                print(f"Please enter a number between 1 and 4")

        except KeyboardInterrupt:
            print(f"\n👋 Exiting...")
            return 'quit'


def main():
    """
    Main function for advanced poster handler.
    Supports both movies and TV shows with interactive and batch processing modes.
    """
    import argparse

    parser = argparse.ArgumentParser(description='Advanced Multi-Source Poster Handler')
    parser.add_argument('--batch', action='store_true',
                       help='Run in batch processing mode (auto-select best posters)')
    parser.add_argument('--interactive-menu', action='store_true',
                       help='Force interactive content selection menu')
    parser.add_argument('--interactive', action='store_true',
                       help='Force interactive mode (override settings)')
    parser.add_argument('--auto', action='store_true',
                       help='Force automatic mode (override settings)')
    parser.add_argument('--movies-only', action='store_true',
                       help='Process only movies (command-line mode)')
    parser.add_argument('--tv-only', action='store_true',
                       help='Process only TV shows (command-line mode)')

    args = parser.parse_args()

    logger.info("🎨 Starting Advanced Multi-Source Poster Handler (Script 07)")
    logger.info("   Supporting Movies (TMDB, Fanart.tv, ThePosterDB) and TV Shows (TVDB, TMDB, Fanart.tv)")
    logger.info("   Default: Interactive mode (use --movies-only or --tv-only for command-line mode)")
    logger.info("=" * 80)

    # Load settings
    try:
        from utils.common_helpers import load_settings
        settings = load_settings("_internal/config/settings.ini")
        logger.info("📋 Settings loaded")
    except Exception as e:
        logger.warning(f"⚠️ Failed to load settings: {e}, using defaults")
        settings = {}

    # Default to interactive mode unless command-line content arguments are specified
    if args.movies_only or args.tv_only or args.batch:
        # Command line mode - user specified content type arguments
        if args.movies_only:
            content_type_choice = 'movies'
        elif args.tv_only:
            content_type_choice = 'tv_shows'
        else:  # batch mode processes both
            content_type_choice = 'both'
    else:
        # Interactive content selection (default)
        content_type_choice = display_interactive_menu()
        
        if content_type_choice == 'quit':
            logger.info("👋 User chose to quit")
            return

    # Determine processing mode
    poster_prefs = settings.get('PosterPreferences', {})

    if args.batch:
        logger.info("🚀 Running in BATCH processing mode")
        
        if content_type_choice == 'movies':
            logger.info("🎬 Processing movies only...")
            awaiting_movies = discover_movies_awaiting_poster()
            if awaiting_movies:
                process_movies_awaiting_poster(awaiting_movies, settings)
        elif content_type_choice == 'tv_shows':
            logger.info("📺 Processing TV shows only...")
            awaiting_tv_shows = discover_tv_shows_awaiting_poster()
            if awaiting_tv_shows:
                process_tv_shows_awaiting_poster(awaiting_tv_shows, settings)
        else:
            # Process both movies and TV shows
            logger.info("🎬📺 Processing both movies and TV shows...")
            process_posters()

    elif args.interactive:
        logger.info("👤 Running in INTERACTIVE mode (forced)")
        # Temporarily override settings
        settings.setdefault('PosterPreferences', {})['processing_mode'] = 'interactive'
        
        # Process based on content selection
        if content_type_choice == 'movies':
            logger.info("🎬 Processing movies only...")
            awaiting_movies = discover_movies_awaiting_poster()
            if awaiting_movies:
                process_movies_awaiting_poster(awaiting_movies, settings)
        elif content_type_choice == 'tv_shows':
            logger.info("📺 Processing TV shows only...")
            awaiting_tv_shows = discover_tv_shows_awaiting_poster()
            if awaiting_tv_shows:
                process_tv_shows_awaiting_poster(awaiting_tv_shows, settings)
        else:
            process_posters()

    elif args.auto:
        logger.info("🤖 Running in AUTOMATIC mode (forced)")
        # Temporarily override settings
        settings.setdefault('PosterPreferences', {})['processing_mode'] = 'auto'
        
        # Process based on content selection
        if content_type_choice == 'movies':
            logger.info("🎬 Processing movies only...")
            awaiting_movies = discover_movies_awaiting_poster()
            if awaiting_movies:
                process_movies_awaiting_poster(awaiting_movies, settings)
        elif content_type_choice == 'tv_shows':
            logger.info("📺 Processing TV shows only...")
            awaiting_tv_shows = discover_tv_shows_awaiting_poster()
            if awaiting_tv_shows:
                process_tv_shows_awaiting_poster(awaiting_tv_shows, settings)
        else:
            process_posters()

    else:
        # Use settings configuration with content selection
        processing_mode = poster_prefs.get('processing_mode', 'interactive')
        
        content_type_str = {
            'movies': 'Movies',
            'tv_shows': 'TV Shows', 
            'both': 'Movies and TV Shows'
        }.get(content_type_choice, 'Content')
        
        if processing_mode == 'batch':
            logger.info(f"🚀 Running in BATCH processing mode for {content_type_str} (from settings)")
            if content_type_choice == 'movies':
                awaiting_movies = discover_movies_awaiting_poster()
                if awaiting_movies:
                    batch_process_all_movies(settings)
            elif content_type_choice == 'tv_shows':
                awaiting_tv_shows = discover_tv_shows_awaiting_poster()
                if awaiting_tv_shows:
                    process_tv_shows_awaiting_poster(awaiting_tv_shows, settings)
            else:
                batch_process_all_movies(settings)
                awaiting_tv_shows = discover_tv_shows_awaiting_poster()
                if awaiting_tv_shows:
                    process_tv_shows_awaiting_poster(awaiting_tv_shows, settings)
        else:
            logger.info(f"🎯 Running in {processing_mode.upper()} mode for {content_type_str} (from settings)")
            if content_type_choice == 'movies':
                awaiting_movies = discover_movies_awaiting_poster()
                if awaiting_movies:
                    process_movies_awaiting_poster(awaiting_movies, settings)
            elif content_type_choice == 'tv_shows':
                awaiting_tv_shows = discover_tv_shows_awaiting_poster()
                if awaiting_tv_shows:
                    process_tv_shows_awaiting_poster(awaiting_tv_shows, settings)
            else:
                process_posters()


if __name__ == "__main__":
    main()
