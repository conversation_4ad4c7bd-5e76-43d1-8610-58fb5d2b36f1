#!/usr/bin/env python3
from __future__ import annotations

import json
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, Optional

from _internal.utils.common_helpers import ensure_dir_exists, safe_move_file, safe_delete_folder, get_setting

from _internal.src.season_pack_utils import detect_season_pack, validate_episodes
from _internal.src.event_queue import get_event_queue
from _internal.src import request_state


@dataclass
class MovieInfo:
    title: str
    year: Optional[int]


async def organize_movie(
    content_info: Dict[str, Any],
    main_file_path: str,
    download_dir: Path,
    organized_base_dir: Path,
    resolution: str,
    logger_instance,
    settings_dict: Optional[Dict[str, Any]] = None,
) -> bool:
    """
    Organize a movie into the Plex library structure.

    Returns True on success, False otherwise.
    """
    try:
        title = content_info.get("title", "Unknown")
        year = content_info.get("year", "")

        # Clean overly verbose release-like titles
        original_title = title
        if title and title != "Unknown":
            import re
            release_indicators = [
                '.', 'bluray', 'web-dl', '1080p', '2160p', '720p', 'h264', 'h265', 'x264', 'x265',
                'remux', 'truehd', 'atmos', 'dts', 'avc', 'hevc', 'remaster', 'extended', 'directors'
            ]
            if any(indicator in title.lower() for indicator in release_indicators):
                year_match = re.search(r'(\d{4})', title)
                if year_match:
                    year_pos = year_match.start()
                    clean_title = title[:year_pos].strip()
                    clean_title = re.sub(r'\.+$', '', clean_title)
                    clean_title = re.sub(r'\.', ' ', clean_title)
                    clean_title = re.sub(r'\s+', ' ', clean_title).strip()
                    if clean_title and len(clean_title) > 2:
                        title = clean_title
                        logger_instance.info(f"🧹 Cleaned Radarr movie title: '{original_title}' → '{title}'")
                        if not year and year_match:
                            year = int(year_match.group(1))
                            logger_instance.info(f"📅 Extracted year from title: {year}")
            else:
                logger_instance.info(f"✅ Radarr movie title looks clean: '{title}'")

        logger_instance.info(f"🎯 FINAL MOVIE METADATA: '{title}' ({year}) - cleaned from Radarr")

        # Build destination paths
        import re
        sanitized_title = re.sub(r'[<>:"/\\|?*]', '', title).strip()
        year_str = str(year) if year else "UnknownYear"
        plex_folder_name = f"{sanitized_title} ({year_str})"
        extension = Path(main_file_path).suffix
        plex_filename = f"{plex_folder_name}{extension}"

        movies_dir = (organized_base_dir / "movies" / resolution) if resolution else (organized_base_dir / "movies")
        final_movie_parent_dir = movies_dir / plex_folder_name
        final_movie_file_path = final_movie_parent_dir / plex_filename

        if final_movie_parent_dir.exists():
            logger_instance.warning(f"Movie destination folder already exists: {final_movie_parent_dir}. Skipping to avoid overwrite.")
            return False

        logger_instance.info(f"Organizing movie '{download_dir.name}' to '{final_movie_file_path}'")

        if ensure_dir_exists(final_movie_parent_dir, logger_instance):
            if safe_move_file(main_file_path, str(final_movie_file_path), logger_instance=logger_instance):
                # Cleanup the raw download folder
                logger_instance.info("Successfully organized movie file. Cleaning up raw download folder.")
                safe_delete_folder(str(download_dir), ignore_errors=True)

                # Create organized marker with metadata via MarkerManager
                from _internal.utils.marker_manager import get_marker_manager
                from _internal.src.idempotency import compute_movie_key, IdempotencyIndex
                idx = IdempotencyIndex()
                key = compute_movie_key(content_info)
                movie_metadata = {
                    'status': 'organized',
                    'movie_title': content_info.get('title', 'Unknown'),
                    'movie_year': content_info.get('year', 'Unknown'),
                    'file_path': str(final_movie_file_path),
                    'file_size': final_movie_file_path.stat().st_size if final_movie_file_path.exists() else 0,
                    'idempotency_key': key,
                }
                idx.mark_processed(key, 'organized', {'file_path': str(final_movie_file_path)})
                try:
                    mm = get_marker_manager(final_movie_parent_dir)
                    if mm.write('.organized', movie_metadata):
                        logger_instance.info(f"✅ Movie organized successfully with metadata: {final_movie_file_path}")
                    else:
                        logger_instance.warning("⚠️ Failed to create organized marker")
                except Exception as e:
                    try:
                        from _internal.src.marker_utils import write_organized_marker
                        write_organized_marker(final_movie_parent_dir, movie_metadata)
                    except Exception:
                        pass
                    logger_instance.warning(f"Failed to write metadata via marker manager; attempted fallback: {e}")
                    logger_instance.info(f"✅ Movie organized successfully: {final_movie_file_path}")
                # Emit FileOrganized event and update request state
                try:
                    eq = get_event_queue(settings_dict if isinstance(settings_dict, dict) else {'EventQueue': {'enabled': True}})
                    await eq.publish('file.organized', {
                        'type': 'movie',
                        'title': content_info.get('title'),
                        'year': content_info.get('year'),
                        'resolution': resolution,
                        'path': str(final_movie_file_path),
                        'radarr_id': content_info.get('radarr_id') or (content_info.get('radarr_movie') or {}).get('id'),
                        'tmdb_id': content_info.get('tmdb_id') or content_info.get('tmdbId')
                    })
                except Exception:
                    pass
                try:
                    request_state.mark_movie_complete(content_info.get('title'), content_info.get('year'), str(final_movie_file_path))
                except Exception:
                    pass
                # Radarr refresh/organize rescan
                try:
                    from _internal.src.radarr_integration import RadarrClient
                    radarr_url = settings_dict.get('Radarr', {}).get('url', 'http://localhost:7878') if settings_dict else 'http://localhost:7878'
                    radarr_api_key = settings_dict.get('Radarr', {}).get('api_key') if settings_dict else None
                    if radarr_api_key:
                        client = RadarrClient(radarr_url, radarr_api_key)
                        import aiohttp
                        async with aiohttp.ClientSession() as session:
                            # If we have a radarr_id, trigger a RefreshMovie; otherwise a general Wanted search/organize
                            radarr_id = (content_info.get('radarr_id') or (content_info.get('radarr_movie') or {}).get('id')) if isinstance(content_info, dict) else None
                            if radarr_id:
                                await client.issue_command(session, {"name": "RefreshMovie", "movieId": int(radarr_id)})
                                logger_instance.info(f"📡 Radarr refresh issued for movieId={radarr_id}")
                            else:
                                await client.issue_command(session, {"name": "RefreshMonitoredDownloads"})
                                logger_instance.info("📡 Radarr global refresh monitored downloads issued")
                except Exception as e:
                    logger_instance.warning(f"Radarr refresh failed: {e}")
                return True
            else:
                logger_instance.error(f"Failed to move movie file: {main_file_path} -> {final_movie_file_path}")
                return False
        else:
            logger_instance.error(f"Failed to create movie directory: {final_movie_parent_dir}")
            return False

    except Exception as e:
        logger_instance.error(f"Error organizing movie {download_dir.name}: {e}", exc_info=True)
        return False


async def organize_tv_show(
    content_info: Dict[str, Any],
    main_file_path: str,
    download_dir: Path,
    organized_base_dir: Path,
    resolution: str,
    tv_naming_helper,
    logger_instance,
    settings_dict: Optional[Dict[str, Any]] = None,
) -> bool:
    """
    Organize TV content into Plex structure.
    - Single-episode downloads: replace existing files (assumed upgrade).
    - Season packs: only organize missing episodes; skip existing, do not overwrite.
    """
    try:
        # Parse from folder and file
        tv_info = tv_naming_helper.parse_tv_show_filename(download_dir.name)
        try:
            tv_info_file = tv_naming_helper.parse_tv_show_filename(Path(main_file_path).name)
            if tv_info_file.season:
                tv_info.season = tv_info_file.season
            if tv_info_file.episode:
                tv_info.episode = tv_info_file.episode
            if getattr(tv_info_file, "end_episode", None):
                tv_info.end_episode = tv_info_file.end_episode
            if getattr(tv_info_file, "episode_list", None):
                tv_info.episode_list = tv_info_file.episode_list
        except Exception:
            pass

        # Prefer authoritative metadata
        meta_title = content_info.get("title") or content_info.get("cleaned_title")
        if meta_title:
            tv_info.series_title = str(meta_title)
        meta_year = content_info.get("year") or content_info.get("Year")
        if meta_year:
            tv_info.year = meta_year

        # If year still missing, try Sonarr lookup
        if not tv_info.year and settings_dict:
            try:
                from _internal.src.sonarr_integration import SonarrClient
                sonarr_url = settings_dict.get('Sonarr', {}).get('url', 'http://localhost:8989')
                sonarr_api_key = settings_dict.get('Sonarr', {}).get('api_key')
                if sonarr_api_key:
                    client = SonarrClient(sonarr_url, sonarr_api_key)
                    import aiohttp
                    async with aiohttp.ClientSession() as session:
                        results = await client.series_lookup(session, tv_info.series_title)
                        if results:
                            yr = results[0].get('year')
                            if yr:
                                tv_info.year = yr
                                logger_instance.info(f"📡 Retrieved year from Sonarr: {tv_info.year}")
            except Exception as e:
                logger_instance.warning(f"Failed Sonarr lookup for year: {e}")

        # Destination roots
        tv_shows_dir = (organized_base_dir / "tv_shows" / resolution) if resolution else (organized_base_dir / "tv_shows")

        # Detect season pack using centralized helper (no min size threshold in tests)
        download_path = Path(download_dir)
        from _internal.src.fs_helpers import find_video_files
        files_in_pack = find_video_files(download_path)
        is_season_pack = detect_season_pack(files_in_pack, tv_naming_helper, min_size_bytes=None)

        if is_season_pack:
            # Validate pack composition (single vs multi-season) for better logging and handling
            valid, mode = validate_episodes(files_in_pack, tv_naming_helper, min_size_bytes=None)
            logger_instance.info(f"🎯 SEASON PACK DETECTED: {len(files_in_pack)} files | valid={valid} mode={mode}")

            organized_count = 0
            failed_count = 0
            organized_eps: set[tuple[int,int]] = set()

            # First pass: parse all files to gather context (episodes present/missing)
            parsed_entries: list[dict] = []
            present_eps: set[int] = set()
            unlabeled_entries: list[dict] = []
            for video_file in files_in_pack:
                info = tv_naming_helper.parse_tv_show_filename(video_file.name)
                # Use authoritative series metadata
                info.series_title = tv_info.series_title
                info.year = tv_info.year
                if not info.season:
                    info.season = tv_info.season
                entry = {"file": video_file, "info": info}
                parsed_entries.append(entry)
                logger_instance.info(f"🧠 Parsed {video_file.name}: season={info.season}, episode={info.episode}, tv_season={tv_info.season}")
                if info.season == tv_info.season and info.episode:
                    present_eps.add(info.episode)
                    logger_instance.info(f"🧠 Added to present_eps: {info.episode}")
                elif info.episode is None or info.episode == 0:
                    unlabeled_entries.append(entry)
                    logger_instance.info(f"🧠 Added to unlabeled: {video_file.name}")

            # Quick-win inference: single unlabeled + single missing → assign
            missing_eps: set[int] = set()
            if present_eps:
                try:
                    # For FLCL case: present_eps = {2,3,4,5,6}, missing = {1}
                    max_ep = max(present_eps)
                    missing_eps = set(range(1, max_ep + 1)) - present_eps
                    logger_instance.info(f"🧠 Episode analysis: present={sorted(present_eps)}, missing={sorted(missing_eps)}")
                except Exception:
                    missing_eps = set()

            logger_instance.info(f"🧠 Inference check: {len(unlabeled_entries)} unlabeled entries, {len(missing_eps)} missing episodes")

            # Apply inference based on different scenarios
            if len(unlabeled_entries) == 1 and len(missing_eps) == 1:
                # Perfect case: one unlabeled, one missing
                inferred = next(iter(missing_eps))
                unlabeled_entries[0]["info"].episode = inferred
                logger_instance.info(f"🧠 Inferred missing episode E{inferred:02d} for unlabeled file: {unlabeled_entries[0]['file'].name}")
            elif len(unlabeled_entries) == 1 and missing_eps and 1 in missing_eps:
                # Common case: one unlabeled, multiple missing, but E01 is missing → assume unlabeled is E01
                unlabeled_entries[0]["info"].episode = 1
                logger_instance.info(f"🧠 Inferred E01 for unlabeled file (E01 missing from present episodes): {unlabeled_entries[0]['file'].name}")
            elif len(unlabeled_entries) == 1 and not missing_eps and present_eps:
                # Edge case: unlabeled file but no gaps (could be E01 if we have E02+)
                min_present = min(present_eps)
                if min_present > 1:
                    # We have E02+ but no E01, assume unlabeled is E01
                    unlabeled_entries[0]["info"].episode = 1
                    logger_instance.info(f"🧠 Inferred E01 for unlabeled file (episodes start at E{min_present:02d}): {unlabeled_entries[0]['file'].name}")
            elif unlabeled_entries:
                logger_instance.info(f"🧠 Cannot infer episodes: {len(unlabeled_entries)} unlabeled, {len(missing_eps)} missing")

            # Process all entries (parsed with potential inference applied)
            for entry in parsed_entries:
                try:
                    video_file = entry["file"]
                    file_tv_info = entry["info"]

                    # Build destination
                    file_struct = tv_naming_helper.generate_plex_tv_structure(file_tv_info)
                    file_parent = tv_shows_dir / file_struct['full_series_path']
                    file_ext = video_file.suffix
                    file_name = file_struct['episode_filename'].replace('.mkv', file_ext)
                    file_dest = file_parent / file_name

                    if ensure_dir_exists(file_parent, logger_instance):
                        # CRITICAL FIX: Smart resolution-aware duplicate handling
                        # Only skip if episode exists in SAME or HIGHER resolution
                        should_skip_episode = False
                        if file_tv_info.season and file_tv_info.episode:
                            episode_filename = f"S{file_tv_info.season:02d}E{file_tv_info.episode:02d}.mkv"

                            # Use resolution from path (tv_shows_dir contains resolution) for current
                            current_resolution = tv_shows_dir.name if tv_shows_dir.name in ['4k','1080p','720p','480p'] else '720p'
                            resolution_hierarchy = ['4k', '1080p', '720p', '480p']
                            current_res_index = resolution_hierarchy.index(current_resolution)

                            # Check across all resolution folders
                            for resolution_check in ['720p', '1080p', '4k']:
                                check_path = tv_shows_dir.parent / resolution_check / file_struct['full_series_path'] / episode_filename
                                if check_path.exists():
                                    check_res_index = resolution_hierarchy.index(resolution_check)
                                    if check_res_index <= current_res_index:
                                        should_skip_episode = True
                                        logger_instance.info(f"⏭️ Episode exists in {resolution_check} (same/higher quality), skipping: {episode_filename}")
                                        break
                                    else:
                                        logger_instance.info(f"🔄 Episode exists in {resolution_check} (lower quality), will upgrade: {episode_filename}")

                        if should_skip_episode:
                            continue
                        if safe_move_file(str(video_file), str(file_dest), logger_instance=logger_instance):
                            organized_count += 1
                            if file_tv_info.season and file_tv_info.episode:
                                organized_eps.add((file_tv_info.season, file_tv_info.episode))
                            logger_instance.info(f"   ✅ Organized: {file_name}")
                        else:
                            failed_count += 1
                            logger_instance.error(f"   ❌ Failed to move: {video_file.name}")
                    else:
                        failed_count += 1
                        logger_instance.error(f"   ❌ Could not create directory: {file_parent}")
                except Exception as e:
                    failed_count += 1
                    logger_instance.error(f"   ❌ Error organizing {video_file.name}: {e}")

            organization_success = organized_count > 0 and failed_count == 0

            # Handle leftovers in raw folder
            from _internal.src.fs_helpers import find_video_files
            remaining = find_video_files(download_path)
            if remaining and failed_count:
                quarantine_dir = organized_base_dir / 'issues_hold' / download_path.name
                quarantine_dir.mkdir(parents=True, exist_ok=True)
                for leftover in remaining:
                    try:
                        target_q = quarantine_dir / leftover.name
                        leftover.replace(target_q)
                        logger_instance.warning(f"⚠️ Quarantined leftover: {leftover.name}")
                    except Exception:
                        pass
                logger_instance.warning("Partial success with leftovers quarantined; raw folder cleaned.")
                safe_delete_folder(str(download_dir), ignore_errors=True)
            elif failed_count == 0:
                # Either everything organized or skipped as duplicates; remove raw
                safe_delete_folder(str(download_dir), ignore_errors=True)
            else:
                logger_instance.info("Keeping raw folder (no actionable leftovers).")

            # Emit FileOrganized events and update request state for each organized episode
            try:
                eq = get_event_queue(settings_dict if isinstance(settings_dict, dict) else {'EventQueue': {'enabled': True}})
                series_title = tv_info.series_title
                series_year = tv_info.year
                for s, e in sorted(organized_eps):
                    try:
                        # Determine final path for event payload
                        struct_tmp = tv_naming_helper.generate_plex_tv_structure(type('X', (), {'series_title': series_title, 'year': series_year, 'season': s, 'episode': e}))
                        final_parent = tv_shows_dir / struct_tmp['full_series_path']
                        final_file = None
                        # Attempt to find any file with episode filename (any ext)
                        stem = Path(struct_tmp['episode_filename']).stem
                        for p in final_parent.glob(f"{stem}.*"):
                            final_file = p
                            break
                        await eq.publish('file.organized', {
                            'type': 'episode',
                            'title': series_title,
                            'year': series_year,
                            'resolution': resolution,
                            'season': s,
                            'episode': e,
                            'series_id': content_info.get('seriesId') or content_info.get('series_id'),
                            'path': str(final_file) if final_file else None
                        })
                    except Exception:
                        pass
                    try:
                        request_state.mark_episode_complete(series_title, series_year, s, e, str(final_file) if final_file else '')
                    except Exception:
                        pass
            except Exception:
                pass

            # Create episode-aware markers for organized episodes
            try:
                if organized_eps:
                    from _internal.utils.episode_marker_manager import get_episode_marker_manager
                    series_struct = tv_naming_helper.generate_plex_tv_structure(tv_info)
                    final_parent_dir = tv_shows_dir / series_struct['full_series_path']
                    ep_mgr = get_episode_marker_manager(final_parent_dir, ".organized")
                    for s, e in organized_eps:
                        ok = ep_mgr.add_episode(s, e, logger_instance)
                        if ok:
                            logger_instance.info(f"   🏷️ Marker created for S{s:02d}E{e:02d}")
                        # Mark idempotency index (best-effort)
                        try:
                            from _internal.src.tv_idempotency import mark_episode_organized
                            mark_episode_organized(None, content_info.get('seriesId') or content_info.get('series_id'), s, e, None, final_parent_dir)
                        except Exception:
                            pass
                    # Sonarr Rescan after manual pack organization
                    try:
                        from _internal.src.sonarr_integration import SonarrClient
                        sonarr_url = settings_dict.get('Sonarr', {}).get('url', 'http://localhost:8989') if settings_dict else 'http://localhost:8989'
                        sonarr_api_key = settings_dict.get('Sonarr', {}).get('api_key') if settings_dict else None
                        if sonarr_api_key:
                            client = SonarrClient(sonarr_url, sonarr_api_key)
                            import aiohttp
                            async with aiohttp.ClientSession() as session:
                                # Determine seriesId from metadata if present
                                series_id = content_info.get('seriesId') or content_info.get('series_id')
                                if not series_id and isinstance(content_info.get('series'), dict):
                                    series_id = content_info['series'].get('id')
                                if series_id:
                                    cmd = {"name": "RescanSeries", "seriesId": int(series_id)}
                                    await client.issue_command(session, cmd)
                                    logger_instance.info(f"📡 Sonarr rescan issued for seriesId={series_id}")
                    except Exception as e:
                        logger_instance.warning(f"Sonarr rescan failed: {e}")
            except Exception as me:
                logger_instance.warning(f"Marker creation issue for season pack: {me}")

            return organized_count > 0

        # ---- Single-episode path ----
        struct = tv_naming_helper.generate_plex_tv_structure(tv_info)
        final_parent_dir = tv_shows_dir / struct['full_series_path']
        extension = Path(main_file_path).suffix
        episode_filename = struct['episode_filename'].replace('.mkv', extension)
        final_file_path = final_parent_dir / episode_filename

        # Decide overwrite policy for single-episode
        overwrite_policy = (get_setting("TV", "single_episode_overwrite_policy", default="replace", expected_type=str, settings_dict=settings_dict) or "replace").lower()
        should_replace = True
        if final_file_path.exists():
            if overwrite_policy == "replace":
                should_replace = True
            elif overwrite_policy == "larger":
                try:
                    existing_size = final_file_path.stat().st_size
                    new_size = Path(main_file_path).stat().st_size
                    should_replace = new_size > existing_size
                except Exception:
                    should_replace = False
            elif overwrite_policy == "newer":
                try:
                    existing_mtime = final_file_path.stat().st_mtime
                    new_mtime = Path(main_file_path).stat().st_mtime
                    should_replace = new_mtime > existing_mtime
                except Exception:
                    should_replace = False
            else:
                should_replace = False

            if should_replace:
                try:
                    final_file_path.unlink(missing_ok=True)
                except Exception:
                    pass
            else:
                logger_instance.info(f"⏭️ Single-episode exists and policy '{overwrite_policy}' prevents replacement: {final_file_path.name}")
                # Still ensure marker exists to indicate processed
                try:
                    from _internal.utils.episode_marker_manager import get_episode_marker_manager
                    if tv_info.season and tv_info.episode:
                        episode_mgr = get_episode_marker_manager(final_parent_dir, ".organized")
                        episode_mgr.add_episode(tv_info.season, tv_info.episode, logger_instance)
                        # Mark idempotency best-effort
                        try:
                            from _internal.src.tv_idempotency import mark_episode_organized
                            mark_episode_organized(None, content_info.get('seriesId') or content_info.get('series_id'), tv_info.season, tv_info.episode, None, final_parent_dir)
                        except Exception:
                            pass
                except Exception:
                    (final_parent_dir / ".organized").touch(exist_ok=True)
                return True

        if ensure_dir_exists(final_parent_dir, logger_instance):
            if safe_move_file(main_file_path, str(final_file_path), logger_instance=logger_instance):
                logger_instance.info("Successfully organized TV show file. Cleaning up raw download folder.")
                safe_delete_folder(str(download_dir), ignore_errors=True)

                try:
                    from _internal.utils.episode_marker_manager import get_episode_marker_manager
                    if tv_info.season and tv_info.episode:
                        episode_mgr = get_episode_marker_manager(final_parent_dir, ".organized")
                        ok = episode_mgr.add_episode(tv_info.season, tv_info.episode, logger_instance)
                        if ok:
                            logger_instance.info(f"✅ Episode-aware marker set for S{tv_info.season:02d}E{tv_info.episode:02d}")
                        else:
                            (final_parent_dir / ".organized").touch(exist_ok=True)
                        # Mark idempotency best-effort
                        try:
                            from _internal.src.tv_idempotency import mark_episode_organized
                            mark_episode_organized(None, content_info.get('seriesId') or content_info.get('series_id'), tv_info.season, tv_info.episode, None, final_parent_dir)
                        except Exception:
                            pass
                    else:
                        (final_parent_dir / ".organized").touch(exist_ok=True)
                except Exception:
                    (final_parent_dir / ".organized").touch(exist_ok=True)

                # Emit event for single-episode organization
                try:
                    eq = get_event_queue(settings_dict if isinstance(settings_dict, dict) else {'EventQueue': {'enabled': True}})
                    await eq.publish('file.organized', {
                        'type': 'episode',
                        'title': content_info.get('title') or tv_info.series_title,
                        'year': content_info.get('year') or tv_info.year,
                        'resolution': resolution,
                        'season': tv_info.season,
                        'episode': tv_info.episode,
                        'series_id': content_info.get('seriesId') or content_info.get('series_id'),
                        'path': str(final_file_path)
                    })
                except Exception:
                    pass

                return True
            else:
                logger_instance.error(f"Failed to move TV file: {main_file_path} -> {final_file_path}")
                return False
        else:
            logger_instance.error(f"Failed to create TV directory: {final_parent_dir}")
            return False

    except Exception as e:
        logger_instance.error(f"Error organizing TV show {download_dir.name}: {e}", exc_info=True)
        return False

