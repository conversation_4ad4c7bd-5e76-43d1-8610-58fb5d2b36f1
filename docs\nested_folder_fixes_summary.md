# PlexAutomator Nested Folder & File Format Fixes

## Overview
Fixed several file organization and format support issues in PlexAutomator pipeline to handle:
1. **Nested folder organization failures** - Files in identically named subfolders (e.g., "Movie Name/Movie Name/file.mkv")
2. **Missing .ts format support** - Transport Stream files not recognized as video
3. **Failed extraction handling** - Unextracted .rar files left in download directories
4. **Improved file discovery** - Recursive search for video files in nested structures

## Issues Resolved

### 1. Missing .ts Format Support
**Problem**: .ts (Transport Stream) files were not recognized as valid video formats
**Files Modified**: `_internal/src/constants.py`
**Solution**: Added `.ts` to `VIDEO_EXTENSIONS` list
```python
VIDEO_EXTENSIONS = [
    ".mkv",
    ".mp4", 
    ".m4v",
    ".avi",
    ".mov",
    ".ts",  # Transport Stream files - ADDED
]
```

### 2. Nested Folder Detection Failure  
**Problem**: Movie discovery only looked for files in root directory with `*.mkv` glob patterns, missing nested structures like "There Will Be Blood/There Will Be Blood/movie.mkv"

**Files Modified**: 
- `_internal/utils/filesystem_first_state_manager.py`
- `_internal/utils/mkv_processor_filesystem_first.py`

**Solution**: Replaced non-recursive file discovery with recursive `find_video_files()` function

#### In filesystem_first_state_manager.py:
```python
# OLD (non-recursive):
movie_files = list(movie_dir.glob("*.mkv"))
if movie_files:
    movie_info['main_movie_file'] = str(movie_files[0])

# NEW (recursive):
from _internal.src.fs_helpers import find_video_files
video_files = find_video_files(movie_dir)
if video_files:
    # Choose the largest video file as the main movie file
    main_file = max(video_files, key=lambda f: f.stat().st_size if f.exists() else 0)
    movie_info['main_movie_file'] = str(main_file)
```

#### In mkv_processor_filesystem_first.py:
```python  
# OLD (non-recursive):
mkv_files = list(movie_dir.glob("*.mkv"))

# NEW (recursive):
from _internal.src.fs_helpers import find_video_files
video_files = find_video_files(movie_dir)
```

### 3. Failed Extraction Detection & Cleanup
**Problem**: No mechanism to detect and handle failed extractions that leave RAR archive files without corresponding video files

**Files Modified**: `02_download_and_organize.py`

**Solution**: Added `handle_failed_extractions()` function with support for multi-part RAR archives and integrated it into the main monitoring workflow

#### Enhanced Multi-Part RAR Detection:
```python
async def handle_failed_extractions(download_dir: Path, logger):
    """
    Detect failed extractions and move them to recycling bin.
    Failed extractions are identified by:
    1. Presence of RAR archive parts (.rar, .r00, .r01, etc.) without corresponding video files
    2. Empty directories after presumed extraction
    """
    # Check for RAR archive files (including multi-part: .rar, .r00, .r01, .r02, etc.)
    rar_patterns = ["*.rar", "*.r[0-9][0-9]"]
    rar_files = []
    for pattern in rar_patterns:
        rar_files.extend(list(item.glob(pattern)))
    
    # Categorize for better logging
    main_rar = [f for f in rar_files if f.suffix.lower() == '.rar']
    part_rars = [f for f in rar_files if f.suffix.lower().startswith('.r') and f.suffix.lower() != '.rar']
    
    if main_rar and part_rars:
        logger.warning(f"   Found multi-part RAR: {len(main_rar)} .rar + {len(part_rars)} parts (.r00, .r01, etc.)")
```

#### Integration in Main Monitor:
```python
# Phase 3: Check for and clean up failed extractions
logger_instance.info("🧹 Phase 3: Checking for failed extractions...")
try:
    # ... get download directory ...
    failed_extractions = await handle_failed_extractions(download_raw_dir, logger_instance)
    if failed_extractions:
        logger_instance.info(f"✅ Cleaned up {len(failed_extractions)} failed extractions")
```

**Supports**: `.rar`, `.r00`, `.r01`, `.r02`, `.r03`, etc. (full multi-part RAR sequence detection)

## Testing
Created comprehensive test suite (`test_nested_folder_fixes.py`) that verifies:

### Test Results:
```
=== Testing PlexAutomator Nested Folder & Format Fixes ===
Testing .ts format support...
✅ .ts format is now supported in VIDEO_EXTENSIONS
✅ find_video_files() correctly detects .ts files

Testing nested folder detection...
✅ find_video_files() correctly finds files in nested folders
✅ State manager correctly analyzes nested folder structure
   Found main file: test_nested_dir\There Will Be Blood\There Will Be Blood\There.Will.Be.Blood.2007.1080p.mkv

Testing extraction failure detection...
✅ Test setup correct: Multi-part RAR files present, no video files
   Found 4 RAR files (.rar + .r00/.r01/.r02) and 0 video files
✅ Multi-part RAR pattern detection working correctly
   Detected: 1 main .rar + 3 parts (.r00, .r01, .r02)

=== Test Results ===
Tests passed: 3/3
🎉 All tests passed! Fixes are working correctly.
```

## Impact
- **Nested Folders**: Movies in nested identical folder structures now properly detected and organized
- **Format Support**: .ts files now recognized and processed through the pipeline
- **Multi-Part RAR Cleanup**: Failed extractions with .rar, .r00, .r01, .r02, etc. files automatically moved to recycling bin, preventing pipeline blockage
- **Robustness**: Recursive file discovery handles complex download folder structures and multi-part archive formats
- **Better Logging**: Detailed categorization of extraction failures (single .rar vs multi-part archives)

## Files Modified
1. `_internal/src/constants.py` - Added .ts format support
2. `_internal/utils/filesystem_first_state_manager.py` - Recursive movie file discovery
3. `_internal/utils/mkv_processor_filesystem_first.py` - Recursive main file discovery  
4. `02_download_and_organize.py` - Failed extraction detection and cleanup
5. `test_nested_folder_fixes.py` - Comprehensive test suite (NEW)

All changes maintain backward compatibility and improve pipeline reliability.
