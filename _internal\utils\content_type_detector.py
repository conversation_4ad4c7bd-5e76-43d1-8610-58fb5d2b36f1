#!/usr/bin/env python3
"""
PlexMovieAutomator/_internal/utils/content_type_detector.py

Content Type Detection Utility for Movies vs TV Shows

This utility provides functions to detect whether downloaded content is a movie or TV show
based on filename patterns, folder structure, and metadata.
"""

import re
import logging
from pathlib import Path
from typing import Dict, Optional, Tuple, List
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class ContentInfo:
    """Information about detected content."""
    content_type: str  # "movie" or "tv_show"
    title: str
    year: Optional[int] = None
    season: Optional[int] = None
    episode: Optional[int] = None
    episode_title: Optional[str] = None
    resolution: Optional[str] = None
    confidence: float = 0.0  # 0.0 to 1.0
    detection_reason: str = ""

class ContentTypeDetector:
    """Detects whether content is a movie or TV show."""
    
    def __init__(self):
        # TV show indicators (higher confidence)
        self.tv_show_patterns = [
            # Season/Episode patterns
            r'S\d{2}E\d{2}',  # S01E01
            r'S\d{1,2}E\d{1,2}',  # S1E1 or S01E1
            
            # Season pack patterns (NO episode numbers)
            r'S\d{1,2}(?!E)\b',  # S01, S1 not followed by E (season packs)
            r'S\d{1,2}\.(?!E)',  # S01. not followed by E (season packs with period)
            r'S\d{1,2}\s+(?!E)',  # S01 followed by space, not E
            
            # Traditional season/episode patterns
            r'Season\s*\d+',  # Season 1, Season 01
            r'Episode\s*\d+',  # Episode 1
            r'\d{1,2}x\d{1,2}',  # 1x01, 01x01
            
            # TV show structure indicators
            r'Complete[\s\.\-_]*Series',  # Complete Series, Complete.Series, Complete_Series
            r'Complete[\s\.\-_]*Season',  # Complete Season, Complete.Season, Complete_Season  
            r'Season\s*\d+[\s\.\-_]*Complete',  # Season 1 Complete
            r'Full[\s\.\-_]*Series',  # Full Series, Full.Series
            r'Entire[\s\.\-_]*Series',  # Entire Series, Entire.Series
            
            # Episode numbering
            r'E\d{2,3}',  # E01, E001
            r'Ep\d{1,3}',  # Ep1, Ep01
            r'Part\s*\d+',  # Part 1
        ]
        
        # Movie indicators (lower confidence, used when TV patterns don't match)
        self.movie_patterns = [
            r'\b\d{4}\b',  # Year (1999, 2023)
            r'BluRay|BDRip|DVD|HDTV|WEB-DL|WEBRip',  # Release types
            r'PROPER|REPACK|EXTENDED|DIRECTORS?.CUT',  # Movie editions
        ]
        
        # Known TV show title patterns
        self.tv_show_titles = [
            'Breaking Bad', 'Game of Thrones', 'The Office', 'Friends',
            'The Walking Dead', 'Stranger Things', 'House of Cards',
            'Orange Is the New Black', 'Better Call Saul', 'Westworld',
            'The Crown', 'Naruto', 'One Piece', 'Attack on Titan',
            'Rick and Morty', 'South Park', 'The Simpsons', 'Family Guy',
            'Modern Family', 'How I Met Your Mother', 'Big Bang Theory'
        ]
        
        # Resolution patterns
        self.resolution_patterns = {
            '4k': [r'2160p', r'4K', r'UHD'],
            '1080p': [r'1080p', r'FHD'],
            '720p': [r'720p', r'HD']
        }
    
    def detect_content_type(self, filename_or_path: str) -> ContentInfo:
        """
        Detect whether content is a movie or TV show.
        
        Args:
            filename_or_path: Filename or path to analyze
            
        Returns:
            ContentInfo object with detection results
        """
        path = Path(filename_or_path)
        filename = path.name if path.is_file() else str(path)
        
        logger.debug(f"Analyzing content type for: {filename}")
        
        # Initialize result
        result = ContentInfo(
            content_type="movie",  # Default to movie
            title="Unknown",
            confidence=0.0
        )
        
        # Clean filename for analysis
        clean_name = self._clean_filename(filename)
        
        # Check for TV show patterns
        tv_confidence = self._check_tv_patterns(clean_name)
        
        # Check for movie patterns
        movie_confidence = self._check_movie_patterns(clean_name)
        
        # Extract basic info
        title, year = self._extract_title_and_year(clean_name)
        result.title = title
        result.year = year
        
        # Determine content type
        if tv_confidence > movie_confidence and tv_confidence > 0.5:
            result.content_type = "tv_show"
            result.confidence = tv_confidence
            result.detection_reason = "TV show patterns detected"
            
            # Extract TV-specific info
            season, episode = self._extract_season_episode(clean_name)
            result.season = season
            result.episode = episode
            
        else:
            result.content_type = "movie"
            result.confidence = max(movie_confidence, 0.3)  # Minimum confidence for movies
            result.detection_reason = "Movie patterns detected or no TV patterns found"
        
        # Detect resolution
        result.resolution = self._detect_resolution(clean_name)
        
        logger.info(f"Content detection: {result.title} -> {result.content_type} "
                   f"(confidence: {result.confidence:.2f})")
        
        return result
    
    def _clean_filename(self, filename: str) -> str:
        """Clean filename for analysis."""
        # Remove file extension
        name = Path(filename).stem
        
        # Replace common separators with spaces
        name = re.sub(r'[._\-\[\]()]', ' ', name)
        
        # Clean up multiple spaces
        name = re.sub(r'\s+', ' ', name).strip()
        
        return name
    
    def _check_tv_patterns(self, clean_name: str) -> float:
        """Check for TV show patterns and return confidence score."""
        confidence = 0.0
        matches = 0
        
        # High-confidence TV patterns (very strong indicators)
        high_confidence_patterns = [
            r'S\d{1,2}E\d{1,2}',  # S01E01 format
            r'Complete[\s\.\-_]*Series',  # Complete Series
            r'Complete[\s\.\-_]*Season',  # Complete Season
            r'Full[\s\.\-_]*Series',  # Full Series
            r'Entire[\s\.\-_]*Series',  # Entire Series
        ]
        
        # Medium-confidence TV patterns
        medium_confidence_patterns = [
            r'S\d{1,2}(?!E)\b',  # Season packs
            r'Season\s*\d+',  # Season 1
            r'Episode\s*\d+',  # Episode 1
            r'\d{1,2}x\d{1,2}',  # 1x01 format
        ]
        
        # Low-confidence TV patterns
        low_confidence_patterns = [
            r'E\d{2,3}',  # E01, E001
            r'Ep\d{1,3}',  # Ep1, Ep01
            r'Part\s*\d+',  # Part 1
        ]
        
        # Check high-confidence patterns first
        for pattern in high_confidence_patterns:
            if re.search(pattern, clean_name, re.IGNORECASE):
                matches += 1
                confidence += 0.8  # Very high confidence
                logger.debug(f"High-confidence TV pattern match: {pattern}")
        
        # Check medium-confidence patterns
        for pattern in medium_confidence_patterns:
            if re.search(pattern, clean_name, re.IGNORECASE):
                matches += 1
                confidence += 0.5  # Medium confidence
                logger.debug(f"Medium-confidence TV pattern match: {pattern}")
        
        # Check low-confidence patterns
        for pattern in low_confidence_patterns:
            if re.search(pattern, clean_name, re.IGNORECASE):
                matches += 1
                confidence += 0.3  # Lower confidence
                logger.debug(f"Low-confidence TV pattern match: {pattern}")
        
        # Check against known TV show titles
        for tv_title in self.tv_show_titles:
            if tv_title.lower() in clean_name.lower():
                confidence += 0.4
                matches += 1
                logger.debug(f"Known TV show title match: {tv_title}")
        
        # Bonus for multiple matches
        if matches > 1:
            confidence += 0.2
        
        return min(confidence, 1.0)
    
    def _check_movie_patterns(self, clean_name: str) -> float:
        """Check for movie patterns and return confidence score."""
        confidence = 0.0
        
        for pattern in self.movie_patterns:
            if re.search(pattern, clean_name, re.IGNORECASE):
                confidence += 0.2
                logger.debug(f"Movie pattern match: {pattern}")
        
        # Year pattern gets higher confidence for movies
        if re.search(r'\b(19|20)\d{2}\b', clean_name):
            confidence += 0.3
        
        return min(confidence, 1.0)
    
    def _extract_title_and_year(self, clean_name: str) -> Tuple[str, Optional[int]]:
        """Extract title and year from filename."""
        # Look for year pattern
        year_match = re.search(r'\b(19|20)(\d{2})\b', clean_name)
        year = None
        title = clean_name
        
        if year_match:
            year = int(year_match.group(0))
            # Remove year and everything after it for title extraction
            title = clean_name[:year_match.start()].strip()
        
        # Clean up title
        title = re.sub(r'\s+', ' ', title).strip()
        
        # Remove common release info from title
        release_terms = ['BluRay', 'BDRip', 'DVDRip', 'WEBRip', 'HDTV', 'x264', 'x265', 'HEVC']
        for term in release_terms:
            title = re.sub(f'\\b{term}\\b', '', title, flags=re.IGNORECASE)
        
        title = re.sub(r'\s+', ' ', title).strip()
        
        if not title:
            title = "Unknown"
        
        return title, year
    
    def _extract_season_episode(self, clean_name: str) -> Tuple[Optional[int], Optional[int]]:
        """Extract season and episode numbers."""
        season = None
        episode = None
        
        # Try S##E## pattern first
        se_match = re.search(r'S(\d+)E(\d+)', clean_name, re.IGNORECASE)
        if se_match:
            season = int(se_match.group(1))
            episode = int(se_match.group(2))
            return season, episode
        
        # Try ##x## pattern
        x_match = re.search(r'(\d+)x(\d+)', clean_name)
        if x_match:
            season = int(x_match.group(1))
            episode = int(x_match.group(2))
            return season, episode
        
        # Try Season ## pattern
        season_match = re.search(r'Season\s*(\d+)', clean_name, re.IGNORECASE)
        if season_match:
            season = int(season_match.group(1))
        
        # Try Episode ## pattern
        episode_match = re.search(r'Episode\s*(\d+)', clean_name, re.IGNORECASE)
        if episode_match:
            episode = int(episode_match.group(1))
        
        return season, episode
    
    def _detect_resolution(self, clean_name: str) -> Optional[str]:
        """Detect video resolution from filename."""
        for resolution, patterns in self.resolution_patterns.items():
            for pattern in patterns:
                if re.search(pattern, clean_name, re.IGNORECASE):
                    return resolution
        
        return None
    
    def analyze_batch(self, file_paths: List[str]) -> List[ContentInfo]:
        """Analyze multiple files and return results."""
        results = []
        for file_path in file_paths:
            result = self.detect_content_type(file_path)
            results.append(result)
        return results
    
    def get_content_statistics(self, results: List[ContentInfo]) -> Dict[str, int]:
        """Get statistics from batch analysis results."""
        stats = {
            'total': len(results),
            'movies': sum(1 for r in results if r.content_type == 'movie'),
            'tv_shows': sum(1 for r in results if r.content_type == 'tv_show'),
            'high_confidence': sum(1 for r in results if r.confidence > 0.7),
            'low_confidence': sum(1 for r in results if r.confidence < 0.3)
        }
        return stats

def test_content_detection():
    """Test function for content type detection."""
    detector = ContentTypeDetector()
    
    test_cases = [
        # TV Shows
        "Breaking.Bad.S01E01.1080p.BluRay.x264",
        "Game.of.Thrones.S08E06.FINAL.720p.HDTV.x264",
        "The.Office.US.S02E15.Boys.and.Girls.1080p",
        "Friends.Season.10.Complete.720p.BluRay",
        "Naruto.Shippuden.Episode.500.1080p",
        
        # Movies
        "The.Matrix.1999.1080p.BluRay.x264",
        "Avengers.Endgame.2019.4K.UHD.BluRay",
        "Inception.2010.Directors.Cut.1080p.BDRip",
        "Pulp.Fiction.1994.REMASTERED.720p.BluRay",
        
        # Edge cases
        "Monster.2004.720p.BluRay.x264",  # Could be movie or TV show
        "24.Season.1.Complete.DVD.Rip",  # TV show with number as title
    ]
    
    for test_case in test_cases:
        result = detector.detect_content_type(test_case)
        print(f"{test_case}")
        print(f"  -> {result.content_type}: {result.title} ({result.year})")
        print(f"     Season: {result.season}, Episode: {result.episode}")
        print(f"     Confidence: {result.confidence:.2f} - {result.detection_reason}")
        print()

if __name__ == "__main__":
    # Run tests
    test_content_detection()
