import unittest
from types import SimpleNamespace

from _internal.src.quality_strategy import (
    determine_movie_quality_profile_by_year,
    determine_tv_quality_profile_by_year,
)


class DummyLogger:
    def __getattr__(self, name):
        def _log(*args, **kwargs):
            pass
        return _log


class TestQualityStrategy(unittest.TestCase):
    def test_movie_quality_profiles(self):
        logger = DummyLogger()
        settings = {"Radarr": {"hd_quality_profile_id": 10, "uhd_quality_profile_id": 20}}
        self.assertEqual(determine_movie_quality_profile_by_year(2000, logger, settings)["profiles"], [10])
        self.assertEqual(determine_movie_quality_profile_by_year(2012, logger, settings)["profiles"], [10, 20])
        self.assertEqual(determine_movie_quality_profile_by_year(2020, logger, settings)["profiles"], [20])

    def test_tv_quality_profiles_legacy(self):
        logger = DummyLogger()
        settings = {"Sonarr": {"tv_hd_quality_profile_id": 4, "tv_uhd_quality_profile_id": 5, "adaptive_quality_enabled": False}}
        self.assertEqual(determine_tv_quality_profile_by_year(2000, logger, settings)["profiles"], [4])
        self.assertEqual(determine_tv_quality_profile_by_year(2012, logger, settings)["profiles"], [4, 5])
        self.assertEqual(determine_tv_quality_profile_by_year(2020, logger, settings)["profiles"], [5])

    def test_tv_quality_profiles_adaptive(self):
        logger = DummyLogger()
        settings = {"Sonarr": {"adaptive_quality_enabled": True, "tv_inclusive_quality_profile_id": 9}}
        self.assertEqual(determine_tv_quality_profile_by_year(1990, logger, settings)["profiles"], [9])


if __name__ == "__main__":
    unittest.main()

