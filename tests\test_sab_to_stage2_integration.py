import sys
from pathlib import Path
import asyncio
from unittest.mock import patch

# Ensure imports work
REPO_ROOT = Path(__file__).resolve().parents[1]
sys.path.insert(0, str(REPO_ROOT))

from _internal.src.event_queue import get_event_queue


class DummyLogger:
    def info(self, *a, **k): pass
    def warning(self, *a, **k): pass
    def error(self, *a, **k): pass


def test_sab_to_stage2_to_organizer_flow(tmp_path):
    # Arrange: set EventQueue dir to temp
    eq_dir = tmp_path / 'events'
    eq = get_event_queue({'EventQueue': {'enabled': True, 'dir': str(eq_dir)}})

    # Simulate SAB post-process event
    async def _emit():
        await eq.publish('sab.postprocess', {
            'final_folder': str(tmp_path / 'Movie.Name.2020.1080p.WEB-DL'),
            'original_nzb_name': 'Movie.Name.2020.1080p.WEB-DL',
            'clean_job_name': 'Movie.Name.2020.1080p.WEB-DL',
            'status': '0'
        })
    asyncio.run(_emit())

    # Create the expected final folder with a main file
    dl = tmp_path / 'Movie.Name.2020.1080p.WEB-DL'
    dl.mkdir(parents=True, exist_ok=True)
    main = dl / 'Movie.Name.2020.1080p.WEB-DL.mkv'
    main.write_bytes(b'0' * 1024 * 1024)

    # Force content type hint to avoid quarantine gate in Stage 2 for this test
    content_info = {'title': 'Movie Name', 'year': 2020, 'content_type': 'movie'}

    # Patch EventQueue in organizer to write into our temp dir; Radarr refresh to no-op
    captured = []
    class DummyQueue:
        async def publish(self, event_type, data):
            captured.append((event_type, data))

    with patch('_internal.src.file_organizer.get_event_queue', return_value=DummyQueue()):
        with patch('_internal.src.radarr_integration.RadarrClient.issue_command'):
            # Act: call Stage 2 organizer entrypoint for completed content via wrapper
            from _internal.src.stage2_api import organize_completed_content
            ok = asyncio.run(organize_completed_content(content_info, str(main), dl, {'EventQueue': {'enabled': True, 'dir': str(eq_dir)}}, DummyLogger()))
            assert ok is True

    # Assert: there is at least one file.organized event published
    assert any(evt == 'file.organized' for evt, _ in captured)

