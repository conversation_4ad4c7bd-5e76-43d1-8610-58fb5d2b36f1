onsolidating Mixed-Resolution TV Episodes in Folder 5
1. Unified TV Show Folder Structure (No Resolution Subfolders)

To fix the mixed-resolution issue, all TV episodes should be merged under a single series folder in Folder 5, without any resolution-based subdirectories. The target structure in 5_awaiting_poster will be:

5_awaiting_poster/tv_shows/The Office (2005)/Season 01/S01E01.mkv  


This contrasts with earlier behavior where episodes might be segregated by resolution (e.g. under .../tv_shows/4k/SeriesName/…). In the current move_episode_to_folder_5 implementation, episodes are already placed directly under tv_shows/<Series>/<Season>/<Episode> (no resolution folder). We will ensure this unified structure is consistently used going forward and that any remnants of resolution folders in Folder 5 are eliminated or merged.

2. Detecting Existing Series and Preparing Consolidation

Before moving a new episode into Folder 5, the script should detect if that series already has episodes in Folder 5 (in the unified structure). We introduce a helper function detect_existing_series_in_folder5(series_name) that checks for an existing series directory in 5_awaiting_poster/tv_shows:

RESOLUTION_PRIORITY = {
    "4k": 4, "2160p": 4,
    "1080p": 3,
    "720p": 2,
    "480p": 1,
    "sd_or_unknown": 0
}

def detect_existing_series_in_folder5(series_name: str) -> bool:
    base = Path("workspace") / "5_awaiting_poster" / "tv_shows"
    series_path = base / series_name
    return series_path.exists()


What it does: This returns True if the series folder already exists in Folder 5 (meaning one or more episodes of that series have been moved previously), or False if this is the first episode for that series. (If the project previously placed episodes under resolution subfolders in Folder 5, this function should also check those and initiate a merge, but after our changes all new moves use the unified path.)

When a series already exists, we will consolidate the new episode into the same folder instead of creating any duplicate structure. This might include creating the season subfolder if it’s not there yet (e.g. moving Season 02 when only Season 01 exists so far). We log a debug/info message indicating that the series was found and we’ll merge the episode into it.

Note: If older episodes are still organized under a resolution subfolder (from a previous run), a one-time migration can be done. For example, if tv_shows/4k/SeriesName exists, the script can move its contents to tv_shows/SeriesName. This ensures no lingering resolution folders. Future episodes will be placed in the unified structure.

3. Resolution Priority for Duplicate Episodes

To avoid duplicates, we implement resolution-based priority when the same episode exists in multiple qualities. We define a RESOLUTION_PRIORITY map (as above) where higher values mean better quality. For example, 4K/2160p = 4, 1080p = 3, 720p = 2, etc.

Before moving a new episode file, we must identify its resolution and check if that episode already exists in Folder 5. The new episode’s resolution can be inferred from its folder path or metadata. For example, if the episode was in a 1080p folder in stage 4, we treat it as 1080p. We can use similar logic to the movie handler:

def get_resolution_from_path(path: Path) -> str:
    """Infer resolution tag from a path (folder names or file name)."""
    path_str = str(path).lower()
    if "2160p" in path_str or "4k" in path_str:
        return "4k"
    elif "1080p" in path_str:
        return "1080p"
    elif "720p" in path_str:
        return "720p"
    elif "480p" in path_str:
        return "480p"
    else:
        return "sd_or_unknown"


When a duplicate episode is detected (same series, season, and episode already present in Folder 5), we compare resolutions:

If the new episode’s resolution is higher priority than the existing one, we replace the existing file with the new one.

If the new episode is lower or equal quality, we skip moving it to avoid overwriting a better version.

This logic will be handled in a new function resolve_duplicate_episode():

def resolve_duplicate_episode(series_path: Path, season_name: str, episode_name: str, 
                              new_video_path: Path, new_resolution: str) -> str:
    """Resolve a duplicate episode by quality. Returns 'replace', 'skip', or 'keep'. """
    episode_folder = series_path / season_name / episode_name
    existing_files = list(episode_folder.glob("*.mkv"))
    if not existing_files:
        return "keep"  # No existing video, safe to keep new
    existing_video = existing_files[0]
    # Determine existing resolution (if not stored, analyze the video file)
    existing_resolution = "sd_or_unknown"
    try:
        info = get_mkv_info(existing_video)  # gather video info e.g. width/height
        if info:
            width = info.get("width", 0)
            # Simplified: categorize by width
            if width >= 3800:
                existing_resolution = "4k"
            elif width >= 1900:
                existing_resolution = "1080p"
            elif width >= 1250:
                existing_resolution = "720p"
            elif width >= 700:
                existing_resolution = "480p"
            else:
                existing_resolution = "sd_or_unknown"
    except Exception as e:
        logger.warning(f"Could not determine resolution of existing episode: {e}")
    # Compare priority
    if RESOLUTION_PRIORITY.get(new_resolution, 0) > RESOLUTION_PRIORITY.get(existing_resolution, 0):
        logger.info(f"🔄 Replacing existing episode with higher quality: "
                    f"{episode_name} ({existing_resolution} → {new_resolution})")
        # Remove or archive the old video and its marker
        try:
            existing_video.unlink()  # delete old MKV
        except Exception as e:
            logger.error(f"Failed to remove old episode file: {e}")
        old_marker = episode_folder / ".muxed"
        if old_marker.exists():
            old_marker.unlink(missing_ok=True)
        return "replace"
    else:
        logger.info(f"⚠️ Skipping new episode {episode_name} ({new_resolution}); "
                    f"higher-quality version already exists.")
        return "skip"


In this snippet, we attempt to use get_mkv_info() (from utils.mkv_utils) to analyze the existing file’s resolution. If that fails or is unavailable, we default to comparing using sd_or_unknown which yields the lowest priority. The comparison uses our RESOLUTION_PRIORITY map to decide the outcome. We log an informative message in each case: either replacing the old lower-res file or skipping the new one.

Note: We ensure that if we replace an episode, we also remove its old .muxed marker (and any other leftover files in that episode’s folder) so that the folder can be cleanly updated. If we skip moving the new episode, we will later clean up the new files in the staging area to avoid clutter, as explained below.

4. Merging Episodes into the Unified Series Folder

With these utilities in place, we update the main move function (TV episode version) to consolidate episodes into the existing series folder and handle duplicates:

def consolidate_episode_into_series(episode_dir: Path):
    series_name = episode_dir.parent.parent.name
    season_name = episode_dir.parent.name
    episode_name = episode_dir.name
    folder5_base = Path("workspace") / "5_awaiting_poster" / "tv_shows"
    series_dest = folder5_base / series_name
    season_dest = series_dest / season_name
    episode_dest = season_dest / episode_name

    ensure_dir_exists(episode_dest)  # create series/season/episode folders as needed

    new_resolution = get_resolution_from_path(episode_dir)
    if episode_dest.exists():
        # Episode folder already exists in final – potential duplicate episode
        action = resolve_duplicate_episode(series_dest, season_name, episode_name, 
                                           episode_dir, new_resolution)
        if action == "skip":
            # Do not move new files; cleanup staging and exit
            shutil.rmtree(episode_dir, ignore_errors=True)
            return False
        elif action == "replace":
            # Old episode folder is now cleared of the outdated file/marker
            pass  # continue to move new file into episode_dest
    else:
        if detect_existing_series_in_folder5(series_name):
            logger.info(f"➕ Merging new episode into existing series folder: {series_name}")
        else:
            logger.info(f"🗂️ Creating new series folder in 5_awaiting_poster: {series_name}")
    return True  # indicates we should proceed with moving the new episode


In the updated move_episode_to_folder_5, we will call consolidate_episode_into_series() before file moves:

def move_episode_to_folder_5(episode_dir: Path):
    try:
        # Determine series/season/episode names
        series_name = episode_dir.parent.parent.name
        season_name = episode_dir.parent.name
        episode_name = episode_dir.name

        # Consolidate into series folder (handles duplicates and structure)
        result = consolidate_episode_into_series(episode_dir)
        if not result:
            logger.info(f"✅ Episode {episode_name} skipped due to duplicate higher-quality version.")
            return  # Skip moving this episode

        folder5_base = Path("workspace") / "5_awaiting_poster"
        folder5_dest = folder5_base / "tv_shows" / series_name / season_name / episode_name
        folder5_dest.mkdir(parents=True, exist_ok=True)  # ensure path exists

        logger.info(f"📁 Moving episode to folder 5: {series_name} - {season_name} - {episode_name}")
        # Move the final MKV
        mkv_files = list(episode_dir.glob("*.mkv"))
        if mkv_files:
            final_mkv = mkv_files[0]
            dest_mkv = folder5_dest / final_mkv.name
            shutil.move(str(final_mkv), str(dest_mkv))
            logger.info(f"  📁 Moved episode MKV: {final_mkv.name} → {dest_mkv}")
        # Move the .muxed marker
        muxed_marker = episode_dir / ".muxed"
        if muxed_marker.exists():
            dest_marker = folder5_dest / ".muxed"
            shutil.move(str(muxed_marker), str(dest_marker))
            logger.info(f"  📁 Moved episode marker: .muxed → {folder5_dest}")
        # Remove empty staging folder
        try:
            episode_dir.rmdir()
            logger.debug(f"🗑️ Removed empty folder4 episode directory: {episode_name}")
        except OSError:
            remaining = [f.name for f in episode_dir.iterdir()]
            logger.warning(f"⚠️ Staging folder not empty (leftover files): {remaining}")
        logger.info(f"✅ Episode moved to folder 5: {series_name}/Season {season_name[-2:]} — {episode_name}")
    except Exception as e:
        logger.error(f"❌ Failed to move episode to folder 5: {e}")
        raise


How this works: Before any file operations, we call consolidate_episode_into_series() to handle structure creation and duplicate resolution. If resolve_duplicate_episode returns "skip", it means a better-quality episode already exists; we log this and abort moving the new file (and we remove the new episode’s staging folder to keep things tidy). If it returns "replace" or "keep", we proceed to move the new MKV and marker into the unified series folder as usual. The code above ensures the destination directories exist (creating the series/season folders if needed) and then moves the .mkv and .muxed files into place. Finally, it cleans up the now-empty folder4 episode directory.

All marker files and metadata are preserved in the new structure. We specifically move the .muxed status file into the episode’s Folder 5 directory along with the video file, maintaining the pipeline’s marker-based state. If an older version of the episode was replaced, its .muxed file was removed to avoid confusion, and the new .muxed now signals the episode is ready for the poster stage in the unified location. Season and series folders remain intact, containing all episodes (regardless of source resolution) under one hierarchy.

5. Robust Logging and Error Handling

We add extensive logging to make the consolidation process transparent:

When a series folder already exists, we log that the episode will be merged into the existing series in Folder 5.

On detecting a duplicate episode in Folder 5, we log a message like:

“🔄 Replacing existing episode with higher quality: S01E01 (1080p → 4k)” if the new file is better, or

“⚠️ Skipping new episode S01E01 (720p); higher-quality version already exists.” if we skip a lower-quality file.

All file operations (moves, deletions) are wrapped in try/except blocks. If a file system operation fails, we log an error with details (logger.error(...)) without crashing the entire script. For example, if deleting an old file fails, we catch it and log a failure, but still attempt to move the new file to avoid data loss.

If any critical step raises an unexpected exception, it is caught at the end of move_episode_to_folder_5 and logged as a failure (❌ Failed to move episode to folder 5: ...), ensuring the pipeline is aware something went wrong. The operation is designed to be idempotent – if it fails midway, the presence of marker files (or lack thereof) will prevent re-processing already-moved episodes on the next run, and any skipped duplicate is already marked as muxed so it won’t be retried unnecessarily.

By implementing these changes, Script 6 will merge all episodes of a series into one resolution-agnostic folder in 5_awaiting_poster, maintaining only the highest-quality version of each episode. This satisfies all the goals: unified folder structure (no resolution subfolders), no duplicate episodes, correct resolution priority handling, preservation of marker files, and clear logging for each action.