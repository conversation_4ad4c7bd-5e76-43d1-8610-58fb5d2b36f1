#!/usr/bin/env python3
"""
Plex Movie Automator - Pipeline Status Dashboard

This script provides an instant overview of your entire movie and TV show processing pipeline,
showing counts and titles at each stage, issues that need attention, and 
overall pipeline health.

Usage:
    python pipeline_status_dashboard.py [--detailed] [--format=terminal|html|json]
"""

import os
import sys
import json
import argparse
from pathlib import Path
from collections import defaultdict
from datetime import datetime
import glob

class PipelineStatusDashboard:
    def __init__(self, workspace_root):
        self.workspace_root = Path(workspace_root)
        self.pipeline_stages = {
            "0_new_movie_requests": "📥 New Movie & TV Requests",
            "1_downloading": "⬇️ Downloading",
            "2_downloaded_and_organized": "📁 Downloaded & Organized", 
            "3_mkv_cleaned_subtitles_extracted": "🎬 MKV Processed",
            "4_ready_for_final_mux": "🔄 Ready for Final Mux",
            "5_awaiting_poster": "🖼️ Awaiting Poster",
            "5_ready_for_quality_check": "✅ Ready for Quality Check",
            "6_awaiting_poster": "🖼️ Awaiting Poster (Stage 6)",
            "6_final_plex_ready": "🎯 Final Plex Ready",
            "7_final_plex_ready": "✨ Final Plex Ready (Stage 7)"
        }
        
        self.issue_categories = {
            "audio_issues": "🔊 Audio Issues",
            "file_corruption": "⚠️ File Corruption", 
            "manual_review_needed": "👁️ Manual Review Needed",
            "metadata_issues": "📝 Metadata Issues",
            "other_issues": "❓ Other Issues",
            "poster_quality_issues": "🖼️ Poster Quality Issues",
            "subtitle_issues": "📄 Subtitle Issues", 
            "video_quality_issues": "📹 Video Quality Issues"
        }
        
        self.quality_tiers = ["720p", "1080p", "4k"]
        
        # Special subdirectories that should be scanned for content (not counted as movies themselves)
        self.special_subdirs = {
            "0_new_movie_requests": ["nzb_files_for_download", "nzb_files_launched_archive"],
            "1_downloading": ["complete_raw", "incomplete"]
        }
        
        self.content_types = ["movies", "tv_shows"]
        
    def scan_directory_for_movies(self, directory_path, stage_folder=None):
        """Scan a directory and return list of movie and TV show folders"""
        movies = []
        if not directory_path.exists():
            return movies
        
        # Check if this is a special stage with subdirectories to scan
        if stage_folder and stage_folder in self.special_subdirs:
            special_dirs = self.special_subdirs[stage_folder]
            
            # Check for new content type structure first
            has_content_type_dirs = any((directory_path / ct).exists() for ct in self.content_types)
            
            if has_content_type_dirs:
                # New structure: scan content types, then special subdirs
                for content_type in self.content_types:
                    content_path = directory_path / content_type
                    if content_path.exists():
                        for special_dir in special_dirs:
                            special_path = content_path / special_dir
                            if special_path.exists():
                                for item in special_path.iterdir():
                                    if item.is_dir() and not item.name.startswith('.'):
                                        movies.append(f"{item.name} ({content_type} in {special_dir})")
            else:
                # Legacy structure: scan special subdirs directly
                for special_dir in special_dirs:
                    special_path = directory_path / special_dir
                    if special_path.exists():
                        for item in special_path.iterdir():
                            if item.is_dir() and not item.name.startswith('.'):
                                movies.append(f"{item.name} (in {special_dir})")
            return sorted(movies)
            
        # Check for new content type structure (movies/tv_shows subdirectories)
        has_content_type_dirs = any((directory_path / ct).exists() for ct in self.content_types)
        
        if has_content_type_dirs:
            # New structure: scan movies/ and tv_shows/ subdirectories
            for content_type in self.content_types:
                content_path = directory_path / content_type
                if content_path.exists():
                    # Check for quality subdirectories within content type
                    has_quality_dirs = any((content_path / quality).exists() for quality in self.quality_tiers)
                    
                    if has_quality_dirs:
                        # Scan each quality tier within content type
                        for quality in self.quality_tiers:
                            quality_path = content_path / quality
                            if quality_path.exists():
                                for item in quality_path.iterdir():
                                    if item.is_dir():
                                        movies.append(f"{item.name} ({content_type} {quality})")
                    else:
                        # Direct content folders within content type
                        for item in content_path.iterdir():
                            if item.is_dir() and not item.name.startswith('.'):
                                movies.append(f"{item.name} ({content_type})")
        else:
            # Legacy structure: check for quality subdirectories first
            has_quality_dirs = any((directory_path / quality).exists() for quality in self.quality_tiers)
            
            if has_quality_dirs:
                # Scan each quality tier
                for quality in self.quality_tiers:
                    quality_path = directory_path / quality
                    if quality_path.exists():
                        for item in quality_path.iterdir():
                            if item.is_dir():
                                movies.append(f"{item.name} ({quality})")
            else:
                # Direct movie folders in this directory
                for item in directory_path.iterdir():
                    if item.is_dir() and not item.name.startswith('.'):
                        # Skip special subdirectories if this is a known stage
                        if stage_folder and stage_folder in self.special_subdirs:
                            if item.name in self.special_subdirs[stage_folder]:
                                continue
                        # Skip content type directories in legacy scan
                        if item.name in self.content_types:
                            continue
                        movies.append(item.name)
                    
        return sorted(movies)
    
    def check_marker_files(self, directory_path):
        """Check for processing marker files in a directory"""
        markers = []
        if not directory_path.exists():
            return markers
            
        marker_patterns = [
            "*.processing",
            "*.complete", 
            "*.pending",
            "*.error",
            "*.retry"
        ]
        
        for pattern in marker_patterns:
            markers.extend(glob.glob(str(directory_path / "**" / pattern), recursive=True))
            
        return [Path(m).name for m in markers]
    
    def get_pipeline_status(self):
        """Get complete pipeline status"""
        status = {
            "timestamp": datetime.now().isoformat(),
            "pipeline_stages": {},
            "issues": {},
            "summary": {
                "total_content": 0,
                "content_with_issues": 0,
                "stages_with_content": 0,
                "issue_categories_with_content": 0
            }
        }
        
        # Scan main pipeline stages
        for stage_folder, stage_name in self.pipeline_stages.items():
            stage_path = self.workspace_root / stage_folder
            movies = self.scan_directory_for_movies(stage_path, stage_folder)
            markers = self.check_marker_files(stage_path)
            
            status["pipeline_stages"][stage_folder] = {
                "name": stage_name,
                "count": len(movies),
                "content": movies,
                "markers": markers
            }
            
            if movies:
                status["summary"]["stages_with_content"] += 1
                status["summary"]["total_content"] += len(movies)
        
        # Scan issues_hold folder
        issues_path = self.workspace_root / "issues_hold"
        if issues_path.exists():
            for issue_folder, issue_name in self.issue_categories.items():
                issue_path = issues_path / issue_folder
                movies = self.scan_directory_for_movies(issue_path)
                
                status["issues"][issue_folder] = {
                    "name": issue_name,
                    "count": len(movies),
                    "content": movies
                }
                
                if movies:
                    status["summary"]["issue_categories_with_content"] += 1
                    status["summary"]["content_with_issues"] += len(movies)
        
        return status
    
    def format_terminal_output(self, status, detailed=False):
        """Format status for terminal display with colors"""
        output = []
        
        # Header
        output.append("=" * 80)
        output.append("🎬 PLEX MOVIE AUTOMATOR - PIPELINE STATUS DASHBOARD 🎬")
        output.append("         📺 Now Supporting Movies & TV Shows! 📺")
        output.append("=" * 80)
        output.append(f"📅 Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        output.append("")
        
        # Pipeline Stages
        output.append("📊 PIPELINE STAGES:")
        output.append("-" * 50)
        
        for stage_folder, stage_data in status["pipeline_stages"].items():
            count = stage_data["count"]
            name = stage_data["name"]
            
            if count > 0:
                status_line = f"✅ {name:<35} : {count:>3} items"
                if detailed and stage_data["content"]:
                    status_line += f" ({', '.join(stage_data['content'][:3])}"
                    if len(stage_data["content"]) > 3:
                        status_line += f", +{len(stage_data['content'])-3} more"
                    status_line += ")"
            else:
                status_line = f"⚪ {name:<35} : {count:>3} items"
                
            output.append(status_line)
            
            # Show markers if any
            if detailed and stage_data["markers"]:
                output.append(f"    📌 Markers: {', '.join(stage_data['markers'])}")
        
        output.append("")
        
        # Issues Section
        total_issues = sum(issue_data["count"] for issue_data in status["issues"].values())
        if total_issues > 0:
            output.append("⚠️  ISSUES REQUIRING ATTENTION:")
            output.append("-" * 50)
            
            for issue_folder, issue_data in status["issues"].items():
                count = issue_data["count"]
                name = issue_data["name"]
                
                if count > 0:
                    status_line = f"🔴 {name:<35} : {count:>3} items"
                    if detailed and issue_data["content"]:
                        status_line += f" ({', '.join(issue_data['content'][:2])}"
                        if len(issue_data["content"]) > 2:
                            status_line += f", +{len(issue_data['content'])-2} more"
                        status_line += ")"
                    output.append(status_line)
            output.append("")
        
        # Summary
        output.append("📈 SUMMARY:")
        output.append("-" * 50)
        summary = status["summary"]
        output.append(f"🎬 Total Content in Pipeline   : {summary['total_content']}")
        output.append(f"⚠️  Content with Issues        : {summary['content_with_issues']}")
        output.append(f"📁 Active Pipeline Stages     : {summary['stages_with_content']}")
        output.append(f"🔴 Issue Categories with Items: {summary['issue_categories_with_content']}")
        
        # Health indicator
        if summary["movies_with_issues"] == 0:
            health = "🟢 HEALTHY - No issues detected!"
        elif summary["movies_with_issues"] < summary["total_movies"] * 0.2:
            health = "🟡 MINOR ISSUES - Some attention needed"
        else:
            health = "🔴 NEEDS ATTENTION - Multiple issues detected"
            
        output.append(f"🏥 Pipeline Health            : {health}")
        output.append("")
        output.append("=" * 80)
        
        return "\n".join(output)
    
    def format_html_output(self, status):
        """Format status as HTML dashboard"""
        html = f"""
<!DOCTYPE html>
<html>
<head>
    <title>Plex Movie Automator - Pipeline Status</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }}
        .container {{ max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
        h1 {{ color: #333; text-align: center; border-bottom: 3px solid #007acc; padding-bottom: 10px; }}
        .timestamp {{ text-align: center; color: #666; margin-bottom: 30px; }}
        .section {{ margin-bottom: 30px; }}
        .section h2 {{ color: #007acc; border-bottom: 2px solid #eee; padding-bottom: 5px; }}
        .stage-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; }}
        .stage-card {{ border: 1px solid #ddd; border-radius: 6px; padding: 15px; background: #fafafa; }}
        .stage-card.has-content {{ background: #e8f5e8; border-color: #4caf50; }}
        .stage-card.has-issues {{ background: #ffe8e8; border-color: #f44336; }}
        .count {{ font-size: 24px; font-weight: bold; color: #007acc; }}
        .movie-list {{ margin-top: 10px; font-size: 12px; color: #666; }}
        .summary {{ background: #e3f2fd; padding: 20px; border-radius: 6px; border-left: 4px solid #007acc; }}
        .summary-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; margin-top: 15px; }}
        .summary-item {{ text-align: center; }}
        .summary-number {{ font-size: 32px; font-weight: bold; color: #007acc; }}
        .health-indicator {{ padding: 10px; border-radius: 4px; text-align: center; font-weight: bold; margin-top: 15px; }}
        .health-good {{ background: #c8e6c9; color: #2e7d32; }}
        .health-warning {{ background: #fff3e0; color: #f57c00; }}
        .health-error {{ background: #ffcdd2; color: #c62828; }}
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 Plex Movie Automator - Pipeline Status</h1>
        <div class="timestamp">Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</div>
        
        <div class="section">
            <h2>📊 Pipeline Stages</h2>
            <div class="stage-grid">
"""
        
        # Pipeline stages
        for stage_folder, stage_data in status["pipeline_stages"].items():
            count = stage_data["count"]
            name = stage_data["name"].replace("🎬 ", "").replace("📁 ", "").replace("⬇️ ", "").replace("📥 ", "").replace("🔄 ", "").replace("🖼️ ", "").replace("✅ ", "").replace("🎯 ", "").replace("✨ ", "")
            card_class = "has-content" if count > 0 else ""
            
            movie_list = ""
            if stage_data["movies"]:
                movie_list = "<div class='movie-list'>" + "<br>".join(stage_data["movies"][:5])
                if len(stage_data["movies"]) > 5:
                    movie_list += f"<br><em>+{len(stage_data['movies'])-5} more...</em>"
                movie_list += "</div>"
            
            html += f"""
                <div class="stage-card {card_class}">
                    <div class="stage-name">{name}</div>
                    <div class="count">{count}</div>
                    <div>movies</div>
                    {movie_list}
                </div>
"""
        
        html += """
            </div>
        </div>
"""
        
        # Issues section
        total_issues = sum(issue_data["count"] for issue_data in status["issues"].values())
        if total_issues > 0:
            html += """
        <div class="section">
            <h2>⚠️ Issues Requiring Attention</h2>
            <div class="stage-grid">
"""
            
            for issue_folder, issue_data in status["issues"].items():
                count = issue_data["count"]
                if count > 0:
                    name = issue_data["name"].replace("🔊 ", "").replace("⚠️ ", "").replace("👁️ ", "").replace("📝 ", "").replace("❓ ", "").replace("🖼️ ", "").replace("📄 ", "").replace("📹 ", "")
                    
                    movie_list = ""
                    if issue_data["movies"]:
                        movie_list = "<div class='movie-list'>" + "<br>".join(issue_data["movies"][:3])
                        if len(issue_data["movies"]) > 3:
                            movie_list += f"<br><em>+{len(issue_data['movies'])-3} more...</em>"
                        movie_list += "</div>"
                    
                    html += f"""
                <div class="stage-card has-issues">
                    <div class="stage-name">{name}</div>
                    <div class="count">{count}</div>
                    <div>movies</div>
                    {movie_list}
                </div>
"""
            
            html += """
            </div>
        </div>
"""
        
        # Summary
        summary = status["summary"]
        health_class = "health-good"
        health_text = "🟢 HEALTHY - No issues detected!"
        
        if summary["movies_with_issues"] > 0:
            if summary["movies_with_issues"] < summary["total_movies"] * 0.2:
                health_class = "health-warning"
                health_text = "🟡 MINOR ISSUES - Some attention needed"
            else:
                health_class = "health-error" 
                health_text = "🔴 NEEDS ATTENTION - Multiple issues detected"
        
        html += f"""
        <div class="section">
            <h2>📈 Summary</h2>
            <div class="summary">
                <div class="summary-grid">
                    <div class="summary-item">
                        <div class="summary-number">{summary['total_movies']}</div>
                        <div>Total Movies</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-number">{summary['movies_with_issues']}</div>
                        <div>Movies with Issues</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-number">{summary['stages_with_content']}</div>
                        <div>Active Stages</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-number">{summary['issue_categories_with_content']}</div>
                        <div>Issue Categories</div>
                    </div>
                </div>
                <div class="health-indicator {health_class}">
                    {health_text}
                </div>
            </div>
        </div>
    </div>
</body>
</html>
"""
        
        return html
    
    def generate_report(self, format_type="terminal", detailed=False):
        """Generate status report in specified format"""
        status = self.get_pipeline_status()
        
        if format_type == "terminal":
            return self.format_terminal_output(status, detailed)
        elif format_type == "html":
            return self.format_html_output(status)
        elif format_type == "json":
            return json.dumps(status, indent=2)
        else:
            raise ValueError(f"Unsupported format: {format_type}")

def main():
    parser = argparse.ArgumentParser(description="Plex Movie Automator Pipeline Status Dashboard")
    parser.add_argument("--workspace", default="workspace", help="Path to workspace directory")
    parser.add_argument("--format", choices=["terminal", "html", "json"], default="terminal", help="Output format")
    parser.add_argument("--detailed", action="store_true", help="Include detailed movie lists and markers")
    parser.add_argument("--output", help="Output file (default: stdout for terminal, status.html for html, status.json for json)")
    
    args = parser.parse_args()
    
    # Determine workspace path
    if os.path.isabs(args.workspace):
        workspace_path = args.workspace
    else:
        # Relative to script directory
        script_dir = Path(__file__).parent
        workspace_path = script_dir / args.workspace
    
    if not workspace_path.exists():
        print(f"❌ Error: Workspace directory not found: {workspace_path}")
        sys.exit(1)
    
    # Generate report
    dashboard = PipelineStatusDashboard(workspace_path)
    report = dashboard.generate_report(args.format, args.detailed)
    
    # Output report
    if args.output:
        output_path = Path(args.output)
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(report)
        print(f"✅ Report saved to: {output_path}")
    else:
        if args.format == "terminal":
            print(report)
        else:
            # Default output files for non-terminal formats
            default_outputs = {"html": "status.html", "json": "status.json"}
            output_file = default_outputs[args.format]
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(report)
            print(f"✅ Report saved to: {output_file}")
    
    # Pause before closing (so window doesn't disappear immediately)
    print("\n" + "="*50)
    input("Press Enter to exit...")

if __name__ == "__main__":
    main()
