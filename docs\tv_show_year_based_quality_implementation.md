# TV Show Year-Based Quality Preferences Implementation

## Overview
Successfully implemented sophisticated year-based quality preferences for TV shows, matching the level of detail already present for movies in the PlexMovieAutomator pipeline.

## Implementation Details

### 1. New TV Show Quality Logic Function
**Location**: `01_intake_and_nzb_search.py`
**Function**: `_determine_tv_quality_profile_by_year()`

**Quality Strategies by Era**:
- **≤2005**: 720p/1080p only (early digital era, poor 4K remastering)
  - Profiles: [3, 4] (720p + 1080p)
  - Strategy: "sd_hd_only"

- **2006-2015**: 1080p preferred (HD broadcast era)
  - Profiles: [4] (1080p only)
  - Strategy: "hd_preferred"

- **2016-2020**: Both 1080p AND 4K (early streaming 4K era)
  - Profiles: [4, 5] (1080p + 4K)
  - Strategy: "hd_and_4k"

- **2021+**: 4K preferred (modern production era)
  - Profiles: [5] (4K only)
  - Strategy: "4k_preferred"

### 2. New TV Show Sonarr Integration Function
**Location**: `01_intake_and_nzb_search.py`
**Function**: `_add_tv_show_to_sonarr_modern()`

**Features**:
- Automatic TV show search via Sonarr API
- Year-based quality profile selection
- Multiple quality profile support (downloads both 1080p and 4K when appropriate)
- TVDB ID matching for accurate show identification
- Automatic monitoring and search initiation
- Comprehensive error handling and logging

### 3. Configuration Updates

#### Settings.ini
**New Sonarr Section Added**:
```ini
[Sonarr]
url = http://localhost:8989
api_key = 745e39af03d3443c989632c27a0fcd47
tv_720p_quality_profile_id = 3
tv_hd_quality_profile_id = 4
tv_uhd_quality_profile_id = 5
```

**New TV Show Path**:
```ini
plex_tv_directory = G:/Shows
```

#### MCP Config
**Enhanced Sonarr Integration**:
- Added year-based quality profile IDs
- Enabled year-based quality logic
- Updated quality profile naming

### 4. Testing
**Test Results**: ✅ All year-based logic working correctly
- 1999-2005: 720p/1080p only
- 2006-2015: 1080p preferred  
- 2016-2020: Both 1080p AND 4K
- 2021-2025: 4K preferred

## TV Show vs Movie Quality Logic Comparison

### Movies (Radarr)
- **≤2009**: 1080p only
- **2010-2015**: BOTH 1080p AND 4K
- **2016+**: 4K only

### TV Shows (Sonarr) - NEW
- **≤2005**: 720p/1080p only
- **2006-2015**: 1080p preferred
- **2016-2020**: BOTH 1080p AND 4K  
- **2021+**: 4K preferred

## Quality Evolution Rationale

### TV Shows Have Different Quality Evolution Than Movies:
1. **Early Digital Era (≤2005)**: TV shows from this era often have poor source material and benefit from multiple quality options
2. **HD Broadcast Era (2006-2015)**: Peak of HD broadcast television, 1080p is the sweet spot
3. **Early Streaming 4K (2016-2020)**: Some shows started being produced in 4K, but not all - download both
4. **Modern Production (2021+)**: Most premium TV shows now shot in 4K, prefer highest quality

## Integration Status
- ✅ Year-based quality logic implemented
- ✅ Sonarr API integration function created
- ✅ Configuration files updated
- ✅ Test validation complete
- ✅ Plex naming convention support (already implemented in other scripts)
- ✅ Quality tier directory structure (720p/1080p/4k) support

## Next Steps
1. **Integration Testing**: Test the new TV show functions in the full pipeline
2. **Edge Case Handling**: Add handling for shows without clear year information
3. **User Preferences**: Consider adding user overrides for specific show preferences
4. **Quality Monitoring**: Implement quality tracking and optimization suggestions

## Benefits
- **Intelligent Downloads**: No more manual quality selection for TV shows
- **Era-Appropriate Quality**: Matches quality expectations to production capabilities
- **Storage Optimization**: Downloads appropriate quality for content era
- **Future-Proof**: Automatically handles new shows with modern quality preferences
- **Parity with Movies**: TV shows now have same sophistication as movie processing
