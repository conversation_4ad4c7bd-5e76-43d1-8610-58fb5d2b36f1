#!/usr/bin/env python3
from __future__ import annotations

import argparse
import asyncio
import io
import json
import os
from pathlib import Path
from typing import Iterable, List, Optional

# Local import of event queue for default location
try:
    from _internal.src.event_queue import get_event_queue, EventQueue
except Exception:
    get_event_queue = None  # type: ignore
    EventQueue = None  # type: ignore

# ANSI colors (avoid extra deps)
class C:
    RESET = "\033[0m"
    DIM = "\033[2m"
    BOLD = "\033[1m"
    BLUE = "\033[34m"
    GREEN = "\033[32m"
    YELLOW = "\033[33m"
    RED = "\033[31m"
    CYAN = "\033[36m"


def colorize(s: str, color: str, use_color: bool) -> str:
    return f"{color}{s}{C.RESET}" if use_color else s


def default_events_path() -> Path:
    # Prefer using EventQueue's computed path if available
    if get_event_queue is not None:
        eq = get_event_queue({"EventQueue": {"enabled": True}})
        fp = getattr(eq, "file_path", None)
        if fp:
            return Path(fp)
    # Fallback to conventional path
    return Path("_internal/state/event_queue/events.jsonl")


def load_last_lines(path: Path, n: int) -> List[str]:
    if not path.exists():
        return []
    try:
        with path.open("r", encoding="utf-8") as f:
            lines = f.readlines()
            return lines[-n:]
    except Exception:
        return []


def parse_types(types_arg: Optional[str]) -> Optional[set]:
    if not types_arg:
        return None
    return {t.strip() for t in types_arg.split(",") if t.strip()}


def pretty_event_line(event: dict, use_color: bool = True, compact: bool = False) -> str:
    ts = event.get("ts", "")
    etype = event.get("type", "")
    data = event.get("data", {}) or {}

    header = f"[{ts}] {etype}"
    # Special formatting per type
    if etype == "movie.added":
        s = f"radarr_id={data.get('radarr_id')} title='{data.get('title')}' year={data.get('year')} profile={data.get('profile_id')}"
        return f"{colorize(header, C.GREEN, use_color)} {s}"
    if etype == "movie.add_failed":
        s = f"title='{data.get('title')}' year={data.get('year')} profile={data.get('profile_id')}"
        return f"{colorize(header, C.YELLOW, use_color)} {s}"
    if etype in {"sonarr.episode_search.triggered", "sonarr.episode_search.failed", "sonarr.episode_search.error"}:
        s = f"series={data.get('series_id')} S{str(data.get('season')).zfill(2)}E{str(data.get('episode')).zfill(2)} episode_id={data.get('episode_id')}"
        color = C.BLUE if etype.endswith("triggered") else (C.YELLOW if etype.endswith("failed") else C.RED)
        if etype.endswith("error") and data.get("error") and not compact:
            s += f" error={json.dumps(data.get('error'))}"
        return f"{colorize(header, color, use_color)} {s}"

    # Default pretty JSON
    if compact:
        return f"{colorize(header, C.CYAN, use_color)} {json.dumps(data, ensure_ascii=False)}"
    else:
        payload = json.dumps(data, ensure_ascii=False, indent=2)
        return f"{colorize(header, C.CYAN, use_color)}\n{payload}"


def iter_new_lines(f: io.TextIOBase) -> Iterable[str]:
    while True:
        line = f.readline()
        if not line:
            yield from ()
            return
        yield line


async def follow(path: Path, types_filter: Optional[set], use_color: bool, compact: bool):
    # Ensure file exists
    path.parent.mkdir(parents=True, exist_ok=True)
    path.touch(exist_ok=True)

    with path.open("r", encoding="utf-8") as f:
        # Seek to end for follow
        f.seek(0, os.SEEK_END)
        while True:
            pos = f.tell()
            line = f.readline()
            if not line:
                await asyncio.sleep(0.5)
                f.seek(pos)
                continue
            try:
                event = json.loads(line)
            except json.JSONDecodeError:
                continue
            if types_filter and event.get("type") not in types_filter:
                continue
            print(pretty_event_line(event, use_color=use_color, compact=compact), flush=True)


def main():
    ap = argparse.ArgumentParser(description="Tail and pretty-print Stage 01/02 event queue")
    ap.add_argument("--path", type=str, default=None, help="Path to events.jsonl (defaults to internal path)")
    ap.add_argument("-f", "--follow", action="store_true", help="Follow new events like tail -f")
    ap.add_argument("-n", "--last", type=int, default=20, help="Show last N events (when not following)")
    ap.add_argument("-t", "--types", type=str, default=None, help="Comma-separated type filter (e.g. movie.added,sonarr.episode_search.triggered)")
    ap.add_argument("--no-color", action="store_true", help="Disable ANSI colors")
    ap.add_argument("--compact", action="store_true", help="One-line JSON for unknown event types")

    args = ap.parse_args()
    types_filter = parse_types(args.types)
    use_color = not args.no_color

    path = Path(args.path) if args.path else default_events_path()
    print(f"Using events file: {path}")

    if args.follow:
        asyncio.run(follow(path, types_filter, use_color, args.compact))
        return

    # Non-follow: print last N
    lines = load_last_lines(path, args.last)
    for line in lines:
        try:
            event = json.loads(line)
        except json.JSONDecodeError:
            continue
        if types_filter and event.get("type") not in types_filter:
            continue
        print(pretty_event_line(event, use_color=use_color, compact=args.compact))


if __name__ == "__main__":
    main()

