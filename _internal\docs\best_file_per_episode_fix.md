# 🚨 SECOND CRITICAL BUG FIXED: "Best File Per Episode" Logic

## Problem Identified:
The preflight analyzer was downloading **ALL accepted files per episode** instead of choosing the **BEST one per episode**!

### **WRONG (Before Fix):**
- Episode 1 had 2 accepted files → Downloaded BOTH files ❌
- Episode 2 had 1 accepted file → Downloaded 1 file ✅  
- Episode 6 had 1 accepted file → Downloaded 1 file ✅
- **Total**: 4 individual episode files + 1 season pack = 5 downloads ❌

### **CORRECT (After Fix):**
- Episode 1 had 2 accepted files → Choose BEST 1 file ✅
- Episode 2 had 1 accepted file → Choose BEST 1 file ✅
- Episode 6 had 1 accepted file → Choose BEST 1 file ✅  
- **Total**: 3 individual episode files + 1 season pack = 4 downloads ✅

## Implementation Fix:

```python
# OLD CODE (Bug):
accepted_eps = [r for r in episode_reports if r.get('decision') in ('ACCEPT', 'RISKY_LOW_PARITY')]
grab_plan['episodes'] = [r['guid'] for r in accepted_eps]  # Downloads ALL accepted files

# NEW CODE (Fixed):
# Group accepted episodes by episode_id and choose the best one for each
eps_by_id = defaultdict(list)
for ep_report in accepted_eps:
    ep_id = ep_report.get('episode_id')
    if ep_id:
        eps_by_id[ep_id].append(ep_report)

# Choose best file for each episode (lowest risk score)
best_eps_per_episode = []
for ep_id, ep_options in eps_by_id.items():
    best_ep = min(ep_options, key=lambda x: x.get('risk_score', float('inf')))
    best_eps_per_episode.append(best_ep)

grab_plan['episodes'] = [r['guid'] for r in best_eps_per_episode]  # Downloads BEST file per episode
```

## FLCL Example - Expected Results:

**Before Fix**: 25 downloads (4 accepted + 21 rejected + maybe duplicates)
**After First Fix**: 5 downloads (4 files + 1 pack)  
**After Second Fix**: **4 downloads (3 best files + 1 pack)** ✅

### **Specific FLCL Downloads (After Both Fixes):**
1. **Best file for S01E01** (chosen from 2 accepted options based on lowest risk)
2. **Best file for S01E02** (only 1 option, so that one)
3. **Best file for S01E06** (only 1 option, so that one)
4. **Best season pack** (chosen from 2 accepted packs based on risk/quality)

**Total: 4 downloads exactly** - This matches your expectation! 🎉

## Summary:
- ✅ **First Fix**: Filter out rejected files (no more 25 downloads)
- ✅ **Second Fix**: Choose best file per episode (no more duplicate files per episode)
- ✅ **Final Result**: Exactly 4 downloads for FLCL (3 episodes + 1 pack)

**The logic is now implemented exactly as you specified!**
