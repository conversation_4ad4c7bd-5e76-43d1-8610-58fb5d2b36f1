from __future__ import annotations
import xml.etree.ElementTree as ET
from dataclasses import dataclass, field
from pathlib import Path
from typing import List, Optional
import re
import gzip

# NOTE: Many NZB files declare a default XML namespace (e.g., newzbin / nzb v1 DTD).
# Original parser looked for 'file' elements without namespace, returning zero results.
# We now match tags by suffix (endswith('file')) to be namespace-agnostic.

PAR2_VOL_PATTERN = re.compile(r"\.vol(\d+)\+(\d+)\.par2$", re.IGNORECASE)

@dataclass
class NZBSegment:
    message_id: str
    bytes: Optional[int] = None

@dataclass
class NZBFile:
    subject: str
    poster: str
    date: int
    groups: List[str]
    segments: List[NZBSegment]
    filename_hint: Optional[str] = None
    is_par2: bool = False
    parity_blocks: int = 0

@dataclass
class NZBMetadata:
    file_count: int
    total_segments: int
    data_segments: int
    par2_segments: int
    estimated_parity_blocks: int
    files: List[NZBFile] = field(default_factory=list)

class NZBParser:
    """Lightweight NZB parser with parity heuristic."""

    def parse(self, nzb_path: Path) -> NZBMetadata:
        # Load file (handle gzip-compressed NZB)
        raw: bytes
        with open(nzb_path, 'rb') as fh:
            head = fh.read(2)
            fh.seek(0)
            if head == b'\x1f\x8b':  # gzip magic
                raw = gzip.decompress(fh.read())
            else:
                raw = fh.read()
        return self._parse_raw(raw)

    def parse_bytes(self, raw: bytes) -> NZBMetadata:
        """Parse NZB from raw bytes (handles gzip automatically)."""
        if raw[:2] == b'\x1f\x8b':
            try:
                raw = gzip.decompress(raw)
            except OSError:
                # Leave as-is if decompression fails
                pass
        return self._parse_raw(raw)

    # Internal shared implementation
    def _parse_raw(self, raw: bytes) -> NZBMetadata:
        try:
            root = ET.fromstring(raw)
        except ET.ParseError as e:
            raise ValueError(f"Invalid NZB XML: {e}")

        files: List[NZBFile] = []
        total_segments: int = 0
        par2_segments: int = 0
        estimated_parity_blocks: int = 0

        # Collect file elements (namespace agnostic)
        file_elements = [el for el in root.iter() if el.tag.lower().endswith('file')]
        for file_el in file_elements:
            subject = file_el.get('subject', '')
            poster = file_el.get('poster', '')
            try:
                date = int(file_el.get('date', '0') or 0)
            except ValueError:
                date = 0
            # Groups: traverse descendants ending with 'group'
            groups: List[str] = []
            for g_el in file_el.iter():
                if g_el.tag.lower().endswith('group') and g_el.text:
                    groups.append(g_el.text.strip())
            # Segments container: element whose tag endswith 'segments'
            segments_el = None
            for maybe in file_el.iter():
                if maybe.tag.lower().endswith('segments'):
                    segments_el = maybe
                    break
            segs: List[NZBSegment] = []
            if segments_el is not None:
                for seg_el in segments_el:
                    if not seg_el.tag.lower().endswith('segment'):
                        continue
                    mid = (seg_el.text or '').strip()
                    try:
                        bytes_len = int(seg_el.get('bytes', '0'))
                    except (ValueError, TypeError):
                        bytes_len = None
                    if mid:
                        segs.append(NZBSegment(message_id=mid, bytes=bytes_len))
            is_par2 = subject.lower().endswith('.par2')
            parity_blocks = 0
            if is_par2:
                m = PAR2_VOL_PATTERN.search(subject.lower())
                if m:
                    # m.group(2) are recovery blocks in this volume
                    try:
                        parity_blocks = int(m.group(2))
                    except ValueError:
                        parity_blocks = 0
                    estimated_parity_blocks += parity_blocks
                par2_segments += len(segs)
            total_segments += len(segs)
            files.append(NZBFile(
                subject=subject,
                poster=poster,
                date=date,
                groups=groups,
                segments=segs,
                filename_hint=_extract_filename_from_subject(subject),
                is_par2=is_par2,
                parity_blocks=parity_blocks,
            ))

        if not files:
            # Provide diagnostic snippet to help troubleshooting
            sample = raw[:400].decode('utf-8', errors='replace')
            raise ValueError("No <file> elements found in NZB (namespace issue or empty NZB). First 400 bytes:\n" + sample)

        data_segments = total_segments - par2_segments

        return NZBMetadata(
            file_count=len(files),
            total_segments=total_segments,
            data_segments=data_segments,
            par2_segments=par2_segments,
            estimated_parity_blocks=estimated_parity_blocks,
            files=files,
        )

def _extract_filename_from_subject(subject: str) -> Optional[str]:
    # Basic heuristic: look for quoted filename or the last token ending with typical extentions
    m = re.search(r'"([^"]+)"', subject)
    if m:
        return m.group(1)
    m2 = re.search(r'([\w.\-]+\.(mkv|mp4|avi|par2|rar|zip))', subject, re.IGNORECASE)
    if m2:
        return m2.group(1)
    return None
