import asyncio
import ssl
from dataclasses import dataclass
from typing import List, Dict, Optional

@dataclass
class NNTPServerConfig:
    host: str
    port: int = 563
    username: Optional[str] = None
    password: Optional[str] = None
    use_ssl: bool = True
    name: Optional[str] = None
    timeout: float = 10.0

@dataclass
class ProbeResult:
    server: str
    total: int
    available: int
    missing: int
    errors: int

class AsyncNNTPProbe:
    def __init__(self, servers: List[NNTPServerConfig], verbose: bool = False):
        self.servers = servers
        self.verbose = verbose

    async def _open_connection(self, server: NNTPServerConfig):
        ssl_ctx = ssl.create_default_context() if server.use_ssl else None
        reader, writer = await asyncio.wait_for(asyncio.open_connection(server.host, server.port, ssl=ssl_ctx), timeout=server.timeout)
        # Greeting (consume possibly multi-line until a line starting with a code + space)
        greeting = await asyncio.wait_for(reader.readline(), timeout=server.timeout)
        if self.verbose:
            print(f"[NNTP:{server.name}] GREETING: {greeting.decode(errors='ignore').strip()}")
        if server.username:
            writer.write(f"AUTHINFO USER {server.username}\r\n".encode()); await writer.drain()
            user_resp = await asyncio.wait_for(reader.readline(), timeout=server.timeout)
            if self.verbose:
                print(f"[NNTP:{server.name}] AUTH USER -> {user_resp.decode(errors='ignore').strip()}")
            writer.write(f"AUTHINFO PASS {server.password}\r\n".encode()); await writer.drain()
            pass_resp = await asyncio.wait_for(reader.readline(), timeout=server.timeout)
            if self.verbose:
                print(f"[NNTP:{server.name}] AUTH PASS -> {pass_resp.decode(errors='ignore').strip()}")
        # Some servers require MODE READER
        writer.write(b"MODE READER\r\n"); await writer.drain()
        try:
            mode_resp = await asyncio.wait_for(reader.readline(), timeout=server.timeout)
            if self.verbose:
                print(f"[NNTP:{server.name}] MODE READER -> {mode_resp.decode(errors='ignore').strip()}")
        except Exception:
            pass
        return reader, writer

    async def _stat(self, reader: asyncio.StreamReader, writer: asyncio.StreamWriter, message_id: str, timeout: float) -> str:
        try:
            # Normalize message-id (ensure angle brackets present)
            mid = message_id.strip()
            if not mid.startswith('<'):
                mid = f'<{mid.strip("<>")}>'
            writer.write(f"STAT {mid}\r\n".encode()); await writer.drain()
            resp = await asyncio.wait_for(reader.readline(), timeout=timeout)
            if resp.startswith(b'223'):
                return 'available'
            if resp.startswith(b'430'):
                return 'missing'
            # 4xx other = temp/unavailable, 5xx permanent error
            if resp[:1] in (b'4', b'5'):
                return 'error'
            return 'error'
        except Exception:
            return 'error'

    async def probe_sample(self, message_ids: List[str], per_server_timeout: float = 60.0) -> Dict[str, ProbeResult]:
        results: Dict[str, ProbeResult] = {}
        for server in self.servers:
            counts = {'available': 0, 'missing': 0, 'error': 0}
            if not message_ids:
                results[server.name or server.host] = ProbeResult(server=server.name or server.host, total=0, available=0, missing=0, errors=0)
                continue
            reader = writer = None
            try:
                reader, writer = await self._open_connection(server)
                start = asyncio.get_event_loop().time()
                for mid in message_ids:
                    if asyncio.get_event_loop().time() - start > per_server_timeout:
                        break
                    status = await self._stat(reader, writer, mid, timeout=server.timeout)
                    counts[status] += 1
            except Exception as exc:
                if self.verbose:
                    print(f"[NNTP:{server.name}] Connection failure: {exc}")
                # Count remaining as error if connection failed early
                counts['error'] += max(0, len(message_ids) - sum(counts.values()))
            finally:
                if writer and not writer.is_closing():
                    try:
                        writer.write(b"QUIT\r\n"); await writer.drain()
                    except Exception:
                        pass
                    writer.close()
                    try:
                        await writer.wait_closed()
                    except Exception:
                        pass
            results[server.name or server.host] = ProbeResult(
                server=server.name or server.host,
                total=len(message_ids),
                available=counts['available'],
                missing=counts['missing'],
                errors=counts['error'],
            )
        return results
