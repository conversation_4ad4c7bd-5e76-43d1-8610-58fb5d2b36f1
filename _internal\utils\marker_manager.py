#!/usr/bin/env python3
from __future__ import annotations

import json
from pathlib import Path
from typing import Any, Dict, Optional
from datetime import datetime


class MarkerManager:
    """
    Centralized manager for writing/checking processing markers (e.g., .organized, .mkv_complete).
    Supports JSON metadata and idempotent writes.
    """
    def __init__(self, folder: Path):
        self.folder = Path(folder)

    def _marker_path(self, marker_name: str) -> Path:
        return self.folder / marker_name

    def check(self, marker_name: str) -> bool:
        return self._marker_path(marker_name).exists()

    def write(self, marker_name: str, metadata: Optional[Dict[str, Any]] = None) -> bool:
        try:
            mp = self._marker_path(marker_name)
            if metadata is None:
                mp.touch(exist_ok=True)
                return True
            else:
                content = {
                    "written_at": datetime.now().isoformat(),
                    **metadata,
                }
                mp.write_text(json.dumps(content, ensure_ascii=False, indent=2), encoding="utf-8")
                return True
        except Exception:
            return False


def get_marker_manager(folder: Path) -> MarkerManager:
    return MarkerManager(folder)

