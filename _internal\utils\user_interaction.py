#!/usr/bin/env python3
"""
PlexAutomator/_internal/utils/user_interaction.py

User interaction utilities for fuzzy matching confirmation and error handling.
Provides both interactive and non-interactive modes for different use cases.
"""

import logging
from typing import Dict, Any, Optional, List, Tuple
from pathlib import Path

logger = logging.getLogger(__name__)


class MatchConfirmationHandler:
    """Handles user confirmation for ambiguous metadata matches."""
    
    def __init__(self, interactive_mode: bool = False, log_unresolved: bool = True):
        self.interactive_mode = interactive_mode
        self.log_unresolved = log_unresolved
        self.unresolved_matches: List[Dict[str, Any]] = []
    
    def handle_ambiguous_match(self, match_result: Dict[str, Any], 
                             original_query: str) -> Tuple[bool, Optional[Dict[str, Any]]]:
        """
        Handle an ambiguous match that requires user confirmation.
        
        Args:
            match_result: Result from fuzzy matching
            original_query: Original user query
        
        Returns:
            Tuple of (should_proceed, updated_match_result)
        """
        if self.interactive_mode:
            return self._interactive_confirmation(match_result, original_query)
        else:
            return self._non_interactive_handling(match_result, original_query)
    
    def _interactive_confirmation(self, match_result: Dict[str, Any], 
                                original_query: str) -> Tuple[bool, Optional[Dict[str, Any]]]:
        """Handle confirmation in interactive mode."""
        confidence = match_result.get("confidence_score", 0)
        suggestion = match_result.get("suggestion", "")
        matched_title = match_result.get("matched_title", "Unknown")
        candidate_year = match_result.get("candidate_year", "Unknown")
        
        print(f"\n🤔 Ambiguous match for '{original_query}':")
        print(f"   Best guess: {matched_title} ({candidate_year})")
        print(f"   Confidence: {confidence:.1f}%")
        
        if suggestion:
            print(f"   {suggestion}")
        
        while True:
            choice = input("\nOptions: (Y)es / (N)o / (S)kip / (V)iew alternatives: ").strip().lower()
            
            if choice in ['y', 'yes']:
                logger.info(f"User confirmed match: {matched_title}")
                # Update match result to indicate user confirmation
                updated_result = match_result.copy()
                updated_result["user_confirmed"] = True
                updated_result["confidence_score"] = max(95.0, confidence)  # Boost confidence
                return True, updated_result
                
            elif choice in ['n', 'no']:
                logger.info(f"User rejected match for: {original_query}")
                return False, None
                
            elif choice in ['s', 'skip']:
                logger.info(f"User skipped match for: {original_query}")
                self._log_unresolved_match(match_result, original_query, "user_skipped")
                return False, None
                
            elif choice in ['v', 'view']:
                self._show_alternatives(match_result)
                continue
                
            else:
                print("Invalid choice. Please enter Y, N, S, or V.")
    
    def _non_interactive_handling(self, match_result: Dict[str, Any], 
                                original_query: str) -> Tuple[bool, Optional[Dict[str, Any]]]:
        """Handle confirmation in non-interactive mode."""
        # Log for manual review later
        self._log_unresolved_match(match_result, original_query, "needs_confirmation")
        
        # Don't proceed with ambiguous matches in batch mode
        logger.warning(f"Ambiguous match logged for manual review: {original_query}")
        return False, None
    
    def _show_alternatives(self, match_result: Dict[str, Any]):
        """Show alternative matches to the user."""
        match_metadata = match_result.get("match_metadata", {})
        total_candidates = match_metadata.get("total_candidates", 0)
        
        print(f"\n📋 Found {total_candidates} total candidates")
        print("This feature will be enhanced in future versions to show top alternatives.")
        # TODO: Implement showing top 3-5 candidates with their scores
    
    def _log_unresolved_match(self, match_result: Dict[str, Any], 
                            original_query: str, reason: str):
        """Log an unresolved match for later review."""
        if not self.log_unresolved:
            return
        
        unresolved_entry = {
            "original_query": original_query,
            "matched_title": match_result.get("matched_title"),
            "candidate_year": match_result.get("candidate_year"),
            "confidence_score": match_result.get("confidence_score"),
            "reason": reason,
            "suggestion": match_result.get("suggestion"),
            "timestamp": self._get_timestamp()
        }
        
        self.unresolved_matches.append(unresolved_entry)
        logger.info(f"Logged unresolved match: {original_query} -> {reason}")
    
    def _get_timestamp(self) -> str:
        """Get current timestamp as string."""
        from datetime import datetime, timezone
        return datetime.now(timezone.utc).isoformat()
    
    def save_unresolved_matches(self, output_path: Optional[Path] = None):
        """Save unresolved matches to a file for manual review."""
        if not self.unresolved_matches:
            return
        
        output_path = output_path or Path("logs/unresolved_matches.txt")
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            with open(output_path, 'a', encoding='utf-8') as f:
                f.write(f"\n# Unresolved Matches - {self._get_timestamp()}\n")
                f.write("# Format: Original Query | Best Guess | Confidence | Reason\n")
                
                for entry in self.unresolved_matches:
                    line = (
                        f"{entry['original_query']} | "
                        f"{entry['matched_title']} ({entry['candidate_year']}) | "
                        f"{entry['confidence_score']:.1f}% | "
                        f"{entry['reason']}\n"
                    )
                    f.write(line)
                
                f.write(f"# End of batch - {len(self.unresolved_matches)} entries\n\n")
            
            logger.info(f"Saved {len(self.unresolved_matches)} unresolved matches to {output_path}")
            self.unresolved_matches.clear()
            
        except Exception as e:
            logger.error(f"Failed to save unresolved matches: {e}")
    
    def get_unresolved_count(self) -> int:
        """Get count of unresolved matches."""
        return len(self.unresolved_matches)
    
    def clear_unresolved(self):
        """Clear the unresolved matches list."""
        self.unresolved_matches.clear()


class FuzzyMatchingReporter:
    """Generates reports on fuzzy matching performance and decisions."""
    
    def __init__(self):
        self.match_stats: Dict[str, int] = {
            "total_queries": 0,
            "high_confidence": 0,
            "moderate_confidence": 0,
            "low_confidence": 0,
            "rejected": 0,
            "alternative_titles_used": 0,
            "user_confirmations": 0
        }
        self.match_decisions: List[Dict[str, Any]] = []
    
    def record_match_decision(self, original_query: str, match_result: Dict[str, Any], 
                            final_decision: str):
        """Record a fuzzy matching decision for reporting."""
        self.match_stats["total_queries"] += 1
        
        confidence = match_result.get("confidence_score", 0)
        if confidence >= 95:
            self.match_stats["high_confidence"] += 1
        elif confidence >= 80:
            self.match_stats["moderate_confidence"] += 1
        elif confidence >= 60:
            self.match_stats["low_confidence"] += 1
        else:
            self.match_stats["rejected"] += 1
        
        if match_result.get("match_metadata", {}).get("used_alternative_titles"):
            self.match_stats["alternative_titles_used"] += 1
        
        if match_result.get("user_confirmed"):
            self.match_stats["user_confirmations"] += 1
        
        decision_record = {
            "original_query": original_query,
            "matched_title": match_result.get("matched_title"),
            "confidence_score": confidence,
            "final_decision": final_decision,
            "used_alternatives": match_result.get("match_metadata", {}).get("used_alternative_titles", False),
            "user_confirmed": match_result.get("user_confirmed", False),
            "timestamp": self._get_timestamp()
        }
        
        self.match_decisions.append(decision_record)
    
    def _get_timestamp(self) -> str:
        """Get current timestamp as string."""
        from datetime import datetime, timezone
        return datetime.now(timezone.utc).isoformat()
    
    def generate_summary_report(self) -> str:
        """Generate a summary report of fuzzy matching performance."""
        if self.match_stats["total_queries"] == 0:
            return "No fuzzy matching operations recorded."
        
        total = self.match_stats["total_queries"]
        high_pct = (self.match_stats["high_confidence"] / total) * 100
        mod_pct = (self.match_stats["moderate_confidence"] / total) * 100
        low_pct = (self.match_stats["low_confidence"] / total) * 100
        reject_pct = (self.match_stats["rejected"] / total) * 100
        
        report = f"""
🎯 Fuzzy Matching Performance Summary
{'='*50}
Total Queries Processed: {total}

Confidence Distribution:
  • High Confidence (≥95%):     {self.match_stats['high_confidence']:>3} ({high_pct:5.1f}%)
  • Moderate Confidence (80-94%): {self.match_stats['moderate_confidence']:>3} ({mod_pct:5.1f}%)
  • Low Confidence (60-79%):    {self.match_stats['low_confidence']:>3} ({low_pct:5.1f}%)
  • Rejected (<60%):            {self.match_stats['rejected']:>3} ({reject_pct:5.1f}%)

Enhancement Features Used:
  • Alternative Titles:         {self.match_stats['alternative_titles_used']:>3}
  • User Confirmations:         {self.match_stats['user_confirmations']:>3}

Success Rate: {((total - self.match_stats['rejected']) / total * 100):5.1f}%
"""
        return report
    
    def save_detailed_report(self, output_path: Optional[Path] = None):
        """Save detailed matching decisions to a file."""
        output_path = output_path or Path("logs/fuzzy_matching_report.txt")
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(self.generate_summary_report())
                f.write("\n\nDetailed Match Decisions:\n")
                f.write("=" * 50 + "\n")
                
                for decision in self.match_decisions:
                    f.write(
                        f"Query: {decision['original_query']}\n"
                        f"Match: {decision['matched_title']}\n"
                        f"Confidence: {decision['confidence_score']:.1f}%\n"
                        f"Decision: {decision['final_decision']}\n"
                        f"Features: Alt={decision['used_alternatives']}, "
                        f"UserConf={decision['user_confirmed']}\n"
                        f"Time: {decision['timestamp']}\n"
                        f"{'-' * 40}\n"
                    )
            
            logger.info(f"Saved detailed fuzzy matching report to {output_path}")
            
        except Exception as e:
            logger.error(f"Failed to save fuzzy matching report: {e}")


# Global instances
_global_confirmation_handler: Optional[MatchConfirmationHandler] = None
_global_reporter: Optional[FuzzyMatchingReporter] = None


def get_confirmation_handler(interactive_mode: Optional[bool] = None) -> MatchConfirmationHandler:
    """Get global confirmation handler instance."""
    global _global_confirmation_handler
    
    if _global_confirmation_handler is None:
        if interactive_mode is None:
            try:
                from _internal.utils.fuzzy_config import get_fuzzy_config
                interactive_mode = get_fuzzy_config().enable_interactive_confirmation()
            except ImportError:
                interactive_mode = False
        
        _global_confirmation_handler = MatchConfirmationHandler(
            interactive_mode=interactive_mode,
            log_unresolved=True
        )
    
    return _global_confirmation_handler


def get_reporter() -> FuzzyMatchingReporter:
    """Get global reporter instance."""
    global _global_reporter
    if _global_reporter is None:
        _global_reporter = FuzzyMatchingReporter()
    return _global_reporter


def reset_globals():
    """Reset global instances (useful for testing)."""
    global _global_confirmation_handler, _global_reporter
    _global_confirmation_handler = None
    _global_reporter = None
