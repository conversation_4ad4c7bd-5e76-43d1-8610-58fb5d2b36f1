#!/usr/bin/env python3
"""
SABnzbd Failure Monitor & Learning System
=========================================

Monitors SABnzbd for download failures (especially missing articles) and maintains
a learning database to avoid re-downloading known bad releases. Provides intelligent
fallback suggestions when individual episodes consistently fail.

Key Features:
- Real-time SABnzbd failure detection
- Article missing pattern recognition  
- Release blacklist management
- Season pack fallback recommendations
- Learning from failure patterns

Author: PlexMovieAutomator Enhanced Intelligence System
"""

import asyncio
import aiohttp
import sqlite3
import json
import re
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import sys
import os

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from _internal.utils.common_helpers import get_setting

class SABnzbdFailureMonitor:
    """
    Monitors SABnzbd for download failures and maintains a learning database
    to avoid repeating failed downloads and suggest better strategies.
    """
    
    def __init__(self, db_path: Optional[str] = None):
        """Initialize the failure monitor with database connection."""
        if not db_path:
            data_dir = Path(__file__).parent.parent / "data"
            data_dir.mkdir(exist_ok=True)
            db_path = data_dir / "release_failure_tracker.db"
        
        self.db_path = str(db_path)
        self.logger = logging.getLogger("SABnzbdFailureMonitor")
        self._init_database()
        
        # Load settings if not already loaded
        try:
            from _internal.utils.common_helpers import load_settings
            settings_dict = load_settings("_internal/config/settings.ini")
        except Exception as e:
            self.logger.warning(f"Could not load settings: {e}")
            settings_dict = None
        
        # SABnzbd connection details (from DownloadAndOrganize section)
        self.sabnzbd_url = get_setting("DownloadAndOrganize", "sabnzbd_url", default="http://localhost:8080", settings_dict=settings_dict)
        self.sabnzbd_api_key = get_setting("DownloadAndOrganize", "sabnzbd_api_key", settings_dict=settings_dict)
        
        # Sonarr connection details  
        self.sonarr_url = get_setting("Sonarr", "url", default="http://localhost:8989", settings_dict=settings_dict)
        self.sonarr_api_key = get_setting("Sonarr", "api_key", settings_dict=settings_dict)
        
        # Failure patterns
        self.article_missing_patterns = [
            r"(\d+)\s+articles?\s+were?\s+missing",
            r"missing\s+(\d+)\s+articles?",
            r"incomplete\s+download.*missing.*(\d+)",
            r"failed.*(\d+).*articles?.*missing"
        ]
    
    def _init_database(self):
        """Initialize the failure tracking database."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Release failures table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS release_failures (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                release_name TEXT NOT NULL,
                release_hash TEXT,
                series_name TEXT,
                series_id INTEGER,
                season_number INTEGER,
                episode_number INTEGER,
                failure_reason TEXT,
                missing_articles INTEGER,
                failure_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                indexer TEXT,
                size_bytes INTEGER,
                retry_count INTEGER DEFAULT 0,
                UNIQUE(release_name, series_id, season_number, episode_number)
            )
        ''')
        
        # Series failure patterns table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS series_failure_patterns (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                series_id INTEGER NOT NULL,
                series_name TEXT NOT NULL,
                season_number INTEGER,
                total_attempts INTEGER DEFAULT 0,
                successful_attempts INTEGER DEFAULT 0,
                failed_attempts INTEGER DEFAULT 0,
                article_failure_rate REAL DEFAULT 0.0,
                last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                recommended_strategy TEXT DEFAULT 'individual_episodes',
                UNIQUE(series_id, season_number)
            )
        ''')
        
        # Blacklisted releases table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS blacklisted_releases (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                release_name TEXT NOT NULL,
                release_pattern TEXT,
                blacklist_reason TEXT,
                blacklist_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                permanent BOOLEAN DEFAULT 0,
                UNIQUE(release_name)
            )
        ''')
        
        conn.commit()
        conn.close()
        
        self.logger.info(f"📚 Failure tracking database initialized: {self.db_path}")
    
    async def monitor_sabnzbd_failures(self, check_interval: int = 30) -> None:
        """
        Continuously monitor SABnzbd for download failures.
        
        Args:
            check_interval: Seconds between checks
        """
        self.logger.info(f"🔍 Starting SABnzbd failure monitoring (every {check_interval}s)")
        
        async with aiohttp.ClientSession() as session:
            while True:
                try:
                    await self._check_recent_failures(session)
                    await asyncio.sleep(check_interval)
                except Exception as e:
                    self.logger.error(f"❌ Error in failure monitoring: {e}")
                    await asyncio.sleep(check_interval)
    
    async def _check_recent_failures(self, session: aiohttp.ClientSession) -> None:
        """Check SABnzbd history for recent failures."""
        try:
            # Get SABnzbd history
            params = {
                "mode": "history",
                "limit": 50,  # Check last 50 items
                "apikey": self.sabnzbd_api_key,
                "output": "json"
            }
            
            async with session.get(f"{self.sabnzbd_url}/api", params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    history_slots = data.get("history", {}).get("slots", [])
                    
                    for slot in history_slots:
                        await self._process_history_slot(slot)
                else:
                    self.logger.warning(f"⚠️ Failed to get SABnzbd history: HTTP {response.status}")
                    
        except Exception as e:
            self.logger.error(f"❌ Error checking SABnzbd failures: {e}")
    
    async def _process_history_slot(self, slot: Dict) -> None:
        """Process a single SABnzbd history slot for failures."""
        try:
            status = slot.get("status", "").lower()
            name = slot.get("name", "")
            fail_message = slot.get("fail_message", "")
            
            # Only process failed downloads
            if status != "failed" or not name:
                return
            
            # Check if it's an article missing failure
            missing_articles = self._extract_missing_articles(fail_message)
            if missing_articles is None:
                return  # Not an article failure
            
            # Extract series information from name
            series_info = self._extract_series_info(name)
            if not series_info:
                return  # Can't parse series info
            
            # Record the failure
            await self._record_failure(
                release_name=name,
                series_info=series_info,
                failure_reason=fail_message,
                missing_articles=missing_articles,
                slot_data=slot
            )
            
            self.logger.info(f"📝 Recorded failure: {name} - {missing_articles} articles missing")
            
        except Exception as e:
            self.logger.error(f"❌ Error processing history slot: {e}")
    
    def _extract_missing_articles(self, fail_message: str) -> Optional[int]:
        """Extract number of missing articles from failure message."""
        if not fail_message:
            return None
            
        for pattern in self.article_missing_patterns:
            match = re.search(pattern, fail_message, re.IGNORECASE)
            if match:
                try:
                    return int(match.group(1))
                except (ValueError, IndexError):
                    continue
        
        # Check for general "missing" or "incomplete" indicators
        if any(keyword in fail_message.lower() for keyword in ["missing", "incomplete", "article"]):
            return -1  # Unknown number of missing articles
        
        return None
    
    def _extract_series_info(self, release_name: str) -> Optional[Dict]:
        """Extract series, season, and episode info from release name."""
        try:
            # Common TV show patterns
            patterns = [
                r"^(.+?)\.S(\d{1,2})E(\d{1,2})",  # Series.S01E01
                r"^(.+?)\s+S(\d{1,2})E(\d{1,2})", # Series S01E01
                r"^(.+?)\.(\d{1,2})x(\d{1,2})",   # Series.1x01
                r"^(.+?)\s+(\d{1,2})x(\d{1,2})",  # Series 1x01
            ]
            
            for pattern in patterns:
                match = re.search(pattern, release_name, re.IGNORECASE)
                if match:
                    series_name = match.group(1).replace(".", " ").strip()
                    season = int(match.group(2))
                    episode = int(match.group(3))
                    
                    return {
                        "series_name": series_name,
                        "season_number": season,
                        "episode_number": episode
                    }
            
            return None
            
        except Exception as e:
            self.logger.error(f"❌ Error extracting series info from {release_name}: {e}")
            return None
    
    async def _record_failure(self, release_name: str, series_info: Dict, 
                            failure_reason: str, missing_articles: int, slot_data: Dict) -> None:
        """Record a failure in the database."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Get Sonarr series ID if possible
            series_id = await self._get_sonarr_series_id(series_info["series_name"])
            
            # Insert or update failure record
            cursor.execute('''
                INSERT OR REPLACE INTO release_failures 
                (release_name, series_name, series_id, season_number, episode_number,
                 failure_reason, missing_articles, size_bytes, indexer)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                release_name,
                series_info["series_name"],
                series_id,
                series_info["season_number"],
                series_info["episode_number"],
                failure_reason,
                missing_articles,
                slot_data.get("bytes", 0),
                slot_data.get("report", {}).get("indexer", "unknown")
            ))
            
            # Update series failure patterns
            await self._update_series_failure_pattern(cursor, series_id, series_info)
            
            # Auto-blacklist if too many failures
            await self._check_auto_blacklist(cursor, release_name, series_id, series_info)
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"❌ Error recording failure: {e}")
    
    async def _get_sonarr_series_id(self, series_name: str) -> Optional[int]:
        """Get Sonarr series ID for a series name."""
        try:
            async with aiohttp.ClientSession() as session:
                headers = {"X-Api-Key": self.sonarr_api_key}
                
                async with session.get(f"{self.sonarr_url}/api/v3/series", headers=headers) as response:
                    if response.status == 200:
                        series_list = await response.json()
                        
                        for series in series_list:
                            if series.get("title", "").lower() == series_name.lower():
                                return series.get("id")
            
            return None
            
        except Exception as e:
            self.logger.error(f"❌ Error getting Sonarr series ID: {e}")
            return None
    
    async def _update_series_failure_pattern(self, cursor, series_id: Optional[int], series_info: Dict) -> None:
        """Update failure patterns for a series/season."""
        try:
            if not series_id:
                return
            
            season_number = series_info["season_number"]
            
            # Get current stats
            cursor.execute('''
                SELECT total_attempts, failed_attempts FROM series_failure_patterns
                WHERE series_id = ? AND season_number = ?
            ''', (series_id, season_number))
            
            result = cursor.fetchone()
            if result:
                total_attempts, failed_attempts = result
                total_attempts += 1
                failed_attempts += 1
            else:
                total_attempts = 1
                failed_attempts = 1
            
            article_failure_rate = failed_attempts / total_attempts if total_attempts > 0 else 0
            
            # Determine recommended strategy
            recommended_strategy = "individual_episodes"
            if article_failure_rate > 0.7:  # > 70% failure rate
                recommended_strategy = "season_pack"
            elif article_failure_rate > 0.5:  # > 50% failure rate
                recommended_strategy = "hybrid"
            
            # Update or insert pattern
            cursor.execute('''
                INSERT OR REPLACE INTO series_failure_patterns
                (series_id, series_name, season_number, total_attempts, failed_attempts,
                 article_failure_rate, recommended_strategy, last_updated)
                VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            ''', (
                series_id,
                series_info["series_name"],
                season_number,
                total_attempts,
                failed_attempts,
                article_failure_rate,
                recommended_strategy
            ))
            
            self.logger.info(f"📊 Updated failure pattern: {series_info['series_name']} S{season_number:02d} - {article_failure_rate:.1%} failure rate, strategy: {recommended_strategy}")
            
        except Exception as e:
            self.logger.error(f"❌ Error updating series failure pattern: {e}")
    
    async def _check_auto_blacklist(self, cursor, release_name: str, series_id: Optional[int], series_info: Dict) -> None:
        """Check if a release should be auto-blacklisted based on failure patterns."""
        try:
            # Count failures for this specific release
            cursor.execute('''
                SELECT COUNT(*) FROM release_failures 
                WHERE release_name = ?
            ''', (release_name,))
            
            failure_count = cursor.fetchone()[0]
            
            # Auto-blacklist after 2 failures
            if failure_count >= 2:
                cursor.execute('''
                    INSERT OR IGNORE INTO blacklisted_releases
                    (release_name, blacklist_reason, permanent)
                    VALUES (?, ?, 0)
                ''', (
                    release_name,
                    f"Auto-blacklisted after {failure_count} article failures",
                ))
                
                self.logger.warning(f"🚫 Auto-blacklisted release: {release_name} (failed {failure_count} times)")
            
        except Exception as e:
            self.logger.error(f"❌ Error checking auto-blacklist: {e}")
    
    async def get_series_recommendation(self, series_id: int, season_number: int) -> Dict:
        """
        Get failure-based recommendation for a series/season.
        
        Returns:
            Dict with strategy recommendation and reasoning
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Get failure pattern for this series/season
            cursor.execute('''
                SELECT total_attempts, failed_attempts, article_failure_rate, recommended_strategy
                FROM series_failure_patterns
                WHERE series_id = ? AND season_number = ?
            ''', (series_id, season_number))
            
            result = cursor.fetchone()
            conn.close()
            
            if result:
                total_attempts, failed_attempts, failure_rate, recommended_strategy = result
                
                return {
                    "strategy": recommended_strategy,
                    "confidence": "high" if total_attempts >= 3 else "medium",
                    "failure_rate": failure_rate,
                    "total_attempts": total_attempts,
                    "failed_attempts": failed_attempts,
                    "reason": f"Based on {failed_attempts}/{total_attempts} failures ({failure_rate:.1%} failure rate)"
                }
            else:
                return {
                    "strategy": "individual_episodes",
                    "confidence": "low",
                    "failure_rate": 0.0,
                    "total_attempts": 0,
                    "failed_attempts": 0,
                    "reason": "No failure history available"
                }
                
        except Exception as e:
            self.logger.error(f"❌ Error getting series recommendation: {e}")
            return {
                "strategy": "individual_episodes",
                "confidence": "low",
                "failure_rate": 0.0,
                "reason": f"Error retrieving data: {e}"
            }
    
    async def is_release_blacklisted(self, release_name: str) -> Tuple[bool, Optional[str]]:
        """
        Check if a release is blacklisted.
        
        Returns:
            Tuple of (is_blacklisted, reason)
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT blacklist_reason FROM blacklisted_releases
                WHERE release_name = ?
            ''', (release_name,))
            
            result = cursor.fetchone()
            conn.close()
            
            if result:
                return True, result[0]
            else:
                return False, None
                
        except Exception as e:
            self.logger.error(f"❌ Error checking blacklist: {e}")
            return False, None
    
    async def cleanup_old_failures(self, days_old: int = 30) -> None:
        """Clean up failure records older than specified days."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cutoff_date = datetime.now() - timedelta(days=days_old)
            
            # Clean up old failure records
            cursor.execute('''
                DELETE FROM release_failures 
                WHERE failure_timestamp < ?
            ''', (cutoff_date,))
            
            deleted_count = cursor.rowcount
            
            # Clean up old blacklist entries (non-permanent)
            cursor.execute('''
                DELETE FROM blacklisted_releases 
                WHERE blacklist_timestamp < ? AND permanent = 0
            ''', (cutoff_date,))
            
            blacklist_deleted = cursor.rowcount
            
            conn.commit()
            conn.close()
            
            self.logger.info(f"🧹 Cleaned up {deleted_count} old failure records and {blacklist_deleted} blacklist entries")
            
        except Exception as e:
            self.logger.error(f"❌ Error cleaning up old failures: {e}")

# Global instance for easy access
failure_monitor = SABnzbdFailureMonitor()

async def start_failure_monitoring(check_interval: int = 30) -> None:
    """Start the SABnzbd failure monitoring service."""
    await failure_monitor.monitor_sabnzbd_failures(check_interval)

if __name__ == "__main__":
    # Test the failure monitor
    logging.basicConfig(level=logging.INFO)
    asyncio.run(start_failure_monitoring())
