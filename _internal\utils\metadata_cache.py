#!/usr/bin/env python3
"""
PlexAutomator/_internal/utils/metadata_cache.py

Caching system for TMDb API requests to optimize fuzzy matching performance.
Implements configurable caching with TTL and concurrency controls.
"""

import asyncio
import json
import logging
import time
from pathlib import Path
from typing import Dict, Any, Optional, Tuple, List
from datetime import datetime, timedelta
import aiohttp

logger = logging.getLogger(__name__)


class MetadataCache:
    """Thread-safe cache for TMDb API responses with TTL support."""
    
    def __init__(self, cache_dir: Optional[Path] = None, ttl_hours: int = 24):
        self.cache_dir = cache_dir or Path("data/metadata_cache")
        self.ttl_hours = ttl_hours
        self.memory_cache: Dict[str, Dict[str, Any]] = {}
        self._ensure_cache_dir()
    
    def _ensure_cache_dir(self):
        """Ensure cache directory exists."""
        self.cache_dir.mkdir(parents=True, exist_ok=True)
    
    def _get_cache_key(self, api_type: str, params: Dict[str, Any]) -> str:
        """Generate cache key from API type and parameters."""
        # Sort params for consistent keys
        sorted_params = sorted(params.items())
        params_str = "&".join(f"{k}={v}" for k, v in sorted_params if k != "api_key")
        return f"{api_type}_{hash(params_str)}"
    
    def _get_cache_file(self, cache_key: str) -> Path:
        """Get cache file path for a given key."""
        return self.cache_dir / f"{cache_key}.json"
    
    def _is_expired(self, timestamp: float) -> bool:
        """Check if cache entry is expired."""
        return time.time() - timestamp > (self.ttl_hours * 3600)
    
    def get_cached_response(self, api_type: str, params: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Get cached API response if available and not expired."""
        cache_key = self._get_cache_key(api_type, params)
        
        # Check memory cache first
        if cache_key in self.memory_cache:
            entry = self.memory_cache[cache_key]
            if not self._is_expired(entry["timestamp"]):
                logger.debug(f"Memory cache hit for {cache_key}")
                return entry["data"]
            else:
                # Remove expired entry
                del self.memory_cache[cache_key]
        
        # Check file cache
        cache_file = self._get_cache_file(cache_key)
        if cache_file.exists():
            try:
                with open(cache_file, 'r', encoding='utf-8') as f:
                    entry = json.load(f)
                
                if not self._is_expired(entry["timestamp"]):
                    logger.debug(f"File cache hit for {cache_key}")
                    # Load into memory cache
                    self.memory_cache[cache_key] = entry
                    return entry["data"]
                else:
                    # Remove expired file
                    cache_file.unlink()
            except (json.JSONDecodeError, KeyError) as e:
                logger.warning(f"Invalid cache file {cache_file}: {e}")
                cache_file.unlink(missing_ok=True)
        
        return None
    
    def cache_response(self, api_type: str, params: Dict[str, Any], response_data: Dict[str, Any]):
        """Cache API response both in memory and file."""
        cache_key = self._get_cache_key(api_type, params)
        
        entry = {
            "timestamp": time.time(),
            "api_type": api_type,
            "params": {k: v for k, v in params.items() if k != "api_key"},
            "data": response_data
        }
        
        # Store in memory
        self.memory_cache[cache_key] = entry
        
        # Store in file
        cache_file = self._get_cache_file(cache_key)
        try:
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(entry, f, indent=2, ensure_ascii=False)
            logger.debug(f"Cached response for {cache_key}")
        except Exception as e:
            logger.warning(f"Failed to cache response to file {cache_file}: {e}")
    
    def clear_expired(self):
        """Clear expired cache entries."""
        # Clear memory cache
        expired_keys = [
            key for key, entry in self.memory_cache.items() 
            if self._is_expired(entry["timestamp"])
        ]
        for key in expired_keys:
            del self.memory_cache[key]
        
        # Clear file cache
        if self.cache_dir.exists():
            for cache_file in self.cache_dir.glob("*.json"):
                try:
                    with open(cache_file, 'r', encoding='utf-8') as f:
                        entry = json.load(f)
                    
                    if self._is_expired(entry["timestamp"]):
                        cache_file.unlink()
                        logger.debug(f"Removed expired cache file {cache_file}")
                except Exception:
                    # Remove corrupted files
                    cache_file.unlink(missing_ok=True)
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        memory_count = len(self.memory_cache)
        file_count = len(list(self.cache_dir.glob("*.json"))) if self.cache_dir.exists() else 0
        
        return {
            "memory_entries": memory_count,
            "file_entries": file_count,
            "cache_dir": str(self.cache_dir),
            "ttl_hours": self.ttl_hours
        }


class ConcurrentTMDbClient:
    """TMDb API client with concurrency control and caching."""
    
    def __init__(self, api_key: str, concurrency_limit: int = 5, cache: Optional[MetadataCache] = None):
        self.api_key = api_key
        self.semaphore = asyncio.Semaphore(concurrency_limit)
        self.cache = cache or MetadataCache()
        self.base_url = "https://api.themoviedb.org/3"
    
    async def make_request(self, session: aiohttp.ClientSession, endpoint: str, 
                          params: Optional[Dict[str, Any]] = None) -> Tuple[int, Optional[Dict[str, Any]]]:
        """Make a rate-limited TMDb API request with caching."""
        params = params or {}
        params["api_key"] = self.api_key
        
        # Check cache first
        cached_response = self.cache.get_cached_response(endpoint, params)
        if cached_response is not None:
            return 200, cached_response
        
        async with self.semaphore:
            try:
                url = f"{self.base_url}/{endpoint.strip('/')}"
                
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        # Cache successful responses
                        self.cache.cache_response(endpoint, params, data)
                        return response.status, data
                    elif response.status == 429:
                        # Rate limit hit, wait and retry
                        retry_after = int(response.headers.get("Retry-After", 2))
                        logger.warning(f"TMDb rate limit hit, waiting {retry_after}s")
                        await asyncio.sleep(retry_after)
                        
                        # Retry once
                        async with session.get(url, params=params) as retry_response:
                            if retry_response.status == 200:
                                data = await retry_response.json()
                                self.cache.cache_response(endpoint, params, data)
                                return retry_response.status, data
                            else:
                                return retry_response.status, None
                    else:
                        logger.warning(f"TMDb API error {response.status} for {endpoint}")
                        return response.status, None
                        
            except asyncio.TimeoutError:
                logger.error(f"Timeout for TMDb request: {endpoint}")
                return 408, None
            except Exception as e:
                logger.error(f"TMDb request error for {endpoint}: {e}")
                return 500, None
    
    async def search_movies(self, session: aiohttp.ClientSession, query: str, 
                           year: Optional[int] = None) -> Tuple[int, List[Dict[str, Any]]]:
        """Search for movies with caching."""
        params = {"query": query, "language": "en-US"}
        if year:
            params["year"] = year
        
        status, data = await self.make_request(session, "search/movie", params)
        if status == 200 and data:
            return status, data.get("results", [])
        return status, []
    
    async def search_tv(self, session: aiohttp.ClientSession, query: str, 
                       year: Optional[int] = None) -> Tuple[int, List[Dict[str, Any]]]:
        """Search for TV shows with caching."""
        params = {"query": query, "language": "en-US"}
        if year:
            params["first_air_date_year"] = year
        
        status, data = await self.make_request(session, "search/tv", params)
        if status == 200 and data:
            return status, data.get("results", [])
        return status, []
    
    async def get_movie_details(self, session: aiohttp.ClientSession, 
                               movie_id: str) -> Tuple[int, Optional[Dict[str, Any]]]:
        """Get movie details with caching."""
        params = {"language": "en-US", "append_to_response": "external_ids,alternative_titles"}
        return await self.make_request(session, f"movie/{movie_id}", params)
    
    async def get_tv_details(self, session: aiohttp.ClientSession, 
                            tv_id: str) -> Tuple[int, Optional[Dict[str, Any]]]:
        """Get TV show details with caching."""
        params = {"language": "en-US", "append_to_response": "external_ids,alternative_titles"}
        return await self.make_request(session, f"tv/{tv_id}", params)
    
    async def get_alternative_titles(self, session: aiohttp.ClientSession, 
                                   content_id: str, content_type: str = "movie") -> Tuple[int, List[str]]:
        """Get alternative titles with caching."""
        endpoint = f"{content_type}/{content_id}/alternative_titles"
        status, data = await self.make_request(session, endpoint)
        
        if status == 200 and data:
            if content_type == "movie":
                titles = [alt.get("title") for alt in data.get("titles", []) if alt.get("title")]
            else:
                titles = [alt.get("title") for alt in data.get("results", []) if alt.get("title")]
            return status, titles
        
        return status, []


# Global cache instance
_global_cache: Optional[MetadataCache] = None
_global_client: Optional[ConcurrentTMDbClient] = None


def get_metadata_cache() -> MetadataCache:
    """Get global metadata cache instance."""
    global _global_cache
    if _global_cache is None:
        try:
            from _internal.utils.fuzzy_config import get_fuzzy_config
            config = get_fuzzy_config()
            ttl = config.get_cache_timeout_hours()
        except ImportError:
            ttl = 24
        
        _global_cache = MetadataCache(ttl_hours=ttl)
    return _global_cache


def get_tmdb_client(api_key: str) -> ConcurrentTMDbClient:
    """Get global TMDb client instance."""
    global _global_client
    if _global_client is None or _global_client.api_key != api_key:
        try:
            from _internal.utils.fuzzy_config import get_fuzzy_config
            config = get_fuzzy_config()
            concurrency = config.get_search_concurrency_limit()
        except ImportError:
            concurrency = 5
        
        cache = get_metadata_cache()
        _global_client = ConcurrentTMDbClient(api_key, concurrency, cache)
    return _global_client


def clear_global_cache():
    """Clear global cache (useful for testing)."""
    global _global_cache, _global_client
    if _global_cache:
        _global_cache.clear_expired()
    _global_cache = None
    _global_client = None
