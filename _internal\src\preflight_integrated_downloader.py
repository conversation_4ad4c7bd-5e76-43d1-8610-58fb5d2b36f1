#!/usr/bin/env python3
"""
Preflight Integration for Intelligent Fallback System

This module integrates the preflight analysis workflow with the new intelligent
fallback system. It ensures that:

1. Movies are added to Radarr without auto-monitoring 
2. User selections are tracked for fallback hierarchy
3. Downloads are monitored for failures
4. Fallback system is triggered when needed

Usage:
- Replace manual Radarr API calls with these integrated functions
- Ensures proper tracking and fallback behavior
"""

import logging
import asyncio
import aiohttp
from typing import Dict, Any, Optional, List
from datetime import datetime

from .download_failure_monitor import get_failure_monitor
from .intelligent_fallback_system import get_fallback_system
from .radarr_integration import RadarrClient
from ..utils.common_helpers import get_setting


class PreflightIntegratedDownloader:
    """
    Handles downloads with integrated preflight analysis and intelligent fallback.
    """
    
    def __init__(self, settings_dict: dict, logger: logging.Logger):
        self.settings = settings_dict
        self.logger = logger
        self.failure_monitor = get_failure_monitor(settings_dict, logger)
        self.fallback_system = get_fallback_system(settings_dict, logger)
        
        self.radarr_url = get_setting("Radarr", "url", settings_dict=settings_dict, default="http://localhost:7878")
        self.radarr_api_key = get_setting("Radarr", "api_key", settings_dict=settings_dict)
    
    async def download_user_selected_candidate(self, radarr_id: int, selected_candidate: Dict[str, Any], 
                                             candidates_list: List[Dict[str, Any]], 
                                             session: aiohttp.ClientSession) -> bool:
        """
        Download a user-selected candidate with intelligent fallback integration.
        
        Args:
            radarr_id: Radarr movie ID
            selected_candidate: The candidate chosen by the user
            candidates_list: Full list of acceptable candidates from preflight analysis
            session: HTTP session
            
        Returns:
            bool: True if download triggered successfully
        """
        try:
            # Find the index of the selected candidate in the list
            candidate_index = -1
            for i, candidate in enumerate(candidates_list):
                if candidate.get("guid") == selected_candidate.get("guid"):
                    candidate_index = i
                    break
            
            if candidate_index == -1:
                self.logger.error(f"Selected candidate not found in acceptable candidates list")
                return False
            
            # Record the user's selection for fallback hierarchy
            self.failure_monitor.record_user_selection(
                radarr_id=radarr_id,
                selected_candidate=selected_candidate,
                candidate_index=candidate_index
            )
            
            # Disable Radarr auto-monitoring to prevent interference
            await self.fallback_system.disable_radarr_auto_monitoring(radarr_id, session)
            
            # Trigger the download
            success = await self._trigger_download(radarr_id, selected_candidate, session)
            
            if success:
                # Record download start for monitoring
                self.failure_monitor.record_download_start(
                    radarr_id=radarr_id,
                    candidate=selected_candidate,
                    is_fallback=False
                )
                
                self.logger.info(f"✅ User-selected download triggered: {selected_candidate.get('title')}")
                self.logger.info(f"   📋 Fallback hierarchy: {len(candidates_list) - candidate_index - 1} alternatives available")
                
            return success
            
        except Exception as e:
            self.logger.error(f"Error downloading user-selected candidate: {e}")
            return False
    
    async def download_system_recommended_candidate(self, radarr_id: int, best_candidate: Dict[str, Any],
                                                  candidates_list: List[Dict[str, Any]],
                                                  session: aiohttp.ClientSession) -> bool:
        """
        Download the system-recommended best candidate with intelligent fallback integration.
        
        Args:
            radarr_id: Radarr movie ID
            best_candidate: The system-recommended best candidate
            candidates_list: Full list of acceptable candidates from preflight analysis
            session: HTTP session
            
        Returns:
            bool: True if download triggered successfully
        """
        try:
            # System recommendation uses index 0 (best candidate)
            self.logger.info(f"📊 Downloading system recommendation: {best_candidate.get('title')}")
            
            # Disable Radarr auto-monitoring to prevent interference
            await self.fallback_system.disable_radarr_auto_monitoring(radarr_id, session)
            
            # Trigger the download
            success = await self._trigger_download(radarr_id, best_candidate, session)
            
            if success:
                # Record download start for monitoring (no user selection recorded)
                self.failure_monitor.record_download_start(
                    radarr_id=radarr_id,
                    candidate=best_candidate,
                    is_fallback=False
                )
                
                self.logger.info(f"✅ System-recommended download triggered: {best_candidate.get('title')}")
                self.logger.info(f"   📋 Fallback hierarchy: {len(candidates_list) - 1} alternatives available")
                
            return success
            
        except Exception as e:
            self.logger.error(f"Error downloading system-recommended candidate: {e}")
            return False
    
    async def _trigger_download(self, radarr_id: int, candidate: Dict[str, Any], session: aiohttp.ClientSession) -> bool:
        """
        Internal method to trigger a download via Radarr API.
        
        Args:
            radarr_id: Radarr movie ID
            candidate: Candidate to download
            session: HTTP session
            
        Returns:
            bool: True if download triggered successfully
        """
        try:
            # Get indexer mapping
            indexer_mapping = await self._get_indexer_mapping(session)
            indexer_name = candidate.get("indexer", "")
            indexer_id = indexer_mapping.get(indexer_name)
            
            if not indexer_id:
                self.logger.error(f"Unknown indexer: {indexer_name}")
                return False
            
            # Prepare download payload
            download_payload = {
                'guid': candidate.get("guid"),
                'indexerId': indexer_id
            }
            
            self.logger.info(f"🚀 Triggering download via Radarr API")
            self.logger.info(f"   📡 Indexer: {indexer_name} (ID: {indexer_id})")
            self.logger.info(f"   📄 GUID: {candidate.get('guid')}")
            
            # Send download command to Radarr
            url = f"{self.radarr_url}/api/v3/release"
            headers = {'X-Api-Key': self.radarr_api_key}
            
            async with session.post(url, headers=headers, json=download_payload) as response:
                if response.status in (200, 201):
                    self.logger.info(f"✅ Download triggered successfully")
                    return True
                else:
                    error_text = await response.text()
                    self.logger.error(f"❌ Failed to trigger download: HTTP {response.status}")
                    self.logger.error(f"Response: {error_text}")
                    return False
                    
        except Exception as e:
            self.logger.error(f"Error triggering download: {e}")
            return False
    
    async def _get_indexer_mapping(self, session: aiohttp.ClientSession) -> Dict[str, int]:
        """Get mapping of indexer names to IDs from Radarr."""
        try:
            url = f"{self.radarr_url}/api/v3/indexer"
            headers = {'X-Api-Key': self.radarr_api_key}
            
            async with session.get(url, headers=headers) as response:
                if response.status == 200:
                    indexers = await response.json()
                    mapping = {}
                    for indexer in indexers:
                        name = indexer.get('name', '')
                        indexer_id = indexer.get('id')
                        if name and indexer_id:
                            mapping[name] = indexer_id
                    return mapping
                else:
                    self.logger.error(f"Failed to get indexers: HTTP {response.status}")
                    return {}
                    
        except Exception as e:
            self.logger.error(f"Error getting indexer mapping: {e}")
            return {}
    
    async def report_download_failure_external(self, radarr_id: int, failed_guid: str, failure_reason: str = "external_detection"):
        """
        External API to report download failures from other systems (like telemetry).
        
        Args:
            radarr_id: Radarr movie ID that failed
            failed_guid: GUID of the failed download
            failure_reason: Reason for the failure
        """
        await self.failure_monitor.report_download_failure(radarr_id, failed_guid, failure_reason)
    
    async def report_download_success_external(self, radarr_id: int, completed_guid: str):
        """
        External API to report download success from other systems.
        
        Args:
            radarr_id: Radarr movie ID that completed
            completed_guid: GUID of the completed download
        """
        self.failure_monitor.report_download_success(radarr_id, completed_guid)
    
    async def start_monitoring(self):
        """Start the download failure monitoring service."""
        self.logger.info("🔍 Starting integrated download monitoring")
        await self.failure_monitor.start_monitoring(check_interval=30)
    
    def stop_monitoring(self):
        """Stop the download failure monitoring service."""
        self.failure_monitor.stop_monitoring()
    
    def get_status(self) -> Dict[str, Any]:
        """Get current status of the integrated system."""
        return {
            "monitoring_active": self.failure_monitor.monitoring,
            "timestamp": datetime.now().isoformat(),
            **self.failure_monitor.get_monitoring_status()
        }


# Global instance for easy access
_integrated_downloader: Optional[PreflightIntegratedDownloader] = None

def get_integrated_downloader(settings_dict: dict, logger: logging.Logger) -> PreflightIntegratedDownloader:
    """Get or create the global integrated downloader instance."""
    global _integrated_downloader
    if _integrated_downloader is None:
        _integrated_downloader = PreflightIntegratedDownloader(settings_dict, logger)
    return _integrated_downloader


# Convenience functions for easy integration into existing code

async def download_with_fallback_protection(radarr_id: int, selected_candidate: Dict[str, Any], 
                                          candidates_list: List[Dict[str, Any]], 
                                          settings_dict: dict, logger: logging.Logger,
                                          is_user_selection: bool = True) -> bool:
    """
    Convenience function to download with fallback protection.
    
    Args:
        radarr_id: Radarr movie ID
        selected_candidate: The candidate to download
        candidates_list: Full list of acceptable candidates
        settings_dict: PlexAutomator settings
        logger: Logger instance
        is_user_selection: True if user manually selected, False if system recommendation
        
    Returns:
        bool: True if download triggered successfully
    """
    downloader = get_integrated_downloader(settings_dict, logger)
    
    async with aiohttp.ClientSession() as session:
        if is_user_selection:
            return await downloader.download_user_selected_candidate(
                radarr_id, selected_candidate, candidates_list, session
            )
        else:
            return await downloader.download_system_recommended_candidate(
                radarr_id, selected_candidate, candidates_list, session
            )


async def report_failure_from_telemetry(radarr_id: int, failed_guid: str, 
                                       settings_dict: dict, logger: logging.Logger):
    """
    Convenience function for telemetry system to report failures.
    
    Args:
        radarr_id: Radarr movie ID that failed
        failed_guid: GUID of failed download
        settings_dict: PlexAutomator settings
        logger: Logger instance
    """
    downloader = get_integrated_downloader(settings_dict, logger)
    await downloader.report_download_failure_external(radarr_id, failed_guid, "telemetry_detection")


async def report_success_from_telemetry(radarr_id: int, completed_guid: str,
                                       settings_dict: dict, logger: logging.Logger):
    """
    Convenience function for telemetry system to report successes.
    
    Args:
        radarr_id: Radarr movie ID that completed
        completed_guid: GUID of completed download
        settings_dict: PlexAutomator settings
        logger: Logger instance
    """
    downloader = get_integrated_downloader(settings_dict, logger)
    await downloader.report_download_success_external(radarr_id, completed_guid)
