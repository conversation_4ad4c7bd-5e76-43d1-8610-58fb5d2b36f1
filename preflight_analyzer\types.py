"""Typed protocol/structures to quiet type warnings for preflight analyzer and related selectors."""
from __future__ import annotations
from typing import TypedDict, List, Literal, Optional, Dict, Any

ContentType = Literal["movie", "tv_show"]

class EpisodeSelectorInput(TypedDict, total=False):
    series_id: int
    season: int
    episode: int
    title: str
    year: int
    content_type: ContentType

class ReleaseAnalysis(TypedDict, total=False):
    guid: str
    title: str
    season: int
    episode: int
    score: float
    size_mb: float
    accepted: bool
    rejection_reasons: List[str]

class SeasonPackCandidate(TypedDict, total=False):
    guid: str
    title: str
    season: int
    total_episodes: int
    accepted: bool
    score: float

class HybridPlan(TypedDict, total=False):
    strategy: Literal["episodes_only", "pack_only", "hybrid"]
    episodes: List[ReleaseAnalysis]
    season_pack: Optional[SeasonPackCandidate]
    acceptance_fraction: float
    threshold: float

class ReleaseMeta(TypedDict, total=False):
    guid: str
    indexer: str
    title: str
    size: int
    episode_id: int
    movie_id: int
    is_pack: bool
    decision: str
    error: str

class RadarrRelease(TypedDict, total=False):
    guid: str
    title: str
    size: int
    indexer: str
    indexerId: int
    quality: Dict[str, Any]
    protocol: str

class SonarrRelease(TypedDict, total=False):
    guid: str
    title: str
    size: int
    indexer: str
    indexerId: int
    quality: Dict[str, Any]
    protocol: str

class NewznabJsonChannel(TypedDict, total=False):
    item: List[Dict[str, Any]]

class NewznabJsonRoot(TypedDict, total=False):
    channel: NewznabJsonChannel

JsonDict = Dict[str, Any]

__all__ = [
    "ContentType",
    "EpisodeSelectorInput",
    "ReleaseAnalysis",
    "SeasonPackCandidate",
    "HybridPlan",
    "JsonDict",
]
