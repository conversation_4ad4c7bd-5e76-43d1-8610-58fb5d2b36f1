# Race Condition Fix - Implementation Summary

## Problem Identified
TV shows like Gravity Falls were being downloaded immediately during Sonarr addition, before the preflight analyzer could complete its analysis. This was caused by immediate search triggers in the monitoring configuration functions.

## Root Cause
The following functions were triggering immediate episode searches during Sonarr series addition:
1. `_configure_multiple_episodes_monitoring()` - Used for multiple episode requests
2. `_configure_specific_episode_monitoring()` - Used for single episode requests  
3. `_configure_specific_season_monitoring()` - Used for season requests

These searches caused Sonarr to immediately grab releases that met quality profile criteria, bypassing preflight analysis completely.

## Solution Implemented

### 1. Added Interactive Choice System
For each TV show, users now get prompted with three options:
- **Option 1**: 🔬 Preflight Analysis (recommended)
- **Option 2**: ⚡ Sonarr Auto-Grab (fast)
- **Option 3**: ⏭️ Skip Downloads (manual control)

### 2. Added Race Condition Prevention
- Added `disable_immediate_search: bool = False` parameter to all monitoring functions
- Modified all calls during Sonarr addition to use `disable_immediate_search=True`
- This prevents any searches from triggering before user choice

### 3. Function Modifications

#### `_configure_multiple_episodes_monitoring()`
- Added `disable_immediate_search` parameter
- Wrapped search triggers with condition: `if not disable_immediate_search:`
- Added skip message when searches are disabled

#### `_configure_specific_episode_monitoring()` 
- Added `disable_immediate_search` parameter
- Wrapped search triggers with condition: `if not disable_immediate_search:`
- Added skip message when searches are disabled

#### `_configure_specific_season_monitoring()`
- Added `disable_immediate_search` parameter  
- Wrapped search triggers with condition: `if not disable_immediate_search:`
- Added skip message when searches are disabled

### 4. Updated Function Calls
All calls to these functions during Sonarr addition now use `disable_immediate_search=True`:
- Line 2306: `_configure_multiple_episodes_monitoring(..., disable_immediate_search=True)`
- Line 2312: `_configure_multiple_episodes_monitoring(..., disable_immediate_search=True)`
- Line 2313: `_configure_specific_episode_monitoring(..., disable_immediate_search=True)`
- Line 2405: `_configure_multiple_episodes_monitoring(..., disable_immediate_search=True)`
- Line 2414: `_configure_specific_episode_monitoring(..., disable_immediate_search=True)`
- Line 2444: `_configure_multiple_episodes_monitoring(..., disable_immediate_search=True)`
- Line 2449: `_configure_specific_season_monitoring(..., disable_immediate_search=True)`
- Line 2453: `_configure_specific_season_monitoring(..., disable_immediate_search=True)`

## Expected Behavior Now

### Before Fix:
1. User selects TV show
2. Show gets added to Sonarr  
3. **Immediate searches triggered** ⚠️
4. Episodes start downloading immediately
5. Preflight analysis starts (too late)

### After Fix:
1. User selects TV show
2. Show gets added to Sonarr (no immediate searches)
3. **User gets choice prompt** 🎯
4. If "Preflight Analysis" selected:
   - Preflight analysis runs first
   - Only acceptable releases are downloaded
5. If "Auto-Grab" selected:
   - Immediate monitoring enabled
   - Sonarr searches and grabs based on quality profiles
6. If "Skip" selected:
   - No downloads start, manual control retained

## Test Results Expected
- Gravity Falls should no longer download before choice prompt appears
- User should see the interactive choice for each show
- Preflight analysis should complete before any downloads when option 1 is selected
- Option 2 should work like the old behavior for users who want fast grabbing
- Option 3 should allow complete manual control

## Files Modified
- `01_intake_and_nzb_search.py` - Main changes to prevent race condition and add interactive choice
- `docs/interactive_download_strategy.md` - Documentation of new feature
