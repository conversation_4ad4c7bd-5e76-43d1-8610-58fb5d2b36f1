# Example Telemetry Dashboard Configuration
# Add these settings to your _internal/config/settings.ini

[TELEMETRY]
# Display mode: False = Dashboard (clean), True = Verbose (detailed)
verbose_mode = False

[SEARCH]
# Maximum number of release candidates to analyze per movie/show
max_candidates = 25

# Enable automatic quality fallback (4K -> 1080p when no 4K available)
enable_quality_fallback = True

# Dashboard Display Example:
# ┌─ DOWNLOAD PROGRESS DASHBOARD ─┐
# │ There Will Be Blood    │ 38.3% │ 47.1 GB │ ETA: 09:17 │
# │ Star Trek Into Dark.   │  0.0% │ 55.4 GB │ ETA: 26:59 │
# │ The Dark Knight        │  0.0% │ 33.6 GB │ ETA: 37:44 │
# ├─ VERIFICATION QUEUE ─────────────────────────────────┤
# │ Don't Breathe          │ Pending (attempt 0/6)    │
# │ Top Gun: Maverick      │ Pending (attempt 0/6)    │
# └───────────────────────────────────────────────────────┘

# Verbose Display Example:
# RADARR | ⏳ Downloading: There Will Be Blood (2007) — 38.3% of 47.1 GB at 0 B/s (ETA 0:09:17)
# [JSON telemetry data]
# RADARR | ⏳ Downloading: Star Trek Into Darkness (2013) — 0.0% of 55.4 GB at 0 B/s (ETA 0:26:59)
# [JSON telemetry data]
