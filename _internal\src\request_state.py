#!/usr/bin/env python3
from __future__ import annotations

import sqlite3
from dataclasses import dataclass
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime, timezone
import json
import threading
import os

DB_DIR = Path("_internal/state")
_env_db_path = os.environ.get('REQUEST_STATE_DB')
DB_PATH = Path(_env_db_path) if _env_db_path else (DB_DIR / "request_state.sqlite3")

_thread_local = threading.local()


def _utc_now() -> str:
    return datetime.now(timezone.utc).isoformat()


def _get_conn() -> sqlite3.Connection:
    if not hasattr(_thread_local, "conn"):
        DB_DIR.mkdir(parents=True, exist_ok=True)
        conn = sqlite3.connect(str(DB_PATH))
        conn.row_factory = sqlite3.Row
        conn.execute("PRAGMA foreign_keys = ON")
        _thread_local.conn = conn
    return _thread_local.conn  # type: ignore[attr-defined]


def init_db() -> None:
    conn = _get_conn()
    conn.executescript(
        """
        CREATE TABLE IF NOT EXISTS request_groups (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            type TEXT NOT NULL,              -- 'movie' | 'tv'
            title TEXT NOT NULL,
            year INTEGER,
            created_at TEXT NOT NULL,
            organized INTEGER NOT NULL DEFAULT 0,
            metadata TEXT
        );

        CREATE TABLE IF NOT EXISTS child_items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            group_id INTEGER NOT NULL,
            type TEXT NOT NULL,              -- 'movie' | 'episode'
            season INTEGER,
            episode INTEGER,
            title TEXT,
            arr_id INTEGER,
            status TEXT NOT NULL DEFAULT 'QUEUED',  -- QUEUED|DOWNLOADING|COMPLETE|FAILED
            from_pack INTEGER NOT NULL DEFAULT 0,
            download_id TEXT,
            library_path TEXT,
            updated_at TEXT NOT NULL,
            UNIQUE(group_id, type, season, episode),
            FOREIGN KEY(group_id) REFERENCES request_groups(id) ON DELETE CASCADE
        );

        CREATE INDEX IF NOT EXISTS idx_groups_title ON request_groups(title, year);
        CREATE INDEX IF NOT EXISTS idx_child_by_group ON child_items(group_id);
        """
    )
    conn.commit()


@dataclass
class RequestGroup:
    id: int
    type: str
    title: str
    year: Optional[int]
    organized: bool
    metadata: Dict[str, Any]


@dataclass
class ChildItem:
    id: int
    group_id: int
    type: str
    season: Optional[int]
    episode: Optional[int]
    title: Optional[str]
    arr_id: Optional[int]
    status: str
    from_pack: bool
    download_id: Optional[str]
    library_path: Optional[str]


def create_request_group(group_type: str, title: str, year: Optional[int] = None, metadata: Optional[Dict[str, Any]] = None) -> int:
    init_db()
    conn = _get_conn()
    cur = conn.cursor()
    cur.execute(
        "INSERT INTO request_groups(type, title, year, created_at, organized, metadata) VALUES(?,?,?,?,?,?)",
        (group_type, title, year, _utc_now(), 0, json.dumps(metadata or {}, ensure_ascii=False)),
    )
    conn.commit()
    return int(cur.lastrowid)


def get_or_create_group(group_type: str, title: str, year: Optional[int] = None, metadata: Optional[Dict[str, Any]] = None) -> int:
    init_db()
    conn = _get_conn()
    cur = conn.execute("SELECT id FROM request_groups WHERE type=? AND title=? AND IFNULL(year,'')=IFNULL(?, '') ORDER BY id DESC LIMIT 1", (group_type, title, year))
    row = cur.fetchone()
    if row:
        return int(row[0])
    return create_request_group(group_type, title, year, metadata)


def add_child_item(group_id: int, item_type: str, season: Optional[int] = None, episode: Optional[int] = None, title: Optional[str] = None, arr_id: Optional[int] = None, from_pack: bool = False) -> int:
    init_db()
    conn = _get_conn()
    cur = conn.cursor()
    cur.execute(
        """
        INSERT OR IGNORE INTO child_items(group_id, type, season, episode, title, arr_id, status, from_pack, updated_at)
        VALUES(?,?,?,?,?,?, 'QUEUED', ?, ?)
        """,
        (group_id, item_type, season, episode, title, arr_id, 1 if from_pack else 0, _utc_now()),
    )
    conn.commit()
    # Return existing or inserted id
    cur = conn.execute(
        "SELECT id FROM child_items WHERE group_id=? AND type=? AND IFNULL(season,'')=IFNULL(?, '') AND IFNULL(episode,'')=IFNULL(?, '')",
        (group_id, item_type, season, episode),
    )
    row = cur.fetchone()
    return int(row[0]) if row else -1


def set_child_status(group_id: int, season: Optional[int], episode: Optional[int], status: str, library_path: Optional[str] = None, from_pack: Optional[bool] = None) -> None:
    init_db()
    conn = _get_conn()
    fields = ["status = ?", "updated_at = ?"]
    params: List[Any] = [status, _utc_now()]
    if library_path is not None:
        fields.append("library_path = ?")
        params.append(library_path)
    if from_pack is not None:
        fields.append("from_pack = ?")
        params.append(1 if from_pack else 0)
    params.extend([group_id, season, episode])
    conn.execute(
        f"UPDATE child_items SET {', '.join(fields)} WHERE group_id=? AND IFNULL(season,'')=IFNULL(?, '') AND IFNULL(episode,'')=IFNULL(?, '')",
        params,
    )
    conn.commit()


def all_children_terminal(group_id: int) -> bool:
    init_db()
    conn = _get_conn()
    cur = conn.execute("SELECT COUNT(*) FROM child_items WHERE group_id=? AND status NOT IN ('COMPLETE','FAILED')", (group_id,))
    remaining = int(cur.fetchone()[0])
    return remaining == 0


def mark_group_organized(group_id: int) -> None:
    init_db()
    conn = _get_conn()
    conn.execute("UPDATE request_groups SET organized=1 WHERE id=?", (group_id,))
    conn.commit()


def find_group_by_title(group_type: str, title: str, year: Optional[int]) -> Optional[int]:
    init_db()
    conn = _get_conn()
    cur = conn.execute("SELECT id FROM request_groups WHERE type=? AND title=? AND IFNULL(year,'')=IFNULL(?, '') ORDER BY id DESC LIMIT 1", (group_type, title, year))
    row = cur.fetchone()
    return int(row[0]) if row else None


def pending_groups() -> List[RequestGroup]:
    """Return groups that are not yet marked organized."""
    init_db()
    conn = _get_conn()
    cur = conn.execute("SELECT id, type, title, year, organized, metadata FROM request_groups WHERE organized=0 ORDER BY id DESC")
    out: List[RequestGroup] = []
    for r in cur.fetchall():
        out.append(RequestGroup(
            id=int(r[0]), type=str(r[1]), title=str(r[2]), year=(int(r[3]) if r[3] is not None else None), organized=bool(r[4]), metadata=json.loads(r[5] or '{}')
        ))
    return out


def list_children(group_id: int) -> List[ChildItem]:
    init_db()
    conn = _get_conn()
    cur = conn.execute("SELECT id, group_id, type, season, episode, title, arr_id, status, from_pack, download_id, library_path FROM child_items WHERE group_id=? ORDER BY season, episode", (group_id,))
    out: List[ChildItem] = []
    for r in cur.fetchall():
        out.append(ChildItem(
            id=int(r[0]), group_id=int(r[1]), type=str(r[2]), season=(int(r[3]) if r[3] is not None else None), episode=(int(r[4]) if r[4] is not None else None),
            title=(str(r[5]) if r[5] is not None else None), arr_id=(int(r[6]) if r[6] is not None else None), status=str(r[7]), from_pack=bool(r[8]),
            download_id=(str(r[9]) if r[9] is not None else None), library_path=(str(r[10]) if r[10] is not None else None)
        ))
    return out


# Convenience helpers used by organizer

def mark_movie_complete(title: str, year: Optional[int], library_path: str) -> None:
    gid = find_group_by_title('movie', title, year)
    if gid is None:
        gid = get_or_create_group('movie', title, year)
        add_child_item(gid, 'movie', None, None, title=title)
    set_child_status(gid, None, None, 'COMPLETE', library_path=library_path)
    if all_children_terminal(gid):
        mark_group_organized(gid)


def mark_episode_complete(series_title: str, year: Optional[int], season: int, episode: int, library_path: str) -> None:
    gid = find_group_by_title('tv', series_title, year)
    if gid is None:
        gid = get_or_create_group('tv', series_title, year)
    add_child_item(gid, 'episode', season=season, episode=episode, title=series_title)
    set_child_status(gid, season, episode, 'COMPLETE', library_path=library_path)
    # Do not auto-mark organized here; organizing of a season may be deferred by caller if batching


def mark_episode_failed(series_title: str, year: Optional[int], season: int, episode: int) -> None:
    gid = find_group_by_title('tv', series_title, year)
    if gid is None:
        gid = get_or_create_group('tv', series_title, year)
    set_child_status(gid, season, episode, 'FAILED')


def mark_group_if_complete(series_title: str, year: Optional[int]) -> bool:
    gid = find_group_by_title('tv', series_title, year)
    if gid is None:
        return False
    if all_children_terminal(gid):
        mark_group_organized(gid)
        return True
    return False


def close() -> None:
    """Close thread-local connection (for tests)."""
    try:
        if hasattr(_thread_local, 'conn') and _thread_local.conn:
            _thread_local.conn.close()
            delattr(_thread_local, 'conn')
    except Exception:
        pass

