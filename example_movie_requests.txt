# Example Movie Requests File
# PlexMovieAutomator - Script 01 Intake Format Examples
# 
# MOVIE FORMAT: "Movie Title (Year)"
# - Movies use a simple title and year format
# - Year-based quality selection automatically applied:
#   • ≤2009: 1080p only (largest available)
#   • 2010-2015: BOTH 1080p AND 4K (downloads both)
#   • 2016+: 4K only (largest available)

# --- CLASSIC MOVIES (≤2009) - Will get 1080p only ---
The Dark Knight (2008)
Iron Man (2008)
WALL-E (2008)
Slumdog Millionaire (2008)
The Departed (2006)
Casino Royale (2006)
Pirates of the Caribbean: Dead Man's Chest (2006)
The Bourne Ultimatum (2007)

# --- TRANSITIONAL MOVIES (2010-2015) - Will get BOTH 1080p AND 4K ---
Inception (2010)
The Avengers (2012)
Interstellar (2014)
Mad Max: Fury Road (2015)
The Martian (2015)
Guardians of the Galaxy (2014)
Captain America: The Winter Soldier (2014)
Gravity (2013)

# --- MODERN MOVIES (2016+) - Will get 4K only ---
Avengers: Endgame (2019)
Top Gun: Maverick (2022)
<PERSON>ne (2021)
Spider-Man: No Way Home (2021)
The Batman (2022)
Avatar: The Way of Water (2022)
Black Panther: Wakanda Forever (2022)
Everything Everywhere All at Once (2022)

# --- SPECIAL CASES ---
# Movies with complex titles
The Lord of the Rings: The Fellowship of the Ring (2001)
Pirates of the Caribbean: The Curse of the Black Pearl (2003)
Harry Potter and the Philosopher's Stone (2001)
The Fast and the Furious (2001)

# Movies with numbers in titles
2001: A Space Odyssey (1968)
12 Monkeys (1995)
21 Jump Street (2012)
300 (2006)

# Foreign films
Parasite (2019)
Amélie (2001)
Spirited Away (2001)
The Grand Budapest Hotel (2014)
