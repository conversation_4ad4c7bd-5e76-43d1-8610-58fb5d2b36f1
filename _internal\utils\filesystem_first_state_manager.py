#!/usr/bin/env python3
"""
Filesystem-First State Management System for PlexMovieAutomator.

This implements the new architecture where:
- Filesystem is the single source of truth for pipeline state
- Database stores only static metadata (TMDB ID, user preferences, etc.)
- Marker files track processing state
- All scripts use filesystem scanning to discover work

Key principles:
1. Filesystem scanning as state: Scripts discover movies by scanning directories
2. Marker files for progress: Hidden marker files indicate processing stages
3. Idempotent operations: Safe to retry after interruptions
4. Database for metadata only: No status or path information stored in DB
"""

import sqlite3
import json
import logging
import shutil
import os
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timezone
import re
import threading
from contextlib import contextmanager

logger = logging.getLogger(__name__)


class FilesystemFirstStateManager:
    """
    Filesystem-first state management using marker files and directory scanning.
    
    This class provides:
    - File-based discovery of movies by scanning workspace directories
    - Marker file management for tracking processing stages
    - Idempotent operations that can recover from interruptions
    - Auto-detection of interrupted work for resumption
    """
    
    # Standard marker files for tracking processing stages
    MARKERS = {
        'organized': '.organized',
        'mkv_processing': '.mkv_processing',
        'mkv_complete': '.mkv_complete',
        'encoded': '.encoded',
        'subtitle_processing_pending': '.subtitle_processing_pending',
        'subtitle_processing': '.subtitle_processing',
        'subtitle_complete': '.subtitle_complete',
        'muxed': '.muxed',
        'final_mux_complete': '.final_mux_complete',
        'error': '.error'
    }
    
    def __init__(self, workspace_root: Path):
        self.workspace_root = Path(workspace_root)
        
        # Define workspace directories for each stage  
        self.stage_directories = {
            'new_requests': self.workspace_root / "workspace" / "0_new_requests",
            'downloading': self.workspace_root / "workspace" / "1_downloading", 
            'download_raw': self.workspace_root / "workspace" / "1_downloading" / "complete_raw",
            'organized': self.workspace_root / "workspace" / "2_downloaded_and_organized",
            'mkv_processed': self.workspace_root / "workspace" / "3_mkv_cleaned_subtitles_extracted",
            'ready_for_mux': self.workspace_root / "workspace" / "4_ready_for_final_mux",
            'awaiting_poster': self.workspace_root / "workspace" / "5_awaiting_poster",
            'final_plex_ready': self.workspace_root / "workspace" / "6_final_plex_ready",
            'issues_hold': self.workspace_root / "workspace" / "issues_hold",
            'temp_backup': self.workspace_root / "workspace" / "temp_original_backup"
        }
        
        # Create directories if they don't exist
        for directory in self.stage_directories.values():
            directory.mkdir(parents=True, exist_ok=True)
    
    def discover_movies_by_stage(self) -> Dict[str, List[Dict[str, Any]]]:
        """
        Discover movies at each pipeline stage by scanning filesystem.
        Returns movies organized by their current stage.
        Supports both legacy structure and new movies/tv_shows structure.
        """
        movies = {
            'download_completed': [],
            'organized': [],
            'mkv_processing_pending': [],
            'mkv_processing_active': [],
            'mkv_processing_interrupted': [],
            'mkv_processing_complete': [],
            'video_encoding_pending': [],
            'subtitle_processing_pending': [],
            'subtitle_processing_active': [],
            'subtitle_processing_complete': [],
            'final_mux_pending': [],
            'final_mux_complete': [],
            'completed': [],
            'error': []
        }
        
        # 1. Scan for completed downloads (ready for organization)
        if self.stage_directories['download_raw'].exists():
            for item in self.stage_directories['download_raw'].iterdir():
                if item.is_dir():
                    movie_info = self._analyze_download_folder(item)
                    if movie_info:
                        movies['download_completed'].append(movie_info)
        
        # 2. Scan organized directory and its resolution subfolders
        # Handle both new structure (movies/tv_shows) and legacy structure
        if self.stage_directories['organized'].exists():
            self._scan_content_directory(self.stage_directories['organized'], movies)
        
        # 3. Scan other processing directories
        for stage_name, directory in self.stage_directories.items():
            if stage_name in ['organized', 'download_raw', 'temp_backup']:
                continue  # Already scanned above or excluded (temp_backup is not a processing stage)
                
            if directory.exists():
                self._scan_content_directory(directory, movies, stage_name)
        
        return movies

    def discover_tv_episodes_by_stage(self) -> Dict[str, List[Dict[str, Any]]]:
        """
        Discover TV episodes at each pipeline stage by scanning filesystem.
        Episodes are detected under workspace/2_downloaded_and_organized/tv_shows/<resolution>/<Series (Year)>/Season XX/*.mkv

        Returns a dict keyed by stage names similar to movies, but entries contain episode metadata:
        {
          'organized': [ { series_title, season_number, episode_number, episode_directory, main_episode_file, ... } ],
          'mkv_processing_pending': [ ... ],
          'mkv_processing_interrupted': [ ... ],
          'final_mux_pending': [ ... ],
          'error': [ ... ]
        }
        """
        episodes: Dict[str, List[Dict[str, Any]]] = {
            'organized': [],
            'mkv_processing_pending': [],
            'mkv_processing_active': [],
            'mkv_processing_interrupted': [],
            'mkv_processing_complete': [],
            'final_mux_pending': [],
            'final_mux_complete': [],
            'completed': [],
            'error': []
        }

        organized_root = self.stage_directories['organized']
        if not organized_root.exists():
            return episodes

        # Look for new-structure tv shows: 2_downloaded_and_organized/tv_shows/<resolution>/<Series (Year)>/Season XX/*.mkv
        tv_root = organized_root / 'tv_shows'
        if not tv_root.exists():
            return episodes

        # Helper: parse season and episode from filename
        import re as _re
        sxe_pattern = _re.compile(r'[Ss](\d{1,2})[ ._-]*[Ee](\d{1,2})')

        for resolution_dir in tv_root.iterdir():
            if not resolution_dir.is_dir():
                continue
            for series_dir in resolution_dir.iterdir():
                if not series_dir.is_dir():
                    continue
                series_title = series_dir.name

                # Season folders
                for season_dir in series_dir.iterdir():
                    if not season_dir.is_dir():
                        continue

                    # Determine season number if possible
                    season_number = None
                    m = _re.search(r'(?:Season|S)\s*(\d{1,2})', season_dir.name, _re.IGNORECASE)
                    if m:
                        try:
                            season_number = int(m.group(1))
                        except Exception:
                            season_number = None

                    # Enumerate episode files
                    for ep_file in season_dir.glob('*.mkv'):
                        # Skip already processed V/A mkv (we want source episodes)
                        if 'processed' in ep_file.name.lower():
                            continue

                        # Parse SxxExx
                        episode_number = None
                        m2 = sxe_pattern.search(ep_file.name)
                        if m2:
                            try:
                                season_from_name = int(m2.group(1))
                                episode_number = int(m2.group(2))
                                if season_number is None:
                                    season_number = season_from_name
                            except Exception:
                                pass

                        # Determine current stage using markers on the season directory (coarse but effective)
                        stage = 'organized'
                        if (season_dir / self.MARKERS['error']).exists():
                            stage = 'error'
                        elif (season_dir / self.MARKERS['final_mux_complete']).exists():
                            stage = 'final_mux_complete'
                        elif (season_dir / self.MARKERS['subtitle_complete']).exists():
                            stage = 'final_mux_pending'
                        elif (season_dir / self.MARKERS['mkv_complete']).exists():
                            stage = 'final_mux_pending'  # Next after mkv complete
                        elif (season_dir / self.MARKERS['mkv_processing']).exists():
                            stage = 'mkv_processing_interrupted'
                        elif (season_dir / self.MARKERS['organized']).exists():
                            stage = 'mkv_processing_pending'

                        episode_info: Dict[str, Any] = {
                            'unique_id': f"{series_title}_S{(season_number or 0):02d}E{(episode_number or 0):02d}_{ep_file.stem}",
                            'series_title': series_title,
                            'season_number': season_number or 0,
                            'episode_number': episode_number or 0,
                            'episode_title': ep_file.stem,
                            'episode_directory': str(season_dir),
                            'main_episode_file': str(ep_file),
                            'current_stage': stage,
                            'discovered_at': datetime.now().isoformat(),
                            'content_type': 'tv_show',
                        }

                        if stage in episodes:
                            episodes[stage].append(episode_info)
                        else:
                            episodes['organized'].append(episode_info)

        return episodes
    
    def _scan_content_directory(self, directory: Path, movies: Dict[str, List[Dict[str, Any]]], stage_name: Optional[str] = None):
        """
        Scan a directory that may contain movies/tv_shows subdirectories or direct content.
        Handles both new structure (movies/tv_shows/resolution) and legacy structure (resolution only).
        """
        for item in directory.iterdir():
            if not item.is_dir():
                continue
                
            # Check if this is a content type folder (movies or tv_shows)
            if item.name in ['movies', 'tv_shows']:
                # New structure: content_type/resolution/content_folders
                self._scan_content_type_directory(item, movies, stage_name)
            else:
                # Could be legacy structure (resolution directly) or direct content folder
                if any(res in item.name.lower() for res in ['1080p', '4k', 'sd_or_unknown', '720p', '2160p']):
                    # This is a resolution folder in legacy structure
                    self._scan_resolution_directory(item, movies, stage_name)
                else:
                    # This might be a movie/TV show folder directly in the stage directory
                    movie_info = self._analyze_movie_directory(item)
                    if movie_info:
                        stage = self._determine_movie_stage(item)
                        if stage in movies:
                            movies[stage].append(movie_info)
    
    def _scan_content_type_directory(self, content_type_dir: Path, movies: Dict[str, List[Dict[str, Any]]], stage_name: Optional[str] = None):
        """
        Scan a content type directory (movies or tv_shows) for resolution subdirectories.
        """
        for item in content_type_dir.iterdir():
            if not item.is_dir():
                continue
                
            # Check if this is a resolution folder
            if any(res in item.name.lower() for res in ['1080p', '4k', 'sd_or_unknown', '720p', '2160p']):
                self._scan_resolution_directory(item, movies, stage_name, content_type_dir.name)
            else:
                # Direct content folder (shouldn't happen in new structure but handle gracefully)
                movie_info = self._analyze_movie_directory(item)
                if movie_info:
                    # Add content type information
                    movie_info['content_type'] = content_type_dir.name.rstrip('s')  # 'movies' -> 'movie', 'tv_shows' -> 'tv_show'
                    stage = self._determine_movie_stage(item)
                    if stage in movies:
                        movies[stage].append(movie_info)
    
    def _scan_resolution_directory(self, resolution_dir: Path, movies: Dict[str, List[Dict[str, Any]]], stage_name: Optional[str] = None, content_type: Optional[str] = None):
        """
        Scan a resolution directory for movie/TV show folders.
        """
        for movie_dir in resolution_dir.iterdir():
            if not movie_dir.is_dir():
                continue
                
            movie_info = self._analyze_movie_directory(movie_dir)
            if movie_info:
                # Add content type if known
                if content_type:
                    movie_info['content_type'] = content_type.rstrip('s')  # 'movies' -> 'movie', 'tv_shows' -> 'tv_show'
                
                # Add resolution based on parent directory name
                movie_info['resolution'] = resolution_dir.name
                
                stage = self._determine_movie_stage(movie_dir)
                # Map directory-based stage to our stage names, but trust marker-based stage determination
                if stage_name == 'completed' and stage != 'error':
                    movies['completed'].append(movie_info)
                elif stage_name in ['issues_hold', 'issues']:
                    # Override for error directories regardless of markers
                    movies['error'].append(movie_info)
                elif stage in movies:
                    # Trust the marker-based stage determination
                    movies[stage].append(movie_info)
    
    def _analyze_download_folder(self, folder_path: Path) -> Optional[Dict[str, Any]]:
        """Analyze a download folder to extract movie information."""
        try:
            movie_info = self._extract_movie_info_from_filename(folder_path.name)
            movie_info.update({
                'download_folder': str(folder_path),
                'stage': 'download_completed',
                'discovered_at': datetime.now().isoformat()
            })
            return movie_info
            
        except Exception as e:
            logger.warning(f"Error analyzing download folder {folder_path}: {e}")
            return None
    
    def _analyze_movie_directory(self, movie_dir: Path) -> Optional[Dict[str, Any]]:
        """Analyze a movie directory in any processing stage."""
        try:
            movie_info = self._extract_movie_info_from_filename(movie_dir.name)
            
            # Find main movie file using recursive search to handle nested folders
            from _internal.src.fs_helpers import find_video_files
            video_files = find_video_files(movie_dir)
            
            if video_files:
                # Choose the largest video file as the main movie file
                main_file = max(video_files, key=lambda f: f.stat().st_size if f.exists() else 0)
                movie_info['main_movie_file'] = str(main_file)
            
            # Check for processed content
            processed_dir = movie_dir / "_Processed_VideoAudio"
            if processed_dir.exists():
                movie_info['processed_content'] = str(processed_dir)
            
            movie_info.update({
                'movie_directory': str(movie_dir),
                'discovered_at': datetime.now().isoformat()
            })
            
            return movie_info
            
        except Exception as e:
            logger.warning(f"Error analyzing movie directory {movie_dir}: {e}")
            return None
    
    def determine_movie_stage(self, movie_dir: Path) -> str:
        """Public method to determine the current stage of a movie based on marker files and content."""
        return self._determine_movie_stage(movie_dir)
    
    def _determine_movie_stage(self, movie_dir: Path) -> str:
        """Determine the current stage of a movie based on marker files and content."""
        
        # Check for error marker first
        if (movie_dir / self.MARKERS['error']).exists():
            return 'error'
        
        # Check for completion markers in reverse pipeline order
        if (movie_dir / self.MARKERS['final_mux_complete']).exists():
            return 'final_mux_complete'

        if (movie_dir / self.MARKERS['muxed']).exists():
            return 'ready_for_qc'

        if (movie_dir / self.MARKERS['subtitle_complete']).exists():
            return 'final_mux_pending'

        if (movie_dir / self.MARKERS['subtitle_processing']).exists():
            return 'subtitle_processing_active'

        if (movie_dir / self.MARKERS['subtitle_processing_pending']).exists():
            return 'subtitle_processing_pending'

        if (movie_dir / self.MARKERS['encoded']).exists():
            return 'subtitle_processing_pending'

        if (movie_dir / self.MARKERS['mkv_complete']).exists():
            return 'video_encoding_pending'
        
        if (movie_dir / self.MARKERS['mkv_processing']).exists():
            return 'mkv_processing_interrupted'  # Was processing but stopped
        
        if (movie_dir / self.MARKERS['organized']).exists():
            return 'mkv_processing_pending'
        
        # Check based on content if no markers exist
        if (movie_dir / "_Processed_VideoAudio").exists():
            return 'mkv_processing_complete'  # Has processed content, ready for next step
        
        # Has main movie file but no processing done
        if list(movie_dir.glob("*.mkv")):
            return 'organized'
        
        return 'unknown'
    
    def _extract_movie_info_from_filename(self, filename: str) -> Dict[str, Any]:
        """Extract movie title and year from filename."""
        # Try to extract year in parentheses
        year_match = re.search(r'\((\d{4})\)', filename)
        year = int(year_match.group(1)) if year_match else None
        
        # Extract title (everything before the year)
        if year_match:
            title = filename[:year_match.start()].strip()
        else:
            title = filename
        
        # Clean up common artifacts
        title = re.sub(r'\[.*?\]', '', title).strip()  # Remove [tags]
        title = re.sub(r'\{.*?\}', '', title).strip()  # Remove {tags}
        title = re.sub(r'\s+', ' ', title).strip()     # Normalize whitespace
        
        return {
            'title': title,
            'cleaned_title': title,  # Keep for backward compatibility
            'year': year,
            'original_filename': filename
        }
    
    def set_stage_marker(self, movie_dir: Path, stage: str, data: Optional[Dict[str, Any]] = None):
        """Set a stage marker file for a movie or TV episode with episode-aware tracking and request context."""
        if stage not in self.MARKERS:
            logger.warning(f"Unknown stage marker: {stage}")
            return
        
        marker_file = movie_dir / self.MARKERS[stage]
        
        try:
            # Detect if this is a TV show episode by checking directory structure
            is_tv_episode = self._is_tv_episode_directory(movie_dir)
            
            if is_tv_episode:
                # Check for request context to determine tracking approach
                try:
                    from .request_context_manager import get_request_context_manager
                    
                    # Try to find the season directory for context
                    season_dir = self._find_season_directory(movie_dir)
                    if season_dir:
                        context_mgr = get_request_context_manager(season_dir)
                        
                        if context_mgr.has_context():
                            context = context_mgr.get_context()
                            
                            # Enhanced episode-aware marker with request context
                            result = self._set_context_aware_episode_marker(movie_dir, stage, data, context)
                            return result
                    
                except ImportError:
                    # RequestContextManager not available, fall back
                    pass
                except Exception as e:
                    logger.warning(f"Error checking request context for {movie_dir}: {e}")
                
                # Fallback to existing episode-aware system for key stages
                if stage in ['organized', 'mkv_complete', 'plex_deployed']:
                    self._set_episode_aware_marker(movie_dir, stage, data)
                    return True
            
            # Standard marker for movies or non-episode stages
            marker_content = {
                'timestamp': datetime.now().isoformat(),
                'stage': stage
            }
            if data:
                marker_content.update(data)
            
            with open(marker_file, 'w') as f:
                json.dump(marker_content, f, indent=2)
            
            logger.debug(f"Set marker {stage} for {movie_dir.name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to set marker {stage} for {movie_dir}: {e}")
            return False
    
    def _is_tv_episode_directory(self, path: Path) -> bool:
        """
        Detect if a directory represents a TV episode by checking path structure.
        
        TV episode paths typically look like:
        /tv_shows/1080p/Series Name (Year)/Season XX/episode_file.mkv
        """
        try:
            path_parts = path.parts
            # Look for "tv_shows" in path and "Season" folder structure
            return (
                'tv_shows' in path_parts and
                any(part.startswith('Season ') for part in path_parts)
            )
        except Exception:
            return False
    
    def _set_episode_aware_marker(self, episode_dir: Path, stage: str, data: Optional[Dict[str, Any]] = None):
        """
        Set episode-aware marker that tracks specific episodes within seasons.
        """
        try:
            from _internal.utils.episode_marker_manager import get_episode_marker_manager
            
            # Extract episode information from data or directory structure
            season_num = None
            episode_num = None
            
            if data and 'episode_info' in data:
                episode_info = data['episode_info']
                season_num = episode_info.get('season_number')
                episode_num = episode_info.get('episode_number')
            
            # Fallback: extract from directory name or parent directory
            if not (season_num and episode_num):
                season_num, episode_num = self._extract_episode_info_from_path(episode_dir)
            
            if season_num and episode_num:
                # Use episode-aware marker system
                marker_name = f".{stage}"
                marker_manager = get_episode_marker_manager(episode_dir, marker_name)
                
                success = marker_manager.add_episode(season_num, episode_num, logger)
                if not success:
                    # Fallback to simple marker
                    self._create_simple_marker(episode_dir, stage, data)
            else:
                # Can't determine episode info, use simple marker
                logger.warning(f"Could not determine episode info for {episode_dir}, using simple marker")
                self._create_simple_marker(episode_dir, stage, data)
                
        except Exception as e:
            logger.warning(f"Episode-aware marker failed for {episode_dir}, using simple marker: {e}")
            self._create_simple_marker(episode_dir, stage, data)
    
    def _extract_episode_info_from_path(self, episode_dir: Path) -> tuple[Optional[int], Optional[int]]:
        """
        Extract season and episode numbers from directory path or filenames.
        """
        try:
            import re
            
            # Check parent directory for Season info
            season_num = None
            for parent in episode_dir.parents:
                if parent.name.startswith('Season '):
                    match = re.search(r'Season (\d+)', parent.name)
                    if match:
                        season_num = int(match.group(1))
                        break
            
            # Look for episode files to extract episode number
            episode_num = None
            for file_path in episode_dir.glob('*.mkv'):
                match = re.search(r'S(\d+)E(\d+)', file_path.name)
                if match:
                    season_num = season_num or int(match.group(1))
                    episode_num = int(match.group(2))
                    break
            
            return season_num, episode_num
            
        except Exception:
            return None, None
    
    def _create_simple_marker(self, directory: Path, stage: str, data: Optional[Dict[str, Any]] = None):
        """Create a simple marker file (fallback method)."""
        marker_file = directory / self.MARKERS[stage]
        
        marker_content = {
            'timestamp': datetime.now().isoformat(),
            'stage': stage
        }
        if data:
            marker_content.update(data)
        
        with open(marker_file, 'w') as f:
            json.dump(marker_content, f, indent=2)
    
    def _find_season_directory(self, episode_dir: Path) -> Optional[Path]:
        """Find the season directory for a given episode directory."""
        try:
            # Navigate up to find Season XX directory
            current = episode_dir
            while current and current != current.parent:
                if current.name.startswith("Season "):
                    return current
                current = current.parent
            return None
        except Exception:
            return None
    
    def _set_context_aware_episode_marker(self, movie_dir: Path, stage: str, data: Optional[Dict[str, Any]], context: Dict[str, Any]) -> bool:
        """Set episode-aware marker with request context information."""
        try:
            # Extract episode info from path
            season_num, episode_num = self._extract_episode_info_from_path(movie_dir)
            if not (season_num and episode_num):
                # Fallback to regular marker with all data preserved
                return self._set_regular_marker(movie_dir, stage, data)
            
            season_dir = self._find_season_directory(movie_dir)
            if not season_dir:
                return self._set_regular_marker(movie_dir, stage, data)
            
            # Get or create EpisodeMarkerManager for this season
            from _internal.utils.episode_marker_manager import get_episode_marker_manager
            episode_mgr = get_episode_marker_manager(season_dir, f".{stage}")
            
            # Mark this specific episode as complete for this stage
            success = episode_mgr.add_episode(season_num, episode_num, logger)
            if not success:
                return self._set_regular_marker(movie_dir, stage, data)
            
            # ALSO create a regular marker file with full metadata for compatibility
            # This ensures the episode_info is preserved as expected by the test
            regular_marker_success = self._set_regular_marker(movie_dir, stage, data)
            
            # Check if season/series is complete based on request context
            request_type = context.get("request_type", "")
            if request_type == "multiple_episodes":
                # Check if all requested episodes for this season are complete
                expected_episodes = context.get("episodes", [])
                season_episodes = [ep for ep in expected_episodes if ep.get('season') == season_num]
                
                all_complete = True
                for ep in season_episodes:
                    if not episode_mgr.is_episode_complete(ep.get('season'), ep.get('episode')):
                        all_complete = False
                        break
                
                if all_complete:
                    # Set season-level marker
                    season_marker_file = season_dir / self.MARKERS[stage]
                    marker_content = {
                        'timestamp': datetime.now().isoformat(),
                        'stage': stage,
                        'request_type': 'multiple_episodes_complete',
                        'episodes_completed': len(season_episodes)
                    }
                    with open(season_marker_file, 'w') as f:
                        json.dump(marker_content, f, indent=2)
            
            return success and regular_marker_success
            
        except Exception as e:
            logger.warning(f"Error setting context-aware episode marker: {e}")
            # Fallback to regular marker
            return self._set_regular_marker(movie_dir, stage, data)
    
    def _set_regular_marker(self, movie_dir: Path, stage: str, data: Optional[Dict[str, Any]]) -> bool:
        """Set a regular marker file without episode-aware features."""
        try:
            marker_file = movie_dir / self.MARKERS[stage]
            marker_content = {
                'timestamp': datetime.now().isoformat(),
                'stage': stage
            }
            if data:
                marker_content.update(data)
            
            with open(marker_file, 'w') as f:
                json.dump(marker_content, f, indent=2)
            
            return True
        except Exception as e:
            logger.error(f"Error setting regular marker: {e}")
            return False

    def clear_stage_marker(self, movie_dir: Path, stage: str):
        """Clear a stage marker file."""
        if stage not in self.MARKERS:
            logger.warning(f"Unknown stage marker: {stage}")
            return
        
        marker_file = movie_dir / self.MARKERS[stage]
        
        try:
            if marker_file.exists():
                marker_file.unlink()
                logger.debug(f"Cleared marker {stage} for {movie_dir.name}")
        except Exception as e:
            logger.error(f"Failed to clear marker {stage} for {movie_dir}: {e}")
    
    def get_stage_marker_data(self, movie_dir: Path, stage: str) -> Optional[Dict[str, Any]]:
        """Get data from a stage marker file."""
        if stage not in self.MARKERS:
            return None
        
        marker_file = movie_dir / self.MARKERS[stage]
        
        try:
            if marker_file.exists():
                with open(marker_file, 'r') as f:
                    return json.load(f)
        except Exception as e:
            logger.warning(f"Failed to read marker {stage} for {movie_dir}: {e}")
        
        return None
    
    def is_stage_complete(self, movie_dir: Path, stage: str) -> bool:
        """Check if a stage is complete for a movie."""
        marker_file = movie_dir / self.MARKERS.get(stage, f'.{stage}')
        return marker_file.exists()

    def move_original_to_backup(self, movie_dir: Path, resolution: str = "1080p") -> bool:
        """
        Move original movie files to temporary backup folder and delete the entire original folder.
        This preserves the original movie files but removes all processing markers and the folder.

        Args:
            movie_dir: Path to the movie directory in 2_downloaded_and_organized
            resolution: Resolution folder (1080p, 4k, etc.)

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Create backup directory structure
            backup_base = self.stage_directories['temp_backup']
            backup_dest_dir = backup_base / "movies" / resolution / movie_dir.name

            # If backup already exists, remove it first (replace mode)
            if backup_dest_dir.exists():
                logger.info(f"🔄 Replacing existing backup: {backup_dest_dir}")
                shutil.rmtree(backup_dest_dir)

            backup_dest_dir.mkdir(parents=True, exist_ok=True)

            # Copy only the original movie files (not markers) to backup
            files_backed_up = []
            
            # Define processed folders to exclude from backup
            processed_folders = {
                '_Processed_Audio', '_Processed_Subtitles', 'temp_iso_extracted',
                '_temp_extracted', '_processing_temp', '_mkv_temp', '_intermediate'
            }
            
            for item in movie_dir.iterdir():
                if item.is_file() and not item.name.startswith('.'):
                    # This is a regular file (movie, subtitle, etc.), not a marker
                    backup_file_path = backup_dest_dir / item.name
                    shutil.copy2(str(item), str(backup_file_path))
                    files_backed_up.append(item.name)
                    logger.debug(f"Backed up: {item.name}")
                elif item.is_dir() and item.name not in processed_folders:
                    # Copy subdirectories (like subtitle folders, etc.) but exclude processed folders
                    backup_subdir = backup_dest_dir / item.name
                    shutil.copytree(str(item), str(backup_subdir))
                    files_backed_up.append(f"{item.name}/ (directory)")
                    logger.debug(f"Backed up directory: {item.name}")
                elif item.is_dir() and item.name in processed_folders:
                    logger.debug(f"Skipped processed folder from backup: {item.name}")
                    # Don't back up processed folders created during pipeline processing

            # Create a backup marker with metadata
            backup_marker = backup_dest_dir / '.original_backup'
            backup_marker.write_text(json.dumps({
                'timestamp': datetime.now().isoformat(),
                'original_location': str(movie_dir),
                'backup_reason': 'mkv_processing_complete',
                'backup_type': 'original_files_only',
                'files_backed_up': files_backed_up
            }, indent=2))

            # Now completely remove the original movie directory (including all markers)
            shutil.rmtree(str(movie_dir))

            logger.info(f"✅ Backed up {len(files_backed_up)} original files to: {backup_dest_dir}")
            logger.info(f"🗑️ Completely removed original movie folder (including markers) from downloaded_and_organized")
            return True

        except Exception as e:
            logger.error(f"Failed to backup original files and remove folder: {e}")
            return False

    def cleanup_backup_after_quality_check(self, movie_name: str, resolution: str = "1080p") -> bool:
        """
        Clean up backup files after quality check is complete and movie is moved to Plex.

        Args:
            movie_name: Name of the movie directory (e.g., "The Matrix (1999)")
            resolution: Resolution folder (1080p, 4k, etc.)

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            backup_base = self.stage_directories['temp_backup']
            backup_dir = backup_base / resolution / movie_name

            if not backup_dir.exists():
                logger.info(f"No backup found for {movie_name}, nothing to clean up")
                return True

            # Remove the entire backup directory for this movie
            shutil.rmtree(backup_dir)
            logger.info(f"✅ Cleaned up backup for {movie_name}")

            # Clean up empty resolution directory if it's empty
            resolution_dir = backup_base / resolution
            if resolution_dir.exists() and not any(resolution_dir.iterdir()):
                resolution_dir.rmdir()
                logger.info(f"✅ Cleaned up empty resolution directory: {resolution}")

            return True

        except Exception as e:
            logger.error(f"Failed to cleanup backup for {movie_name}: {e}")
            return False

    def cleanup_stage_after_completion(self, movie_dir: Path, completed_stage: str) -> bool:
        """
        Clean up intermediate files and folders after a stage completes successfully.

        Args:
            movie_dir: Path to the movie directory
            completed_stage: The stage that was just completed

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            logger.info(f"🧹 Cleaning up after {completed_stage} completion for {movie_dir.name}")

            if completed_stage == "mkv_processing":
                # After MKV processing, clean up temporary processing files
                temp_files = list(movie_dir.glob("*.tmp")) + list(movie_dir.glob("temp_*"))
                for temp_file in temp_files:
                    try:
                        if temp_file.is_file():
                            temp_file.unlink()
                        elif temp_file.is_dir():
                            shutil.rmtree(temp_file)
                        logger.debug(f"Removed temp file/dir: {temp_file.name}")
                    except Exception as e:
                        logger.warning(f"Failed to remove temp file {temp_file.name}: {e}")

            elif completed_stage == "video_encoding":
                # After video encoding, clean up the processed video file from stage 3
                # (Keep only the encoded version in stage 4)
                stage3_dirs = list(Path("workspace/3_mkv_cleaned_subtitles_extracted/movies").glob(f"*/{movie_dir.name}"))
                for stage3_dir in stage3_dirs:
                    processed_video = stage3_dir / "_Processed_VideoAudio"
                    if processed_video.exists():
                        try:
                            shutil.rmtree(processed_video)
                            logger.info(f"Cleaned up processed video from stage 3: {processed_video}")
                        except Exception as e:
                            logger.warning(f"Failed to cleanup processed video: {e}")

            elif completed_stage == "subtitle_processing":
                # After subtitle processing, clean up the processed subtitle folder from stage 3
                stage3_dirs = list(Path("workspace/3_mkv_cleaned_subtitles_extracted/movies").glob(f"*/{movie_dir.name}"))
                for stage3_dir in stage3_dirs:
                    processed_subs = stage3_dir / "_Processed_Subtitles"
                    if processed_subs.exists():
                        try:
                            shutil.rmtree(processed_subs)
                            logger.info(f"Cleaned up processed subtitles from stage 3: {processed_subs}")
                        except Exception as e:
                            logger.warning(f"Failed to cleanup processed subtitles: {e}")

            elif completed_stage == "final_mux":
                # After final mux, clean up intermediate files but keep processed audio for manual selection
                # Remove .encoded and .subtitle_complete markers as they're no longer needed
                markers_to_remove = ['.encoded', '.subtitle_complete', '.subtitle_processing_pending']
                for marker in markers_to_remove:
                    marker_file = movie_dir / marker
                    if marker_file.exists():
                        try:
                            marker_file.unlink()
                            logger.debug(f"Removed marker: {marker}")
                        except Exception as e:
                            logger.warning(f"Failed to remove marker {marker}: {e}")

            logger.info(f"✅ Cleanup completed for {completed_stage}")
            return True

        except Exception as e:
            logger.error(f"Failed to cleanup after {completed_stage}: {e}")
            return False

    def cleanup_empty_directories(self, base_path: Path) -> bool:
        """
        Clean up empty directories in the workspace.

        Args:
            base_path: Base path to scan for empty directories

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            empty_dirs_removed = 0

            # Walk through directories bottom-up to handle nested empty dirs
            for root, dirs, files in os.walk(base_path, topdown=False):
                root_path = Path(root)

                # Skip if it's a stage directory itself
                if root_path.name in ['1_downloading', '2_downloaded_and_organized',
                                     '3_mkv_cleaned_subtitles_extracted', '4_ready_for_final_mux',
                                     '5_awaiting_poster', '6_awaiting_quality_check',
                                     '7_final_plex_ready', 'temp_original_backup', 'issues_hold']:
                    continue

                # Check if directory is empty (no files and no non-empty subdirectories)
                try:
                    if not any(root_path.iterdir()):
                        root_path.rmdir()
                        logger.debug(f"Removed empty directory: {root_path}")
                        empty_dirs_removed += 1
                except OSError:
                    # Directory not empty or permission issue
                    pass

            if empty_dirs_removed > 0:
                logger.info(f"✅ Removed {empty_dirs_removed} empty directories")

            return True

        except Exception as e:
            logger.error(f"Failed to cleanup empty directories: {e}")
            return False

    def deploy_to_plex_after_quality_check(self, movie_dir: Path, plex_base_path: str = None) -> bool:
        """
        Deploy final movie to Plex folder after quality check completion.
        Also cleans up temporary backup files.

        Args:
            movie_dir: Path to the movie directory (e.g., in 4_ready_for_final_mux or 6_awaiting_quality_check)
            plex_base_path: Base path for Plex movies (optional, uses default if not provided)

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            movie_name = movie_dir.name  # e.g., "The Matrix (1999)"
            logger.info(f"🎬 Deploying {movie_name} to Plex after quality check")

            # Find the final muxed movie file
            movie_files = list(movie_dir.glob(f"{movie_name}.mkv"))
            if not movie_files:
                # Fallback: look for any .mkv file
                movie_files = list(movie_dir.glob("*.mkv"))

            if not movie_files:
                logger.error(f"No movie file found in {movie_dir}")
                return False

            final_movie_file = movie_files[0]

            # Determine Plex destination
            if not plex_base_path:
                # Deploy directly to final Plex directory instead of intermediate workspace stage
                plex_base_path = str(self.workspace_root / ".." / ".." / "plex")  # Default to plex folder

            plex_dest_dir = Path(plex_base_path) / movie_name
            plex_dest_dir.mkdir(parents=True, exist_ok=True)

            # Move the final movie file to Plex
            plex_movie_path = plex_dest_dir / final_movie_file.name
            shutil.move(str(final_movie_file), str(plex_movie_path))
            logger.info(f"✅ Moved final movie to Plex: {plex_movie_path}")

            # Move any additional files (posters, etc.)
            additional_files = [f for f in movie_dir.iterdir() if f.is_file() and f != final_movie_file]
            for additional_file in additional_files:
                if additional_file.suffix.lower() in ['.jpg', '.png', '.nfo', '.srt']:
                    dest_path = plex_dest_dir / additional_file.name
                    shutil.move(str(additional_file), str(dest_path))
                    logger.info(f"✅ Moved additional file: {dest_path}")

            # Create deployment marker
            deployment_marker = plex_dest_dir / '.deployed_to_plex'
            deployment_marker.write_text(json.dumps({
                'timestamp': datetime.now().isoformat(),
                'original_location': str(movie_dir),
                'deployed_file': final_movie_file.name,
                'deployment_reason': 'quality_check_complete'
            }, indent=2))

            # Clean up the original movie directory
            try:
                shutil.rmtree(movie_dir)
                logger.info(f"✅ Cleaned up original movie directory: {movie_dir}")
            except Exception as e:
                logger.warning(f"⚠️ Failed to cleanup original directory: {e}")

            # Clean up backup files
            resolution = "4k" if "4k" in str(movie_dir).lower() or "2160p" in str(movie_dir).lower() else "1080p"
            backup_cleanup_success = self.cleanup_backup_after_quality_check(movie_name, resolution)
            if backup_cleanup_success:
                logger.info(f"✅ Cleaned up backup files for {movie_name}")

            # Clean up empty directories
            self.cleanup_empty_directories(Path("workspace"))

            logger.info(f"🎉 Successfully deployed {movie_name} to Plex!")
            return True

        except Exception as e:
            logger.error(f"Failed to deploy {movie_name} to Plex: {e}")
            return False

    def create_quality_check_script(self, movie_dir: Path) -> bool:
        """
        Create a quality check script that can be run to approve and deploy a movie.

        Args:
            movie_dir: Path to the movie directory ready for quality check

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            movie_name = movie_dir.name
            script_content = f'''#!/usr/bin/env python3
"""
Quality Check and Deployment Script for {movie_name}
Generated automatically by PlexMovieAutomator
"""

import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "_internal"))

from utils.filesystem_first_state_manager import FilesystemFirstStateManager

def main():
    print(f"🎬 Quality Check for: {movie_name}")
    print(f"📁 Movie location: {movie_dir}")

    # Show movie file info
    movie_files = list(Path(r"{movie_dir}").glob("*.mkv"))
    if movie_files:
        movie_file = movie_files[0]
        file_size_gb = movie_file.stat().st_size / (1024**3)
        print(f"📽️ Movie file: {{movie_file.name}} ({{file_size_gb:.2f}} GB)")

    # Ask for user confirmation
    response = input("\\n✅ Quality check passed? Deploy to Plex? (y/N): ").strip().lower()

    if response in ['y', 'yes']:
        print("🚀 Deploying to Plex...")

        fs_manager = FilesystemFirstStateManager(Path.cwd())
        success = fs_manager.deploy_to_plex_after_quality_check(Path(r"{movie_dir}"))

        if success:
            print("🎉 Successfully deployed to Plex!")
            print("🧹 Backup files cleaned up")
        else:
            print("❌ Deployment failed")
            return 1
    else:
        print("⏸️ Quality check not approved. Movie remains in quality check folder.")

    return 0

if __name__ == "__main__":
    sys.exit(main())
'''

            # Create the script file
            script_path = movie_dir / f"quality_check_{movie_name.replace(' ', '_').replace('(', '').replace(')', '')}.py"
            script_path.write_text(script_content)
            script_path.chmod(0o755)  # Make executable

            logger.info(f"✅ Created quality check script: {script_path}")
            return True

        except Exception as e:
            logger.error(f"Failed to create quality check script: {e}")
            return False
    
    def generate_movie_identifier(self, movie_info: Dict[str, Any]) -> str:
        """Generate a unique identifier for a movie."""
        title = movie_info.get('cleaned_title', 'Unknown')
        year = movie_info.get('year', 'Unknown')
        return f"{title} ({year})"
    
    def cleanup_stale_markers(self, movie_dir: Path):
        """Clean up stale or conflicting marker files."""
        try:
            # Remove processing markers if corresponding complete markers exist
            if self.is_stage_complete(movie_dir, 'mkv_complete'):
                self.clear_stage_marker(movie_dir, 'mkv_processing')

            if self.is_stage_complete(movie_dir, 'subtitle_complete'):
                self.clear_stage_marker(movie_dir, 'subtitle_processing')

        except Exception as e:
            logger.warning(f"Failed to cleanup stale markers for {movie_dir}: {e}")
    
    def find_movie_by_identifier(self, movie_identifier: str) -> Optional[Dict[str, Any]]:
        """Find a movie by its identifier across all stages."""
        all_movies = self.discover_movies_by_stage()
        
        for stage, movies in all_movies.items():
            for movie in movies:
                if self.generate_movie_identifier(movie) == movie_identifier:
                    movie['current_stage'] = stage
                    return movie
        
        return None


class MetadataOnlyDatabase:
    """
    Simplified database that stores only static movie metadata.
    
    No status, paths, or processing information - only TMDB IDs, 
    user preferences, and other non-discoverable metadata.
    """
    
    def __init__(self, workspace_root: Path, db_path: Optional[Path] = None):
        self.workspace_root = Path(workspace_root)
        self.db_path = db_path or (self.workspace_root / "_internal" / "data" / "movie_metadata.db")
        
        # Thread-local storage for database connections
        self._local = threading.local()
        
        # Initialize database
        self._init_database()
    
    def _init_database(self):
        """Initialize the SQLite database with metadata-only schema."""
        # Ensure database directory exists
        self.db_path.parent.mkdir(parents=True, exist_ok=True)

        with self._get_connection() as conn:
            # Create metadata-only table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS movies (
                    movie_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    unique_id TEXT UNIQUE NOT NULL,  -- e.g. "Title (Year)"
                    title TEXT NOT NULL,
                    year INTEGER,
                    tmdb_id TEXT,
                    imdb_id TEXT,
                    audio_lang TEXT DEFAULT 'eng',
                    subtitle_lang TEXT DEFAULT 'eng',
                    keep_commentary BOOLEAN DEFAULT 0,
                    metadata_json TEXT,  -- Additional metadata as JSON
                    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
            """)
            
            # Create indexes
            conn.execute("CREATE INDEX IF NOT EXISTS idx_movies_unique_id ON movies(unique_id);")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_movies_tmdb_id ON movies(tmdb_id);")
            
            # Create TV shows table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS tv_shows (
                    tv_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    unique_id TEXT UNIQUE NOT NULL,  -- e.g. "Show Title (Year)"
                    title TEXT NOT NULL,
                    year INTEGER,
                    tvdb_id TEXT,
                    tmdb_id TEXT,
                    imdb_id TEXT,
                    metadata_json TEXT,
                    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
            """)
            conn.execute("CREATE INDEX IF NOT EXISTS idx_tv_unique_id ON tv_shows(unique_id);")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_tv_tvdb_id ON tv_shows(tvdb_id);")
            
            logger.info(f"Initialized metadata database at: {self.db_path}")
    
    def _get_connection(self) -> sqlite3.Connection:
        """Get a thread-local database connection."""
        if not hasattr(self._local, 'connection'):
            self._local.connection = sqlite3.connect(str(self.db_path))
            self._local.connection.row_factory = sqlite3.Row
            self._local.connection.execute("PRAGMA foreign_keys = ON")
        
        return self._local.connection
    
    @contextmanager
    def transaction(self):
        """Context manager for database transactions with automatic rollback on error."""
        conn = self._get_connection()
        try:
            conn.execute("BEGIN")
            yield conn
            conn.commit()
        except Exception:
            conn.rollback()
            raise
    
    def get_movie_metadata(self, unique_id: str) -> Optional[Dict[str, Any]]:
        """Get metadata for a movie by its unique identifier."""
        conn = self._get_connection()
        cursor = conn.execute("SELECT * FROM movies WHERE unique_id = ?", (unique_id,))
        row = cursor.fetchone()
        
        if row:
            movie = dict(row)
            # Parse JSON metadata
            if movie.get('metadata_json'):
                try:
                    movie['metadata'] = json.loads(movie['metadata_json'])
                except json.JSONDecodeError:
                    movie['metadata'] = {}
            else:
                movie['metadata'] = {}
            return movie
        
        return None
    
    def save_movie_metadata(self, unique_id: str, title: str, year: Optional[int] = None,
                           tmdb_id: Optional[str] = None, imdb_id: Optional[str] = None,
                           audio_lang: str = 'eng', subtitle_lang: str = 'eng',
                           keep_commentary: bool = False, metadata: Optional[Dict[str, Any]] = None) -> bool:
        """Save or update movie metadata."""
        with self.transaction() as conn:
            # Check if movie exists
            cursor = conn.execute("SELECT movie_id FROM movies WHERE unique_id = ?", (unique_id,))
            exists = cursor.fetchone()
            
            metadata_json = json.dumps(metadata) if metadata else None
            
            if exists:
                # Update existing
                conn.execute("""
                    UPDATE movies SET 
                        title = ?, year = ?, tmdb_id = ?, imdb_id = ?,
                        audio_lang = ?, subtitle_lang = ?, keep_commentary = ?,
                        metadata_json = ?, last_updated = CURRENT_TIMESTAMP
                    WHERE unique_id = ?
                """, (title, year, tmdb_id, imdb_id, audio_lang, subtitle_lang, 
                     keep_commentary, metadata_json, unique_id))
                logger.debug(f"Updated metadata for {unique_id}")
            else:
                # Insert new
                conn.execute("""
                    INSERT INTO movies (unique_id, title, year, tmdb_id, imdb_id,
                                      audio_lang, subtitle_lang, keep_commentary, metadata_json)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (unique_id, title, year, tmdb_id, imdb_id, audio_lang, 
                     subtitle_lang, keep_commentary, metadata_json))
                logger.debug(f"Added metadata for {unique_id}")
            
            return True
    
    def get_all_movies(self) -> List[Dict[str, Any]]:
        """Get all movies from the metadata database."""
        conn = self._get_connection()
        cursor = conn.execute("SELECT * FROM movies ORDER BY title, year")
        rows = cursor.fetchall()
        
        movies = []
        for row in rows:
            movie = dict(row)
            # Parse JSON metadata
            if movie.get('metadata_json'):
                try:
                    movie['metadata'] = json.loads(movie['metadata_json'])
                except json.JSONDecodeError:
                    movie['metadata'] = {}
            else:
                movie['metadata'] = {}
            movies.append(movie)
        
        return movies
    
    def delete_movie_metadata(self, unique_id: str) -> bool:
        """Delete metadata for a movie."""
        with self.transaction() as conn:
            cursor = conn.execute("DELETE FROM movies WHERE unique_id = ?", (unique_id,))
            if cursor.rowcount > 0:
                logger.info(f"Deleted metadata for {unique_id}")
                return True
            return False
    
    def save_tv_metadata(self, unique_id: str, title: str, year: Optional[int] = None,
                         tvdb_id: Optional[str] = None, tmdb_id: Optional[str] = None,
                         imdb_id: Optional[str] = None, metadata: Optional[Dict[str, Any]] = None) -> bool:
        """Save or update TV show metadata."""
        conn = self._get_connection()
        metadata_json = json.dumps(metadata or {})
        try:
            with self.transaction() as tconn:
                tconn.execute("""
                    INSERT INTO tv_shows (unique_id, title, year, tvdb_id, tmdb_id, imdb_id, metadata_json)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                    ON CONFLICT(unique_id) DO UPDATE SET
                        title=excluded.title,
                        year=excluded.year,
                        tvdb_id=excluded.tvdb_id,
                        tmdb_id=excluded.tmdb_id,
                        imdb_id=excluded.imdb_id,
                        metadata_json=excluded.metadata_json,
                        last_updated=CURRENT_TIMESTAMP
                """, (unique_id, title, year, tvdb_id, tmdb_id, imdb_id, metadata_json))
            return True
        except Exception as e:
            logger.error(f"Failed to save TV metadata for {unique_id}: {e}")
            return False

    def get_tv_metadata(self, unique_id: str) -> Optional[Dict[str, Any]]:
        conn = self._get_connection()
        cur = conn.execute("SELECT * FROM tv_shows WHERE unique_id = ?", (unique_id,))
        row = cur.fetchone()
        if not row:
            return None
        show = dict(row)
        if show.get('metadata_json'):
            try:
                show['metadata'] = json.loads(show['metadata_json'])
            except json.JSONDecodeError:
                show['metadata'] = {}
        else:
            show['metadata'] = {}
        return show

    def get_all_tv(self) -> List[Dict[str, Any]]:
        conn = self._get_connection()
        cur = conn.execute("SELECT * FROM tv_shows")
        rows = cur.fetchall()
        shows = []
        for r in rows:
            rec = dict(r)
            if rec.get('metadata_json'):
                try:
                    rec['metadata'] = json.loads(rec['metadata_json'])
                except json.JSONDecodeError:
                    rec['metadata'] = {}
            else:
                rec['metadata'] = {}
            shows.append(rec)
        return shows
    
    def close(self):
        """Close database connections."""
        if hasattr(self._local, 'connection'):
            self._local.connection.close()
            delattr(self._local, 'connection')


def create_filesystem_first_manager(workspace_root: Optional[str] = None) -> FilesystemFirstStateManager:
    """Factory function to create a FilesystemFirstStateManager instance."""
    if workspace_root is None:
        workspace_root_path = Path.cwd()
    else:
        workspace_root_path = Path(workspace_root)
    
    return FilesystemFirstStateManager(workspace_root_path)


def create_metadata_database(workspace_root: Optional[str] = None, db_path: Optional[str] = None) -> MetadataOnlyDatabase:
    """Factory function to create a MetadataOnlyDatabase instance."""
    if workspace_root is None:
        workspace_root_path = Path.cwd()
    else:
        workspace_root_path = Path(workspace_root)
    
    if db_path:
        db_path_obj = Path(db_path)
    else:
        db_path_obj = None
    
    return MetadataOnlyDatabase(workspace_root_path, db_path_obj)
