#!/usr/bin/env python3
from __future__ import annotations

from pathlib import Path
from typing import List, Optional
import logging

from _internal.src.constants import VIDEO_EXTENSIONS, DISK_IMAGE_EXTENSIONS


def find_video_files(path: Path) -> List[Path]:
    """
    Recursively find video files under path using allowed extensions.
    Case-insensitive by matching lowercase suffix.
    """
    videos: List[Path] = []
    try:
        # Collect with rglob once and filter by suffix lower()
        for p in path.rglob("*"):
            try:
                if p.is_file() and p.suffix.lower() in VIDEO_EXTENSIONS:
                    videos.append(p)
            except Exception:
                continue
    except Exception:
        pass
    return videos


def find_disk_image_files(path: Path) -> List[Path]:
    """
    Recursively find disk image files (.iso) under path.
    Case-insensitive by matching lowercase suffix.
    """
    disk_images: List[Path] = []
    try:
        # Collect with rglob once and filter by suffix lower()
        for p in path.rglob("*"):
            try:
                if p.is_file() and p.suffix.lower() in DISK_IMAGE_EXTENSIONS:
                    disk_images.append(p)
            except Exception:
                continue
    except Exception:
        pass
    return disk_images


def find_all_media_files(path: Path) -> tuple[List[Path], List[Path]]:
    """
    Find both video files and disk image files.
    
    Returns:
        tuple: (video_files, disk_image_files)
    """
    videos = find_video_files(path)
    disk_images = find_disk_image_files(path)
    return videos, disk_images


def get_main_video_file(path: Path, logger=None, temp_extraction_dir: Optional[Path] = None) -> Optional[Path]:
    """
    Get the main video file from a directory, handling both regular video files and ISO extraction.
    
    Args:
        path: Directory to search for media files
        logger: Optional logger for progress messages
        temp_extraction_dir: Directory for ISO extraction (defaults to path/temp_iso_extracted)
        
    Returns:
        Path to the main video file, or None if no suitable file found
    """
    # First find all media files
    video_files, iso_files = find_all_media_files(path)
    
    # If we have ISO files, try to extract them first
    extracted_videos = []
    if iso_files:
        if logger:
            logger.info(f"Found {len(iso_files)} ISO file(s), attempting extraction...")
            
        # Set up extraction directory - use temp folder
        if temp_extraction_dir is None:
            temp_extraction_dir = path / "temp_iso_extracted"
            
        for iso_file in iso_files:
            try:
                if logger:
                    logger.info(f"Processing ISO: {iso_file.name}")
                    
                # Import and use the ISO handler
                from _internal.src.iso_handler import process_iso_file
                import logging
                
                # Create a logger if not provided
                if logger is None:
                    logger = logging.getLogger(__name__)
                    
                # Process the ISO file
                extracted_video = process_iso_file(iso_file, temp_extraction_dir, logger)
                if extracted_video:
                    extracted_videos.append(extracted_video)
                    if logger:
                        logger.info(f"Successfully extracted video from ISO: {extracted_video.name}")
                else:
                    if logger:
                        logger.warning(f"Failed to extract video from ISO: {iso_file.name}")
                        
            except Exception as e:
                if logger:
                    logger.error(f"Error processing ISO {iso_file.name}: {e}")
                continue
    
    # Combine regular video files with extracted videos
    all_video_files = video_files + extracted_videos
    
    if not all_video_files:
        return None
        
    # Return the largest video file
    try:
        main_file = max(all_video_files, key=lambda f: f.stat().st_size)
        if logger:
            size_gb = main_file.stat().st_size / (1024**3)
            logger.info(f"Selected main video file: {main_file.name} ({size_gb:.2f} GB)")
        return main_file
    except Exception as e:
        if logger:
            logger.error(f"Error selecting main video file: {e}")
        return all_video_files[0] if all_video_files else None

