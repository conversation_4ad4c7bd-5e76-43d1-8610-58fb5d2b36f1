import argparse
import as<PERSON><PERSON>
import json
from pathlib import Path
from statistics import mean, pstdev
from typing import Any, Dict, List, Sequence
import fnmatch

from .analyze_release import analyze_one, load_servers, NNTPServerConfig
from .nzb_parser import NZBParser


def extract_additional_stats(report: Dict[str, Any]) -> Dict[str, float]:
    nzb_path = Path(report['nzb'])
    extra: Dict[str, float] = {}
    try:
        meta = NZBParser().parse(nzb_path)
    except Exception:
        return extra
    segment_sizes: List[int] = []
    file_segment_counts: List[int] = []
    hinted_files: int = 0
    for f in meta.files:
        file_segment_counts.append(len(f.segments))
        if f.filename_hint:
            hinted_files += 1
        for seg in f.segments:
            if seg.bytes:
                segment_sizes.append(seg.bytes)
    extra['avg_segments_per_file'] = float(mean(file_segment_counts)) if file_segment_counts else 0.0
    extra['segments_per_file_stddev'] = float(pstdev(file_segment_counts)) if len(file_segment_counts) > 1 else 0.0
    extra['avg_segment_size'] = float(mean(segment_sizes)) if segment_sizes else 0.0
    extra['segment_size_stddev'] = float(pstdev(segment_sizes)) if len(segment_sizes) > 1 else 0.0
    extra['hinted_file_ratio'] = float(hinted_files / len(meta.files)) if meta.files else 0.0
    return extra


def diff_reports(a: Dict[str, Any], b: Dict[str, Any], fields: Sequence[str]) -> Dict[str, Any]:
    diff: Dict[str, Any] = {}
    for f in fields:
        av = a.get(f)
        bv = b.get(f)
        if isinstance(av, (int, float)) and isinstance(bv, (int, float)):
            delta = av - bv
            # bv already confirmed numeric; guard against zero
            rel = (delta / bv) if bv not in (0, None) else None
            diff[f] = {'a': av, 'b': bv, 'delta': delta, 'relative_vs_b': rel}
        else:
            diff[f] = {'a': av, 'b': bv, 'equal': av == bv}
    return diff


def highlight_risk(report: Dict[str, Any], extra: Dict[str, float]) -> List[str]:
    notes: List[str] = []
    if report.get('estimated_parity_blocks', 0) == 0:
        notes.append('No parity detected: fragile')
    if report.get('age_days', 0) > 4000:
        notes.append('Older than typical retention')
    hinted_ratio = extra.get('hinted_file_ratio')
    if hinted_ratio is not None and hinted_ratio < 0.1:
        notes.append('High obfuscation (few filename hints)')
    if report.get('probe_results'):
        for srv, pr in report['probe_results'].items():
            if pr.get('errors') == pr.get('total') and pr.get('total', 0) > 0:
                notes.append(f'Probe to server {srv} failed for all sampled segments (connection/auth issue)')
    return notes


def resolve_nzb(spec: str, search_dirs: Sequence[Path]) -> Path:
    p = Path(spec)
    if p.exists():
        return p
    # If absolute path provided but missing, reduce to basename for pattern search
    if p.is_absolute():
        spec = p.name
    # treat spec (now maybe basename fragment) as pattern (case-insensitive) against provided dirs
    lowered = spec.lower()
    candidates: List[Path] = []
    patterns: List[str] = []
    if lowered.endswith('.nzb'):
        patterns.append(lowered)
    else:
        patterns.append(lowered + '*.nzb')
        patterns.append('*' + lowered + '*.nzb')
    for d in search_dirs:
        if not d.is_dir():
            continue
        for nzb in d.glob('*.nzb'):
            name_lower = nzb.name.lower()
            for pat in patterns:
                # simple fnmatch case-insensitive
                if fnmatch.fnmatch(name_lower, pat):
                    candidates.append(nzb)
                    break
    if not candidates:
        raise FileNotFoundError(f"Could not resolve NZB spec '{spec}' in search dirs: {[str(d) for d in search_dirs]}")
    if len(candidates) == 1:
        return candidates[0]
    # If ambiguous, pick longest filename match (most specific) but inform user via stdout
    candidates.sort(key=lambda x: len(x.name), reverse=True)
    chosen = candidates[0]
    print(f"[resolve_nzb] Multiple matches for '{spec}': {[c.name for c in candidates[:6]]}{'...' if len(candidates)>6 else ''}. Using '{chosen.name}'.")
    return chosen


async def main():
    parser = argparse.ArgumentParser(description='Compare two NZB releases side-by-side and highlight differences.')
    parser.add_argument('--nzb-a', required=True, help='Path to NZB A (expected good)')
    parser.add_argument('--nzb-b', required=True, help='Path to NZB B (expected bad)')
    parser.add_argument('--server-config', help='Server config for probing')
    parser.add_argument('--retention-days', type=int, default=4000)
    parser.add_argument('--search-dir', action='append', help='Directory to search for NZBs when given partial names (can repeat). If omitted, defaults to C:/Users/<USER>/Downloads/Newshosting')
    parser.add_argument('--probe', action='store_true', help='Enable NNTP probing')
    parser.add_argument('--verbose', action='store_true', help='Verbose NNTP logging')
    parser.add_argument('--json', action='store_true', help='Emit JSON instead of text summary')
    args = parser.parse_args()

    servers: List[NNTPServerConfig] = []
    if args.probe:
        if not args.server_config:
            raise SystemExit('ERROR: --server-config required when --probe used')
        servers = load_servers(Path(args.server_config))

    # Build search dirs
    if args.search_dir:
        search_dirs = [Path(d) for d in args.search_dir]
    else:
        default_dir = Path.home() / 'Downloads' / 'Newshosting'
        # Add current working directory and preflight_analyzer directory for convenience
        search_dirs = [default_dir, Path.cwd(), Path(__file__).resolve().parent]

    nzb_a_path = resolve_nzb(args.nzb_a, search_dirs)
    nzb_b_path = resolve_nzb(args.nzb_b, search_dirs)

    rep_a = await analyze_one(nzb_a_path, servers, args.retention_days, dry_run=not args.probe, verbose=args.verbose)
    rep_b = await analyze_one(nzb_b_path, servers, args.retention_days, dry_run=not args.probe, verbose=args.verbose)

    extra_a = extract_additional_stats(rep_a)
    extra_b = extract_additional_stats(rep_b)

    diff = diff_reports({**rep_a, **extra_a}, {**rep_b, **extra_b}, [
    'file_count','data_segments','estimated_parity_blocks','age_days','risk_score','probe_error_ratio','probe_missing_ratio',
    'estimated_missing_segments','decision',
        'avg_segments_per_file','segments_per_file_stddev','avg_segment_size','hinted_file_ratio'
    ])

    risk_a = highlight_risk(rep_a, extra_a)
    risk_b = highlight_risk(rep_b, extra_b)

    output: Dict[str, Any] = {
        'report_a': rep_a,
        'report_b': rep_b,
        'extra_a': extra_a,
        'extra_b': extra_b,
        'diff': diff,
        'risk_notes_a': risk_a,
        'risk_notes_b': risk_b,
    }

    if args.json:
        print(json.dumps(output, indent=2))
        return

    print('=== NZB A Summary ===')
    print(f"Path: {rep_a['nzb']}\nRisk: {rep_a['risk_level']} ({rep_a['risk_score']:.4f}) Age: {rep_a['age_days']}d Files: {rep_a['file_count']} Segments: {rep_a['data_segments']}")
    if risk_a: print('Notes: ' + '; '.join(risk_a))
    print('\n=== NZB B Summary ===')
    print(f"Path: {rep_b['nzb']}\nRisk: {rep_b['risk_level']} ({rep_b['risk_score']:.4f}) Age: {rep_b['age_days']}d Files: {rep_b['file_count']} Segments: {rep_b['data_segments']}")
    if risk_b: print('Notes: ' + '; '.join(risk_b))
    print('\n=== Key Differences ===')
    for k, d in diff.items():
        if 'delta' in d:
            print(f"{k}: A={d['a']} B={d['b']} Δ={d['delta']} rel_vs_B={d['relative_vs_b']}")
        else:
            if not d.get('equal', False):
                print(f"{k}: A={d['a']} B={d['b']} (different)")

if __name__ == '__main__':
    asyncio.run(main())
