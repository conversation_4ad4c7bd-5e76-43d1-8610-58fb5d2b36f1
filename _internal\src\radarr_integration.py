#!/usr/bin/env python3
from __future__ import annotations

import aiohttp
from typing import Any, Dict, List, Optional

from _internal.utils.http_retry import async_request_json, async_delete


class RadarrClient:
    def __init__(self, base_url: str, api_key: str):
        self.base_url = base_url.rstrip('/')
        self.headers = {'X-Api-Key': api_key}

    def _url(self, path: str) -> str:
        return f"{self.base_url}{path}"

    async def movie_lookup(self, session: aiohttp.ClientSession, term: str) -> List[Dict[str, Any]]:
        status, payload = await async_request_json(session, 'GET', self._url('/api/v3/movie/lookup'), headers=self.headers, params={"term": term})
        if status == 200 and isinstance(payload, list):
            return payload
        return []

    async def get_movies(self, session: aiohttp.ClientSession) -> List[Dict[str, Any]]:
        status, payload = await async_request_json(session, 'GET', self._url('/api/v3/movie'), headers=self.headers)
        if status == 200 and isinstance(payload, list):
            return payload
        return []

    async def get_rootfolders(self, session: aiohttp.ClientSession) -> List[Dict[str, Any]]:
        status, payload = await async_request_json(session, 'GET', self._url('/api/v3/rootfolder'), headers=self.headers)
        if status == 200 and isinstance(payload, list):
            return payload
        return []

    async def get_queue(self, session: aiohttp.ClientSession) -> Dict[str, Any]:
        status, payload = await async_request_json(session, 'GET', self._url('/api/v3/queue'), headers=self.headers)
        if status == 200 and isinstance(payload, dict):
            return payload
        return {"records": []}

    async def get_queue_page(self, session: aiohttp.ClientSession, page: int = 1, page_size: int = 100) -> Dict[str, Any]:
        status, payload = await async_request_json(session, 'GET', self._url('/api/v3/queue'), headers=self.headers, params={"page": page, "pageSize": page_size})
        if status == 200 and isinstance(payload, dict):
            return payload
        return {"records": []}

    async def add_movie(self, session: aiohttp.ClientSession, movie_payload: Dict[str, Any]) -> Dict[str, Any]:
        status, payload = await async_request_json(session, 'POST', self._url('/api/v3/movie'), headers=self.headers, json=movie_payload)
        if status in (200, 201) and isinstance(payload, dict):
            return payload
        return {"success": False, "status": status}

    async def issue_command(self, session: aiohttp.ClientSession, command_payload: Dict[str, Any]) -> Dict[str, Any]:
        status, payload = await async_request_json(session, 'POST', self._url('/api/v3/command'), headers=self.headers, json=command_payload)
        if status in (200, 201) and isinstance(payload, dict):
            return payload
        return {"success": False, "status": status}

    async def delete_queue_item(self, session: aiohttp.ClientSession, queue_id: int, remove_from_client: bool = True, blocklist: bool = False) -> bool:
        qs = f"?removeFromClient={'true' if remove_from_client else 'false'}&blocklist={'true' if blocklist else 'false'}"
        status = await async_delete(session, self._url(f"/api/v3/queue/{queue_id}{qs}"), headers=self.headers)
        return status in (200, 204)

    async def delete_movie_file(self, session: aiohttp.ClientSession, movie_file_id: int) -> bool:
        status = await async_delete(session, self._url(f"/api/v3/moviefile/{movie_file_id}"), headers=self.headers)
        return status in (200, 204)

    async def delete_movie(self, session: aiohttp.ClientSession, movie_id: int, delete_files: bool = False, add_import_exclusion: bool = False) -> bool:
        qs = f"?deleteFiles={'true' if delete_files else 'false'}&addImportExclusion={'true' if add_import_exclusion else 'false'}"
        status = await async_delete(session, self._url(f"/api/v3/movie/{movie_id}{qs}"), headers=self.headers)
        return status in (200, 204)

