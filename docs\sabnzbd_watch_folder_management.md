# SABnzbd Watch Folder Management

## Overview

This document explains the new SABnzbd watch folder management system that solves the folder structure issues discovered during testing.

## Problem Identified

During testing of "The Sandman (2022) S02E12", we discovered that:

1. **SABnzbd Watch Folder Limitation**: SABnzbd only watches the specific folder configured, not subfolders
2. **Old Structure Issues**: The previous structure had separate watch folders for movies and TV shows, requiring multiple SABnzbd watch folder configurations
3. **Pipeline Confirmation**: When NZB files were placed in the correct watch folder, SABnzbd successfully processed them and placed them in `1_downloading/complete_raw` ✅

## New Solution

### Folder Structure

```
workspace/
├── 0_new_requests/
│   ├── movies/
│   │   ├── nzb_files_for_download/      # Organized storage for movie NZBs
│   │   └── nzb_files_launched_archive/  # Archive for processed movie NZBs
│   ├── tv_shows/
│   │   ├── nzb_files_for_download/      # Organized storage for TV show NZBs
│   │   └── nzb_files_launched_archive/  # Archive for processed TV show NZBs
│   └── watched/                         # ⭐ CENTRALIZED WATCH FOLDER
└── 1_downloading/
    └── complete_raw/                    # SABnzbd drops completed downloads here
```

### SABnzbd Configuration

**Set your SABnzbd watch folder to:**
```
C:\Users\<USER>\Videos\PlexMovieAutomator\workspace\0_new_requests\watched
```

### Workflow

1. **Organized Storage**: NZB files are initially stored in organized folders:
   - Movies: `0_new_requests/movies/nzb_files_for_download/`
   - TV Shows: `0_new_requests/tv_shows/nzb_files_for_download/`

2. **Move to Watch**: When ready to download, move NZB files to:
   - `0_new_requests/watched/`

3. **SABnzbd Processing**: SABnzbd picks up files from the watch folder and processes them

4. **Archive**: After processing, files can be moved to appropriate archive folders

## Management Tools

### Manual Management

```bash
# Check current status
python utils/nzb_watch_folder_manager.py --status

# Move specific file to watch folder
python utils/nzb_watch_folder_manager.py --move-to-watch movies/some_movie.nzb
python utils/nzb_watch_folder_manager.py --move-to-watch tv_shows/some_show.nzb

# Auto-move all pending NZB files
python utils/nzb_watch_folder_manager.py --auto-move
```

### Configuration

The new structure is configured in `_internal/config/settings.ini`:

```ini
[Paths]
# Updated structure with organized content and centralized watch folder
new_requests_dir = workspace/0_new_requests
movie_requests_dir = %(new_requests_dir)s/movies
tv_requests_dir = %(new_requests_dir)s/tv_shows
# Centralized watch folder that SABnzbd monitors
sabnzbd_watch_folder = %(new_requests_dir)s/watched
```

## Benefits

1. **Single Watch Folder**: Only one SABnzbd watch folder configuration needed
2. **Organized Storage**: Content stays organized by type (movies/TV) until ready to download
3. **Flexible Processing**: Move files to watch folder only when ready to download
4. **Archive Management**: Processed files are archived with timestamps
5. **Pipeline Compatibility**: Maintains compatibility with existing pipeline stages

## Testing Confirmed

✅ **SABnzbd Integration**: Files placed in watch folder are processed by SABnzbd  
✅ **Download Placement**: Completed downloads appear in `1_downloading/complete_raw`  
⚠️ **Sonarr Integration**: Still needs resolution (separate from watch folder issue)

## Migration Notes

- The old `0_new_movie_requests` structure is kept for backward compatibility
- New requests should use the `0_new_requests` structure
- Update your SABnzbd watch folder configuration to point to the new centralized location
