﻿namespace seconv.libse.Common.TextLengthCalculator
{
    public static class TextLengthHelper
    {
        public static decimal CountCharacters(string text, bool forCps)
        {
            return CalcFactory.MakeCalculator(Configuration.Settings.General.CpsLineLengthStrategy).CountCharacters(text, forCps);
        }

        public static bool IsKnownHtmlTag(string input, int index)
        {
            var s = input.Remove(0, index + 1).ToLowerInvariant();
            return s.StartsWith('/') ||
                   s.StartsWith("i>", StringComparison.Ordinal) ||
                   s.StartsWith("b>", StringComparison.Ordinal) ||
                   s.StartsWith("u>", StringComparison.Ordinal) ||
                   s.Starts<PERSON>ith("font ", StringComparison.Ordinal) ||
                   s.<PERSON>s<PERSON>ith("ruby", StringComparison.Ordinal) ||
                   s.StartsWith("span>", StringComparison.Ordinal) ||
                   s.StartsWith("span ", StringComparison.Ordinal) ||
                   s.Starts<PERSON>ith("p>", StringComparison.Ordinal) ||
                   s.Starts<PERSON>ith("br>", StringComparison.Ordinal) ||
                   s.StartsWith("box>", StringComparison.Ordinal) ||
                   s.StartsWith("div>", StringComparison.Ordinal) ||
                   s.StartsWith("div ", StringComparison.Ordinal);
        }
    }
}