#!/usr/bin/env python3
from __future__ import annotations

from typing import Any, Dict, List, Optional
from pathlib import Path
import importlib.util

from _internal.utils.common_helpers import get_setting, get_path_setting
from _internal.src.radarr_integration import RadarrClient
from _internal.src.event_queue import get_event_queue


def _load_stage01_module():
    root = Path(__file__).resolve().parents[2]
    stage1_path = root / "01_intake_and_nzb_search.py"
    spec = importlib.util.spec_from_file_location("stage01", stage1_path)
    if spec and spec.loader:
        mod = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(mod)  # type: ignore[attr-defined]
        return mod
    raise ImportError("Unable to load Stage 01 script")


async def add_movie(movie_data: Dict[str, Any], settings: Dict[str, Any], logger) -> Dict[str, Any]:
    """
    Orchestrate movie intake using Stage 01 implementation for now.
    This is a stable entry point to decouple Stage 01 internals.
    """
    radarr_key = get_setting("Radarr", "api_key", expected_type=str, settings_dict=settings)
    if not radarr_key:
        return {"success": False, "reason": "missing_api_key"}

    stage01 = _load_stage01_module()
    result = await stage01._add_movie_to_radarr_modern(movie_data, settings, logger)
    return result


async def select_root_folder(session, radarr_url: str, api_key: str, settings: Dict[str, Any], logger) -> str:
    """
    Determine the root folder path to use for adding a movie, mirroring Stage 01 logic.
    """
    desired_root = get_path_setting("Paths", "plex_movies_directory", settings_dict=settings)
    if not desired_root:
        desired_root = get_path_setting("Paths", "media_root", settings_dict=settings)
    if not desired_root:
        desired_root = "E:/"  # last-resort default

    desired_root_path = Path(desired_root)

    radarr = RadarrClient(radarr_url, api_key)
    existing_roots = await radarr.get_rootfolders(session)

    normalized_desired = str(desired_root_path.resolve()) if desired_root_path.drive else str(desired_root_path)
    normalized_desired = normalized_desired.replace('/', '\\')

    chosen_root: Optional[str] = None
    for rf in existing_roots:
        try:
            if str(Path(rf.get('path', '')).resolve()).lower() == str(Path(normalized_desired).resolve()).lower():
                chosen_root = rf.get('path')
                break
        except Exception:
            continue

    if not chosen_root:
        try:
            desired_root_path.mkdir(parents=True, exist_ok=True)
        except Exception as mk_err:
            logger.warning(f"⚠️ Could not create desired movie root '{desired_root_path}': {mk_err}")
        if existing_roots:
            chosen_root = existing_roots[0].get('path')
            logger.info(f"🔁 Using existing Radarr root folder: {chosen_root}")
        else:
            chosen_root = normalized_desired
            logger.info(f"🆕 Attempting to use unregistered root folder (Radarr will need access): {chosen_root}")

    return chosen_root.replace('/', '\\')


async def add_with_profiles(session, radarr_url: str, api_key: str, best_match: Dict[str, Any],
                            quality_profile_ids: List[int], root_folder_path: str,
                            search_term: str, logger, *, search_for_movie: bool = True) -> List[Dict[str, Any]]:
    """
    Add the movie for each quality profile and return the list of added movie dicts.
    """
    radarr = RadarrClient(radarr_url, api_key)
    added_movies: List[Dict[str, Any]] = []

    eq = get_event_queue()

    for quality_profile_id in quality_profile_ids:
        add_movie_data = {
            "title": best_match["title"],
            "year": best_match["year"],
            "tmdbId": best_match["tmdbId"],
            "images": best_match.get("images", []),
            "qualityProfileId": quality_profile_id,
            "monitored": False,  # Disable automatic monitoring to prevent unwanted downloads
            "minimumAvailability": "announced",
            "rootFolderPath": root_folder_path,
            "addOptions": {
                "monitor": "movieOnly",
                "searchForMovie": False  # Disable automatic search - we control downloads manually
            }
        }

        logger.info(f"   📥 Adding with quality profile {quality_profile_id} (searchForMovie={bool(search_for_movie)})...")
        added_movie = await radarr.add_movie(session, add_movie_data)
        if added_movie and isinstance(added_movie, dict) and added_movie.get('id'):
            added_movies.append(added_movie)
            logger.info(f"   ✅ Successfully added: {search_term} (ID: {added_movie.get('id')}, Profile: {quality_profile_id})")
            # Publish event
            await eq.publish("movie.added", {
                "radarr_id": added_movie.get('id'),
                "title": added_movie.get('title'),
                "year": added_movie.get('year'),
                "profile_id": quality_profile_id,
            })
        else:
            logger.warning(f"   ⚠️ Failed to add to Radarr with profile {quality_profile_id}")
            await eq.publish("movie.add_failed", {
                "title": best_match.get('title'),
                "year": best_match.get('year'),
                "profile_id": quality_profile_id,
            })

    return added_movies

