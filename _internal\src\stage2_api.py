from __future__ import annotations

from pathlib import Path
from typing import Any, Dict, Optional

# Import-friendly Stage 2 API that avoids importing the large monolithic script
# and instead composes the underlying organizers directly.


def _is_tv_content(content_info: Dict[str, Any]) -> bool:
    if not isinstance(content_info, dict):
        return False
    if content_info.get('content_type') == 'tv_show':
        return True
    lk = {k.lower() for k in content_info.keys()}
    return any(k in lk for k in ('season', 'episode', 'series_id', 'seriesid', 'series_title'))


async def organize_completed_content(content_info: Dict[str, Any], main_file_path: str, download_dir: Path, settings_dict: Dict[str, Any], logger_instance) -> bool:
    from _internal.src.file_organizer import organize_movie, organize_tv_show
    from _internal.utils.tv_show_naming import TVShowNamingHelper

    # Determine organized base dir (mirror behavior in 02 script)
    organized_base_dir: Optional[Path] = None
    if isinstance(settings_dict, dict) and 'Paths' in settings_dict and 'mkv_processing_output_dir' in settings_dict['Paths']:
        organized_base_dir = Path(settings_dict['Paths']['mkv_processing_output_dir'])
    else:
        organized_base_dir = Path('workspace/2_downloaded_and_organized')

    if _is_tv_content(content_info):
        helper = TVShowNamingHelper()
        return await organize_tv_show(content_info, main_file_path, download_dir, organized_base_dir, '1080p', helper, logger_instance, settings_dict)
    else:
        # Default to movie
        return await organize_movie(content_info, main_file_path, download_dir, organized_base_dir, '1080p', logger_instance, settings_dict)


async def run_inbox_processing(settings_dict: Dict[str, Any], logger_instance, events_dir: Optional[Path] = None) -> int:
    """Process SAB postprocess events from EventQueue and organize content.

    Returns number of events processed successfully.
    """
    from _internal.src.sab_event_inbox import EventInboxReader
    from _internal.src.fs_helpers import find_video_files
    from _internal.src.event_queue import get_event_queue

    inbox = EventInboxReader(
        eq=get_event_queue({'EventQueue': {'enabled': True, 'dir': str(events_dir)}}) if events_dir else None,
        cursor_dir=events_dir if events_dir else None,
    )
    processed = 0
    for evt in inbox.iter_new_events():
        data = evt.get('data', {})
        ddir = data.get('final_folder') or data.get('download_dir')
        status = data.get('status')
        ok = (str(status).lower() == 'success') or (str(status) == '0')
        if not ddir or not ok:
            continue
        p = Path(ddir)
        if not p.exists():
            continue
        videos = find_video_files(p)
        if not videos:
            continue
        main_file = max(videos, key=lambda fp: fp.stat().st_size if fp.exists() else 0)
        # Heuristic content_info from folder
        title = p.name.replace('.', ' ').split(' 1080p')[0]
        content_info = {'title': title}
        if any(tok.isdigit() and len(tok) == 4 for tok in p.name.replace('.', ' ').split()):
            try:
                year = int([tok for tok in p.name.replace('.', ' ').split() if tok.isdigit() and len(tok) == 4][0])
                content_info['year'] = year
            except Exception:
                pass
        # Organize
        ok = await organize_completed_content(content_info, str(main_file), p, settings_dict, logger_instance)
        if ok:
            processed += 1
    return processed

