"""
Intelligent Fallback System for PlexAutomator
Handles automatic candidate selection when downloads fail, with blacklisting to prevent retries.

Rankings are by FILE SIZE, not quality:
- #1 = SMALLEST file
- Higher numbers = LARGER files  
- Fallback moves UP toward smaller files (lower index numbers)
"""

import asyncio
import aiohttp
import logging
import json
from typing import Dict, Any, Optional, List
from pathlib import Path

# Import your existing modules
try:
    from ..utils.radarr_client import RadarrClient
    from ..utils.common_helpers import get_setting
except ImportError:
    # For testing purposes
    class RadarrClient:
        def __init__(self, base_url, api_key):
            self.base_url = base_url
            self.api_key = api_key
        
        async def get_movies(self, session):
            return []
    
    def get_setting(section, key, **kwargs):
        return kwargs.get('default', 'test_value')


class IntelligentFallbackSystem:
    """
    Intelligent fallback system that prevents external auto-downloads and uses preflight analysis
    rankings to select the next best candidate when downloads fail.
    """
    
    def __init__(self, settings_dict: Dict[str, Any], logger: logging.Logger, telemetry_system=None):
        self.settings = settings_dict
        self.logger = logger
        self.telemetry_system = telemetry_system  # Reference to telemetry system for updates
        
        # Radarr configuration - get directly from settings_dict
        radarr_config = settings_dict.get("Radarr", {})
        self.radarr_url = radarr_config.get("url", "http://localhost:7878")
        self.radarr_api_key = radarr_config.get("api_key")
        
        # General configuration
        general_config = settings_dict.get("General", {})
        workspace_dir = general_config.get("workspace_dir", "workspace")
        
        # Preflight decisions directory
        self.preflight_decisions_dir = Path(workspace_dir) / "preflight_decisions"
        
        # Track blacklisted candidates per movie to prevent retry loops
        # Format: {movie_id: {set of blacklisted GUIDs}}
        self.blacklisted_candidates: Dict[int, set] = {}
        
        self.logger.info("🔧 Intelligent Fallback System initialized")
        self.logger.info(f"   📁 Preflight decisions: {self.preflight_decisions_dir}")
        self.logger.info(f"   🎬 Radarr URL: {self.radarr_url}")
        self.logger.info(f"   🔑 API Key configured: {'✅' if self.radarr_api_key else '❌'}")
        self.logger.info(f"   📊 Telemetry integration: {'✅' if self.telemetry_system else '❌'}")
        if not self.radarr_api_key:
            self.logger.error("❌ CRITICAL: No Radarr API key found - fallback downloads will fail!")
    
    async def disable_radarr_auto_monitoring(self, radarr_id: int, session: aiohttp.ClientSession) -> bool:
        """
        Disable Radarr's automatic monitoring for a specific movie to prevent
        external systems from triggering unwanted downloads.
        
        Args:
            radarr_id: Radarr movie ID
            session: HTTP session
            
        Returns:
            bool: True if successfully disabled, False otherwise
        """
        try:
            self.logger.info(f"🔍 Looking for movie ID {radarr_id} in Radarr...")
            
            # DIRECT API CALL instead of RadarrClient to debug the issue
            headers = {'X-Api-Key': self.radarr_api_key}
            url = f"{self.radarr_url}/api/v3/movie"
            
            async with session.get(url, headers=headers) as response:
                if response.status != 200:
                    error_text = await response.text()
                    self.logger.error(f"❌ Failed to get movies from Radarr: HTTP {response.status}")
                    self.logger.error(f"   Response: {error_text}")
                    return False
                
                movies = await response.json()
                self.logger.info(f"📊 Retrieved {len(movies)} movies from Radarr")
                
                # Find our specific movie
                movie = next((m for m in movies if m.get('id') == radarr_id), None)
                
                if not movie:
                    # Movie not found - log available movie IDs for debugging
                    movie_ids = [m.get('id') for m in movies[:10]]  # First 10 IDs
                    self.logger.error(f"❌ Movie ID {radarr_id} not found in Radarr")
                    self.logger.error(f"   Available movie IDs (first 10): {movie_ids}")
                    return False
                
                self.logger.info(f"✅ Found movie: '{movie.get('title')}' (ID: {radarr_id})")
                self.logger.info(f"   Monitored: {movie.get('monitored')}")
                self.logger.info(f"   Status: {movie.get('status')}")
                
                if not movie.get('monitored', False):
                    self.logger.info(f"✅ Movie ID {radarr_id} monitoring already disabled")
                    return True
                
                # Update movie to disable monitoring
                self.logger.info(f"🛡️ Disabling monitoring for movie ID {radarr_id}")
                movie_data = {
                    **movie,
                    'monitored': False
                }
                
                update_url = f"{self.radarr_url}/api/v3/movie/{radarr_id}"
                
                async with session.put(update_url, headers=headers, json=movie_data) as update_response:
                    if update_response.status in [200, 201, 202]:  # 202 = Accepted, also success
                        self.logger.info(f"✅ Successfully disabled monitoring for movie ID {radarr_id}")
                        return True
                    else:
                        error_text = await update_response.text()
                        self.logger.error(f"❌ Failed to disable monitoring: HTTP {update_response.status}")
                        self.logger.error(f"   Response: {error_text}")
                        return False
                    
        except Exception as e:
            self.logger.error(f"Error disabling Radarr monitoring: {e}")
            return False
    
    def search_preflight_decision(self, movie_title: str, year: Optional[int] = None) -> Optional[Dict[str, Any]]:
        """
        Search for a preflight decision file for a given movie.
        
        Args:
            movie_title: Movie title to search for
            year: Optional movie year
            
        Returns:
            Dict containing preflight data, or None if not found
        """
        import time
        
        try:
            if not self.preflight_decisions_dir.exists():
                self.logger.warning(f"Preflight decisions directory not found: {self.preflight_decisions_dir}")
                return None
            
            # Create sanitized version of movie title to match filenames
            def sanitize_for_search(title: str) -> str:
                """Sanitize title the same way the intake script does."""
                import re
                # Remove invalid characters for Windows filesystem
                sanitized = re.sub(r'[<>:"/\\|?*]', '', title)
                # Replace spaces with underscores
                sanitized = re.sub(r'\s+', '_', sanitized)
                # Remove parentheses and their contents (like year)
                sanitized = re.sub(r'\([^)]*\)', '', sanitized)
                # Clean up multiple underscores and trailing underscores
                sanitized = re.sub(r'_+', '_', sanitized).strip('_')
                return sanitized.lower()
            
            sanitized_title = sanitize_for_search(movie_title)
            self.logger.info(f"🔍 Searching for preflight decision: '{movie_title}' → '{sanitized_title}'")
            
            # Search for JSON files in the preflight decisions directory
            for json_file in self.preflight_decisions_dir.glob("*.json"):
                try:
                    # WINDOWS FIX: Cross-platform file access with retry logic
                    max_retries = 3
                    for attempt in range(max_retries):
                        try:
                            with open(json_file, 'r', encoding='utf-8') as f:
                                # Try file locking on Unix systems only
                                try:
                                    import fcntl
                                    fcntl.flock(f.fileno(), fcntl.LOCK_SH)  # Shared lock for reading
                                except ImportError:
                                    # Windows doesn't have fcntl, skip locking
                                    pass
                                data = json.load(f)
                            
                            # IMPROVED: Multiple matching strategies
                            filename_lower = json_file.stem.lower().replace('_', ' ')  # Convert underscores to spaces
                            search_title_lower = sanitized_title.replace('_', ' ').lower()
                            
                            self.logger.debug(f"Comparing: '{search_title_lower}' with file: '{filename_lower}'")
                            
                            # Strategy 1: Direct sanitized title match
                            if search_title_lower in filename_lower or filename_lower in search_title_lower:
                                self.logger.info(f"📄 Found match (strategy 1): {json_file.name}")
                                return data
                            
                            # Strategy 2: Extract key words from title (remove common words)
                            def extract_key_words(title: str) -> set:
                                import re
                                # Remove common words and extract key terms
                                title = re.sub(r'\b(the|a|an|and|or|of|in|on|at|to|for|with|by)\b', '', title.lower())
                                title = re.sub(r'[^\w\s]', ' ', title)  # Remove punctuation
                                words = [w for w in title.split() if len(w) > 2]  # Words longer than 2 chars
                                return set(words)
                            
                            movie_words = extract_key_words(movie_title)
                            filename_words = extract_key_words(json_file.stem)
                            
                            # Strategy 3: Word overlap matching (at least 50% overlap)
                            if movie_words and filename_words:
                                overlap = len(movie_words & filename_words)
                                overlap_percentage = overlap / len(movie_words)
                                
                                if overlap_percentage >= 0.5:  # At least 50% word overlap
                                    self.logger.info(f"📄 Found match (strategy 2, {overlap_percentage:.1%} overlap): {json_file.name}")
                                    return data
                            
                            break  # Successfully read file, break retry loop
                            
                        except (IOError, json.JSONDecodeError) as file_err:
                            if attempt < max_retries - 1:
                                self.logger.debug(f"File access retry {attempt + 1}/{max_retries} for {json_file.name}: {file_err}")
                                time.sleep(0.1)  # Brief delay before retry
                                continue
                            else:
                                self.logger.warning(f"Failed to read preflight file {json_file.name} after {max_retries} attempts: {file_err}")
                                break
                                
                except Exception as e:
                    self.logger.warning(f"Error processing preflight file {json_file.name}: {e}")
                    continue
            
            self.logger.warning(f"❌ No preflight decision found for: {movie_title} (searched as: {sanitized_title})")
            return None
            
        except Exception as e:
            self.logger.error(f"Error searching for preflight decision: {e}")
            return None
    
    def find_next_candidate(self, preflight_data: Dict[str, Any], failed_guid: str, user_selection_index: Optional[int] = None, 
                          original_system_recommendation_index: Optional[int] = None) -> Optional[Dict[str, Any]]:
        """
        Find the next candidate based on the preflight analysis ranking with progressive fallback logic.
        
        CRITICAL LOGIC (CORRECTED): 
        1. Rankings are by SIZE: #1 = smallest, higher numbers = larger files
        2. Move UP the list toward smaller files (lower index numbers)
        3. Blacklist failed candidates to prevent retries
        4. Blacklist user-rejected candidates (original system recommendation if user chose different)
        5. Start from the failed candidate's index and work UP to smaller files
        
        Example: User selected #22 (71.86 GB), #22 failed
        - Blacklist #22 (it failed)
        - Try #21, #20, #19... (progressively smaller files)
        - This makes sense: if big file failed, try smaller alternatives
        
        Example: System recommended #5 (15.61 GB), #5 failed  
        - Blacklist #5 (it failed)
        - Try #4, #3, #2, #1... (progressively smaller files)
        
        Args:
            preflight_data: Preflight analysis data
            failed_guid: GUID of the failed download
            user_selection_index: If user manually selected a candidate, use its index
            original_system_recommendation_index: Index of original system recommendation (to blacklist if user chose different)
            
        Returns:
            Dict containing next candidate data, or None if no suitable candidate
        """
        try:
            candidates = preflight_data.get("acceptable_candidates", [])
            if not candidates:
                self.logger.error("No candidates found in preflight data")
                return None
            
            movie_id = preflight_data.get("movie_id")
            if not movie_id:
                self.logger.warning("No movie ID found in preflight data, using generic ID")
                movie_id = 0
            
            # Initialize blacklist for this movie
            if movie_id not in self.blacklisted_candidates:
                self.blacklisted_candidates[movie_id] = set()
            
            blacklist = self.blacklisted_candidates[movie_id]
            
            # Blacklist the failed candidate
            for i, candidate in enumerate(candidates):
                if candidate.get("guid") == failed_guid:
                    blacklist.add(failed_guid)
                    self.logger.info(f"🚫 Blacklisted failed candidate: #{i + 1} - {candidate.get('title')}")
                    break
            
            # If user made a selection different from system recommendation, blacklist the original
            # BUT ONLY if the failed candidate is NOT the original system recommendation
            if user_selection_index is not None and original_system_recommendation_index is not None:
                self.logger.info(f"🔍 Comparing indices: user_selection={user_selection_index} (type: {type(user_selection_index)}), original_recommendation={original_system_recommendation_index} (type: {type(original_system_recommendation_index)})")
                if user_selection_index != original_system_recommendation_index:
                    self.logger.info(f"⚠️ User selection differs from system recommendation - checking if original should be blacklisted")
                    # Don't blacklist the original recommendation if IT'S the one that failed
                    # (that would be redundant since we already blacklisted the failed candidate above)
                    original_guid = candidates[original_system_recommendation_index].get("guid")
                    if original_guid != failed_guid:
                        original_candidate = candidates[original_system_recommendation_index]
                        blacklist.add(original_candidate.get("guid"))
                        self.logger.info(f"🚫 Blacklisted user-rejected candidate: #{original_system_recommendation_index + 1} - {original_candidate.get('title')}")
                    else:
                        self.logger.info(f"✅ Skipped blacklisting original recommendation #{original_system_recommendation_index + 1} (it's the failed candidate already blacklisted)")
                else:
                    self.logger.info(f"✅ User selection matches system recommendation - no additional blacklisting needed")
            
            # Determine starting point for fallback search  
            if user_selection_index is not None:
                # User made a selection - find the NEXT candidate (not skip one!)
                # FIXED: If #28 failed, try #27 next (not start from #26)
                start_index = user_selection_index - 1  # This will be the NEXT candidate to try
                self.logger.info(f"📍 User selection #{user_selection_index + 1} failed, trying next candidate #{start_index + 1}")
            else:
                # System recommendation failed - find the NEXT candidate
                failed_index = next((i for i, c in enumerate(candidates) if c.get("guid") == failed_guid), None)
                if failed_index is not None:
                    start_index = failed_index - 1  # This will be the NEXT candidate to try
                    self.logger.info(f"📍 System recommendation #{failed_index + 1} failed, trying next candidate #{start_index + 1}")
                else:
                    start_index = 0
                    self.logger.warning(f"⚠️ Could not find failed candidate in list, starting from #1")
            
            # Ensure we don't go below index 0
            if start_index < 0:
                self.logger.warning(f"⚠️ No more candidates available (already tried the smallest file)")
                return None
            
            # FIXED: Try the specific next candidate, don't search
            candidate = candidates[start_index]
            candidate_guid = candidate.get("guid")
            
            # Skip if this candidate is blacklisted
            if candidate_guid in blacklist:
                self.logger.info(f"🚫 Next candidate #{start_index + 1} is blacklisted, searching for alternatives")
                
                # Search UP (toward smaller files, lower indices) from the next position
                for i in range(start_index - 1, -1, -1):
                    candidate = candidates[i]
                    candidate_guid = candidate.get("guid")
                    
                    # Skip blacklisted candidates
                    if candidate_guid in blacklist:
                        self.logger.debug(f"   ⏭️  Skipping blacklisted #{i + 1}: {candidate.get('title')}")
                        continue
                    
                    # Found a non-blacklisted candidate
                    self.logger.info(f"🎯 Alternative candidate: #{i + 1} - {candidate.get('title')}")
                    self.logger.info(f"   💾 Size: {candidate.get('size', 0) / (1024**3):.2f} GB")
                    self.logger.info(f"   ⚡ Risk: {candidate.get('risk_score', 0):.4f}")
                    self.logger.info(f"   ✅ Not blacklisted - proceeding with this smaller file")
                    return candidate
                
                self.logger.warning("⚠️ No non-blacklisted alternatives found")
                return None
            else:
                # Next candidate is not blacklisted - use it
                self.logger.info(f"🎯 Next candidate: #{start_index + 1} - {candidate.get('title')}")
                self.logger.info(f"   💾 Size: {candidate.get('size', 0) / (1024**3):.2f} GB")
                self.logger.info(f"   ⚡ Risk: {candidate.get('risk_score', 0):.4f}")
                self.logger.info(f"   ✅ Not blacklisted - proceeding with next file")
                return candidate
                
        except Exception as e:
            self.logger.error(f"Error finding next candidate: {e}")
            return None
    
    async def trigger_fallback_download(self, radarr_id: int, next_candidate: Dict[str, Any], session: aiohttp.ClientSession) -> bool:
        """
        Trigger a download for the next candidate through Radarr.
        
        Args:
            radarr_id: Radarr movie ID
            next_candidate: Candidate data from preflight analysis
            session: HTTP session
            
        Returns:
            bool: True if download triggered successfully, False otherwise
        """
        try:
            # CRITICAL: Ensure monitoring is disabled BEFORE triggering download
            self.logger.info(f"🛡️ Ensuring Radarr monitoring disabled before fallback download")
            await self.disable_radarr_auto_monitoring(radarr_id, session)
            
            # Get indexer ID from indexer name (same logic as intake script)
            indexer_name = next_candidate.get("indexer")
            indexer_id = next_candidate.get("indexerId")
            
            if indexer_id is None and indexer_name:
                self.logger.info(f"🔍 Mapping indexer name '{indexer_name}' to ID...")
                try:
                    headers = {'X-Api-Key': self.radarr_api_key}
                    async with session.get(f"{self.radarr_url}/api/v3/indexer", headers=headers) as idx_resp:
                        if idx_resp.status == 200:
                            indexers = await idx_resp.json()
                            # Look for exact match or partial match
                            for indexer in indexers:
                                if indexer_name in indexer.get('name', '') or indexer.get('name', '') in indexer_name:
                                    indexer_id = indexer.get('id')
                                    self.logger.info(f"✅ Mapped indexer '{indexer_name}' → ID: {indexer_id}")
                                    break
                            
                            # If no match found, use first available indexer
                            if indexer_id is None and indexers:
                                first_indexer = indexers[0]
                                indexer_id = first_indexer.get('id')
                                self.logger.info(f"🔧 Using fallback indexer: {first_indexer.get('name')} (ID: {indexer_id})")
                        else:
                            self.logger.warning(f"⚠️ Could not get indexer mapping: HTTP {idx_resp.status}")
                except Exception as idx_err:
                    self.logger.warning(f"⚠️ Could not get indexer mapping: {idx_err}")
            
            # Default to 1 if still no indexer ID found
            if indexer_id is None:
                indexer_id = 1
                self.logger.warning(f"⚠️ Using default indexer ID: {indexer_id}")
            
            # Use the SAME payload structure that works in your intake script
            # Just guid and indexerId - keep it simple!
            download_payload = {
                "guid": next_candidate.get("guid"),
                "indexerId": indexer_id
            }
            
            self.logger.info(f"🚀 Triggering fallback download with payload: {download_payload}")
            
            # Send download request to Radarr (same as your working intake script)
            url = f"{self.radarr_url}/api/v3/release"
            headers = {'X-Api-Key': self.radarr_api_key}
            
            async with session.post(url, headers=headers, json=download_payload) as response:
                if response.status in [200, 201]:
                    self.logger.info(f"✅ Successfully triggered fallback download")
                    self.logger.info(f"   🎬 Title: {next_candidate.get('title')}")
                    self.logger.info(f"   💾 Size: {next_candidate.get('size', 0) / (1024**3):.2f} GB")
                    
                    # Update telemetry system to show new candidate information
                    if self.telemetry_system:
                        try:
                            candidate_info = {
                                'title': next_candidate.get('title'),
                                'size': next_candidate.get('size', 0),
                                'indexer': next_candidate.get('indexer')
                            }
                            self.telemetry_system.update_fallback_candidate(radarr_id, candidate_info)
                            self.logger.info(f"📊 Updated telemetry dashboard with new candidate info")
                        except Exception as telemetry_err:
                            self.logger.warning(f"⚠️ Failed to update telemetry dashboard: {telemetry_err}")
                    
                    # CRITICAL: Disable monitoring AGAIN after triggering download
                    # Radarr might re-enable it automatically
                    self.logger.info(f"🛡️ Re-disabling Radarr monitoring after download trigger")
                    await self.disable_radarr_auto_monitoring(radarr_id, session)
                    
                    return True
                else:
                    error_text = await response.text()
                    self.logger.error(f"❌ Failed to trigger download: HTTP {response.status}")
                    self.logger.error(f"   Response: {error_text}")
                    
                    # FIXED: If HTTP 404, treat it as a failed candidate and continue chain
                    if response.status == 404 and "cache" in error_text.lower():
                        self.logger.info(f"🔧 HTTP 404 detected (expired release cache) - treating as failed candidate")
                        self.logger.info(f"🔄 Will continue fallback chain to next candidate")
                        # Return False to trigger the next candidate in the chain
                        return False
                    
                    return False
                    
        except Exception as e:
            self.logger.error(f"Error triggering fallback download: {e}")
            return False
    
    async def enforce_monitoring_disabled(self, radarr_id: int, session: aiohttp.ClientSession, duration_minutes: int = 30) -> None:
        """
        Continuously enforce that monitoring remains disabled for a movie.
        This prevents Radarr from automatically re-enabling monitoring after failures.
        
        Args:
            radarr_id: Radarr movie ID
            session: HTTP session
            duration_minutes: How long to enforce (default 30 minutes)
        """
        try:
            import asyncio
            self.logger.info(f"🛡️ Starting continuous monitoring enforcement for movie ID {radarr_id}")
            self.logger.info(f"   ⏰ Duration: {duration_minutes} minutes")
            
            end_time = asyncio.get_event_loop().time() + (duration_minutes * 60)
            check_interval = 60  # Check every minute
            
            while asyncio.get_event_loop().time() < end_time:
                # Check if monitoring is still disabled
                try:
                    radarr = RadarrClient(self.radarr_url, self.radarr_api_key)
                    movies = await radarr.get_movies(session)
                    movie = next((m for m in movies if m.get('id') == radarr_id), None)
                    
                    if movie and movie.get('monitored', False):
                        self.logger.warning(f"⚠️ Radarr re-enabled monitoring for movie ID {radarr_id}! Disabling again...")
                        await self.disable_radarr_auto_monitoring(radarr_id, session)
                    else:
                        self.logger.debug(f"✅ Monitoring still disabled for movie ID {radarr_id}")
                        
                except Exception as e:
                    self.logger.error(f"Error checking monitoring status: {e}")
                
                # Wait before next check
                await asyncio.sleep(check_interval)
            
            self.logger.info(f"🏁 Monitoring enforcement completed for movie ID {radarr_id}")
            
        except Exception as e:
            self.logger.error(f"Error in continuous monitoring enforcement: {e}")
    
    async def _fallback_to_quality_profile_switch(self, radarr_id: int, session: aiohttp.ClientSession, failed_candidate: Dict[str, Any]) -> bool:
        """
        Fallback strategy for HTTP 404 errors: Switch quality profile and trigger fresh search.
        
        This method handles the case where specific release candidates are no longer available
        in the indexer cache (HTTP 404). Instead of failing, we:
        1. Determine appropriate fallback quality profile (4K → 1080p)
        2. Switch the movie to that profile  
        3. Enable monitoring
        4. Trigger fresh search to find currently available releases
        
        Args:
            radarr_id: Radarr movie ID
            session: HTTP session
            failed_candidate: The candidate that failed (for logging)
            
        Returns:
            bool: True if profile switch and search triggered successfully
        """
        try:
            self.logger.info(f"🔄 PROFILE FALLBACK: Starting quality profile fallback for movie ID {radarr_id}")
            
            # Step 1: Get current movie info
            radarr = RadarrClient(self.radarr_url, self.radarr_api_key)
            movies = await radarr.get_movies(session)
            movie = next((m for m in movies if m.get('id') == radarr_id), None)
            
            if not movie:
                self.logger.error(f"❌ Could not find movie ID {radarr_id} in Radarr")
                return False
            
            current_profile_id = movie.get('qualityProfileId')
            self.logger.info(f"📊 Current quality profile: {current_profile_id}")
            
            # Step 2: Get available quality profiles
            headers = {'X-Api-Key': self.radarr_api_key}
            async with session.get(f"{self.radarr_url}/api/v3/qualityprofile", headers=headers) as resp:
                if resp.status != 200:
                    self.logger.error(f"❌ Could not get quality profiles: HTTP {resp.status}")
                    return False
                
                profiles = await resp.json()
                
                # Find 1080p profile (fallback target)
                hd_1080p_profile = None
                for profile in profiles:
                    name = profile['name'].lower()
                    if 'hd-1080p' in name or ('1080p' in name and 'uhd' not in name and '4k' not in name):
                        hd_1080p_profile = profile
                        break
                
                if not hd_1080p_profile:
                    self.logger.error(f"❌ Could not find 1080p quality profile for fallback")
                    return False
                
                fallback_profile_id = hd_1080p_profile['id']
                fallback_profile_name = hd_1080p_profile['name']
                
                # Don't switch if already on target profile
                if current_profile_id == fallback_profile_id:
                    self.logger.info(f"📊 Already on fallback profile {fallback_profile_name} - triggering search only")
                else:
                    self.logger.info(f"🔄 Switching: Profile {current_profile_id} → {fallback_profile_id} ({fallback_profile_name})")
            
            # Step 3: Update movie with fallback profile + enable monitoring
            updated_movie_data = {**movie}
            updated_movie_data['qualityProfileId'] = fallback_profile_id
            updated_movie_data['monitored'] = True  # Enable monitoring for fresh search
            
            # Remove fields that shouldn't be sent in PUT request
            for key in ['movieFile', 'hasFile', 'sizeOnDisk', 'isAvailable', 'statistics']:
                updated_movie_data.pop(key, None)
            
            async with session.put(f"{self.radarr_url}/api/v3/movie/{radarr_id}", headers=headers, json=updated_movie_data) as resp:
                if resp.status not in [200, 202]:
                    error = await resp.text()
                    self.logger.error(f"❌ Failed to update movie profile: HTTP {resp.status} - {error}")
                    return False
                
                self.logger.info(f"✅ Successfully switched to {fallback_profile_name} profile")
                self.logger.info(f"✅ Enabled monitoring for fresh release search")
            
            # Step 4: Trigger fresh movie search
            search_command = {
                "name": "MoviesSearch", 
                "movieIds": [radarr_id]
            }
            
            async with session.post(f"{self.radarr_url}/api/v3/command", headers=headers, json=search_command) as resp:
                if resp.status not in [200, 201, 202]:
                    error = await resp.text()
                    self.logger.error(f"❌ Failed to trigger search: HTTP {resp.status} - {error}")
                    return False
                
                self.logger.info(f"✅ Fresh movie search triggered successfully")
                self.logger.info(f"💡 This will find currently available {fallback_profile_name} releases")
                self.logger.info(f"🎯 SOLUTION: Avoided HTTP 404 by switching to fresh search instead of expired candidate")
                
                # Log the failed candidate for reference
                if failed_candidate:
                    self.logger.info(f"📝 Failed candidate was: {failed_candidate.get('title', 'Unknown')} ({failed_candidate.get('size', 0) / (1024**3):.2f} GB)")
                
                return True
        
        except Exception as e:
            self.logger.error(f"❌ Error in profile fallback strategy: {e}")
            return False
    
    async def handle_download_failure(self, movie_title: str, failed_guid: str, radarr_id: int, 
                                    session: aiohttp.ClientSession, user_selection_index: Optional[int] = None,
                                    original_system_recommendation_index: Optional[int] = None) -> bool:
        """
        Handle a download failure by finding and triggering the next best candidate.
        
        Args:
            movie_title: Title of the movie
            failed_guid: GUID of the failed download
            radarr_id: Radarr movie ID
            session: HTTP session
            user_selection_index: If user manually selected a candidate, use its index
            original_system_recommendation_index: Index of original system recommendation
            
        Returns:
            bool: True if fallback was triggered successfully, False otherwise
        """
        try:
            self.logger.info(f"🔄 Handling download failure for: {movie_title}")
            self.logger.info(f"   💥 Failed GUID: {failed_guid}")
            
            # First, disable Radarr auto-monitoring to prevent external interference
            await self.disable_radarr_auto_monitoring(radarr_id, session)
            
            # Start continuous monitoring enforcement to prevent Radarr from re-enabling
            # This runs in the background for 30 minutes
            import asyncio
            asyncio.create_task(self.enforce_monitoring_disabled(radarr_id, session, duration_minutes=30))
            
            # Search for preflight decision data
            preflight_data = self.search_preflight_decision(movie_title)
            if not preflight_data:
                self.logger.error(f"❌ No preflight decision found for: {movie_title}")
                return False
            
            # Check for movie ID mismatch and update if necessary
            preflight_movie_id = preflight_data.get('movie_id')
            if preflight_movie_id and preflight_movie_id != radarr_id:
                self.logger.warning(f"⚠️ Movie ID mismatch detected: preflight={preflight_movie_id}, current={radarr_id}")
                self.logger.info(f"🔄 Updating telemetry with correct movie ID mapping...")
                
                # Update telemetry with the new movie ID mapping
                if self.telemetry_system:
                    try:
                        self.telemetry_system.update_movie_radarr_id(preflight_movie_id, radarr_id)
                        self.logger.info(f"✅ Updated movie ID mapping: {preflight_movie_id} → {radarr_id}")
                    except Exception as e:
                        self.logger.warning(f"⚠️ Could not update telemetry mapping: {e}")
                
                # Use the current radarr_id for download attempts
                self.logger.info(f"🎯 Using current Radarr ID {radarr_id} for fallback download")
            
            # Find next candidate using corrected logic
            next_candidate = self.find_next_candidate(
                preflight_data, 
                failed_guid, 
                user_selection_index=user_selection_index,
                original_system_recommendation_index=original_system_recommendation_index
            )
            
            if not next_candidate:
                self.logger.warning(f"⚠️ No suitable fallback candidate found for: {movie_title}")
                return False
            
            # Trigger download for next candidate
            success = await self.trigger_fallback_download(radarr_id, next_candidate, session)
            
            if success:
                self.logger.info(f"🎯 Fallback completed successfully for: {movie_title}")
                return True
            else:
                self.logger.error(f"❌ Fallback failed for: {movie_title}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error handling download failure: {e}")
            return False
    
    def clear_active_fallback(self, movie_id: int):
        """
        Clear the blacklist for a movie when a download succeeds.
        This resets the fallback state for future attempts.
        
        Args:
            movie_id: Movie ID to clear
        """
        if movie_id in self.blacklisted_candidates:
            del self.blacklisted_candidates[movie_id]
            self.logger.info(f"✅ Cleared blacklist for movie ID {movie_id} (successful download)")
    
    async def download_with_fallback_protection(self, movie_title: str, radarr_id: int, 
                                              candidate_guid: str, session: aiohttp.ClientSession,
                                              user_selection_index: Optional[int] = None,
                                              original_system_recommendation_index: Optional[int] = None) -> bool:
        """
        Download a candidate with automatic fallback protection.
        This is the main entry point for protected downloads.
        
        Args:
            movie_title: Title of the movie
            radarr_id: Radarr movie ID
            candidate_guid: GUID of the candidate to download
            session: HTTP session
            user_selection_index: If user manually selected a candidate, use its index
            original_system_recommendation_index: Index of original system recommendation
            
        Returns:
            bool: True if download initiated successfully (note: this doesn't guarantee success)
        """
        try:
            self.logger.info(f"🛡️ Starting protected download for: {movie_title}")
            
            # First, disable Radarr auto-monitoring to prevent external interference
            await self.disable_radarr_auto_monitoring(radarr_id, session)
            
            # Start continuous monitoring enforcement to prevent Radarr from re-enabling
            # This runs in the background for 60 minutes (longer for initial downloads)
            import asyncio
            asyncio.create_task(self.enforce_monitoring_disabled(radarr_id, session, duration_minutes=60))
            
            # Trigger the initial download
            # Note: In a real implementation, you would trigger the actual download here
            # For now, we'll just log that we would start the download
            self.logger.info(f"📥 Initiating download: {candidate_guid}")
            self.logger.info(f"   🎬 Movie: {movie_title}")
            self.logger.info(f"   🆔 Radarr ID: {radarr_id}")
            self.logger.info(f"   🛡️ Continuous monitoring enforcement active for 60 minutes")
            
            # The actual download monitoring and failure detection would be handled
            # by your telemetry system, which would call handle_download_failure() if needed
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error in protected download: {e}")
            return False
    
    def report_failure_from_telemetry(self, movie_title: str, failed_guid: str, radarr_id: int,
                                    user_selection_index: Optional[int] = None,
                                    original_system_recommendation_index: Optional[int] = None):
        """
        Report a failure from the telemetry system. This should be called by your
        telemetry monitoring when it detects a download failure.
        
        Args:
            movie_title: Title of the movie
            failed_guid: GUID of the failed download
            radarr_id: Radarr movie ID
            user_selection_index: If user manually selected a candidate, use its index
            original_system_recommendation_index: Index of original system recommendation
        """
        self.logger.info(f"📊 Telemetry reported failure for: {movie_title}")
        
        # Schedule the fallback handling
        async def _handle_failure():
            async with aiohttp.ClientSession() as session:
                await self.handle_download_failure(
                    movie_title, failed_guid, radarr_id, session,
                    user_selection_index=user_selection_index,
                    original_system_recommendation_index=original_system_recommendation_index
                )
        
        # Run the fallback handling
        asyncio.create_task(_handle_failure())


# Example usage
if __name__ == "__main__":
    # Test the corrected logic
    print("🧪 Testing CORRECTED Intelligent Fallback Logic")
    print("=" * 60)
    
    # Setup logging
    logging.basicConfig(level=logging.INFO, format='%(message)s')
    logger = logging.getLogger(__name__)
    
    # Create fallback system
    settings = {"General": {"workspace_dir": "workspace"}}
    fallback_system = IntelligentFallbackSystem(settings, logger)
    
    # Mock Top Gun Maverick candidates (based on your actual data)
    candidates = [
        {"title": "Top.Gun.Maverick.2022.IMAX.2160p.10bit.Bluray.AV1", "guid": "guid_1", "size": 4.65 * (1024**3)},  # 4.65 GB
        {"title": "Top.Gun.Maverick.2022.IMAX.UHD.BluRay.1080p", "guid": "guid_2", "size": 8.35 * (1024**3)},       # 8.35 GB
        {"title": "Top.Gun.Maverick.2022.2160p.PMTP.WEB-DL", "guid": "guid_3", "size": 15.08 * (1024**3)},          # 15.08 GB
        {"title": "Top.Gun.Maverick.2022.2160p.ATVP.WEB-DL", "guid": "guid_5", "size": 15.61 * (1024**3)},          # 15.61 GB (#5)
        {"title": "Top.Gun.Maverick.2022.UHD.BluRay.REMUX", "guid": "guid_22", "size": 71.86 * (1024**3)},           # 71.86 GB (#22)
        {"title": "Top.Gun.Maverick.2022.Multi.UHD.Bluray", "guid": "guid_26", "size": 108.18 * (1024**3)},          # 108.18 GB (#26)
    ]
    
    preflight_data = {
        "movie_id": 123,
        "acceptable_candidates": candidates
    }
    
    print("📊 Test Scenario: System recommended #22 (71.86 GB), #22 failed")
    print("Expected: Try #21, #20, #19... (smaller files)")
    print()
    
    # Test with system recommendation failure
    next_candidate = fallback_system.find_next_candidate(
        preflight_data=preflight_data,
        failed_guid="guid_22",  # #22 failed
        user_selection_index=None,  # System recommendation
        original_system_recommendation_index=None
    )
    
    if next_candidate:
        print(f"✅ RESULT: Would try smaller file candidate")
        print(f"   📍 Logic: If large file (#22, 71.86 GB) failed, try smaller alternatives")
    else:
        print("❌ RESULT: No candidate found")
    
    print()
    print("🧠 CORRECTED Understanding:")
    print("• Rankings are by FILE SIZE (#1 = smallest, higher = larger)")
    print("• Move UP toward smaller files when large files fail")  
    print("• This makes logical sense: if 72GB file fails, try 16GB instead")
    print("• Blacklisting prevents infinite retry loops")
