#!/usr/bin/env python3
"""
Terminal Output Logger Utility

Captures and logs all terminal output (STDOUT and STDERR) to timestamped files.
"""

import sys
import os
import threading
from datetime import datetime, timed<PERSON>ta
from pathlib import Path
from io import String<PERSON>
from typing import Optional, Any, TextIO
import types


class TerminalLogger:
    """
    Context manager that captures all terminal output and saves it to a log file.
    """
    
    def __init__(self, script_name: str, log_dir: Optional[str] = None):
        """
        Initialize the terminal logger.
        
        Args:
            script_name: Name of the script (without .py extension)
            log_dir: Directory to save logs (defaults to logs/ in project root)
        """
        self.script_name = script_name
        self.start_time = datetime.now()
        
        # Setup log directory
        if log_dir is None:
            project_root = Path(__file__).parent.parent.parent
            self.log_dir = project_root / "logs"
        else:
            self.log_dir = Path(log_dir)
        
        self.log_dir.mkdir(exist_ok=True)
        
        # Create timestamped log filename with readable format (YYYY-MM-DD_HH-MM-SS-AM/PM)
        timestamp = self.start_time.strftime("%Y-%m-%d_%I-%M-%S-%p")
        self.log_filename = f"{script_name}_{timestamp}.txt"
        self.log_filepath = self.log_dir / self.log_filename
        
        # Store original stdout/stderr
        self.original_stdout = sys.stdout
        self.original_stderr = sys.stderr
        
        # Create custom streams
        self.log_file: Optional[TextIO] = None
        self.stdout_logger: Optional[LoggerStream] = None
        self.stderr_logger: Optional[LoggerStream] = None
        
        # Thread safety
        self.lock = threading.Lock()
    
    def __enter__(self):
        """Start logging terminal output."""
        # Open log file
        self.log_file = open(self.log_filepath, 'w', encoding='utf-8', buffering=1)
        
        # Write header
        self._write_header()
        
        # Create logger streams
        self.stdout_logger = LoggerStream(self.log_file, self.original_stdout, 
                                        "STDOUT", self.start_time, self.lock)
        self.stderr_logger = LoggerStream(self.log_file, self.original_stderr, 
                                        "STDERR", self.start_time, self.lock)
        
        # Replace sys streams
        sys.stdout = self.stdout_logger
        sys.stderr = self.stderr_logger
        
        # Log start message
        print(f"📝 Terminal logging started for {self.script_name}")
        print(f"📄 Log file: {self.log_filepath}")
        print(f"🕐 Started at: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print("------------------------------------------------------------")
        
        return self
    
    def __exit__(self, exc_type: Optional[type], exc_val: Optional[Exception], exc_tb: Optional[types.TracebackType]) -> None:
        """Stop logging and restore original streams."""
        # Log end message
        end_time = datetime.now()
        duration = end_time - self.start_time
        print("------------------------------------------------------------")
        print(f"🏁 Terminal logging ended for {self.script_name}")
        print(f"🕐 Ended at: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"⏱️ Total duration: {duration}")
        print(f"📄 Log saved to: {self.log_filepath}")
        
        # Restore original streams
        sys.stdout = self.original_stdout
        sys.stderr = self.original_stderr
        
        # Close log file
        if self.log_file:
            self._write_footer(end_time, duration)
            self.log_file.close()
    
    def _write_header(self):
        """Write log file header."""
        header = f"""=== TERMINAL OUTPUT LOG ===
Script: {self.script_name}
Started: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}
Log File: {self.log_filepath}
==================================================

"""
        self.log_file.write(header)
        self.log_file.flush()
    
    def _write_footer(self, end_time: datetime, duration: timedelta) -> None:
        """Write log file footer."""
        footer = f"""

==================================================
=== TERMINAL OUTPUT LOG END ===
Script: {self.script_name}
Ended: {end_time.strftime('%Y-%m-%d %H:%M:%S')}
Duration: {duration}
==================================================
"""
        self.log_file.write(footer)
        self.log_file.flush()


class LoggerStream:
    """
    Custom stream that writes to both original stream and log file.
    """
    
    def __init__(self, log_file: TextIO, original_stream: TextIO, stream_type: str, start_time: datetime, lock: threading.Lock):
        self.log_file = log_file
        self.original_stream = original_stream
        self.stream_type = stream_type
        self.start_time = start_time
        self.lock = lock
        self.encoding = getattr(original_stream, 'encoding', 'utf-8')
    
    def write(self, text: str) -> None:
        """Write text to both original stream and log file."""
        if not text:
            return
        
        # Write to original stream
        try:
            self.original_stream.write(text)
            self.original_stream.flush()
        except Exception:
            pass  # Continue even if original stream fails
        
        # Write to log file with timestamp and stream type
        with self.lock:
            try:
                current_time = datetime.now()
                elapsed = current_time - self.start_time
                
                # Format elapsed time as +H:MM:SS
                total_seconds = int(elapsed.total_seconds())
                hours = total_seconds // 3600
                minutes = (total_seconds % 3600) // 60
                seconds = total_seconds % 60
                elapsed_str = f"+{hours}:{minutes:02d}:{seconds:02d}"
                
                timestamp = current_time.strftime('%Y-%m-%d %H:%M:%S')
                
                # Split text into lines and log each line
                lines = text.rstrip('\n').split('\n') if text.strip() else ['']
                for line in lines:
                    log_entry = f"[{timestamp}] [{self.stream_type}] [{elapsed_str}] {line}\n"
                    self.log_file.write(log_entry)
                
                self.log_file.flush()
            except Exception as e:
                # Fallback: write to original stream if logging fails
                try:
                    self.original_stream.write(f"[LOG ERROR: {e}]\n")
                except Exception:
                    pass
    
    def flush(self):
        """Flush both streams."""
        try:
            self.original_stream.flush()
        except Exception:
            pass
        
        try:
            self.log_file.flush()
        except Exception:
            pass
    
    def __getattr__(self, name):
        """Delegate other attributes to original stream."""
        return getattr(self.original_stream, name)


def start_terminal_logging(script_name: str, log_dir: Optional[str] = None) -> TerminalLogger:
    """
    Convenience function to start terminal logging.
    
    Args:
        script_name: Name of the script (without .py extension)
        log_dir: Directory to save logs (defaults to logs/ in project root)
    
    Returns:
        TerminalLogger context manager
    """
    return TerminalLogger(script_name, log_dir)


# Example usage:
if __name__ == "__main__":
    # Test the logger
    with start_terminal_logging("test_script"):
        print("This is a test message to STDOUT")
        print("This is another test message", file=sys.stderr)
        print("Multiple")
        print("Lines")
        print("Of output")
