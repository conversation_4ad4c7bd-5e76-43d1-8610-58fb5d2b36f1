import asyncio
import tempfile
from pathlib import Path
import os
import unittest

from _internal.src.file_organizer import organize_tv_show
from _internal.utils.tv_show_naming import TVShowNamingHelper


class DummyLogger:
    def __getattr__(self, name):
        def _log(*args, **kwargs):
            pass
        return _log


def run_with_policy(policy: str, tmp_path: Path) -> bool:
    organized_base = tmp_path / "workspace" / "2_downloaded_and_organized"
    organized_base.mkdir(parents=True)

    # Existing episode
    dest_dir = organized_base / "tv_shows" / "1080p" / "Show Name (2020)" / "Season 01"
    dest_dir.mkdir(parents=True)
    existing = dest_dir / "S01E01.mkv"
    existing.write_bytes(b"old")

    # Download single episode
    download_dir = tmp_path / "workspace" / "1_downloading" / "complete_raw" / "Show.Name.S01E01.1080p"
    download_dir.mkdir(parents=True)
    new_file = download_dir / "Show.Name.S01E01.1080p.mkv"
    new_file.write_bytes(b"new")

    # Tweak timestamps/sizes as needed
    if policy == "larger":
        # Keep new larger than old
        existing.write_bytes(b"x")  # 1 byte
        new_file.write_bytes(b"x" * 10)  # 10 bytes
    elif policy == "newer":
        # Ensure new mtime > old mtime
        os.utime(existing, (existing.stat().st_atime - 100, existing.stat().st_mtime - 100))
        os.utime(new_file, None)
    elif policy == "skip":
        # Ensure new looks smaller/older
        existing.write_bytes(b"x" * 10)
        new_file.write_bytes(b"x")
        os.utime(existing, None)
        os.utime(new_file, (new_file.stat().st_atime - 100, new_file.stat().st_mtime - 100))

    content_info = {"title": "Show Name", "year": 2020}
    helper = TVShowNamingHelper()
    logger = DummyLogger()
    settings = {"TV": {"single_episode_overwrite_policy": policy}}

    ok = asyncio.run(organize_tv_show(content_info, str(new_file), download_dir, organized_base, "1080p", helper, logger, settings))
    return ok, existing, download_dir


class TestTVOverwritePolicy(unittest.TestCase):
    def test_replace_policy(self):
        with tempfile.TemporaryDirectory() as tmp:
            ok, existing, dl = run_with_policy("replace", Path(tmp))
            self.assertTrue(ok)
            self.assertTrue(existing.exists())
            self.assertEqual(existing.read_bytes(), b"new")
            self.assertFalse((dl / "Show.Name.S01E01.1080p.mkv").exists())

    def test_larger_policy(self):
        with tempfile.TemporaryDirectory() as tmp:
            ok, existing, dl = run_with_policy("larger", Path(tmp))
            self.assertTrue(ok)
            self.assertTrue(existing.exists())
            # new is larger so should replace
            self.assertEqual(existing.read_bytes(), b"xxxxxxxxxx")
            self.assertFalse((dl / "Show.Name.S01E01.1080p.mkv").exists())

    def test_newer_policy(self):
        with tempfile.TemporaryDirectory() as tmp:
            ok, existing, dl = run_with_policy("newer", Path(tmp))
            self.assertTrue(ok)
            self.assertTrue(existing.exists())
            # newer should replace
            self.assertEqual(existing.read_bytes(), b"new")
            self.assertFalse((dl / "Show.Name.S01E01.1080p.mkv").exists())

    def test_skip_policy(self):
        with tempfile.TemporaryDirectory() as tmp:
            ok, existing, dl = run_with_policy("skip", Path(tmp))
            self.assertTrue(ok)
            self.assertTrue(existing.exists())
            # skip policy should keep old
            self.assertEqual(existing.read_bytes(), b"xxxxxxxxxx")
            # original file should remain in place since we didn't replace
            self.assertTrue((dl / "Show.Name.S01E01.1080p.mkv").exists())


if __name__ == "__main__":
    unittest.main()

