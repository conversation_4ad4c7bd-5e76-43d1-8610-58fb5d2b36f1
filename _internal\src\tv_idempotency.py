#!/usr/bin/env python3
from __future__ import annotations

from pathlib import Path
from typing import Any, Dict, Optional

from _internal.src.idempotency import IdempotencyIndex, compute_episode_key


def mark_episode_organized(index: Optional[IdempotencyIndex], series_id: Optional[int], season: Optional[int], episode: Optional[int], episode_id: Optional[int], final_path: Path) -> None:
    try:
        idx = index or IdempotencyIndex()
        key = compute_episode_key(series_id=series_id, season=season, episode=episode, episode_id=episode_id)
        if key:
            idx.mark_processed(key, 'organized', {'file_path': str(final_path)})
    except Exception:
        pass

