#!/usr/bin/env python3
from __future__ import annotations

import json
import os
from pathlib import Path
from typing import Any, Dict, Optional
from datetime import datetime, timezone
import asyncio


class NoopQueue:
    async def publish(self, event_type: str, data: Dict[str, Any]) -> None:
        return


class EventQueue:
    """
    Lightweight file-backed event queue (JSON Lines) for cross-stage observability.
    Writes to _internal/state/event_queue/events.jsonl by default.
    """

    def __init__(self, root: Optional[Path] = None, enabled: bool = True):
        self.enabled = enabled
        if not enabled:
            self.base = None
            return
        if root is None:
            root = Path("_internal/state/event_queue")
        self.base = root
        self.base.mkdir(parents=True, exist_ok=True)
        self.file_path = self.base / "events.jsonl"

    async def publish(self, event_type: str, data: Dict[str, Any]) -> None:
        if not self.enabled or not self.base:
            return
        record = {
            "ts": datetime.now(timezone.utc).isoformat(),
            "type": event_type,
            "data": data,
        }
        line = json.dumps(record, ensure_ascii=False)
        # offload blocking file I/O
        await asyncio.to_thread(self._append_line, line)

    def _append_line(self, line: str) -> None:
        with open(self.file_path, "a", encoding="utf-8") as f:
            f.write(line + "\n")


def get_event_queue(settings: Optional[Dict[str, Any]] = None) -> EventQueue | NoopQueue:
    enabled = True
    out_dir: Optional[str] = None
    if settings and isinstance(settings, dict):
        cfg = settings.get("EventQueue", {}) or {}
        if isinstance(cfg, dict):
            enabled = cfg.get("enabled", True)
            out_dir = cfg.get("dir")
    root = Path(out_dir) if out_dir else None
    return EventQueue(root=root, enabled=enabled)

