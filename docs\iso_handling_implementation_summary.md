# ISO File Handling Implementation Summary

## Overview
Successfully implemented comprehensive .iso disk image handling for the PlexAutomator system. The system can now process Blu-ray disk images and extract the main video files for standard post-processing.

## What Was Implemented

### 1. Updated Constants (`_internal/src/constants.py`)
- **Removed** `.iso` from `VIDEO_EXTENSIONS` (correctly - ISO is not a video format)
- **Added** `DISK_IMAGE_EXTENSIONS` containing `.iso`
- **Kept** `.m2ts` in `VIDEO_EXTENSIONS` (for extracted Blu-ray video files)

### 2. Enhanced File System Helpers (`_internal/src/fs_helpers.py`)
- **Added** `find_disk_image_files()` - Finds .iso files in directories
- **Added** `find_all_media_files()` - Finds both video and disk image files
- **Added** `get_main_video_file()` - Smart main video detection with ISO support
  - Automatically detects and processes .iso files
  - Extracts main video using 7-Zip
  - Falls back to regular video file detection
  - Returns the largest/main video file

### 3. ISO Handler (`_internal/src/iso_handler.py`)
- **Enhanced** existing mounting-based approach
- **Added** 7-Zip extraction method (preferred)
- **Dual Strategy**:
  1. **Primary**: 7-Zip extraction (avoids mounting issues)
  2. **Fallback**: Windows PowerShell mounting
- **Features**:
  - Automatic 7-Zip detection across common installation paths
  - Intelligent main video file detection (largest .m2ts in BDMV/STREAM)
  - File size verification to avoid re-extraction
  - Proper error handling and logging
  - Context manager for cleanup

### 4. Integration with Post-Processing (`02_download_and_organize.py`)
- **Updated** main video file detection calls
- **Replaced** simple `find_video_files()` with `get_main_video_file()`
- **Maintains** backward compatibility with existing video files

## Workflow

### For Downloads Containing .iso Files:
1. **Detection**: System scans download directory
2. **ISO Found**: Detects .iso file using `DISK_IMAGE_EXTENSIONS`
3. **Extraction**: Uses 7-Zip to extract largest .m2ts from BDMV/STREAM
4. **Optimization**: Checks if extraction already exists to avoid re-processing
5. **Integration**: Extracted .m2ts file enters standard processing pipeline
6. **Organization**: File gets renamed, organized, and placed in final library

### Example: "There Will Be Blood" Processing:
```
Input:  there.will.be.blood.2007.multi.complete.bluray.internal-veil.iso (44.98 GB)
↓
Extract: BDMV/STREAM/00074.m2ts → there.will.be.blood.2007.multi.complete.bluray.internal-veil.m2ts (37.51 GB)
↓
Process: Standard pipeline (MKV processing, encoding, subtitles, etc.)
↓
Output:  Organized movie file in Plex library
```

## Technical Details

### 7-Zip Integration:
- **Command**: `7z l` (list contents) and `7z e` (extract)
- **Path Discovery**: Checks common installation locations
- **File Detection**: Parses 7-Zip output to find BDMV/STREAM/*.m2ts files
- **Size Comparison**: Determines largest file (main movie)
- **Selective Extraction**: Only extracts the main video file

### Error Handling:
- **7-Zip Missing**: Falls back to mounting method
- **Extraction Failure**: Logs errors and continues with other files
- **File Access Issues**: Graceful handling of permission/lock issues
- **Invalid ISOs**: Proper validation and error reporting

### Performance Optimizations:
- **Smart Caching**: Avoids re-extracting if file already exists with correct size
- **Selective Extraction**: Only extracts main movie file, not entire ISO
- **Background Processing**: Non-blocking for large file operations

## Files Modified

1. `_internal/src/constants.py` - Updated extension definitions
2. `_internal/src/fs_helpers.py` - Added ISO-aware file detection
3. `_internal/src/iso_handler.py` - Enhanced with 7-Zip extraction
4. `02_download_and_organize.py` - Integrated ISO handling into main workflow

## Dependencies

### Required:
- **7-Zip**: For extraction (auto-detected)
- **PowerShell**: For mounting fallback (Windows built-in)

### Optional:
- **Windows Disk Management**: For mounting method

## Testing

Successfully tested with:
- **Real ISO**: "There Will Be Blood" Blu-ray (44.98 GB ISO → 37.51 GB .m2ts)
- **7-Zip Detection**: Automatic finding of 7-Zip installation
- **File Extraction**: BDMV/STREAM/00074.m2ts successfully extracted
- **Integration**: Extracted file properly detected as main video file
- **Size Verification**: Correct file size detection and validation

## Future Enhancements

1. **Multiple Video Tracks**: Handle ISOs with multiple main videos
2. **Other Formats**: Support for additional disk image formats (.img, .bin/cue)
3. **Subtitle Extraction**: Extract subtitle tracks from Blu-ray structure
4. **Chapter Information**: Preserve chapter markers from disc structure
5. **Menu Extraction**: Handle complex disc menu structures

## Usage

The system now automatically handles .iso files with no additional configuration required. Simply place Blu-ray ISO files in the download directory and the system will:

1. Detect the .iso file
2. Extract the main video track
3. Process it through the standard pipeline
4. Organize it into the Plex library

This seamlessly extends the existing workflow to support Blu-ray disk images!
