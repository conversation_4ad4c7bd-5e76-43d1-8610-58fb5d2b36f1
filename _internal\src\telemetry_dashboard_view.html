<!doctype html>
<html>
<head>
  <meta charset="utf-8" />
  <title>Telemetry Dashboard</title>
  <style>
    body { font-family: sans-serif; margin: 1rem; }
    table { border-collapse: collapse; }
    th, td { border: 1px solid #ddd; padding: 4px 8px; }
  </style>
</head>
<body>
  <h1>Event Summary</h1>
  <input type="file" id="file" accept=".csv,.json" />
  <button id="load">Load</button>
  <div id="out"></div>

  <script>
    document.getElementById('load').onclick = async () => {
      const fileInput = document.getElementById('file');
      if (!fileInput.files.length) { alert('Choose a CSV or JSON file'); return; }
      const file = fileInput.files[0];
      const text = await file.text();
      let summary;
      if (file.name.endsWith('.json')) {
        summary = JSON.parse(text);
      } else {
        // Simple CSV viewer fallback: show raw
        summary = { raw_csv: text.split('\n').slice(0, 50) };
      }
      const out = document.getElementById('out');
      out.innerHTML = '';
      const pre = document.createElement('pre');
      pre.textContent = JSON.stringify(summary, null, 2);
      out.appendChild(pre);
    };
  </script>
</body>
</html>

