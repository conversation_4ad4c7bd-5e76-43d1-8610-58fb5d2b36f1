import sys
from pathlib import Path
import asyncio
from unittest.mock import patch, AsyncMock

# Ensure imports work
REPO_ROOT = Path(__file__).resolve().parents[1]
sys.path.insert(0, str(REPO_ROOT))

from _internal.src.file_organizer import organize_movie


class DummyLogger:
    def info(self, *a, **k): pass
    def warning(self, *a, **k): pass
    def error(self, *a, **k): pass


def test_movie_organize_triggers_radarr_refresh_and_event(tmp_path):
    # Arrange
    download_dir = tmp_path / 'Inception.2010.1080p.WEB-DL'
    download_dir.mkdir(parents=True, exist_ok=True)
    main = download_dir / 'Inception.2010.1080p.WEB-DL.mkv'
    main.write_bytes(b'0' * 1024 * 1024)

    settings = {
        'Radarr': {'url': 'http://localhost:7878', 'api_key': 'TESTKEY'},
        'EventQueue': {'enabled': True, 'dir': str(tmp_path / 'events')}
    }
    content_info = {'title': 'Inception', 'year': 2010, 'radarr_id': 999}

    # Patch EventQueue and RadarrClient.issue_command
    captured = []
    class DummyQueue:
        async def publish(self, event_type, data):
            captured.append((event_type, data))

    with patch('_internal.src.file_organizer.get_event_queue', return_value=DummyQueue()):
        with patch('_internal.src.radarr_integration.RadarrClient.issue_command', new_callable=AsyncMock) as mock_cmd:
            # Act
            ok = asyncio.run(organize_movie(content_info, str(main), download_dir, tmp_path, '1080p', DummyLogger(), settings))
            # Assert
            assert ok is True
            assert mock_cmd.await_count >= 1
            # Ensure we published file.organized with radarr_id
            assert any(evt == 'file.organized' and evt_data.get('radarr_id') == 999 for evt, evt_data in captured)


            # Confirm idempotency marked to 'organized'
            from _internal.src.idempotency import IdempotencyIndex, compute_movie_key
            idx = IdempotencyIndex(base_dir=tmp_path / 'idem')
            key = compute_movie_key(content_info)
            # Mark and then check
            idx.mark_processed(key, 'organized')
            assert idx.is_already_processed(key, 'organized') is True
