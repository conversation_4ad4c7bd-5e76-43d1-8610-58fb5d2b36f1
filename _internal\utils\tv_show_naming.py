#!/usr/bin/env python3
"""Clean restored TV show naming utilities.

Exports:
 - TVShowInfo (dataclass)
 - TVShowNamingHelper (parsing + path helpers)
 - extract_resolution (public wrapper for tests)
"""

from __future__ import annotations

import logging
import re
from dataclasses import dataclass
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union

logger = logging.getLogger(__name__)


@dataclass(slots=True)
class TVShowInfo:
    series_title: str
    year: Optional[int] = None
    season: Optional[int] = None
    episode: Optional[int] = None
    episode_title: Optional[str] = None
    resolution: str = "1080p"
    is_multi_season: bool = False
    end_episode: Optional[int] = None         # For ranges SxxE01-02
    episode_list: Optional[List[int]] = None  # For explicit multi-episode sequences


class TVShowNamingHelper:
    def __init__(self) -> None:  # pragma: no cover - trivial
        self.logger = logging.getLogger(__name__)

    # ---------------- Public API -----------------
    def parse_tv_show_filename(self, filename: str) -> TVShowInfo:
        clean_name = self._clean_filename(filename)
        info = TVShowInfo(series_title="Unknown")

        season, episode, end_ep, ep_list = self._extract_season_episode_multi(clean_name)
        info.season = season
        info.episode = episode
        info.end_episode = end_ep
        info.episode_list = ep_list

        # Determine prefix for year detection
        cut_points: List[int] = []
        for m in [
            re.search(r'S\d{1,2}E\d{1,3}', clean_name, re.IGNORECASE),
            re.search(r'\d{1,2}x\d{1,3}', clean_name),
            re.search(r'Season\s*\d{1,2}', clean_name, re.IGNORECASE),
            re.search(r'Episode\s*\d{1,3}', clean_name, re.IGNORECASE),
        ]:
            if m:
                cut_points.append(m.start())
        cut_index = min(cut_points) if cut_points else len(clean_name)
        prefix = clean_name[:cut_index]
        year_match = re.search(r'\b(19|20)(\d{2})\b', prefix)
        if year_match:
            info.year = int(year_match.group(0))
            clean_name = prefix[:year_match.start()].strip()

        info.series_title = self._extract_series_title(clean_name)
        info.resolution = self._extract_resolution(filename)
        info.is_multi_season = bool(info.season)
        return info

    def generate_plex_tv_structure(self, tv_info: TVShowInfo) -> Dict[str, str]:
        clean_title = self._clean_series_title(tv_info.series_title)
        series_folder = f"{clean_title} ({tv_info.year})" if tv_info.year else clean_title
        season_folder = f"Season {tv_info.season:02d}" if tv_info.season else ""

        episode_filename = ""
        if tv_info.season and tv_info.episode:
            if tv_info.end_episode and tv_info.end_episode != tv_info.episode:
                episode_filename = f"S{tv_info.season:02d}E{tv_info.episode:02d}-{tv_info.end_episode:02d}.mkv"
            elif tv_info.episode_list and len(tv_info.episode_list) > 1:
                episode_filename = f"S{tv_info.season:02d}E{tv_info.episode_list[0]:02d}-{tv_info.episode_list[-1]:02d}.mkv"
            else:
                episode_filename = f"S{tv_info.season:02d}E{tv_info.episode:02d}.mkv"

        full_series_path = f"{series_folder}/{season_folder}".rstrip("/")
        full_episode_path = (
            f"{series_folder}/{season_folder}/{episode_filename}" if season_folder else f"{series_folder}/{episode_filename}"
        )
        return {
            'series_folder': series_folder,
            'season_folder': season_folder,
            'episode_filename': episode_filename,
            'full_series_path': full_series_path,
            'full_episode_path': full_episode_path,
        }

    def determine_season_structure(self, _series_title: str, available_seasons: List[int]) -> bool:
        if len(available_seasons) == 1 and available_seasons[0] == 1:
            return False
        if len(available_seasons) > 1 or (len(available_seasons) == 1 and available_seasons[0] > 1):
            return True
        return False

    def generate_workspace_tv_paths(self, tv_info: TVShowInfo, stage: str) -> Dict[str, Union[Path, None]]:
        workspace_base = Path("workspace")
        structure = self.generate_plex_tv_structure(tv_info)
        stage_base = workspace_base / stage / "tv_shows" / tv_info.resolution
        series_path = stage_base / structure['series_folder']
        episode_dir = series_path / structure['season_folder'] if structure['season_folder'] else series_path
        episode_file: Optional[Path] = (
            episode_dir / structure['episode_filename'] if structure['episode_filename'] else None
        )
        return {
            'workspace_base': workspace_base,
            'stage_base': stage_base,
            'series_dir': series_path,
            'episode_dir': episode_dir,
            'episode_file': episode_file,
        }

    def validate_tv_structure(self, base_path: Path, tv_info: TVShowInfo) -> Dict[str, bool]:
        structure = self.generate_plex_tv_structure(tv_info)
        series_path = base_path / structure['series_folder']
        results: Dict[str, bool] = {
            'series_folder_exists': series_path.exists(),
            'has_proper_naming': bool(re.match(r'^.*\(\d{4}\)$', structure['series_folder'])),
            'season_structure_correct': True,
            'episode_naming_correct': True,
        }
        if series_path.exists():
            if structure['season_folder']:
                season_path = series_path / structure['season_folder']
                results['season_structure_correct'] = season_path.exists()
            if structure['episode_filename']:
                episode_path = series_path / structure['season_folder'] if structure['season_folder'] else series_path
                episode_files = list(episode_path.glob('S*.mkv'))
                results['episode_naming_correct'] = bool(episode_files)
        return results

    # -------- Internal helpers ---------
    def _clean_series_title(self, title: str) -> str:
        if not title:
            return "Unknown Series"
        clean_title = re.sub(r'[<>:"/\\|?*]', '', title)
        clean_title = re.sub(r'[._\-\[\]\(\)]', ' ', clean_title)
        for pattern in [r'S\d+E\d+.*', r'\d+x\d+.*', r'Season\s*\d+.*', r'Episode\s*\d+.*']:
            clean_title = re.sub(pattern, '', clean_title, flags=re.IGNORECASE)
        for term in [
            '2160p','1080p','720p','4K','UHD','FHD','HD','BluRay','WEBRip','WEB','WEB-DL','HDRip','HDTV','DVDRip',
            'x264','x265','H264','H265','HEVC','DDP','DD+','DD','AAC','DTS','FLAC','EAC3','AC3','AMZN','NF'
        ]:
            clean_title = re.sub(fr'\b{term}\b', '', clean_title, flags=re.IGNORECASE)

        # CRITICAL FIX: Remove years from title (they belong in parentheses, not in the title itself)
        # This prevents "FLCL 2000" from becoming "FLCL 2000 (2000)"
        clean_title = re.sub(r'\b(19|20)\d{2}\b', '', clean_title)

        clean_title = re.sub(r'\s+', ' ', clean_title).strip()
        return clean_title or "Unknown Series"

    def _clean_filename(self, filename: str) -> str:
        name = Path(filename).stem
        name = re.sub(r'[._\-\[\]()]', ' ', name)
        return re.sub(r'\s+', ' ', name).strip()

    def _extract_season_episode_multi(
        self, clean_name: str
    ) -> Tuple[Optional[int], Optional[int], Optional[int], Optional[List[int]]]:
        season: Optional[int] = None
        start_ep: Optional[int] = None
        end_ep: Optional[int] = None
        ep_list: Optional[List[int]] = None
        s_match = re.search(r'S(\d{1,2})', clean_name, re.IGNORECASE)
        if s_match:
            season = int(s_match.group(1))
        e_numbers = [int(n) for n in re.findall(r'[Ee](\d{1,3})', clean_name)]
        if season is not None and e_numbers:
            start_ep = e_numbers[0]
            if len(e_numbers) > 1:
                end_ep = e_numbers[-1]
                ep_list = e_numbers
            hyphen_match = re.search(r'[Ee](\d{1,3})\s*[-]\s*(\d{1,3})', clean_name)
            if hyphen_match:
                start_ep = int(hyphen_match.group(1))
                end_ep = int(hyphen_match.group(2))
                ep_list = [start_ep, end_ep]
            return season, start_ep, end_ep, ep_list
        x_match = re.search(r'(\d{1,2})x(\d{1,3})', clean_name)
        if x_match:
            season = int(x_match.group(1))
            start_ep = int(x_match.group(2))
            tail = clean_name[x_match.end():]
            more_x = re.findall(r'x(\d{1,3})', tail)
            hyphen = re.search(r'-\s*(\d{1,3})', tail)
            if more_x:
                ep_list = [start_ep] + [int(n) for n in more_x]
                end_ep = ep_list[-1] if len(ep_list) > 1 else None
            elif hyphen:
                end_ep = int(hyphen.group(1))
                ep_list = [start_ep, end_ep]
            return season, start_ep, end_ep, ep_list
        season_ep_match = re.search(r'Season\s*(\d{1,2}).*?Episode\s*(\d{1,3})', clean_name, re.IGNORECASE)
        if season_ep_match:
            return int(season_ep_match.group(1)), int(season_ep_match.group(2)), None, None
        
        # FLCL-style episode detection: "- 01 -" format (fixes FLCL season pack extraction)
        # Note: _clean_filename converts dashes to spaces, so we match spaces
        flcl_match = re.search(r'\s(\d{1,3})\s', clean_name)
        if flcl_match:
            start_ep = int(flcl_match.group(1))
            # If we found an episode but no season yet, assume season 1
            if season is None:
                season = 1
            return season, start_ep, None, None
            
        if season is None:
            season_match = re.search(r'Season\s*(\d{1,2})', clean_name, re.IGNORECASE)
            if season_match:
                season = int(season_match.group(1))
        if start_ep is None:
            episode_match = re.search(r'Episode\s*(\d{1,3})', clean_name, re.IGNORECASE)
            if episode_match:
                start_ep = int(episode_match.group(1))
        return season, start_ep, None, None

    def _extract_series_title(self, clean_name: str) -> str:
        title = clean_name
        for pattern in [r'S\d+E\d+.*', r'\d+x\d+.*', r'Season\s*\d+.*', r'Episode\s*\d+.*']:
            title = re.sub(pattern, '', title, flags=re.IGNORECASE)
        for term in [
            '2160p','1080p','720p','4K','UHD','FHD','HD','BluRay','WEBRip','WEB','WEB-DL','HDRip','HDTV','DVDRip',
            'x264','x265','H264','H265','HEVC','DDP','DD+','DD','AAC','DTS','FLAC','EAC3','AC3','AMZN','NF','MeGusta','NTb','CAKES','SPiRiT','TOMMY','ION10','YIFY','RARBG'
        ]:
            title = re.sub(fr'\b{term}\b', '', title, flags=re.IGNORECASE)
        title = re.sub(r'[-_]\s*[A-Za-z0-9]{2,}$', '', title)
        title = re.sub(r'\s+', ' ', title).strip().rstrip('.')
        return title or "Unknown Series"

    def _extract_resolution(self, filename: str) -> str:
        if re.search(r'2160p|4K|UHD', filename, re.IGNORECASE):
            return "4k"
        if re.search(r'1080p|FHD', filename, re.IGNORECASE):
            return "1080p"
        if re.search(r'720p|HD', filename, re.IGNORECASE):
            return "720p"
        return "1080p"


def extract_resolution(filename: str) -> str:
    """Public resolution wrapper for tests (mirrors internal heuristic)."""
    if re.search(r'2160p|4K|UHD', filename, re.IGNORECASE):
        return "4k"
    if re.search(r'1080p|FHD', filename, re.IGNORECASE):
        return "1080p"
    if re.search(r'720p|HD', filename, re.IGNORECASE):
        return "720p"
    return "1080p"


__all__ = ["TVShowInfo", "TVShowNamingHelper", "extract_resolution"]

