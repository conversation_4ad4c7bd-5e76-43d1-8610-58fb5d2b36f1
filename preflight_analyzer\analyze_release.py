import argparse
import json
import asyncio
import os
import re
from typing import Any, Match
from pathlib import Path
from datetime import datetime, timezone
from typing import List, Dict, Any

from .nzb_parser import NZBParser
from .segment_sampler import SegmentSampler
from .async_nntp_probe import AsyncNNT<PERSON><PERSON>, NNTPServerConfig
from .risk_scoring import RiskScorer

# Placeholder history / stats (prototype only)
POSTER_HISTORY: Dict[str, float] = {}
GROUP_HISTORY: Dict[str, float] = {}


_ENV_PATTERN = re.compile(r"\$(?:\{(?P<brace>[A-Za-z_][A-Za-z0-9_]*)\}|(?P<plain>[A-Za-z_][A-Za-z0-9_]*))")

def _expand_env(value: Any) -> Any:
    if not isinstance(value, str):
        return value
    def repl(m: Match[str]) -> str:
        key = m.group('brace') or m.group('plain') or ''
        return os.environ.get(key, '')
    return _ENV_PATTERN.sub(repl, value)

def load_servers(path: Path) -> List[NNTPServerConfig]:
    data = json.loads(path.read_text())
    servers: List[NNTPServerConfig] = []
    for entry in data:
        expanded = {k: _expand_env(v) for k, v in entry.items()}
        servers.append(NNTPServerConfig(
            host=expanded['host'],
            port=expanded.get('port', 563),
            username=expanded.get('username'),
            password=expanded.get('password'),
            use_ssl=expanded.get('use_ssl', True),
            name=expanded.get('name'),
            timeout=expanded.get('timeout', 10.0)
        ))
    return servers


async def analyze_one(nzb_path: Path, servers: List[NNTPServerConfig], retention_days: int, dry_run: bool, verbose: bool=False) -> Dict[str, Any]:
    if not nzb_path.exists():
        raise FileNotFoundError(f"NZB not found: {nzb_path}")
    try:
        nzb_meta = NZBParser().parse(nzb_path)
    except ValueError as e:
        # Return minimal report with error context
        return {
            'nzb': str(nzb_path),
            'error': str(e),
            'file_count': 0,
            'data_segments': 0,
            'par2_segments': 0,
            'estimated_parity_blocks': 0,
            'sample_size': 0,
            'probe_missing_ratio': 0.0,
            'risk_score': 1.0,
            'risk_level': 'high',
            'dry_run': dry_run
        }
    sampler = SegmentSampler()
    sample_plan = sampler.plan(nzb_meta)

    # Build sample message IDs (flatten data segments indices to message IDs)
    message_ids: List[str] = []
    SAMPLE_CAP = 150
    if not dry_run:
        current_index = 0
        for f in nzb_meta.files:
            if f.is_par2:
                current_index += len(f.segments)
                continue
            for seg in f.segments:
                if current_index in sample_plan.selected_indices:
                    message_ids.append(seg.message_id)
                current_index += 1
                if len(message_ids) >= SAMPLE_CAP:
                    break
            if len(message_ids) >= SAMPLE_CAP:
                break

    if dry_run:
        probe_results: Dict[str, Any] = {}
        missing_ratio = 0.0
        error_ratio = 0.0
    else:
        probe = AsyncNNTPProbe(servers, verbose=verbose)
        probe_results = await probe.probe_sample(message_ids)
        miss_ratios = [pr.missing / pr.total for pr in probe_results.values() if pr.total]
        err_ratios = [pr.errors / pr.total for pr in probe_results.values() if pr.total]
        missing_ratio = min(1.0, sum(miss_ratios)/len(miss_ratios)) if miss_ratios else 0.0
        error_ratio = min(1.0, sum(err_ratios)/len(err_ratios)) if err_ratios else 0.0

    poster = nzb_meta.files[0].poster if nzb_meta.files else ''
    groups = nzb_meta.files[0].groups if nzb_meta.files else []
    poster_fail_rate = float(POSTER_HISTORY.get(poster, 0.0))
    group_fail_rate = float(GROUP_HISTORY.get(groups[0], 0.0)) if groups else 0.0

    first_date = nzb_meta.files[0].date if nzb_meta.files else 0
    age_days = int((datetime.now(timezone.utc) - datetime.fromtimestamp(first_date, tz=timezone.utc)).days) if first_date else 0

    obfuscated = False
    for f in nzb_meta.files:
        if f.filename_hint and len(f.filename_hint) > 0:
            if sum(c.isalnum() for c in f.filename_hint) / max(1, len(f.filename_hint)) > 0.9 and f.filename_hint.count('.') < 2:
                obfuscated = True
                break

    redundancy_blocks = nzb_meta.estimated_parity_blocks
    data_segments = nzb_meta.data_segments
    scorer = RiskScorer()
    components = scorer.build_components(
        age_days=age_days,
        retention_days=retention_days,
        poster_fail_rate=poster_fail_rate,
        group_fail_rate=group_fail_rate,
        redundancy_blocks=redundancy_blocks,
        data_segments=data_segments,
        probe_missing_ratio=missing_ratio,
        probe_error_ratio=error_ratio,
        obfuscated=obfuscated,
    )
    risk_score = scorer.score(components)
    # Aggregate sample probe counts (first server as representative for now)
    sample_total = sample_missing = sample_available = 0
    if probe_results:
        first_pr = next(iter(probe_results.values()))
        sample_total = first_pr.get('total', 0)
        sample_available = first_pr.get('available', 0)
        sample_missing = first_pr.get('missing', 0)

    # Estimate total missing segments based on sample (simple proportion)
    estimated_missing_segments = int(round(missing_ratio * data_segments)) if data_segments else 0
    # Simple decision heuristic
    if components.probe_error_ratio >= 0.5:
        decision = 'RETRY_PROBE'
    elif missing_ratio >= 0.3 and redundancy_blocks == 0:
        decision = 'REJECT_INCOMPLETE'
    elif missing_ratio >= 0.15 and redundancy_blocks < data_segments * 0.01:
        decision = 'RISKY_LOW_PARITY'
    else:
        decision = 'ACCEPT'
    return {
        'nzb': str(nzb_path),
        'file_count': nzb_meta.file_count,
        'data_segments': data_segments,
        'par2_segments': nzb_meta.par2_segments,
        'estimated_parity_blocks': redundancy_blocks,
        'sample_size': sample_plan.sample_size,
        'probe_missing_ratio': missing_ratio,
    'risk_score': risk_score,
        'risk_level': scorer.classify(risk_score),
        'age_days': age_days,
        'poster': poster,
        'groups': groups,
        'probe_results': {k: pr.__dict__ for k, pr in probe_results.items()},
    'probe_error_ratio': error_ratio,
        'sample_total': sample_total,
        'sample_available': sample_available,
        'sample_missing': sample_missing,
        'estimated_missing_segments': estimated_missing_segments,
        'decision': decision,
        'components': components.__dict__,
        'dry_run': dry_run,
    }


async def main():
    parser = argparse.ArgumentParser(description='Prototype NZB Preflight Analyzer')
    parser.add_argument('--nzb', required=True, help='Path to NZB file OR directory of NZBs')
    parser.add_argument('--server-config', required=False, help='JSON file with NNTP server configs (omit with --dry-run)')
    parser.add_argument('--retention-days', type=int, default=4000)
    parser.add_argument('--dry-run', action='store_true', help='Skip NNTP probing (static analysis only)')
    parser.add_argument('--jsonl', action='store_true', help='Emit one JSON object per line (batch mode)')
    parser.add_argument('--verbose', action='store_true', help='Verbose NNTP probe logging')
    args = parser.parse_args()

    target = Path(args.nzb)
    if args.dry_run:
        servers: List[NNTPServerConfig] = []
    else:
        if not args.server_config:
            raise SystemExit('ERROR: --server-config is required unless --dry-run is used')
        servers = load_servers(Path(args.server_config))

    if target.is_dir():
        nzb_files = sorted(target.glob('*.nzb'))
        if not nzb_files:
            raise SystemExit(f'No .nzb files found in directory: {target}')
        reports: List[Dict[str, Any]] = []
        for nzb in nzb_files:
            try:
                rep = await analyze_one(nzb, servers, args.retention_days, args.dry_run, verbose=args.verbose)
                reports.append(rep)
                if args.jsonl:
                    print(json.dumps(rep))
            except Exception as e:
                err = {'nzb': str(nzb), 'error': str(e)}
                reports.append(err)
                if args.jsonl:
                    print(json.dumps(err))
        if not args.jsonl:
            print(json.dumps(reports, indent=2))
    else:
        try:
            report = await analyze_one(target, servers, args.retention_days, args.dry_run, verbose=args.verbose)
        except Exception as e:
            raise SystemExit(str(e))
        print(json.dumps(report, indent=2))


if __name__ == '__main__':
    asyncio.run(main())
