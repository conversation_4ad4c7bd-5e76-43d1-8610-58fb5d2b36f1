#!/usr/bin/env python3
"""
Request Context Manager - Tracks original request scope across all pipeline stages

This system ensures that markers understand the original request context:
- "The Office (2005) S01E01, S02E01" = Track only 2 specific episodes  
- "Breaking Bad (2008)" = Track the ENTIRE series (all seasons/episodes from TVDB)

The request context is established in Stage 01 and passed through all subsequent stages
so that each marker knows exactly what "complete" means for that specific request.
"""

import json
from pathlib import Path
from typing import Dict, List, Optional, Union, Any
from datetime import datetime

class RequestContextManager:
    """
    Manages request context information across all pipeline stages.
    
    For TV shows, this determines what "complete" means:
    - Specific episodes: Only those episodes need to be complete
    - Full series: All episodes from TVDB need to be complete
    """
    
    def __init__(self, base_path: Path):
        """
        Initialize the request context manager.
        
        Args:
            base_path: Base directory path (e.g., season folder for TV shows)
        """
        self.base_path = Path(base_path)
        self.context_file = self.base_path / ".request_context.json"
    
    def create_context(self, request_data: Dict[str, Any]) -> bool:
        """
        Create request context from original request.
        
        Args:
            request_data: Original request information containing:
                - request_type: "specific_episodes", "multiple_episodes", "full_series", etc.
                - episodes: List of episode dicts for specific requests
                - tvdb_data: Full episode list from TVDB for series requests
                - original_request: Original user input string
                
        Returns:
            bool: True if context created successfully
        """
        try:
            context = {
                "created_at": datetime.now().isoformat(),
                "request_type": request_data.get("request_type", "unknown"),
                "original_request": request_data.get("original_request", ""),
                "scope": self._determine_scope(request_data),
                "tracking_data": self._extract_tracking_data(request_data)
            }
            
            # Ensure directory exists
            self.base_path.mkdir(parents=True, exist_ok=True)
            
            # Write context file
            with open(self.context_file, 'w', encoding='utf-8') as f:
                json.dump(context, f, indent=2, ensure_ascii=False)
            
            return True
            
        except Exception as e:
            print(f"Failed to create request context: {e}")
            return False
    
    def has_context(self) -> bool:
        """Check if request context exists for this path."""
        return self.context_file.exists()
    
    def get_context(self) -> Dict[str, Any]:
        """
        Get the request context for this path.
        
        Returns:
            Dict with request context or empty dict if not found
        """
        try:
            if not self.context_file.exists():
                return {}
                
            with open(self.context_file, 'r', encoding='utf-8') as f:
                return json.load(f)
                
        except Exception as e:
            print(f"Failed to read request context: {e}")
            return {}
    
    def get_expected_episodes(self) -> List[Dict[str, int]]:
        """
        Get list of episodes that should be tracked for this request.
        
        Returns:
            List of episode dicts: [{"season": 1, "episode": 1}, ...]
        """
        context = self.get_context()
        if not context:
            return []
            
        tracking_data = context.get("tracking_data", {})
        
        if context.get("request_type") in ["specific_episodes", "multiple_episodes"]:
            # For specific episodes, return the exact list
            return tracking_data.get("episodes", [])
        elif context.get("request_type") == "full_series":
            # For full series, return all episodes from TVDB
            return tracking_data.get("all_episodes_from_tvdb", [])
        else:
            return []
    
    def is_episode_expected(self, season: int, episode: int) -> bool:
        """
        Check if a specific episode should be tracked for this request.
        
        Args:
            season: Season number
            episode: Episode number
            
        Returns:
            bool: True if this episode should be tracked
        """
        expected_episodes = self.get_expected_episodes()
        return {"season": season, "episode": episode} in expected_episodes
    
    def _determine_scope(self, request_data: Dict[str, Any]) -> str:
        """
        Determine the scope of the request for tracking purposes.
        
        Returns:
            "specific_episodes", "full_series", or "unknown"
        """
        request_type = request_data.get("request_type", "")
        
        if request_type in ["specific_episodes", "multiple_episodes"]:
            return "specific_episodes"
        elif request_type == "full_series":
            return "full_series"
        else:
            return "unknown"
    
    def _extract_tracking_data(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract the data needed for tracking completion across pipeline stages.
        
        Returns:
            Dict with tracking information
        """
        tracking_data = {}
        
        # Store episode list for specific requests
        if "episodes" in request_data:
            tracking_data["episodes"] = request_data["episodes"]
        
        # Store TVDB data for full series requests
        if "tvdb_data" in request_data and "episodes" in request_data["tvdb_data"]:
            tracking_data["all_episodes_from_tvdb"] = request_data["tvdb_data"]["episodes"]
        
        # Store metadata
        if "show_title" in request_data:
            tracking_data["show_title"] = request_data["show_title"]
        if "year" in request_data:
            tracking_data["year"] = request_data["year"]
            
        return tracking_data


def get_request_context_manager(base_path: Path) -> RequestContextManager:
    """
    Factory function to get a RequestContextManager for a given path.
    
    Args:
        base_path: Directory path to manage context for
        
    Returns:
        RequestContextManager instance
    """
    return RequestContextManager(base_path)


# Example usage:
# 
# # Stage 01: Create context when adding to Sonarr
# context_mgr = get_request_context_manager(Path("/tv_shows/1080p/The Office (2005)/Season 01"))
# context_mgr.create_context({
#     "request_type": "multiple_episodes",
#     "original_request": "The Office (2005) S01E01, S02E01",
#     "episodes": [{"season": 1, "episode": 1}, {"season": 2, "episode": 1}],
#     "show_title": "The Office",
#     "year": 2005
# })
#
# # Stage 03: Check if episode should be tracked
# if context_mgr.is_episode_expected(1, 1):
#     # Process this episode
#     pass
