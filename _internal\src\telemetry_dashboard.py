"""
Telemetry Dashboard with Intelligent Fallback Integration
========================================================

This dashboard integrates the intelligent fallback system with the existing
telemetry monitoring to show:

1. Download failure detection
2. Fallback attempt progression 
3. Multiple attempt tracking
4. Success/failure status for each attempt

When a download fails, it automatically triggers the corrected fallback logic
and shows the progression in the dashboard.
"""

import asyncio
import logging
import json
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from pathlib import Path

# Import existing systems
from ..utils.real_time_telemetry import RealTimeTelemetry, DownloadJob
from ..utils.telemetry_integration import TelemetryIntegrator
from .intelligent_fallback_system import IntelligentFallbackSystem


class TelemetryDashboard:
    """
    Telemetry dashboard that integrates intelligent fallback system.
    
    Features:
    - Tracks multiple download attempts per movie
    - Shows fallback progression and candidate details
    - Automatic failure detection and fallback triggering
    - Enhanced dashboard display with attempt history
    """
    
    def __init__(self, settings_dict: Dict[str, Any], logger: logging.Logger):
        self.settings = settings_dict
        self.logger = logger
        
        # Initialize core systems
        self.telemetry_integrator = None
        self.fallback_system = IntelligentFallbackSystem(settings_dict, logger)
        
        # Track multiple attempts per movie
        self.movie_attempts: Dict[int, Dict[str, Any]] = {}
        
        # Dashboard state
        self.monitoring_active = False
        self.dashboard_file = None
        
        self.logger.info("🔧 Enhanced Telemetry Dashboard initialized")
        self.logger.info("   📊 Telemetry integration ready")
        self.logger.info("   🎯 Intelligent fallback integrated")
    
    async def start_monitoring_with_fallback(self, radarr_id: int, movie_title: str, 
                                           candidate_guid: str, candidate_details: Dict[str, Any],
                                           user_selection_index: Optional[int] = None,
                                           original_system_recommendation_index: Optional[int] = None):
        """
        Start monitoring a movie download with automatic fallback protection.
        
        Args:
            radarr_id: Radarr movie ID
            movie_title: Movie title
            candidate_guid: GUID of the download candidate
            candidate_details: Full candidate information (title, size, index, etc.)
            user_selection_index: If user made a selection, the index
            original_system_recommendation_index: Original system recommendation index
        """
        
        # Initialize attempt tracking for this movie
        attempt_number = 1
        if radarr_id in self.movie_attempts:
            # This is a fallback attempt
            attempt_number = len(self.movie_attempts[radarr_id]["attempts"]) + 1
        else:
            # First attempt
            self.movie_attempts[radarr_id] = {
                "movie_title": movie_title,
                "started": datetime.now(),
                "user_selection_index": user_selection_index,
                "original_system_recommendation_index": original_system_recommendation_index,
                "attempts": []
            }
        
        # Record this attempt
        attempt_info = {
            "attempt_number": attempt_number,
            "candidate_guid": candidate_guid,
            "candidate_details": candidate_details,
            "started": datetime.now(),
            "status": "downloading",
            "progress": 0.0,
            "failure_reason": None,
            "is_fallback": attempt_number > 1
        }
        
        self.movie_attempts[radarr_id]["attempts"].append(attempt_info)
        
        self.logger.info(f"📡 Started monitoring attempt #{attempt_number}: {movie_title}")
        self.logger.info(f"   🎬 Radarr ID: {radarr_id}")
        self.logger.info(f"   📄 Candidate: {candidate_details.get('title', 'Unknown')}")
        self.logger.info(f"   💾 Size: {candidate_details.get('size', 0) / (1024**3):.2f} GB")
        if attempt_number > 1:
            self.logger.info(f"   🔄 This is fallback attempt #{attempt_number}")
        
        # Start telemetry monitoring if not already active
        if not self.monitoring_active:
            await self._start_enhanced_monitoring()
    
    async def _start_enhanced_monitoring(self, check_interval: int = 10):
        """Start the enhanced monitoring loop with fallback integration."""
        
        if self.monitoring_active:
            return
        
        self.monitoring_active = True
        
        # Initialize telemetry integrator
        self.telemetry_integrator = TelemetryIntegrator(self.settings, self.logger, verbose_mode=True)
        await self.telemetry_integrator.__aenter__()
        
        # Start dashboard logging
        self._start_enhanced_dashboard_logging()
        
        self.logger.info(f"🚀 Enhanced monitoring started (interval: {check_interval}s)")
        self.logger.info(f"   📊 Dashboard file: {self.dashboard_file}")
        
        try:
            while self.monitoring_active:
                await self._update_enhanced_dashboard()
                await self._check_for_failures()
                await asyncio.sleep(check_interval)
                
        except asyncio.CancelledError:
            self.logger.info("🛑 Enhanced monitoring cancelled")
        except Exception as e:
            self.logger.error(f"Error in enhanced monitoring: {e}")
        finally:
            await self._cleanup_monitoring()
    
    def _start_enhanced_dashboard_logging(self):
        """Start enhanced dashboard logging to a separate file."""
        
        timestamp = datetime.now().strftime("%Y-%m-%d_%I-%M-%S-%p")
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        self.dashboard_file = log_dir / f"telemetry_dashboard_{timestamp}.txt"
        
        # Write header
        with open(self.dashboard_file, 'w', encoding='utf-8') as f:
            header = f"""=== ENHANCED TELEMETRY DASHBOARD WITH INTELLIGENT FALLBACK ===
Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Log File: {self.dashboard_file}
Features: Multi-attempt tracking, automatic fallback, candidate progression
==============================================================================

"""
            f.write(header)
    
    async def _update_enhanced_dashboard(self):
        """Update the enhanced dashboard display."""
        
        if not self.dashboard_file:
            return
        
        try:
            # Get current telemetry status
            download_summary = self.telemetry_integrator.get_download_summary()
            active_downloads = download_summary.get("active_jobs", [])
            
            # Build enhanced dashboard display
            dashboard_content = self._build_enhanced_dashboard_display(active_downloads)
            
            # Write to dashboard file
            with open(self.dashboard_file, 'a', encoding='utf-8') as f:
                f.write(dashboard_content + "\n")
                f.flush()
                
        except Exception as e:
            self.logger.error(f"Error updating enhanced dashboard: {e}")
    
    def _build_enhanced_dashboard_display(self, active_downloads: List[Dict[str, Any]]) -> str:
        """Build the enhanced dashboard display content."""
        
        lines = []
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        # Header
        lines.append("┌─ ENHANCED DOWNLOAD DASHBOARD ─┐")
        lines.append(f"│ {timestamp} │ Monitoring {len(self.movie_attempts)} movies │")
        lines.append("├────────────────────────────────┤")
        
        if not self.movie_attempts:
            lines.append("│ No movies currently being monitored                           │")
            lines.append("└───────────────────────────────────────────────────────────┘")
            return "\n".join(lines)
        
        # Display each monitored movie
        for radarr_id, movie_info in self.movie_attempts.items():
            movie_title = movie_info["movie_title"]
            attempts = movie_info["attempts"]
            current_attempt = attempts[-1] if attempts else None
            
            if not current_attempt:
                continue
            
            # Find matching active download
            active_download = None
            for download in active_downloads:
                if movie_title in download.get("title", ""):
                    active_download = download
                    break
            
            # Build movie status display
            lines.extend(self._build_movie_status_display(
                movie_title, radarr_id, attempts, current_attempt, active_download
            ))
            lines.append("├────────────────────────────────┤")
        
        lines[-1] = "└───────────────────────────────────────────────────────────┘"
        
        return "\n".join(lines)
    
    def _build_movie_status_display(self, movie_title: str, radarr_id: int, 
                                  attempts: List[Dict[str, Any]], 
                                  current_attempt: Dict[str, Any],
                                  active_download: Optional[Dict[str, Any]]) -> List[str]:
        """Build the status display for a single movie."""
        
        lines = []
        
        # Movie header
        attempt_count = len(attempts)
        status_icon = "🔄" if attempt_count > 1 else "📥"
        lines.append(f"│ {status_icon} {movie_title[:40]:<40} │")
        
        # Current attempt info
        candidate_title = current_attempt["candidate_details"].get("title", "Unknown")
        candidate_size = current_attempt["candidate_details"].get("size", 0) / (1024**3)
        attempt_num = current_attempt["attempt_number"]
        
        lines.append(f"│   Attempt #{attempt_num}: {candidate_title[:35]:<35} │")
        lines.append(f"│   Size: {candidate_size:6.2f} GB │ Radarr ID: {radarr_id:<8} │")
        
        # Progress info
        if active_download:
            progress = active_download.get("progress", 0.0) * 100
            eta = active_download.get("eta", "Unknown")
            status = active_download.get("status", "Unknown")
            
            current_attempt["progress"] = progress / 100
            current_attempt["status"] = "downloading" if progress < 100 else "completed"
            
            lines.append(f"│   Progress: {progress:5.1f}% │ Status: {status:<12} │")
            lines.append(f"│   ETA: {eta:<15} │ Speed: {active_download.get('speed', 0):>8.1f} MB/s │")
        else:
            # No active download - check status
            if current_attempt["status"] == "failed":
                lines.append(f"│   Status: ❌ Failed                                     │")
                if current_attempt["failure_reason"]:
                    lines.append(f"│   Reason: {current_attempt['failure_reason'][:35]:<35} │")
            elif current_attempt["status"] == "completed":
                lines.append(f"│   Status: ✅ Completed                                  │")
            else:
                lines.append(f"│   Status: ⏸️  Queued/Starting                           │")
        
        # Attempt history summary
        if attempt_count > 1:
            lines.append(f"│   Attempt History:                                      │")
            for i, attempt in enumerate(attempts[:-1]):  # All except current
                attempt_status = "❌" if attempt["status"] == "failed" else "✅"
                candidate_name = attempt["candidate_details"].get("title", "Unknown")[:25]
                lines.append(f"│     #{i+1}: {attempt_status} {candidate_name:<25}        │")
        
        return lines
    
    async def _check_for_failures(self):
        """Check for download failures and trigger fallbacks."""
        
        try:
            for radarr_id, movie_info in list(self.movie_attempts.items()):
                current_attempt = movie_info["attempts"][-1]
                
                if current_attempt["status"] == "downloading":
                    # Check if this download has failed
                    failure_detected = await self._detect_download_failure(radarr_id, current_attempt)
                    
                    if failure_detected:
                        failure_reason = failure_detected
                        await self._handle_detected_failure(radarr_id, movie_info, current_attempt, failure_reason)
                        
        except Exception as e:
            self.logger.error(f"Error checking for failures: {e}")
    
    async def _detect_download_failure(self, radarr_id: int, current_attempt: Dict[str, Any]) -> Optional[str]:
        """Detect if a download has failed."""
        
        try:
            # Check telemetry for failure status
            download_summary = self.telemetry_integrator.get_download_summary()
            active_downloads = download_summary.get("active_jobs", [])
            
            movie_title = self.movie_attempts[radarr_id]["movie_title"]
            
            # Look for this movie in active downloads
            found_active = False
            for download in active_downloads:
                if movie_title in download.get("title", ""):
                    found_active = True
                    # Check if status indicates failure
                    if download.get("status") == "failed":
                        return "telemetry_reported_failed"
                    break
            
            # If not found in active downloads and we've been downloading for a while
            elapsed = datetime.now() - current_attempt["started"]
            if not found_active and elapsed > timedelta(minutes=2):
                # Check if progress was made recently
                if current_attempt["progress"] > 0 and current_attempt["progress"] < 1.0:
                    return "download_disappeared_from_queue"
            
            # Check for timeout (downloads taking too long)
            if elapsed > timedelta(hours=6):
                return f"download_timeout_after_{elapsed}"
            
            return None
            
        except Exception as e:
            self.logger.debug(f"Error detecting failure for {radarr_id}: {e}")
            return None
    
    async def _handle_detected_failure(self, radarr_id: int, movie_info: Dict[str, Any], 
                                     current_attempt: Dict[str, Any], failure_reason: str):
        """Handle a detected failure by triggering intelligent fallback."""
        
        try:
            movie_title = movie_info["movie_title"]
            failed_guid = current_attempt["candidate_guid"]
            user_selection_index = movie_info.get("user_selection_index")
            original_system_recommendation_index = movie_info.get("original_system_recommendation_index")
            
            # Mark current attempt as failed
            current_attempt["status"] = "failed"
            current_attempt["failure_reason"] = failure_reason
            
            self.logger.error(f"💥 FAILURE DETECTED: {movie_title}")
            self.logger.error(f"   🔍 Attempt #{current_attempt['attempt_number']} failed")
            self.logger.error(f"   📄 Failed candidate: {current_attempt['candidate_details'].get('title')}")
            self.logger.error(f"   🚨 Reason: {failure_reason}")
            
            # Write failure to dashboard
            if self.dashboard_file:
                failure_msg = f"""
!!! FAILURE DETECTED !!!
Movie: {movie_title}
Attempt: #{current_attempt['attempt_number']}
Failed Candidate: {current_attempt['candidate_details'].get('title')}
Reason: {failure_reason}
Time: {datetime.now().strftime('%H:%M:%S')}

Triggering intelligent fallback...

"""
                with open(self.dashboard_file, 'a', encoding='utf-8') as f:
                    f.write(failure_msg)
                    f.flush()
            
            # Trigger intelligent fallback
            import aiohttp
            async with aiohttp.ClientSession() as session:
                success = await self.fallback_system.handle_download_failure(
                    movie_title=movie_title,
                    failed_guid=failed_guid,
                    radarr_id=radarr_id,
                    session=session,
                    user_selection_index=user_selection_index,
                    original_system_recommendation_index=original_system_recommendation_index
                )
                
                if success:
                    self.logger.info(f"✅ Intelligent fallback triggered for {movie_title}")
                    
                    # The fallback system will start a new download
                    # We need to detect the new candidate and start monitoring it
                    await self._detect_and_monitor_fallback_download(radarr_id, movie_info)
                    
                else:
                    self.logger.error(f"❌ Intelligent fallback failed for {movie_title}")
                    # Mark movie as completely failed
                    movie_info["final_status"] = "all_attempts_failed"
                    
        except Exception as e:
            self.logger.error(f"Error handling detected failure: {e}")
    
    async def _detect_and_monitor_fallback_download(self, radarr_id: int, movie_info: Dict[str, Any]):
        """Detect the new fallback download and start monitoring it."""
        
        try:
            # Wait a moment for the fallback download to start
            await asyncio.sleep(10)
            
            # TODO: Detect the new download candidate
            # This would require checking what's currently downloading for this movie
            # and comparing it to previous attempts
            
            movie_title = movie_info["movie_title"]
            self.logger.info(f"🔍 Looking for new fallback download for {movie_title}")
            
            # For now, we'll assume the fallback is working and monitor will pick it up
            # In a full implementation, you'd query Radarr to get the new download details
            
        except Exception as e:
            self.logger.error(f"Error detecting fallback download: {e}")
    
    async def _cleanup_monitoring(self):
        """Clean up monitoring resources."""
        
        self.monitoring_active = False
        
        if self.telemetry_integrator:
            try:
                await self.telemetry_integrator.__aexit__(None, None, None)
            except Exception as e:
                self.logger.debug(f"Error cleaning up telemetry: {e}")
        
        # Write footer to dashboard
        if self.dashboard_file:
            try:
                footer = f"""

==============================================================================
=== ENHANCED TELEMETRY DASHBOARD END ===
Ended: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
==============================================================================
"""
                with open(self.dashboard_file, 'a', encoding='utf-8') as f:
                    f.write(footer)
            except Exception:
                pass
        
        self.logger.info("🛑 Enhanced monitoring cleanup completed")
    
    def get_monitoring_status(self) -> Dict[str, Any]:
        """Get current monitoring status with attempt details."""
        
        return {
            "monitoring_active": self.monitoring_active,
            "monitored_movies": len(self.movie_attempts),
            "dashboard_file": str(self.dashboard_file) if self.dashboard_file else None,
            "movies": [
                {
                    "radarr_id": radarr_id,
                    "movie_title": info["movie_title"],
                    "attempt_count": len(info["attempts"]),
                    "current_status": info["attempts"][-1]["status"] if info["attempts"] else "unknown",
                    "current_candidate": info["attempts"][-1]["candidate_details"].get("title") if info["attempts"] else "unknown",
                    "started": info["started"].isoformat(),
                    "elapsed_minutes": int((datetime.now() - info["started"]).total_seconds() / 60)
                }
                for radarr_id, info in self.movie_attempts.items()
            ]
        }
    
    def stop_monitoring(self):
        """Stop monitoring."""
        self.monitoring_active = False


# Global instance for easy access
_dashboard: Optional[TelemetryDashboard] = None

def get_dashboard(settings_dict: Dict[str, Any], logger: logging.Logger) -> TelemetryDashboard:
    """Get or create the global dashboard instance."""
    global _dashboard
    
    if _dashboard is None:
        _dashboard = TelemetryDashboard(settings_dict, logger)
    
    return _dashboard


# Integration functions
async def start_enhanced_monitoring_for_movie(radarr_id: int, movie_title: str, 
                                            candidate_guid: str, candidate_details: Dict[str, Any],
                                            settings_dict: Dict[str, Any], logger: logging.Logger,
                                            user_selection_index: Optional[int] = None,
                                            original_system_recommendation_index: Optional[int] = None):
    """
    Start enhanced monitoring for a movie download with automatic fallback.
    
    This is the main function to call when starting a download that should be protected
    by the intelligent fallback system.
    """
    
    dashboard = get_dashboard(settings_dict, logger)
    
    await dashboard.start_monitoring_with_fallback(
        radarr_id=radarr_id,
        movie_title=movie_title,
        candidate_guid=candidate_guid,
        candidate_details=candidate_details,
        user_selection_index=user_selection_index,
        original_system_recommendation_index=original_system_recommendation_index
    )


if __name__ == "__main__":
    # Test the enhanced dashboard
    import logging
    
    logging.basicConfig(level=logging.INFO, format='%(message)s')
    logger = logging.getLogger(__name__)
    
    print("🧪 Testing Telemetry Dashboard")
    print("=" * 60)
    
    settings = {"General": {"workspace_dir": "workspace"}}
    dashboard = TelemetryDashboard(settings, logger)
    
    # Test monitoring status
    status = dashboard.get_monitoring_status()
    print(f"📊 Dashboard Status: {status}")
    
    print("✅ Enhanced telemetry dashboard initialized and ready")


# ============================================================================
# LEGACY COMPATIBILITY FUNCTIONS
# These functions maintain compatibility with existing tests and CLI tools
# ============================================================================

import csv
import sqlite3
from collections import defaultdict
from typing import Iterable


def tail_events(events_file: Path, max_lines: int = 1000) -> Iterable[Dict[str, Any]]:
    """
    Read the last N lines from an events file efficiently.
    Legacy function for backward compatibility.
    """
    if not events_file.exists():
        return []
    # Simple tail: read last N lines efficiently
    try:
        with events_file.open('rb') as f:
            f.seek(0, 2)
            size = f.tell()
            block = bytearray()
            lines = []
            while size > 0 and len(lines) < max_lines:
                step = min(4096, size)
                size -= step
                f.seek(size)
                block = f.read(step) + block
                lines = block.split(b'\n')
            text = block.decode('utf-8', errors='ignore').splitlines()[-max_lines:]
            for line in text:
                try:
                    yield json.loads(line)
                except Exception:
                    continue
    except Exception:
        return []


def aggregate_counts(events: Iterable[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Aggregate event counts by various dimensions.
    Legacy function for backward compatibility.
    """
    by_type = defaultdict(int)
    by_series = defaultdict(int)
    by_movie = defaultdict(int)
    by_year = defaultdict(int)
    by_title = defaultdict(int)
    by_resolution = defaultdict(int)
    by_day = defaultdict(int)
    
    for evt in events:
        etype = evt.get('type')
        if etype:
            by_type[etype] += 1
        data = evt.get('data') or evt  # support both wrapped and direct payloads
        
        # Aggregate by content-kind dimensions (legacy format)
        if 'radarr_id' in data and 'title' in data:
            key = (data['radarr_id'], data['title'])
            by_movie[key] += 1
        if 'series_id' in data and 'title' in data:
            key = (data['series_id'], data['title'])
            by_series[key] += 1
        if 'title' in data:
            by_title[data['title']] += 1
        if 'year' in data:
            by_year[str(data['year'])] += 1
        if 'resolution' in data:
            by_resolution[data['resolution']] += 1
        if 'timestamp' in data:
            try:
                ts = datetime.fromisoformat(data['timestamp'].replace('Z', '+00:00'))
                day_key = ts.strftime('%Y-%m-%d')
                by_day[day_key] += 1
            except Exception:
                pass
    
    return {
        'by_type': dict(by_type),
        'by_series': {str(k): v for k, v in by_series.items()},
        'by_movie': {str(k): v for k, v in by_movie.items()},
        'by_year': dict(by_year),
        'by_title': dict(by_title),
        'by_resolution': dict(by_resolution),
        'by_day': dict(by_day),
    }


def write_csv(summary: Dict[str, Any], out_path: Path) -> None:
    """
    Write aggregated summary to CSV file.
    Legacy function for backward compatibility.
    """
    out_path.parent.mkdir(parents=True, exist_ok=True)
    with out_path.open('w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow(['metric', 'key', 'value'])
        for metric, mapping in summary.items():
            if isinstance(mapping, dict):
                for k, v in mapping.items():
                    writer.writerow([metric, k, v])


def write_sqlite(summary: Dict[str, Any], db_path: Path) -> None:
    """
    Write aggregated summary to SQLite database.
    Legacy function for backward compatibility.
    """
    db_path.parent.mkdir(parents=True, exist_ok=True)
    conn = sqlite3.connect(str(db_path))
    cur = conn.cursor()
    cur.execute('CREATE TABLE IF NOT EXISTS metrics (metric TEXT, key TEXT, value INTEGER)')
    cur.execute('DELETE FROM metrics')
    rows = []
    for metric, mapping in summary.items():
        if isinstance(mapping, dict):
            rows.extend((metric, k, v) for k, v in mapping.items())
    if rows:
        cur.executemany('INSERT INTO metrics (metric, key, value) VALUES (?, ?, ?)', rows)
    conn.commit()
    conn.close()
