from __future__ import annotations
import argparse
from pathlib import Path
from typing import Any, Dict, List, Optional
import json
import sys
from pathlib import Path
from typing import Any, Dict, List, Optional, cast
import asyncio

from .nzb_parser import NZBParser
from .analyze_release import load_servers
from .core_analyzer import analyze_metadata
from .indexer_client import extract_newznab_id, fetch_nzb
from .sonarr_client import fetch_releases_for_episode, manual_search_episode, grab_release
from .history_store import DecisionHistory
from .orchestrator_common import load_config, choose_best


 # load_config and choose_best now imported from orchestrator_common

async def process(args: argparse.Namespace) -> int:
    cfg = load_config(Path(args.config))
    sonarr_cfg_raw = cast(Dict[str, Any], cfg.get('sonarr') or {})
    sonarr_cfg: Dict[str, Any] = sonarr_cfg_raw
    indexers_cfg = {i['name']: i for i in cfg.get('indexers', [])}
    if not sonarr_cfg:
        print('Missing sonarr config', file=sys.stderr)
        return 2
    if not args.episode_id:
        print('At least one --episode-id required', file=sys.stderr)
        return 2
    servers = load_servers(Path(args.servers_config))
    history = DecisionHistory(Path(args.history))
    all_candidate_reports: List[Dict[str, Any]] = []
    parser_obj = NZBParser()
    for ep_id in args.episode_id:
        if args.manual_search and args.series_id:
            manual_search_episode(sonarr_cfg['url'], sonarr_cfg['api_key'], args.series_id, [ep_id])
        releases = fetch_releases_for_episode(sonarr_cfg['url'], sonarr_cfg['api_key'], ep_id)[:args.max_candidates]
        for rel in releases:
            guid = rel.get('guid', '')
            idx_name = rel.get('indexer') or rel.get('indexerName') or ''
            key = f"{idx_name}:{guid}"
            if history.get(key):
                continue
            idx_cfg = indexers_cfg.get(idx_name)
            if not idx_cfg:
                continue
            nzb_id = extract_newznab_id(guid)
            if not nzb_id:
                continue
            try:
                raw = fetch_nzb(idx_cfg['base_url'], idx_cfg['api_key'], nzb_id)
                meta = parser_obj.parse_bytes(raw)  # type: ignore
                report = await analyze_metadata(meta, servers=servers, retention_days=4000, dry_run=args.dry_run, verbose=False, sample_cap=args.sample_cap)
                report.update({'guid': guid, 'indexer': idx_name, 'title': rel.get('title'), 'size': rel.get('size'), 'episode_id': ep_id})
                history.put(key, report['decision'], report)
                all_candidate_reports.append(report)
            except Exception as e:  # noqa
                all_candidate_reports.append({'guid': guid, 'indexer': idx_name, 'error': str(e), 'episode_id': ep_id})
    best: Optional[Dict[str, Any]] = choose_best([r for r in all_candidate_reports if 'decision' in r])
    output: Dict[str, Any] = {'candidates': all_candidate_reports, 'best': best}
    print(json.dumps(output, indent=2))
    if best and not args.no_grab and best['decision'] in ('ACCEPT', 'RISKY_LOW_PARITY'):
        try:
            grab_release(sonarr_cfg['url'], sonarr_cfg['api_key'], best['guid'])
            print(f"Grabbed release: {best['guid']}")
        except Exception as e:  # noqa
            print(f"Failed to grab: {e}", file=sys.stderr)
    return 0
def main():  # noqa
    parser = argparse.ArgumentParser(description='Preflight select best Sonarr release candidates via NZB structural probe.')
    parser.add_argument('--config', required=True, help='Path to config/preflight_config.json')
    parser.add_argument('--episode-id', type=int, action='append', help='Episode ID (repeatable)')
    parser.add_argument('--series-id', type=int, help='Series ID (for manual search)')
    parser.add_argument('--manual-search', action='store_true', help='Trigger a Sonarr manual search before evaluating releases')
    # Increased defaults per restored design discussion (Recording 26)
    parser.add_argument('--max-candidates', type=int, default=15, help='Max releases per episode to evaluate (default 15)')
    parser.add_argument('--servers-config', required=True, help='Path to servers config JSON (same as analyze_release)')
    parser.add_argument('--history', default='data/preflight_history.json', help='Decision history JSON path')
    parser.add_argument('--dry-run', action='store_true', help='Do not actually grab even if acceptable (overridden by --no-grab)')
    parser.add_argument('--sample-cap', type=int, default=500, help='Cap on total sampled segments across files (default 500)')
    parser.add_argument('--no-grab', action='store_true', help='Never grab automatically (output only)')
    args = parser.parse_args()
    rc = asyncio.run(process(args))
    if rc:
        sys.exit(rc)
 
if __name__ == '__main__':  # pragma: no cover
    main()
