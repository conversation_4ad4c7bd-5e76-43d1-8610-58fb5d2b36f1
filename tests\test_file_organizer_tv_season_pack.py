import asyncio
import tempfile
from pathlib import Path
import unittest

from _internal.src.file_organizer import organize_tv_show
from _internal.utils.tv_show_naming import TVShowNamingHelper


class DummyLogger:
    def __getattr__(self, name):
        def _log(*args, **kwargs):
            pass
        return _log


class TestTVOrganizerSeasonPack(unittest.TestCase):
    def test_season_pack_only_missing_episodes(self):
        logger = DummyLogger()
        with tempfile.TemporaryDirectory() as tmp:
            tmp_path = Path(tmp)
            organized_base = tmp_path / "workspace" / "2_downloaded_and_organized"
            organized_base.mkdir(parents=True)

            # Existing library: episode 1 already organized
            series_dir = organized_base / "tv_shows" / "1080p" / "Show Name (2020)" / "Season 01"
            series_dir.mkdir(parents=True)
            existing_ep = series_dir / "S01E01.mkv"
            existing_ep.write_bytes(b"old")
            (series_dir / ".organized").touch()  # marker exists

            # Season pack download containing E01 and E02
            download_dir = tmp_path / "workspace" / "1_downloading" / "complete_raw" / "Show.Name.S01.Season.Pack"
            download_dir.mkdir(parents=True)
            ep1 = download_dir / "Show.Name.S01E01.1080p.mkv"
            ep1.write_bytes(b"new1")
            ep2 = download_dir / "Show.Name.S01E02.1080p.mkv"
            ep2.write_bytes(b"new2")

            content_info = {"title": "Show Name", "year": 2020}
            resolution = "1080p"
            helper = TVShowNamingHelper()

            # season-pack detection happens inside organize_tv_show; main_file_path can be one of the files
            ok = asyncio.run(organize_tv_show(content_info, str(ep1), download_dir, organized_base, resolution, helper, logger))
            self.assertTrue(ok)

            # E01 should remain old (skip overwrite), E02 should be moved
            self.assertTrue(existing_ep.exists())
            self.assertEqual((series_dir / "S01E02.mkv").read_bytes(), b"new2")
            self.assertFalse(ep2.exists())


if __name__ == "__main__":
    unittest.main()

